#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت ترحيل النظام من JSON إلى MySQL
يقوم بترحيل جميع البيانات وتحديث النظام لاستخدام MySQL
"""

import os
import sys
import logging
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from shared.database.migration_script import DataMigrator
from shared.database.mysql_manager import mysql_manager
from shared.database.mysql_backup_manager import mysql_backup_manager

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('migration.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def print_banner():
    """طباعة شعار الترحيل"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    🚀 ترحيل قاعدة البيانات                    ║
    ║                     من JSON إلى MySQL                       ║
    ║                                                              ║
    ║              نظام صلاح الدين الدروبي المتقدم                 ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_prerequisites():
    """التحقق من المتطلبات المسبقة"""
    logger.info("🔍 التحقق من المتطلبات المسبقة...")
    
    # التحقق من وجود MySQL
    try:
        import mysql.connector
        logger.info("✅ مكتبة MySQL متوفرة")
    except ImportError:
        logger.error("❌ مكتبة MySQL غير متوفرة. يرجى تثبيت المتطلبات:")
        logger.error("pip install -r requirements.txt")
        return False
    
    # التحقق من الاتصال بقاعدة البيانات
    try:
        health = mysql_manager.health_check()
        if health['status'] == 'healthy':
            logger.info("✅ الاتصال بقاعدة البيانات MySQL سليم")
        else:
            logger.error("❌ فشل في الاتصال بقاعدة البيانات MySQL")
            logger.error(f"الخطأ: {health.get('error', 'غير محدد')}")
            return False
    except Exception as e:
        logger.error(f"❌ خطأ في التحقق من قاعدة البيانات: {e}")
        return False
    
    # التحقق من وجود ملفات JSON
    json_files = [
        "shared/database/users_data.json",
        "shared/database/wallets_database.json",
        "shared/database/admin_data.json"
    ]
    
    missing_files = []
    for file_path in json_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        logger.warning("⚠️ بعض ملفات JSON غير موجودة:")
        for file_path in missing_files:
            logger.warning(f"   - {file_path}")
        logger.info("سيتم تخطي الملفات المفقودة أثناء الترحيل")
    else:
        logger.info("✅ جميع ملفات JSON موجودة")
    
    return True

def create_initial_backup():
    """إنشاء نسخة احتياطية أولية"""
    logger.info("💾 إنشاء نسخة احتياطية أولية من قاعدة البيانات...")
    
    try:
        success, backup_name, backup_path = mysql_backup_manager.create_backup(
            backup_type="manual", 
            compress=True
        )
        
        if success:
            logger.info(f"✅ تم إنشاء النسخة الاحتياطية: {backup_name}")
            logger.info(f"📁 المسار: {backup_path}")
            return True
        else:
            logger.error("❌ فشل في إنشاء النسخة الاحتياطية الأولية")
            return False
            
    except Exception as e:
        logger.error(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
        return False

def run_migration():
    """تشغيل عملية الترحيل"""
    logger.info("🔄 بدء عملية ترحيل البيانات...")
    
    try:
        migrator = DataMigrator()
        success = migrator.run_migration()
        
        if success:
            logger.info("🎉 تم ترحيل البيانات بنجاح!")
            return True
        else:
            logger.error("❌ فشل في ترحيل البيانات")
            return False
            
    except Exception as e:
        logger.error(f"❌ خطأ في عملية الترحيل: {e}")
        return False

def verify_migration():
    """التحقق من صحة الترحيل"""
    logger.info("🔍 التحقق من صحة الترحيل...")
    
    try:
        # التحقق من وجود البيانات في الجداول
        tables_to_check = ['users', 'wallets', 'admin_data']
        
        for table in tables_to_check:
            try:
                query = f"SELECT COUNT(*) as count FROM {table}"
                result = mysql_manager.execute_query(query)
                count = result[0]['count'] if result else 0
                
                if count > 0:
                    logger.info(f"✅ جدول {table}: {count} سجل")
                else:
                    logger.warning(f"⚠️ جدول {table}: فارغ")
                    
            except Exception as e:
                logger.error(f"❌ خطأ في فحص جدول {table}: {e}")
        
        logger.info("✅ تم التحقق من صحة الترحيل")
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في التحقق من صحة الترحيل: {e}")
        return False

def setup_backup_scheduler():
    """إعداد جدولة النسخ الاحتياطي التلقائي"""
    logger.info("⏰ إعداد جدولة النسخ الاحتياطي التلقائي...")
    
    try:
        mysql_backup_manager.start_scheduler()
        logger.info("✅ تم تفعيل النسخ الاحتياطي التلقائي")
        logger.info("📅 الجدولة:")
        logger.info("   - نسخة احتياطية يومية: 02:00 صباحاً")
        logger.info("   - نسخة احتياطية أسبوعية: الأحد 03:00 صباحاً")
        logger.info("   - نسخة احتياطية شهرية: أول كل شهر")
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في إعداد جدولة النسخ الاحتياطي: {e}")
        return False

def print_migration_summary():
    """طباعة ملخص الترحيل"""
    logger.info("📊 ملخص الترحيل:")
    
    try:
        # إحصائيات المستخدمين
        from shared.database.mysql_user_manager import mysql_user_manager
        user_stats = mysql_user_manager.get_user_statistics()
        
        logger.info(f"👥 المستخدمون:")
        logger.info(f"   - إجمالي المستخدمين: {user_stats.get('total_users', 0)}")
        logger.info(f"   - المستخدمون النشطون: {user_stats.get('active_users', 0)}")
        logger.info(f"   - المستخدمون المحظورون: {user_stats.get('banned_users', 0)}")
        
        # إحصائيات المحافظ
        from shared.database.mysql_wallet_manager import mysql_wallet_manager
        wallet_stats = mysql_wallet_manager.get_wallet_statistics()
        
        logger.info(f"💰 المحافظ:")
        logger.info(f"   - إجمالي المحافظ: {wallet_stats.get('total_wallets', 0)}")
        logger.info(f"   - المحافظ النشطة: {wallet_stats.get('active_wallets', 0)}")
        logger.info(f"   - إجمالي الأرصدة: {wallet_stats.get('total_balance', 0):.8f} إكسا")
        logger.info(f"   - إجمالي القروض: {wallet_stats.get('total_loans', 0):.8f} إكسا")
        
    except Exception as e:
        logger.error(f"❌ خطأ في عرض ملخص الترحيل: {e}")

def print_next_steps():
    """طباعة الخطوات التالية"""
    next_steps = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                        🎯 الخطوات التالية                    ║
    ╠══════════════════════════════════════════════════════════════╣
    ║                                                              ║
    ║  1. تحديث ملفات التكوين لاستخدام MySQL                      ║
    ║  2. تحديث الكود لاستخدام المدراء الجديدة                    ║
    ║  3. اختبار النظام للتأكد من عمله بشكل صحيح                  ║
    ║  4. إنشاء نسخة احتياطية نهائية من ملفات JSON                ║
    ║  5. حذف ملفات JSON القديمة (اختياري)                        ║
    ║                                                              ║
    ║  📝 ملاحظات مهمة:                                           ║
    ║  • تم تفعيل النسخ الاحتياطي التلقائي                        ║
    ║  • يمكن استعادة البيانات من النسخ الاحتياطية عند الحاجة     ║
    ║  • تحقق من سجلات النظام بانتظام                            ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(next_steps)

def main():
    """الدالة الرئيسية"""
    print_banner()
    
    logger.info("🚀 بدء عملية ترحيل النظام من JSON إلى MySQL")
    
    # التحقق من المتطلبات المسبقة
    if not check_prerequisites():
        logger.error("❌ فشل في التحقق من المتطلبات المسبقة")
        return False
    
    # إنشاء نسخة احتياطية أولية
    if not create_initial_backup():
        logger.error("❌ فشل في إنشاء النسخة الاحتياطية الأولية")
        response = input("هل تريد المتابعة بدون نسخة احتياطية؟ (y/N): ")
        if response.lower() != 'y':
            return False
    
    # تشغيل عملية الترحيل
    if not run_migration():
        logger.error("❌ فشل في عملية الترحيل")
        return False
    
    # التحقق من صحة الترحيل
    if not verify_migration():
        logger.error("❌ فشل في التحقق من صحة الترحيل")
        return False
    
    # إعداد النسخ الاحتياطي التلقائي
    setup_backup_scheduler()
    
    # طباعة ملخص الترحيل
    print_migration_summary()
    
    # طباعة الخطوات التالية
    print_next_steps()
    
    logger.info("🎉 تم ترحيل النظام بنجاح من JSON إلى MySQL!")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("⏹️ تم إيقاف عملية الترحيل بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ خطأ غير متوقع: {e}")
        sys.exit(1)
