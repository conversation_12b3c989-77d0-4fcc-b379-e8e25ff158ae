# 🏦 نظام المحافظ التلقائي - دليل شامل

**الرقم:** WL-002  
**التاريخ:** 2025-07-22  
**الحالة:** ✅ مكتمل  

---

## 📋 نظرة عامة

تم تطبيق نظام محافظ تلقائي في البوت يقوم بإنشاء محفظة لكل مستخدم جديد يدخل البوت لأول مرة.

## 🎯 الميزات

### ✨ الإنشاء التلقائي
- **إنشاء فوري**: يتم إنشاء المحفظة تلقائياً عند أول دخول للمستخدم
- **رقم فريد**: كل محفظة لها رقم فريد مكون من 10 خانات
- **تنسيق الرقم**: يبدأ بـ 909 (ثابت) + 7 أرقام عشوائية

### 💳 معلومات المحفظة
- **رقم المحفظة**: رقم فريد للتعريف
- **الرصيد**: يبدأ بـ 0.00 إكسا
- **العملة**: إكسا افتراضياً (1 إكسا = 3 USD)
- **الحالة**: نشطة/معطلة
- **التحقق**: مُفعل/غير مُفعل
- **تاريخ الإنشاء**: وقت إنشاء المحفظة

## 🗂️ هيكل قاعدة البيانات

### 📁 الملفات
```
shared/database/
├── wallets_database.json     # قاعدة بيانات المحافظ
├── users_data.json          # بيانات المستخدمين (محدثة)
└── wallet_manager.py        # مدير المحافظ المشترك
```

### 📊 بنية بيانات المحفظة
```json
{
  "9091234567": {
    "wallet_number": "9091234567",
    "user_id": 123456789,
    "user_name": "اسم المستخدم",
    "username": "@username",
    "balance": 0.0,
    "currency": "إكسا",
    "status": "active",
    "created_at": "2025-07-22 12:00:00",
    "last_transaction": null,
    "transaction_count": 0,
    "is_verified": false,
    "security_level": "basic"
  }
}
```

## 🔧 التكامل مع النظام

### 🤖 البوت الرئيسي
- **إنشاء تلقائي**: عند تسجيل مستخدم جديد
- **عرض المحفظة**: زر "💰 محفظتي"
- **معلومات شاملة**: رقم المحفظة، الرصيد، الحالة
- **إدارة الرصيد**: إضافة وخصم الرصيد
- **نظام الفواتير**: إنشاء فواتير للمعاملات

### 🛡️ بوت الإدارة
- **قائمة المستخدمين**: تعرض معلومات المحفظة
- **إحصائيات المحافظ**: زر "💰 إحصائيات المحافظ"
- **إدارة المحافظ**: تفعيل/إلغاء تفعيل
- **إدارة الأرصدة**: إضافة وخصم الأرصدة للمستخدمين
- **تقارير مالية**: تقارير شاملة للمعاملات

## 💰 نظام العملات

### 🪙 العملة الأساسية - إكسا
- **الرمز**: إكسا
- **القيمة**: 1 إكسا = 3 USD
- **التوكن**: 1 إكسا = 524,288 ألف توكن
- **العرض**: بدون أرقام عشرية (.00)

### 💵 التحويلات
```python
# أمثلة التحويل
100 إكسا = 300 USD
50 إكسا = 150 USD = 26,214,400 ألف توكن
```

## 📈 الإحصائيات

### 📊 المتاحة في بوت الإدارة
- إجمالي المحافظ
- المحافظ النشطة/المعطلة
- إجمالي الرصيد (بالإكسا والدولار)
- المحافظ المُحققة/غير المُحققة
- إحصائيات المعاملات اليومية/الشهرية

## 🚀 التشغيل

### 1️⃣ للمستخدمين الجدد
المحافظ تُنشأ تلقائياً - لا حاجة لأي إجراء

### 2️⃣ للمستخدمين الموجودين
```bash
python create_wallets_for_existing_users.py
```

## 🔍 استخدام المحفظة

### 👤 للمستخدمين
1. اضغط على زر "💰 محفظتي"
2. اعرض معلومات محفظتك
3. انسخ رقم المحفظة عند الحاجة
4. اطلب إضافة رصيد من الإدارة

### 👨‍💼 للمديرين
1. "👥 قائمة المستخدمين" - لرؤية محافظ جميع المستخدمين
2. "💰 إحصائيات المحافظ" - لرؤية الإحصائيات العامة
3. أوامر إدارة الأرصدة:
   - `/wallet_info [رقم_المحفظة]`
   - `/add_balance [رقم_المحفظة] [المبلغ]`
   - `/deduct_balance [رقم_المحفظة] [المبلغ]`
   - `/balance_history [رقم_المحفظة]`

## 🧾 نظام الفواتير

### 📄 إنشاء الفواتير
- **تلقائي**: فاتورة لكل معاملة
- **تفصيلي**: يحتوي على جميع تفاصيل المعاملة
- **QR Code**: رمز استجابة سريعة للتحقق
- **PDF**: ملف PDF عالي الجودة

### 📱 الفواتير التفاعلية
- **أزرار سريعة**: عرض وطباعة الفاتورة
- **انتهاء صلاحية**: 24 ساعة افتراضياً
- **تنظيف تلقائي**: حذف الفواتير منتهية الصلاحية

## 🔧 الصيانة

### 📝 السجلات
- جميع عمليات المحافظ مُسجلة
- تتبع الإنشاء والتحديثات
- رصد الأخطاء والمشاكل
- سجلات منفصلة للمعاملات المالية

### 🔄 النسخ الاحتياطية
- تُحفظ مع بيانات النظام
- استعادة تلقائية عند الحاجة
- نسخ احتياطية يومية للمعاملات

## ⚠️ ملاحظات مهمة

1. **الأمان**: أرقام المحافظ فريدة ولا تتكرر
2. **الأداء**: النظام محسن للسرعة والكفاءة
3. **التوافق**: يعمل مع البوت الرئيسي والإداري
4. **المستقبل**: قابل للتوسع لإضافة ميزات دفع
5. **العملة**: جميع المبالغ تُعرض بالإكسا بدون أرقام عشرية

## 🛠️ استكشاف الأخطاء

### ❌ مشاكل شائعة
- **لا توجد محفظة**: تشغيل سكريبت إنشاء المحافظ
- **رقم مكرر**: النظام يتعامل مع هذا تلقائياً
- **خطأ في البيانات**: فحص ملفات قاعدة البيانات
- **مشاكل الفواتير**: فحص مجلد الفواتير المؤقتة

### 🔧 الحلول
```bash
# إنشاء محافظ للمستخدمين الموجودين
python create_wallets_for_existing_users.py

# فحص قاعدة البيانات
python -c "from shared.database.wallet_manager import WalletManager; wm = WalletManager(); print(wm.get_wallet_statistics())"

# تنظيف الفواتير منتهية الصلاحية
python -c "from shared.invoices.interactive_invoice_manager import InteractiveInvoiceManager; iim = InteractiveInvoiceManager(); print(iim.cleanup_expired_invoices())"
```

## 📞 الدعم

للمساعدة أو الإبلاغ عن مشاكل، تواصل مع مطور النظام.

---

## 🔗 روابط ذات صلة

- [نظرة عامة على نظام المحافظ](001_wallet_system_overview.md)
- [نظام إدارة الفواتير](../invoices/README.md)
- [دليل الإدارة](../admin/001_admin_monitoring_overview.md)

---

**تاريخ التحديث**: 2025-07-22  
**الإصدار**: 2.0.0  
**المطور**: صلاح الدين الدروبي
