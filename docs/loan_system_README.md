# 💳 نظام السلف التلقائي

## 📋 نظرة عامة

تم تطبيق نظام سلف تلقائي متطور في البوت يوفر خدمة سلف سريعة وآمنة للمستخدمين المؤهلين.

## 🎯 الميزات الرئيسية

### ✨ السلف التلقائي
- **منح فوري**: يتم منح السلفة تلقائياً للمستخدمين المؤهلين
- **مبلغ ثابت**: 3 إكسا (9 دولار أمريكي)
- **خصم تلقائي**: يتم خصم السلفة تلقائياً عند إضافة رصيد جديد

### 🔒 شروط الأهلية
- **المحفظة المُحققة**: يجب أن تكون لديك معاملات سابقة في المحفظة
- **الرصيد صفر**: يجب أن يكون رصيدك الحالي 0 إكسا
- **لا سلف نشط**: لا يوجد سلف سابق غير مسدد

### ⚡ آلية العمل
1. **فحص الأهلية**: النظام يفحص تلقائياً أهلية المستخدم
2. **منح السلفة**: إضافة 3 إكسا فوراً للمحفظة
3. **خصم تلقائي**: عند إضافة رصيد جديد، يتم خصم السلفة أولاً
4. **خصم جزئي**: إذا كان الرصيد المضاف أقل من السلفة، يتم خصم المتاح
5. **إصدار فواتير**: فاتورة لمنح السلفة وفاتورة لخصم السلفة

## 🗂️ هيكل قاعدة البيانات

### 💾 بيانات المحفظة الجديدة
```json
{
  "wallet_number": "9091234567",
  "user_id": 123456789,
  "balance": 0.0,
  "loan_amount": 0.0,
  "has_active_loan": false,
  "loan_granted_at": null,
  "loan_history": []
}
```

### 📈 سجل السلف
```json
{
  "type": "loan_granted",
  "amount": 3.0,
  "granted_at": "2025-01-22 15:30:00",
  "old_balance": 0.0,
  "new_balance": 3.0
}
```

## 🔧 التكامل مع النظام

### 🤖 البوت الرئيسي
- **زر خدمة سلفني**: فحص الأهلية وطلب السلفة
- **منح تلقائي**: السلفة تُمنح فوراً للمستخدمين المؤهلين
- **حالة السلف**: عرض معلومات السلف الحالي
- **فواتير تفاعلية**: فواتير PDF لجميع عمليات السلف

### 🛡️ بوت الإدارة
- **تقارير السلف**: إحصائيات شاملة لجميع السلف
- **خصم تلقائي**: عند إضافة رصيد، يتم خصم السلف تلقائياً
- **فواتير مزدوجة**: فاتورة إضافة الرصيد + فاتورة خصم السلف
- **أمر /loan_stats**: إحصائيات مفصلة للسلف

## 📊 الإحصائيات المتاحة

### 📈 في بوت الإدارة
- إجمالي السلف الممنوحة
- السلف المسددة والنشطة
- معدل السداد
- إجمالي المبالغ النشطة
- أكبر السلف النشطة
- المستخدمون الذين لديهم سلف نشط

## 🚀 كيفية الاستخدام

### 👤 للمستخدمين
1. اضغط على زر "💳 خدمة سلفني"
2. النظام سيفحص أهليتك تلقائياً
3. إذا كنت مؤهلاً، اضغط "💳 طلب سلفة"
4. ستحصل على 3 إكسا فوراً
5. عند إضافة رصيد جديد، سيتم خصم السلفة تلقائياً

### 👨‍💼 للمديرين
1. "💳 تقارير سلفني" - لرؤية الإحصائيات
2. `/loan_stats` - إحصائيات مفصلة
3. عند إضافة رصيد للمستخدمين، السلف يُخصم تلقائياً

## 🧾 نظام الفواتير

### 📄 فواتير السلف
- **فاتورة منح السلفة**: رقم يبدأ بـ 301
- **فاتورة خصم السلفة**: رقم يبدأ بـ 302
- **تفاصيل شاملة**: جميع معلومات المعاملة
- **QR Code**: للتحقق من صحة الفاتورة

## 🔍 أمثلة الاستخدام

### مثال 1: مستخدم مؤهل
```
المستخدم: رصيد 0 إكسا، محفظة مُحققة
النتيجة: ✅ يحصل على 3 إكسا فوراً
```

### مثال 2: مستخدم غير مؤهل
```
المستخدم: رصيد 5 إكسا، محفظة مُحققة
النتيجة: ❌ غير مؤهل (الرصيد ليس صفر)
```

### مثال 3: خصم السلف
```
المستخدم: لديه سلف 3 إكسا، يضيف 2 إكسا
النتيجة: يتم خصم 2 إكسا من السلف، يتبقى 1 إكسا سلف
```

## ⚠️ ملاحظات مهمة

1. **الأمان**: جميع عمليات السلف مُسجلة ومُراقبة
2. **الشفافية**: فواتير تفصيلية لكل عملية
3. **التلقائية**: لا حاجة لتدخل يدوي من الإدارة
4. **المرونة**: خصم جزئي أو كامل حسب الرصيد المضاف
5. **التتبع**: تاريخ كامل لجميع عمليات السلف

## 🛠️ الاختبار

### تشغيل اختبارات النظام
```bash
python test_loan_system.py
```

### الاختبارات المتضمنة
- إنشاء محفظة تجريبية
- فحص الأهلية قبل وبعد التحقق
- منح السلفة
- خصم السلف الجزئي والكامل
- إحصائيات السلف

## 🔧 استكشاف الأخطاء

### ❌ مشاكل شائعة
- **غير مؤهل**: تأكد من تحقق المحفظة ورصيد صفر
- **سلف نشط**: لا يمكن الحصول على سلف جديد قبل سداد الحالي
- **خطأ في البيانات**: فحص ملفات قاعدة البيانات

### 🔧 الحلول
```bash
# فحص حالة السلف لمستخدم معين
python -c "from shared.database.wallet_manager import WalletManager; wm = WalletManager(); print(wm.get_user_loan_info(USER_ID))"

# فحص إحصائيات السلف العامة
python -c "from shared.database.wallet_manager import WalletManager; wm = WalletManager(); print(wm.get_loan_statistics())"
```

## 📞 الدعم

للمساعدة أو الإبلاغ عن مشاكل في نظام السلف، تواصل مع مطور النظام.

---

**تاريخ التحديث**: 2025-01-22  
**الإصدار**: 1.0.0  
**المطور**: صلاح الدين الدروبي
