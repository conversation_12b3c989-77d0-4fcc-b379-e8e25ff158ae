# 📁 إدارة الوسائط - Media Management

**الرقم:** DT-002  
**التاريخ:** 2025-07-22  
**الحالة:** ✅ مكتمل  

---

## 🎯 نظرة عامة

نظام إدارة الوسائط في بوت صلاح الدين يوفر معالجة شاملة لجميع أنواع الملفات والوسائط التي يرسلها المستخدمون.

## 📂 هيكل مجلدات الوسائط

```
main/media/
├── imagery/          # الصور
│   ├── about.jpg
│   ├── achievements.jpg
│   ├── experience.jpg
│   ├── location.jpg
│   └── works.jpg
├── videos/           # الفيديوهات
├── acoustics/        # الصوتيات
├── document/         # المستندات
└── files/           # الملفات الأخرى
```

## 🎨 أنواع الوسائط المدعومة

### 📸 الصور (imagery/)
- **الأنواع المدعومة:** JPG, PNG, GIF, WEBP
- **الحد الأقصى:** 10MB
- **الاستخدام:** صور شخصية، أعمال، إنجازات

### 🎬 الفيديوهات (videos/)
- **الأنواع المدعومة:** MP4, AVI, MOV, MKV
- **الحد الأقصى:** 50MB
- **الاستخدام:** مقاطع تعريفية، عروض أعمال

### 🎵 الصوتيات (acoustics/)
- **الأنواع المدعومة:** MP3, WAV, OGG, M4A
- **الحد الأقصى:** 20MB
- **الاستخدام:** رسائل صوتية، موسيقى

### 📄 المستندات (document/)
- **الأنواع المدعومة:** PDF, DOC, DOCX, TXT, RTF
- **الحد الأقصى:** 20MB
- **الاستخدام:** سيرة ذاتية، شهادات، مستندات

### 📦 الملفات الأخرى (files/)
- **الأنواع المدعومة:** جميع الأنواع
- **الحد الأقصى:** 20MB
- **الاستخدام:** ملفات متنوعة

## ⚙️ معالجة الوسائط

### 📥 استقبال الملفات
```python
async def handle_document(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """معالجة المستندات المرسلة"""
    document = update.message.document
    file_name = document.file_name
    file_size = document.file_size
    
    # فحص نوع الملف وحجمه
    if file_size > MAX_FILE_SIZE:
        await update.message.reply_text("❌ حجم الملف كبير جداً")
        return
    
    # تحديد المجلد المناسب
    folder = determine_media_folder(file_name)
    
    # حفظ الملف
    await save_media_file(document, folder)
```

### 🔍 تحليل الملفات
- **فحص النوع:** تحديد نوع الملف تلقائياً
- **فحص الحجم:** التأكد من عدم تجاوز الحد المسموح
- **فحص الأمان:** فحص الملفات للتأكد من سلامتها
- **إنشاء معاينة:** إنشاء صور مصغرة للملفات المرئية

### 📊 إحصائيات الوسائط
- **عدد الملفات:** تتبع عدد الملفات في كل مجلد
- **المساحة المستخدمة:** حساب المساحة الإجمالية
- **الأنواع الأكثر استخداماً:** إحصائيات أنواع الملفات

## 🛡️ الأمان والحماية

### 🔒 فحص الأمان
- **فحص الفيروسات:** فحص الملفات قبل الحفظ
- **فحص المحتوى:** التأكد من مناسبة المحتوى
- **تشفير البيانات:** حماية الملفات الحساسة

### 🚫 القيود والحدود
- **حجم الملف:** حدود مختلفة لكل نوع
- **عدد الملفات:** حد أقصى للملفات اليومية
- **أنواع محظورة:** منع أنواع ملفات خطيرة

## 📈 التحسينات والأداء

### ⚡ تحسين الأداء
- **ضغط الصور:** تقليل حجم الصور تلقائياً
- **تحويل الصيغ:** تحويل الملفات لصيغ محسنة
- **تخزين مؤقت:** حفظ الملفات المستخدمة بكثرة

### 🔄 النسخ الاحتياطي
- **نسخ تلقائي:** نسخ احتياطي يومي للوسائط
- **استرداد الملفات:** إمكانية استرداد الملفات المحذوفة
- **أرشفة قديمة:** أرشفة الملفات القديمة

## 🎛️ إدارة المساحة

### 📊 مراقبة المساحة
```python
def get_storage_stats():
    """الحصول على إحصائيات المساحة"""
    stats = {}
    for folder in MEDIA_FOLDERS:
        folder_path = os.path.join(MEDIA_DIR, folder)
        size = get_folder_size(folder_path)
        count = count_files(folder_path)
        stats[folder] = {'size': size, 'count': count}
    return stats
```

### 🧹 تنظيف تلقائي
- **حذف الملفات القديمة:** حذف الملفات غير المستخدمة
- **ضغط الأرشيف:** ضغط الملفات القديمة
- **تحسين المساحة:** إعادة تنظيم الملفات

## 📱 واجهة المستخدم

### 🎨 عرض الوسائط
- **معاينة سريعة:** عرض معاينة للملفات
- **تفاصيل الملف:** عرض معلومات مفصلة
- **تحميل سريع:** روابط تحميل مباشرة

### 🔧 أدوات الإدارة
- **رفع ملفات:** واجهة سهلة لرفع الملفات
- **تنظيم المجلدات:** إعادة تنظيم الملفات
- **البحث:** البحث في الملفات بالاسم أو النوع

## 📋 سجل التغييرات

### 2025-07-22:
- ✅ إنشاء نظام إدارة الوسائط الشامل
- ✅ تحديد هيكل المجلدات المنظم
- ✅ إضافة فحوصات الأمان والحماية
- ✅ تحسين الأداء والتخزين

---

## 🔗 روابط ذات صلة

- [نظرة عامة على إدارة البيانات](001_data_overview.md)
- [تحليل البيانات](003_data_analytics.md)
- [دليل الأمان](../security/001_security_overview.md)

---

**📞 للمساعدة:** راجع [دليل المطور](../setup/001_installation_guide.md) أو اتصل بفريق الدعم.
