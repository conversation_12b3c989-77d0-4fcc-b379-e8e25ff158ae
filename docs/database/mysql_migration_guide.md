# 🗄️ دليل ترحيل قاعدة البيانات من JSON إلى MySQL

**رقم التوثيق**: DB-MIGRATION-001  
**التاريخ**: 2025-08-05  
**الإصدار**: 1.0.0  
**المطور**: صلاح الدين الدروبي  

---

## 📖 المحتويات

1. [نظرة عامة](#نظرة-عامة)
2. [المتطلبات المسبقة](#المتطلبات-المسبقة)
3. [إعداد قاعدة البيانات](#إعداد-قاعدة-البيانات)
4. [تشغيل الترحيل](#تشغيل-الترحيل)
5. [التحقق من الترحيل](#التحقق-من-الترحيل)
6. [العمليات المتزامنة](#العمليات-المتزامنة)
7. [النسخ الاحتياطي والاستعادة](#النسخ-الاحتياطي-والاستعادة)
8. [استكشاف الأخطاء](#استكشاف-الأخطاء)

---

## 🎯 نظرة عامة

يوفر هذا الدليل خطوات مفصلة لترحيل نظام تخزين البيانات من ملفات JSON إلى قاعدة بيانات MySQL مع دعم العمليات المتزامنة والنسخ الاحتياطي التلقائي.

### المميزات الجديدة:
- ✅ قاعدة بيانات MySQL عالية الأداء
- ✅ دعم العمليات المتزامنة (Concurrent Operations)
- ✅ نسخ احتياطي تلقائي مجدول
- ✅ استعادة سريعة للبيانات
- ✅ مراقبة وإحصائيات متقدمة
- ✅ أمان محسن مع التشفير

---

## 🔧 المتطلبات المسبقة

### 1. متطلبات النظام
```bash
# Python 3.8 أو أحدث
python --version

# MySQL Server 8.0 أو أحدث
mysql --version
```

### 2. تثبيت المكتبات المطلوبة
```bash
# تثبيت جميع المتطلبات
pip install -r requirements.txt

# أو تثبيت مكتبات MySQL منفصلة
pip install mysql-connector-python PyMySQL SQLAlchemy schedule
```

### 3. إعداد قاعدة البيانات MySQL
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE salah_bot_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إنشاء مستخدم قاعدة البيانات
CREATE USER 'bot_user'@'localhost' IDENTIFIED BY 'secure_password_123';

-- منح الصلاحيات
GRANT ALL PRIVILEGES ON salah_bot_system.* TO 'bot_user'@'localhost';
FLUSH PRIVILEGES;
```

---

## ⚙️ إعداد قاعدة البيانات

### 1. إعداد متغيرات البيئة
```bash
# نسخ ملف الإعدادات
cp .env.example .env

# تحرير الإعدادات
nano .env
```

### 2. تكوين إعدادات MySQL
```env
# إعدادات قاعدة البيانات
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=bot_user
MYSQL_PASSWORD=secure_password_123
MYSQL_DATABASE=salah_bot_system

# إعدادات الأمان
DB_ENCRYPTION_KEY=your_32_character_encryption_key
SECRET_KEY=your_secret_key_here

# إعدادات البيئة
ENVIRONMENT=production
```

### 3. اختبار الاتصال
```python
# اختبار سريع للاتصال
from shared.database.mysql_manager import mysql_manager

health = mysql_manager.health_check()
print(f"حالة قاعدة البيانات: {health['status']}")
```

---

## 🚀 تشغيل الترحيل

### 1. تشغيل سكريبت الترحيل الرئيسي
```bash
# تشغيل الترحيل التلقائي
python migrate_to_mysql.py
```

### 2. تشغيل الترحيل اليدوي (خطوة بخطوة)
```python
from shared.database.migration_script import DataMigrator

# إنشاء مثيل المرحل
migrator = DataMigrator()

# إنشاء نسخة احتياطية
migrator.create_backup()

# تشغيل الترحيل
success = migrator.run_migration()

if success:
    print("✅ تم الترحيل بنجاح!")
else:
    print("❌ فشل في الترحيل")
```

### 3. مراقبة عملية الترحيل
```bash
# مراقبة سجلات الترحيل
tail -f migration.log

# فحص حالة قاعدة البيانات
python -c "from shared.database.mysql_manager import mysql_manager; print(mysql_manager.health_check())"
```

---

## ✅ التحقق من الترحيل

### 1. فحص البيانات المرحلة
```python
from shared.database.mysql_user_manager import mysql_user_manager
from shared.database.mysql_wallet_manager import mysql_wallet_manager

# إحصائيات المستخدمين
user_stats = mysql_user_manager.get_user_statistics()
print(f"إجمالي المستخدمين: {user_stats['total_users']}")

# إحصائيات المحافظ
wallet_stats = mysql_wallet_manager.get_wallet_statistics()
print(f"إجمالي المحافظ: {wallet_stats['total_wallets']}")
```

### 2. اختبار العمليات الأساسية
```python
# اختبار إنشاء مستخدم جديد
user_data = {
    'id': 123456789,
    'username': 'test_user',
    'first_name': 'مستخدم',
    'last_name': 'تجريبي'
}

success = mysql_user_manager.register_user(user_data)
print(f"تسجيل المستخدم: {'نجح' if success else 'فشل'}")

# اختبار إنشاء محفظة
wallet_number = mysql_wallet_manager.create_wallet(
    user_id=123456789,
    user_name="مستخدم تجريبي",
    username="test_user"
)
print(f"رقم المحفظة الجديدة: {wallet_number}")
```

---

## 🔄 العمليات المتزامنة

### 1. العمليات غير المتزامنة
```python
import asyncio
from shared.database.mysql_user_manager import mysql_user_manager

async def concurrent_operations():
    # عمليات متزامنة متعددة
    tasks = [
        mysql_user_manager.get_user_by_id_async(591967813),
        mysql_user_manager.get_user_by_id_async(6417160909),
        mysql_user_manager.register_user_async({
            'id': 999999999,
            'username': 'async_user',
            'first_name': 'مستخدم متزامن'
        })
    ]
    
    results = await asyncio.gather(*tasks)
    return results

# تشغيل العمليات المتزامنة
results = asyncio.run(concurrent_operations())
```

### 2. معالجة العمليات المتوازية
```python
from concurrent.futures import ThreadPoolExecutor
import threading

def process_users_batch(user_ids):
    """معالجة دفعة من المستخدمين بشكل متوازي"""
    with ThreadPoolExecutor(max_workers=5) as executor:
        futures = [
            executor.submit(mysql_user_manager.get_user_by_id, user_id)
            for user_id in user_ids
        ]
        
        results = [future.result() for future in futures]
        return results

# معالجة 100 مستخدم بشكل متوازي
user_ids = range(1, 101)
results = process_users_batch(user_ids)
```

---

## 💾 النسخ الاحتياطي والاستعادة

### 1. النسخ الاحتياطي التلقائي
```python
from shared.database.mysql_backup_manager import mysql_backup_manager

# بدء النسخ الاحتياطي التلقائي
mysql_backup_manager.start_scheduler()

# إنشاء نسخة احتياطية فورية
success, name, path = mysql_backup_manager.create_backup("manual", compress=True)
print(f"النسخة الاحتياطية: {name} في {path}")
```

### 2. عرض النسخ الاحتياطية
```python
# عرض جميع النسخ الاحتياطية
backups = mysql_backup_manager.list_backups()

for backup in backups:
    print(f"📁 {backup['name']}")
    print(f"   النوع: {backup['type']}")
    print(f"   الحجم: {backup['size_mb']} MB")
    print(f"   التاريخ: {backup['created_at']}")
    print()
```

### 3. استعادة نسخة احتياطية
```python
# استعادة نسخة احتياطية
backup_path = "backups/mysql_backups/manual/backup_manual_20250805_120000.sql.gz"
success, message = mysql_backup_manager.restore_backup(backup_path)

if success:
    print("✅ تم استعادة النسخة الاحتياطية بنجاح")
else:
    print(f"❌ فشل في الاستعادة: {message}")
```

### 4. جدولة النسخ الاحتياطي
```python
# الجدولة التلقائية:
# - نسخة احتياطية يومية: 02:00 صباحاً
# - نسخة احتياطية أسبوعية: الأحد 03:00 صباحاً  
# - نسخة احتياطية شهرية: أول كل شهر

# إيقاف الجدولة
mysql_backup_manager.stop_scheduler()
```

---

## 🔍 استكشاف الأخطاء

### 1. مشاكل الاتصال بقاعدة البيانات
```python
# فحص الاتصال
from shared.database.mysql_manager import mysql_manager

try:
    health = mysql_manager.health_check()
    if health['status'] != 'healthy':
        print(f"❌ مشكلة في قاعدة البيانات: {health.get('error')}")
except Exception as e:
    print(f"❌ خطأ في الاتصال: {e}")
```

### 2. مشاكل الترحيل
```bash
# فحص سجلات الترحيل
grep "ERROR" migration.log

# فحص سجلات MySQL
sudo tail -f /var/log/mysql/error.log
```

### 3. مشاكل الأداء
```python
# مراقبة أداء قاعدة البيانات
query = "SHOW PROCESSLIST"
processes = mysql_manager.execute_query(query)

for process in processes:
    if process['Time'] > 30:  # العمليات التي تستغرق أكثر من 30 ثانية
        print(f"⚠️ عملية بطيئة: {process['Info']}")
```

### 4. استعادة من النسخ الاحتياطية
```python
# في حالة فشل الترحيل، يمكن استعادة البيانات
from shared.database.mysql_backup_manager import mysql_backup_manager

# البحث عن آخر نسخة احتياطية صحيحة
backups = mysql_backup_manager.list_backups("manual")
latest_backup = backups[0] if backups else None

if latest_backup:
    success, message = mysql_backup_manager.restore_backup(latest_backup['path'])
    print(f"استعادة النسخة الاحتياطية: {'نجحت' if success else 'فشلت'}")
```

---

## 📊 مراقبة الأداء

### 1. إحصائيات قاعدة البيانات
```python
# إحصائيات شاملة
from shared.database.mysql_manager import mysql_manager

# حجم قاعدة البيانات
size_query = """
SELECT 
    table_schema as 'Database',
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) as 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'salah_bot_system'
GROUP BY table_schema
"""

size_info = mysql_manager.execute_query(size_query)
print(f"حجم قاعدة البيانات: {size_info[0]['Size (MB)']} MB")
```

### 2. مراقبة الاستعلامات البطيئة
```python
# تفعيل سجل الاستعلامات البطيئة
slow_query_config = """
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2;
SET GLOBAL log_queries_not_using_indexes = 'ON';
"""

mysql_manager.execute_update(slow_query_config)
```

---

## 🎯 الخطوات التالية

1. **تحديث الكود**: تحديث جميع أجزاء النظام لاستخدام MySQL
2. **اختبار شامل**: اختبار جميع وظائف النظام
3. **مراقبة الأداء**: مراقبة أداء قاعدة البيانات
4. **تحسين الاستعلامات**: تحسين الاستعلامات البطيئة
5. **نسخ احتياطية منتظمة**: التأكد من عمل النسخ الاحتياطي التلقائي

---

## 📞 الدعم والمساعدة

في حالة مواجهة أي مشاكل:

1. راجع سجلات النظام (`migration.log`)
2. تحقق من إعدادات قاعدة البيانات
3. تأكد من صحة متغيرات البيئة
4. استخدم النسخ الاحتياطية للاستعادة عند الحاجة

---

**تم إنشاء هذا الدليل بواسطة**: صلاح الدين الدروبي  
**آخر تحديث**: 2025-08-05  
**الإصدار**: 1.0.0
