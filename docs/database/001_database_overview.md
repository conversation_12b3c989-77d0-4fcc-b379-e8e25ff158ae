# 🗄️ نظرة عامة على قاعدة البيانات

**رقم التوثيق**: DB-001  
**التاريخ**: 2025-07-10  
**الإصدار**: 1.0.0  
**المطور**: صلاح الدين الدروبي  

---

## 📖 المحتويات

1. [نظرة عامة](#نظرة-عامة)
2. [هيكل قاعدة البيانات](#هيكل-قاعدة-البيانات)
3. [الجداول الرئيسية](#الجداول-الرئيسية)
4. [العلاقات](#العلاقات)
5. [الفهارس والأمان](#الفهارس-والأمان)

---

## 🎯 نظرة عامة

نظام قاعدة البيانات في بوت صلاح الدين مصمم لتخزين وإدارة:

- **بيانات المستخدمين** ومعلوماتهم الشخصية
- **سجلات الأنشطة** والتفاعلات
- **الإحصائيات** والتقارير
- **إعدادات النظام** والتكوينات
- **سجلات الأمان** والمراقبة

---

## 🏗️ هيكل قاعدة البيانات

### الموقع:
```
shared/database/
├── database_manager.py      # مدير قاعدة البيانات
├── models.py               # نماذج البيانات
├── migrations/             # ملفات الترحيل
├── backups/               # النسخ الاحتياطية
└── logs/                  # سجلات قاعدة البيانات
```

### نوع قاعدة البيانات:
- **SQLite**: للتطوير والاختبار
- **PostgreSQL**: للإنتاج (اختياري)
- **MySQL**: دعم إضافي (اختياري)

---

## 📊 الجداول الرئيسية

### 1. **جدول المستخدمين** (`users`)
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    telegram_id BIGINT UNIQUE NOT NULL,
    username VARCHAR(255),
    first_name VARCHAR(255) NOT NULL,
    last_name VARCHAR(255),
    language_code VARCHAR(10) DEFAULT 'ar',
    is_active BOOLEAN DEFAULT TRUE,
    is_admin BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP
);
```

#### الحقول:
- **id**: المعرف الفريد الداخلي
- **telegram_id**: معرف تليجرام الفريد
- **username**: اسم المستخدم في تليجرام
- **first_name**: الاسم الأول
- **last_name**: الاسم الأخير
- **language_code**: لغة المستخدم المفضلة
- **is_active**: حالة نشاط المستخدم
- **is_admin**: صلاحيات إدارية
- **created_at**: تاريخ التسجيل
- **updated_at**: آخر تحديث
- **last_activity**: آخر نشاط

### 2. **جدول الأنشطة** (`activities`)
```sql
CREATE TABLE activities (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    activity_type VARCHAR(50) NOT NULL,
    activity_data JSON,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    session_id VARCHAR(255),
    ip_address VARCHAR(45),
    user_agent TEXT,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

#### أنواع الأنشطة:
- **button_click**: ضغط زر
- **command_used**: استخدام أمر
- **message_sent**: إرسال رسالة
- **file_requested**: طلب ملف
- **ai_conversation**: محادثة مع الذكاء الاصطناعي

### 3. **جدول الجلسات** (`sessions`)
```sql
CREATE TABLE sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    session_id VARCHAR(255) UNIQUE NOT NULL,
    session_type VARCHAR(50) NOT NULL,
    session_data JSON,
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ended_at TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

#### أنواع الجلسات:
- **main_bot**: جلسة البوت الرئيسي
- **admin_bot**: جلسة بوت الإدارة
- **ai_conversation**: جلسة محادثة ذكية
- **file_transfer**: جلسة نقل ملفات

### 4. **جدول الإحصائيات** (`statistics`)
```sql
CREATE TABLE statistics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    stat_type VARCHAR(50) NOT NULL,
    stat_key VARCHAR(100) NOT NULL,
    stat_value INTEGER DEFAULT 0,
    date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(stat_type, stat_key, date)
);
```

#### أنواع الإحصائيات:
- **daily_users**: المستخدمين اليوميين
- **button_clicks**: ضغطات الأزرار
- **command_usage**: استخدام الأوامر
- **ai_requests**: طلبات الذكاء الاصطناعي
- **file_downloads**: تحميل الملفات

### 5. **جدول الإعدادات** (`settings`)
```sql
CREATE TABLE settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type VARCHAR(20) DEFAULT 'string',
    description TEXT,
    is_system BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### أنواع الإعدادات:
- **bot_settings**: إعدادات البوت
- **ai_settings**: إعدادات الذكاء الاصطناعي
- **security_settings**: إعدادات الأمان
- **notification_settings**: إعدادات الإشعارات

### 6. **جدول السجلات** (`logs`)
```sql
CREATE TABLE logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    log_level VARCHAR(20) NOT NULL,
    logger_name VARCHAR(100) NOT NULL,
    message TEXT NOT NULL,
    user_id INTEGER,
    session_id VARCHAR(255),
    extra_data JSON,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

#### مستويات السجلات:
- **DEBUG**: معلومات التطوير
- **INFO**: معلومات عامة
- **WARNING**: تحذيرات
- **ERROR**: أخطاء
- **CRITICAL**: أخطاء حرجة

---

## 🔗 العلاقات

### مخطط العلاقات:
```
users (1) ←→ (N) activities
users (1) ←→ (N) sessions
users (1) ←→ (N) logs

sessions (1) ←→ (N) activities
```

### أمثلة على الاستعلامات:
```sql
-- الحصول على أنشطة مستخدم معين
SELECT a.*, u.first_name, u.username
FROM activities a
JOIN users u ON a.user_id = u.id
WHERE u.telegram_id = 123456789
ORDER BY a.timestamp DESC;

-- إحصائيات يومية
SELECT 
    DATE(timestamp) as date,
    COUNT(*) as total_activities,
    COUNT(DISTINCT user_id) as unique_users
FROM activities
WHERE timestamp >= DATE('now', '-7 days')
GROUP BY DATE(timestamp)
ORDER BY date DESC;

-- الأزرار الأكثر استخداماً
SELECT 
    JSON_EXTRACT(activity_data, '$.button_name') as button_name,
    COUNT(*) as click_count
FROM activities
WHERE activity_type = 'button_click'
GROUP BY button_name
ORDER BY click_count DESC
LIMIT 10;
```

---

## 🔍 الفهارس والأمان

### الفهارس المهمة:
```sql
-- فهرس معرف تليجرام
CREATE INDEX idx_users_telegram_id ON users(telegram_id);

-- فهرس الأنشطة حسب المستخدم والوقت
CREATE INDEX idx_activities_user_time ON activities(user_id, timestamp);

-- فهرس الجلسات النشطة
CREATE INDEX idx_sessions_active ON sessions(is_active, user_id);

-- فهرس الإحصائيات حسب التاريخ
CREATE INDEX idx_statistics_date ON statistics(date, stat_type);

-- فهرس السجلات حسب المستوى والوقت
CREATE INDEX idx_logs_level_time ON logs(log_level, timestamp);
```

### إجراءات الأمان:
```sql
-- إنشاء مستخدم قاعدة بيانات محدود الصلاحيات
CREATE USER 'bot_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT SELECT, INSERT, UPDATE ON bot_database.* TO 'bot_user'@'localhost';

-- منع حذف البيانات المهمة
CREATE TRIGGER prevent_user_delete
BEFORE DELETE ON users
FOR EACH ROW
BEGIN
    IF OLD.is_admin = TRUE THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Cannot delete admin user';
    END IF;
END;
```

---

## 🔄 النسخ الاحتياطية

### استراتيجية النسخ الاحتياطية:
```python
class BackupManager:
    """مدير النسخ الاحتياطية"""
    
    def create_daily_backup(self):
        """إنشاء نسخة احتياطية يومية"""
        
    def create_weekly_backup(self):
        """إنشاء نسخة احتياطية أسبوعية"""
        
    def restore_from_backup(self, backup_file):
        """استعادة من نسخة احتياطية"""
```

### جدولة النسخ الاحتياطية:
- **يومياً**: نسخة احتياطية كاملة
- **أسبوعياً**: نسخة احتياطية مضغوطة
- **شهرياً**: أرشفة طويلة المدى
- **عند الطلب**: نسخ احتياطية فورية

---

## 📊 مراقبة الأداء

### مؤشرات الأداء:
```python
class PerformanceMonitor:
    """مراقب أداء قاعدة البيانات"""
    
    def monitor_query_performance(self):
        """مراقبة أداء الاستعلامات"""
        
    def check_database_size(self):
        """فحص حجم قاعدة البيانات"""
        
    def analyze_slow_queries(self):
        """تحليل الاستعلامات البطيئة"""
```

### التحسينات:
- **تحسين الاستعلامات**: استخدام الفهارس المناسبة
- **تنظيف البيانات**: حذف البيانات القديمة
- **ضغط البيانات**: تقليل حجم التخزين
- **تحليل الأداء**: مراقبة مستمرة

---

## 🎯 الخلاصة

نظام قاعدة البيانات يوفر:

- ✅ **تخزين آمن ومنظم** للبيانات
- ✅ **أداء عالي** مع الفهارس المحسنة
- ✅ **نسخ احتياطية منتظمة** لحماية البيانات
- ✅ **مراقبة مستمرة** للأداء والأمان
- ✅ **قابلية التوسع** للنمو المستقبلي

---

**آخر تحديث**: 2025-07-10  
**المرجع التالي**: [DB-002](002_data_models.md)
