# 🗄️ دليل ترحيل النظام إلى SQLite

## 📋 نظرة عامة

تم تطوير نظام قاعدة بيانات SQLite متقدم يحل محل نظام ملفات JSON السابق. يوفر النظام الجديد:

- **أداء محسن**: استعلامات أسرع ومعالجة أكثر كفاءة
- **العمليات المتزامنة**: دعم كامل للقراءة والكتابة المتزامنة
- **النسخ الاحتياطي التلقائي**: نظام نسخ احتياطي ذكي ومجدول
- **الأمان المحسن**: تشفير البيانات وحماية متقدمة
- **واجهة موحدة**: API بسيط وسهل الاستخدام

---

## 🚀 خطوات الترحيل

### 1. التحضير للترحيل

```bash
# التأكد من تثبيت المتطلبات الجديدة
pip install -r requirements.txt

# إنشاء نسخة احتياطية يدوية (اختياري)
cp -r shared/database backups/manual_backup_$(date +%Y%m%d)
```

### 2. تشغيل عملية الترحيل

```bash
# تشغيل سكريبت الترحيل
python migrate_to_sqlite.py
```

### 3. التحقق من نجاح الترحيل

بعد اكتمال الترحيل، ستحصل على تقرير مفصل يتضمن:
- عدد السجلات المرحلة لكل جدول
- أي أخطاء حدثت أثناء الترحيل
- الوقت المستغرق للعملية

---

## 📊 هيكل قاعدة البيانات الجديدة

### الجداول الرئيسية

| الجدول | الوصف | السجلات المتوقعة |
|---------|--------|------------------|
| `users` | بيانات المستخدمين | من `users_data.json` |
| `wallets` | بيانات المحافظ | من `wallets_database.json` |
| `transactions` | سجل المعاملات | جديد |
| `loan_history` | تاريخ القروض | من بيانات المحافظ |
| `admin_data` | البيانات الإدارية | من `admin_data.json` |
| `monitoring_data` | بيانات المراقبة | من `monitoring_data.json` |
| `ai_models` | إعدادات نماذج الذكاء الاصطناعي | من `ai_models_config.json` |
| `token_usage` | سجل استخدام التوكن | من `token_usage_log.json` |

### الفهارس المحسنة

تم إنشاء فهارس محسنة لتحسين الأداء:
- فهرس على `user_id` في جدول المستخدمين
- فهرس على `last_activity` للبحث السريع
- فهرس على `wallet_number` في المعاملات
- فهارس زمنية للاستعلامات التاريخية

---

## 🔧 استخدام النظام الجديد

### الواجهة البرمجية الموحدة

```python
from shared.database import db_api

# الحصول على مستخدم
user = db_api.get_user(user_id)

# تحديث نشاط المستخدم
db_api.update_user_activity(user_id)

# الحصول على محفظة المستخدم
wallet = db_api.get_user_wallet(user_id)

# تحديث رصيد المحفظة
db_api.update_wallet_balance(wallet_number, new_balance)

# تسجيل استخدام التوكن
db_api.log_token_usage({
    'user_id': user_id,
    'total_tokens': tokens,
    'cost_in_exa': cost
})
```

### العمليات المتزامنة

النظام يدعم العمليات المتزامنة تلقائياً:

```python
import threading
from shared.database import db_api

def update_user_data(user_id, data):
    # آمن للاستخدام في خيوط متعددة
    db_api.update_user(user_id, data)

# تشغيل عمليات متزامنة
threads = []
for i in range(10):
    t = threading.Thread(target=update_user_data, args=(user_id, data))
    threads.append(t)
    t.start()

for t in threads:
    t.join()
```

---

## 💾 نظام النسخ الاحتياطي

### النسخ التلقائي

النظام يقوم بإنشاء نسخ احتياطية تلقائياً:
- **يومي**: الساعة 2:00 صباحاً
- **أسبوعي**: يوم الأحد الساعة 3:00 صباحاً  
- **شهري**: اليوم الأول من كل شهر الساعة 4:00 صباحاً

### النسخ اليدوي

```python
from shared.database import db_api

# إنشاء نسخة احتياطية فورية
backup_path = db_api.create_backup("manual")

# عرض النسخ الاحتياطية
backups = db_api.list_backups()

# استعادة نسخة احتياطية (احذر!)
db_api.restore_backup(backup_path, confirm=True)
```

### إعدادات النسخ الاحتياطي

```json
{
  "auto_backup": {
    "enabled": true,
    "daily": {"enabled": true, "time": "02:00"},
    "weekly": {"enabled": true, "day": "sunday", "time": "03:00"},
    "monthly": {"enabled": true, "day": 1, "time": "04:00"}
  },
  "retention": {
    "daily_backups": 7,
    "weekly_backups": 4,
    "monthly_backups": 12
  },
  "compression": true,
  "encryption": true
}
```

---

## 🔒 الأمان والحماية

### التشفير

- **النسخ الاحتياطية**: مشفرة باستخدام Fernet
- **البيانات الحساسة**: حماية إضافية للبيانات المهمة
- **مفاتيح التشفير**: محفوظة بشكل آمن ومخفية

### الحماية من التعارض

- **Locks**: حماية العمليات المتزامنة
- **Transactions**: ضمان سلامة البيانات
- **Connection Pooling**: إدارة ذكية للاتصالات

---

## 📈 مراقبة الأداء

### الإحصائيات

```python
# الحصول على إحصائيات النظام
stats = db_api.get_system_statistics()

print(f"إجمالي المستخدمين: {stats['users']['total']}")
print(f"المستخدمون النشطون اليوم: {stats['users']['active_today']}")
print(f"حجم قاعدة البيانات: {stats['database']['database_size']} بايت")
```

### مراقبة الاستخدام

```python
# إحصائيات استخدام التوكن
token_stats = db_api.get_token_usage_statistics(30)  # آخر 30 يوم

print(f"إجمالي الطلبات: {token_stats['total_requests']}")
print(f"إجمالي التوكن: {token_stats['total_tokens']}")
print(f"إجمالي التكلفة: {token_stats['total_cost_exa']} إكسا")
```

---

## 🛠️ الصيانة والتحسين

### التنظيف الدوري

```python
# تنظيف قاعدة البيانات
db_api.cleanup_database()

# تحسين الأداء
db_api.db_manager.vacuum()
```

### إدارة النسخ الاحتياطية

```python
# بدء النسخ التلقائي
db_api.start_auto_backup()

# إيقاف النسخ التلقائي
db_api.stop_auto_backup()

# تنظيف النسخ القديمة
db_api.backup_system.cleanup_old_backups()
```

---

## 🚨 استكشاف الأخطاء

### مشاكل شائعة

#### 1. خطأ في الاتصال بقاعدة البيانات
```
خطأ: database is locked
الحل: إعادة تشغيل النظام أو إغلاق الاتصالات المفتوحة
```

#### 2. نفاد مساحة القرص
```
خطأ: disk I/O error
الحل: تنظيف النسخ الاحتياطية القديمة أو زيادة المساحة
```

#### 3. فشل في الترحيل
```
خطأ: migration failed
الحل: راجع ملف migration.log والتقرير المفصل
```

### السجلات

- **migration.log**: سجل عملية الترحيل
- **backups/migration_report.json**: تقرير مفصل للترحيل
- **logs/**: سجلات النظام العامة

---

## 📞 الدعم والمساعدة

### في حالة وجود مشاكل:

1. **راجع السجلات**: تحقق من ملفات السجل للأخطاء
2. **النسخ الاحتياطية**: استخدم النسخ الاحتياطية للاستعادة
3. **إعادة التشغيل**: أعد تشغيل النظام
4. **التواصل**: تواصل مع المطور للدعم

### معلومات مفيدة:

- **إصدار النظام**: 1.0.0
- **نوع قاعدة البيانات**: SQLite 3
- **مسار قاعدة البيانات**: `shared/database/bot_database.db`
- **مجلد النسخ الاحتياطية**: `backups/database/`

---

## ✅ قائمة التحقق بعد الترحيل

- [ ] تم تشغيل سكريبت الترحيل بنجاح
- [ ] لا توجد أخطاء في تقرير الترحيل
- [ ] تم اختبار الوظائف الأساسية
- [ ] يعمل النسخ الاحتياطي التلقائي
- [ ] تم تحديث جميع الملفات للاستخدام الجديد
- [ ] تم حفظ نسخة احتياطية من النظام القديم

---

*تم إنشاء هذا الدليل بواسطة نظام صلاح الدين الدروبي المتقدم*
