# 🔐 نظرة عامة على الأمان

**رقم التوثيق**: SC-001  
**التاريخ**: 2025-07-10  
**الإصدار**: 1.0.0  
**المطور**: صلاح الدين الدروبي  

---

## 📖 المحتويات

1. [نظرة عامة](#نظرة-عامة)
2. [مستويات الأمان](#مستويات-الأمان)
3. [حماية البيانات](#حماية-البيانات)
4. [التحكم في الوصول](#التحكم-في-الوصول)
5. [مراقبة الأمان](#مراقبة-الأمان)

---

## 🎯 نظرة عامة

نظام الأمان في بوت صلاح الدين مصمم لحماية:

- **البيانات الشخصية** والمعلومات الحساسة
- **التوكنات ومفاتيح API** من التسريب
- **الوصول للنظام** من المستخدمين غير المصرح لهم
- **سجلات النظام** من التلاعب
- **الاتصالات** من التنصت والاختراق

---

## 🛡️ مستويات الأمان

### 1. **الأمان على مستوى التطبيق**
```python
# حماية التوكنات
BOT_TOKEN = os.getenv('BOT_TOKEN')
ADMIN_BOT_TOKEN = os.getenv('ADMIN_BOT_TOKEN')
EXA_AI_TOKEN = os.getenv('EXA_AI_TOKEN')

# التحقق من صحة التوكنات
if not BOT_TOKEN or len(BOT_TOKEN) < 40:
    raise ValueError("Invalid bot token")
```

### 2. **الأمان على مستوى قاعدة البيانات**
```sql
-- تشفير البيانات الحساسة
CREATE TABLE users (
    id INTEGER PRIMARY KEY,
    telegram_id BIGINT UNIQUE NOT NULL,
    encrypted_data TEXT,  -- بيانات مشفرة
    password_hash TEXT,   -- كلمة مرور مشفرة
    salt VARCHAR(32)      -- ملح التشفير
);

-- فهارس آمنة
CREATE INDEX idx_users_secure ON users(telegram_id) 
WHERE is_active = TRUE;
```

### 3. **الأمان على مستوى الشبكة**
```python
# تشفير الاتصالات
import ssl
import certifi

# إعدادات SSL آمنة
ssl_context = ssl.create_default_context(cafile=certifi.where())
ssl_context.check_hostname = True
ssl_context.verify_mode = ssl.CERT_REQUIRED
```

---

## 🔒 حماية البيانات

### 1. **تشفير البيانات**
```python
from cryptography.fernet import Fernet
import hashlib
import secrets

class DataEncryption:
    """نظام تشفير البيانات"""
    
    def __init__(self):
        self.key = self._load_or_generate_key()
        self.cipher = Fernet(self.key)
    
    def encrypt_data(self, data: str) -> str:
        """تشفير البيانات"""
        return self.cipher.encrypt(data.encode()).decode()
    
    def decrypt_data(self, encrypted_data: str) -> str:
        """فك تشفير البيانات"""
        return self.cipher.decrypt(encrypted_data.encode()).decode()
    
    def hash_password(self, password: str, salt: str = None) -> tuple:
        """تشفير كلمة المرور"""
        if not salt:
            salt = secrets.token_hex(16)
        
        password_hash = hashlib.pbkdf2_hmac(
            'sha256',
            password.encode(),
            salt.encode(),
            100000  # 100,000 iterations
        )
        
        return password_hash.hex(), salt
```

### 2. **حماية الملفات الحساسة**
```python
import os
import stat

class FileProtection:
    """حماية الملفات"""
    
    def secure_file_permissions(self, file_path: str):
        """تأمين صلاحيات الملف"""
        # قراءة وكتابة للمالك فقط
        os.chmod(file_path, stat.S_IRUSR | stat.S_IWUSR)
    
    def secure_directory(self, dir_path: str):
        """تأمين المجلد"""
        # قراءة وكتابة وتنفيذ للمالك فقط
        os.chmod(dir_path, stat.S_IRWXU)
```

### 3. **إخفاء البيانات الحساسة**
```python
class DataMasking:
    """إخفاء البيانات الحساسة"""
    
    def mask_token(self, token: str) -> str:
        """إخفاء التوكن في السجلات"""
        if len(token) > 8:
            return f"{token[:4]}...{token[-4:]}"
        return "***"
    
    def mask_user_id(self, user_id: int) -> str:
        """إخفاء معرف المستخدم"""
        user_str = str(user_id)
        if len(user_str) > 6:
            return f"{user_str[:3]}***{user_str[-3:]}"
        return "***"
```

---

## 🚪 التحكم في الوصول

### 1. **نظام الصلاحيات**
```python
from enum import Enum

class UserRole(Enum):
    """أدوار المستخدمين"""
    SUPER_ADMIN = "super_admin"
    ADMIN = "admin"
    MODERATOR = "moderator"
    USER = "user"
    GUEST = "guest"

class Permission(Enum):
    """الصلاحيات"""
    READ_USERS = "read_users"
    WRITE_USERS = "write_users"
    DELETE_USERS = "delete_users"
    VIEW_LOGS = "view_logs"
    SYSTEM_CONFIG = "system_config"
    BROADCAST = "broadcast"

# خريطة الصلاحيات
ROLE_PERMISSIONS = {
    UserRole.SUPER_ADMIN: [p for p in Permission],
    UserRole.ADMIN: [
        Permission.READ_USERS,
        Permission.WRITE_USERS,
        Permission.VIEW_LOGS,
        Permission.BROADCAST
    ],
    UserRole.MODERATOR: [
        Permission.READ_USERS,
        Permission.VIEW_LOGS
    ],
    UserRole.USER: [],
    UserRole.GUEST: []
}
```

### 2. **التحقق من الصلاحيات**
```python
class AccessControl:
    """التحكم في الوصول"""
    
    def check_permission(self, user_id: int, permission: Permission) -> bool:
        """فحص الصلاحية"""
        user_role = self.get_user_role(user_id)
        return permission in ROLE_PERMISSIONS.get(user_role, [])
    
    def require_permission(self, permission: Permission):
        """ديكوريتر للتحقق من الصلاحية"""
        def decorator(func):
            def wrapper(self, update, context, *args, **kwargs):
                user_id = update.effective_user.id
                if not self.check_permission(user_id, permission):
                    return self.send_access_denied_message(update, context)
                return func(self, update, context, *args, **kwargs)
            return wrapper
        return decorator
```

### 3. **جلسات آمنة**
```python
import jwt
import datetime

class SessionManager:
    """مدير الجلسات الآمنة"""
    
    def __init__(self, secret_key: str):
        self.secret_key = secret_key
    
    def create_session(self, user_id: int, role: UserRole) -> str:
        """إنشاء جلسة آمنة"""
        payload = {
            'user_id': user_id,
            'role': role.value,
            'exp': datetime.datetime.utcnow() + datetime.timedelta(hours=24),
            'iat': datetime.datetime.utcnow()
        }
        return jwt.encode(payload, self.secret_key, algorithm='HS256')
    
    def validate_session(self, token: str) -> dict:
        """التحقق من صحة الجلسة"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=['HS256'])
            return payload
        except jwt.ExpiredSignatureError:
            raise ValueError("Session expired")
        except jwt.InvalidTokenError:
            raise ValueError("Invalid session")
```

---

## 👁️ مراقبة الأمان

### 1. **تسجيل الأحداث الأمنية**
```python
import logging
from datetime import datetime

class SecurityLogger:
    """مسجل الأحداث الأمنية"""
    
    def __init__(self):
        self.logger = logging.getLogger('security')
        self.setup_logger()
    
    def log_login_attempt(self, user_id: int, success: bool, ip: str = None):
        """تسجيل محاولة تسجيل دخول"""
        status = "SUCCESS" if success else "FAILED"
        self.logger.warning(
            f"LOGIN_ATTEMPT: User {user_id} - {status} - IP: {ip}"
        )
    
    def log_permission_denied(self, user_id: int, action: str):
        """تسجيل رفض صلاحية"""
        self.logger.error(
            f"PERMISSION_DENIED: User {user_id} attempted {action}"
        )
    
    def log_suspicious_activity(self, user_id: int, activity: str):
        """تسجيل نشاط مشبوه"""
        self.logger.critical(
            f"SUSPICIOUS_ACTIVITY: User {user_id} - {activity}"
        )
```

### 2. **كشف التهديدات**
```python
class ThreatDetection:
    """كشف التهديدات"""
    
    def __init__(self):
        self.failed_attempts = {}
        self.rate_limits = {}
    
    def check_brute_force(self, user_id: int) -> bool:
        """فحص هجمات القوة الغاشمة"""
        now = datetime.now()
        attempts = self.failed_attempts.get(user_id, [])
        
        # إزالة المحاولات القديمة (أكثر من ساعة)
        recent_attempts = [
            attempt for attempt in attempts 
            if (now - attempt).seconds < 3600
        ]
        
        self.failed_attempts[user_id] = recent_attempts
        
        # إذا كان هناك أكثر من 5 محاولات فاشلة في الساعة
        return len(recent_attempts) >= 5
    
    def check_rate_limit(self, user_id: int, action: str) -> bool:
        """فحص حد المعدل"""
        now = datetime.now()
        key = f"{user_id}:{action}"
        
        if key not in self.rate_limits:
            self.rate_limits[key] = []
        
        # إزالة الطلبات القديمة (أكثر من دقيقة)
        recent_requests = [
            req for req in self.rate_limits[key]
            if (now - req).seconds < 60
        ]
        
        self.rate_limits[key] = recent_requests
        
        # حد أقصى 10 طلبات في الدقيقة
        return len(recent_requests) >= 10
```

### 3. **التنبيهات الأمنية**
```python
class SecurityAlerts:
    """تنبيهات الأمان"""
    
    def __init__(self, admin_chat_id: int):
        self.admin_chat_id = admin_chat_id
    
    async def send_security_alert(self, alert_type: str, details: dict):
        """إرسال تنبيه أمني"""
        message = f"""
🚨 تنبيه أمني: {alert_type}

📊 التفاصيل:
{self._format_details(details)}

⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        """
        
        # إرسال للمدير
        await self.send_to_admin(message)
    
    def _format_details(self, details: dict) -> str:
        """تنسيق تفاصيل التنبيه"""
        formatted = []
        for key, value in details.items():
            formatted.append(f"• {key}: {value}")
        return "\n".join(formatted)
```

---

## 🔧 إعدادات الأمان

### متغيرات البيئة الآمنة:
```bash
# ملف .env (يجب عدم رفعه لـ Git)
BOT_TOKEN=your_bot_token_here
ADMIN_BOT_TOKEN=your_admin_bot_token_here
EXA_AI_TOKEN=your_exa_ai_token_here
DATABASE_URL=your_database_url_here
SECRET_KEY=your_secret_key_here
ENCRYPTION_KEY=your_encryption_key_here
```

### ملف .gitignore:
```
# ملفات الأمان
.env
*.key
*.pem
config/secrets.py
logs/security.log
backups/
```

---

## 📋 قائمة مراجعة الأمان

### ✅ **التحقق الدوري:**
- [ ] تحديث التوكنات بانتظام
- [ ] مراجعة سجلات الأمان
- [ ] فحص الصلاحيات
- [ ] تحديث كلمات المرور
- [ ] نسخ احتياطية آمنة
- [ ] فحص الثغرات الأمنية
- [ ] تحديث المكتبات الأمنية
- [ ] مراجعة إعدادات الشبكة

### 🚨 **في حالة الطوارئ:**
1. **إيقاف النظام فوراً**
2. **تغيير جميع التوكنات**
3. **فحص السجلات للاختراق**
4. **إعادة تعيين كلمات المرور**
5. **إشعار المستخدمين المتأثرين**
6. **تحديث إجراءات الأمان**

---

## 🎯 الخلاصة

نظام الأمان يوفر:

- ✅ **حماية شاملة** للبيانات والنظام
- ✅ **تشفير قوي** للمعلومات الحساسة
- ✅ **تحكم دقيق** في الوصول والصلاحيات
- ✅ **مراقبة مستمرة** للأنشطة المشبوهة
- ✅ **تنبيهات فورية** للتهديدات الأمنية

---

**آخر تحديث**: 2025-07-10  
**المرجع التالي**: [SC-002](002_token_management.md)
