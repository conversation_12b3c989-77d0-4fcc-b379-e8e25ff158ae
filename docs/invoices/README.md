# نظام إدارة الفواتير
# Invoice Management System

## نظرة عامة

نظام شامل لإدارة وإنشاء الفواتير الإلكترونية مع دعم كامل للغة العربية وميزات متقدمة للتحكم في التصميم والتخطيط.

## الميزات الرئيسية

### 🎯 **إنشاء الفواتير**
- إنشاء فواتير PDF عالية الجودة
- دعم كامل للنصوص العربية
- QR Code مع بيانات الفاتورة
- تصميم احترافي مع ألوان مخصصة

### 🎨 **التحكم في التصميم**
- **عنوان الفاتورة**: تحكم كامل في الموقع والمحاذاة والتباعد
- **الشعار**: تخصيص الموقع والحجم والتباعد
- **تخطيط الرأس**: عروض أعمدة مخصصة بدلاً من التقسيم المتساوي
- **الملاحظات**: تحكم في الموقع والنمط والتباعد

### 📊 **أنواع المعاملات**
- إضافة رصيد
- خصم رصيد
- تحويلات مالية
- عمليات المحفظة

### 💱 **العملات المدعومة**
- إكسا (العملة الأساسية)
- دولار أمريكي (1 إكسا = 3 دولار)
- توكن (1 إكسا = 524,288 ألف توكن)

## الملفات الأساسية

### `invoice_manager.py`
الملف الرئيسي لإدارة الفواتير يحتوي على:
- `InvoiceManager`: الكلاس الرئيسي
- دوال إنشاء الفواتير
- دوال التحكم في التصميم
- معالجة النصوص العربية

### `interactive_invoice_manager.py`
واجهة تفاعلية لإنشاء الفواتير عبر سطر الأوامر

### `pdf_to_image.py`
تحويل ملفات PDF إلى صور

## المجلدات

### `generated/`
- `PDF/`: ملفات PDF المُنشأة
- `QR Code/`: صور QR Code
- `Image/`: صور الفواتير

### `assets/`
- `logo.png`: شعار النظام
- `fonts/`: الخطوط العربية

## الاستخدام السريع

```python
from shared.invoices.invoice_manager import InvoiceManager

# إنشاء مدير الفواتير
invoice_manager = InvoiceManager()

# بيانات الفاتورة
invoice_data = {
    "invoice_number": "8081234567",
    "user_name": "أحمد محمد",
    "wallet_number": "9091234567",
    "transaction_type": "إضافة رصيد",
    "amount": 100,
    "currency": "إكسا",
    "previous_balance": 50,
    "new_balance": 150
}

# إنشاء الفاتورة
success, pdf_path, message = invoice_manager.create_invoice_pdf(invoice_data)

if success:
    print(f"تم إنشاء الفاتورة: {pdf_path}")
else:
    print(f"خطأ: {message}")
```

## التخصيص المتقدم

### تخصيص عنوان الفاتورة
```python
# وضع العنوان على اليمين مع محاذاة يسار
invoice_manager.set_title_to_right_with_left_alignment(left_indent=40)

# تخصيص التباعد
invoice_manager.set_invoice_title_spacing(vertical_spacing=0.15)

# تغيير النص والألوان
invoice_manager.set_invoice_title_text("عنوان مخصص", "نص فرعي")
invoice_manager.set_invoice_title_text_style(
    line1_font_size=18,
    line1_color='#2A3C73',
    line2_font_size=16,
    line2_color='#F0522E'
)
```

### تخصيص الشعار
```python
# تعيين موقع الشعار
invoice_manager.set_logo_position_and_spacing(
    position='right',
    horizontal_offset=20,
    top_padding=15
)

# تغيير حجم الشعار
invoice_manager.set_logo_size(width=1.6, height=1.2)
```

### تخصيص تخطيط الرأس
```python
# عروض أعمدة مخصصة
invoice_manager.set_header_column_widths(
    qr_width=1.3,      # عمود QR
    logo_width=2.2,    # عمود الشعار
    title_width=1.5    # عمود العنوان
)
```

## الإعدادات المسبقة

```python
# إعدادات جاهزة للاستخدام
invoice_manager.apply_preset_header_layout('title_right_left')  # عنوان يمين مع محاذاة يسار
invoice_manager.apply_preset_header_layout('title_center')      # عنوان في الوسط
invoice_manager.apply_preset_header_layout('compact')           # تخطيط مضغوط
```

## متطلبات النظام

### المكتبات المطلوبة
```
reportlab>=4.0.0
qrcode[pil]>=7.4.0
python-bidi>=0.4.2
arabic-reshaper>=3.0.0
Pillow>=10.0.0
```

### الخطوط العربية
- NotoSansArabic-Regular.ttf
- NotoSansArabic-Bold.ttf
- Amiri-Regular.ttf (بديل)
- Amiri-Bold.ttf (بديل)

## الأمان والأداء

- تشفير بيانات QR Code
- تحسين استهلاك الذاكرة
- معالجة الأخطاء الشاملة
- تسجيل العمليات (Logging)

## الدعم والصيانة

- دعم كامل للنصوص العربية
- توافق مع جميع أحجام الصفحات
- معالجة الأخطاء التلقائية
- تنظيف الملفات المؤقتة

---

للمزيد من التفاصيل، راجع الملفات التالية:
- `header_control.md`: التحكم في رأس الفاتورة
- `notes_control.md`: التحكم في الملاحظات
- `spacing_control.md`: التحكم في التباعد
- `examples.md`: أمثلة شاملة
