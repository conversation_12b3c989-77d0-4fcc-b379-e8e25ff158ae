# التحكم في رأس الفاتورة
# Invoice Header Control

## نظرة عامة

ميزات شاملة للتحكم في رأس الفاتورة مع إمكانية التحكم الكامل في عنوان الفاتورة والشعار وتخطيط الأعمدة.

## عنوان الفاتورة

### الموقع والمحاذاة
```python
# تعيين موقع العنوان والمحاذاة الداخلية
invoice_manager.set_invoice_title_position_and_alignment(
    position='right',           # 'center', 'right', 'left'
    internal_alignment='left'   # 'center', 'right', 'left'
)
```

### التباعد والإزاحة
```python
# التحكم في التباعد العمودي والأفقي
invoice_manager.set_invoice_title_spacing(
    vertical_spacing=0.2,    # التباعد بين السطرين (بالإنش)
    horizontal_offset=0,     # الإزاحة الأفقية (بالنقاط)
    vertical_offset=0,       # الإزاحة العمودية (بالنقاط)
    left_indent=0,          # الإزاحة من اليسار (بالنقاط)
    right_indent=0          # الإزاحة من اليمين (بالنقاط)
)
```

### النص والألوان
```python
# تخصيص نمط النص لكل سطر
invoice_manager.set_invoice_title_text_style(
    line1_font_size=15,      # حجم خط السطر الأول
    line1_color='#2A3C73',   # لون السطر الأول
    line2_font_size=15,      # حجم خط السطر الثاني
    line2_color='#F0522E',   # لون السطر الثاني
    leading=14               # المسافة بين الأسطر
)

# تغيير نص العنوان
invoice_manager.set_invoice_title_text(
    line1_text="عنوان الفاتورة",
    line2_text="الإلكترونية"
)
```

## الشعار

### الموقع والتباعد
```python
# تعيين موقع وتباعد الشعار
invoice_manager.set_logo_position_and_spacing(
    position='center',        # 'center', 'right', 'left'
    horizontal_offset=0,      # الإزاحة الأفقية (بالنقاط)
    vertical_offset=0,        # الإزاحة العمودية (بالنقاط)
    left_indent=0,           # الإزاحة من اليسار (بالنقاط)
    right_indent=0,          # الإزاحة من اليمين (بالنقاط)
    top_padding=10,          # التباعد العلوي (بالنقاط)
    bottom_padding=10        # التباعد السفلي (بالنقاط)
)
```

### الحجم
```python
# تعيين حجم الشعار
invoice_manager.set_logo_size(
    width=1.4,              # العرض (بالإنش)
    height=1.0703           # الارتفاع (بالإنش)
)
```

## تخطيط الرأس

### عروض الأعمدة المخصصة
```python
# تعيين عروض أعمدة الرأس (بدلاً من التقسيم المتساوي)
invoice_manager.set_header_column_widths(
    qr_width=1.5,           # عرض عمود QR (بالإنش)
    logo_width=2.0,         # عرض عمود الشعار (بالإنش)
    title_width=1.5         # عرض عمود العنوان (بالإنش)
)
```

## الدوال المختصرة

### عنوان على اليمين مع محاذاة يسار
```python
invoice_manager.set_title_to_right_with_left_alignment(left_indent=40)
```

### عنوان في الوسط مع خط كبير
```python
invoice_manager.set_title_to_center_with_large_font(font_size=18)
```

### شعار على اليمين مع تباعد
```python
invoice_manager.set_logo_to_right_with_spacing(horizontal_offset=20)
```

## الإعدادات المسبقة

### الإعداد الافتراضي
```python
invoice_manager.apply_preset_header_layout('default')
```

### عنوان على اليمين مع محاذاة يسار
```python
invoice_manager.apply_preset_header_layout('title_right_left')
```

### عنوان في الوسط
```python
invoice_manager.apply_preset_header_layout('title_center')
```

### تخطيط مضغوط
```python
invoice_manager.apply_preset_header_layout('compact')
```

## أمثلة عملية

### المثال الأساسي
```python
from shared.invoices.invoice_manager import InvoiceManager

# إنشاء مدير الفواتير
invoice_manager = InvoiceManager()

# تطبيق الإعداد: عنوان على اليمين مع محاذاة يسار
invoice_manager.set_title_to_right_with_left_alignment(left_indent=40)

# تخصيص التباعد العمودي
invoice_manager.set_invoice_title_spacing(vertical_spacing=0.15)

# تخصيص الشعار
invoice_manager.set_logo_position_and_spacing(
    position='right',
    horizontal_offset=20,
    top_padding=5
)

# إنشاء الفاتورة
invoice_data = {
    "invoice_number": "8081234567",
    "user_name": "أحمد محمد",
    "wallet_number": "9091234567",
    "transaction_type": "إضافة رصيد",
    "amount": 100,
    "currency": "إكسا"
}

success, pdf_path, message = invoice_manager.create_invoice_pdf(invoice_data)
```

### تخصيص شامل
```python
# تخصيص كامل للرأس
invoice_manager.set_invoice_title_position_and_alignment('right', 'left')
invoice_manager.set_invoice_title_spacing(
    vertical_spacing=0.3,     # تباعد كبير بين السطرين
    horizontal_offset=10,     # إزاحة أفقية
    left_indent=50,          # إزاحة كبيرة من اليسار
    vertical_offset=5        # إزاحة عمودية
)

invoice_manager.set_logo_position_and_spacing(
    position='center',
    horizontal_offset=15,
    top_padding=15,
    bottom_padding=15
)

invoice_manager.set_header_column_widths(
    qr_width=1.3,           # عمود QR أضيق
    logo_width=2.2,         # عمود الشعار أوسع
    title_width=1.5         # عمود العنوان متوسط
)
```

## التغييرات الرئيسية

1. **تم تغيير الاسم**: من "النص اليمين" إلى "عنوان الفاتورة"
2. **إزالة التقسيم المتساوي**: لم يعد الرأس مقسم بالتساوي على 3 أعمدة
3. **تحكم مخصص**: إمكانية التحكم الكامل في التباعد والمحاذاة
4. **إعدادات منفصلة**: كل عنصر (QR، شعار، عنوان) له إعدادات منفصلة

## نصائح الاستخدام

- استخدم القيم بالنقاط للتباعد الدقيق
- استخدم القيم بالإنش للأحجام والعروض
- اختبر الإعدادات مع أنواع مختلفة من البيانات
- احفظ الإعدادات المفضلة كإعدادات مسبقة مخصصة
