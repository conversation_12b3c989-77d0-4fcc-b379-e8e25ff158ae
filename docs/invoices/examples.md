# أمثلة شاملة لنظام الفواتير
# Comprehensive Invoice System Examples

## الاستخدام الأساسي

### إنشاء فاتورة بسيطة
```python
from shared.invoices.invoice_manager import InvoiceManager

# إنشاء مدير الفواتير
invoice_manager = InvoiceManager()

# بيانات الفاتورة
invoice_data = {
    "invoice_number": "8081234567",
    "user_name": "أحمد محمد علي",
    "wallet_number": "9091234567",
    "transaction_type": "إضافة رصيد",
    "amount": 100,
    "currency": "إكسا",
    "previous_balance": 50,
    "new_balance": 150
}

# إنشاء الفاتورة
success, pdf_path, message = invoice_manager.create_invoice_pdf(invoice_data)

if success:
    print(f"✅ تم إنشاء الفاتورة: {pdf_path}")
else:
    print(f"❌ خطأ: {message}")
```

## تخصيص رأس الفاتورة

### عنوان على اليمين مع محاذاة يسار
```python
# المثال المطلوب في السؤال
invoice_manager = InvoiceManager()

# تطبيق الإعداد: عنوان على اليمين مع محاذاة يسار
invoice_manager.set_title_to_right_with_left_alignment(left_indent=40)

# تخصيص التباعد العمودي
invoice_manager.set_invoice_title_spacing(vertical_spacing=0.15)

# تخصيص الشعار - وضعه في الأعلى على اليمين
invoice_manager.set_logo_position_and_spacing(
    position='right',
    horizontal_offset=20,
    top_padding=5
)

# تخصيص عروض الأعمدة
invoice_manager.set_header_column_widths(
    qr_width=1.3,      # عمود QR أضيق
    logo_width=2.2,    # عمود الشعار أوسع
    title_width=1.5    # عمود العنوان متوسط
)

# إنشاء الفاتورة
success, pdf_path, message = invoice_manager.create_invoice_pdf(invoice_data)
```

### عنوان في الوسط مع خط كبير
```python
invoice_manager = InvoiceManager()

# استخدام الدالة المختصرة
invoice_manager.set_title_to_center_with_large_font(font_size=20)

# تخصيص الألوان
invoice_manager.set_invoice_title_text_style(
    line1_color='#8E44AD',  # بنفسجي
    line2_color='#E67E22'   # برتقالي
)

# تغيير النص
invoice_manager.set_invoice_title_text(
    line1_text="عنوان الفاتورة",
    line2_text="الإلكترونية"
)

success, pdf_path, message = invoice_manager.create_invoice_pdf(invoice_data)
```

### تخصيص شامل للرأس
```python
invoice_manager = InvoiceManager()

# تخصيص العنوان
invoice_manager.set_invoice_title_position_and_alignment('right', 'left')
invoice_manager.set_invoice_title_spacing(
    vertical_spacing=0.3,     # تباعد كبير بين السطرين
    horizontal_offset=10,     # إزاحة أفقية
    left_indent=50,          # إزاحة كبيرة من اليسار
    vertical_offset=5        # إزاحة عمودية
)
invoice_manager.set_invoice_title_text_style(
    line1_font_size=18,
    line1_color='#2E86AB',
    line2_font_size=16,
    line2_color='#F39C12'
)

# تخصيص الشعار
invoice_manager.set_logo_position_and_spacing(
    position='center',
    horizontal_offset=15,
    top_padding=15,
    bottom_padding=15
)
invoice_manager.set_logo_size(width=1.6, height=1.2)

# تخصيص عروض الأعمدة
invoice_manager.set_header_column_widths(
    qr_width=1.3,
    logo_width=2.2,
    title_width=1.5
)

success, pdf_path, message = invoice_manager.create_invoice_pdf(invoice_data)
```

## تخصيص الملاحظات

### ملاحظات على اليمين مع محاذاة يسار
```python
invoice_manager = InvoiceManager()

# تطبيق الإعداد المطلوب
invoice_manager.set_notes_to_right_with_left_alignment(left_indent=30)

# تخصيص التباعد العمودي
invoice_manager.set_notes_spacing(
    top_spacing=0.2,      # مسافة قبل الملاحظات
    bottom_spacing=0.3,   # مسافة بعد الملاحظات
    between_spacing=0.12  # مسافة بين الملاحظات
)

success, pdf_path, message = invoice_manager.create_invoice_pdf(invoice_data)
```

### تخصيص شامل للملاحظات
```python
invoice_manager = InvoiceManager()

# تخصيص كامل للملاحظات
invoice_manager.set_notes_position_and_alignment('right', 'left')
invoice_manager.set_notes_spacing(
    top_spacing=0.25, 
    bottom_spacing=0.35, 
    between_spacing=0.15,
    left_indent=40, 
    right_indent=5
)
invoice_manager.set_notes_text_style(
    font_size=10, 
    text_color='#34495E', 
    leading=14
)
invoice_manager.set_notes_background_and_border(
    background_color='#F8F9FA', 
    border_color='#BDC3C7', 
    border_width=1, 
    padding=8
)

success, pdf_path, message = invoice_manager.create_invoice_pdf(invoice_data)
```

## استخدام الإعدادات المسبقة

### إعدادات رأس الفاتورة
```python
# إعدادات مختلفة لرأس الفاتورة
presets = ['default', 'title_right_left', 'title_center', 'compact']

for preset in presets:
    invoice_manager = InvoiceManager()
    
    # تطبيق الإعداد المسبق
    invoice_manager.apply_preset_header_layout(preset)
    
    # تعديل رقم الفاتورة لكل إعداد
    invoice_data['invoice_number'] = f"808{1234567 + presets.index(preset)}"
    
    success, pdf_path, message = invoice_manager.create_invoice_pdf(invoice_data)
    print(f"إعداد {preset}: {pdf_path if success else message}")
```

### إعدادات الملاحظات
```python
# إعدادات مختلفة للملاحظات
notes_presets = ['default', 'right_left', 'left_right', 'center_large', 'minimal']

for preset in notes_presets:
    invoice_manager = InvoiceManager()
    
    # تطبيق الإعداد المسبق للملاحظات
    invoice_manager.apply_preset_notes_settings(preset)
    
    # تعديل رقم الفاتورة لكل إعداد
    invoice_data['invoice_number'] = f"809{1234567 + notes_presets.index(preset)}"
    
    success, pdf_path, message = invoice_manager.create_invoice_pdf(invoice_data)
    print(f"ملاحظات {preset}: {pdf_path if success else message}")
```

## أنواع المعاملات المختلفة

### فاتورة إضافة رصيد
```python
add_balance_data = {
    "invoice_number": "8081111111",
    "user_name": "سارة أحمد",
    "wallet_number": "9091111111",
    "transaction_type": "إضافة رصيد",
    "amount": 200,
    "currency": "إكسا",
    "previous_balance": 100,
    "new_balance": 300
}

success, pdf_path, message = invoice_manager.create_invoice_pdf(add_balance_data)
```

### فاتورة خصم رصيد
```python
deduct_balance_data = {
    "invoice_number": "8082222222",
    "user_name": "محمد علي",
    "wallet_number": "9092222222",
    "transaction_type": "خصم رصيد",
    "amount": 50,
    "currency": "إكسا",
    "previous_balance": 300,
    "new_balance": 250
}

success, pdf_path, message = invoice_manager.create_invoice_pdf(deduct_balance_data)
```

## مثال شامل متقدم

```python
def create_custom_invoice():
    """مثال شامل لإنشاء فاتورة مخصصة"""
    
    # إنشاء مدير الفواتير
    invoice_manager = InvoiceManager()
    
    # تخصيص رأس الفاتورة
    invoice_manager.set_invoice_title_position_and_alignment('right', 'left')
    invoice_manager.set_invoice_title_spacing(
        vertical_spacing=0.2,
        left_indent=35,
        vertical_offset=3
    )
    invoice_manager.set_invoice_title_text_style(
        line1_font_size=16,
        line1_color='#1B4F72',
        line2_font_size=14,
        line2_color='#E74C3C'
    )
    
    # تخصيص الشعار
    invoice_manager.set_logo_position_and_spacing(
        position='center',
        horizontal_offset=10,
        top_padding=12,
        bottom_padding=12
    )
    invoice_manager.set_logo_size(width=1.5, height=1.1)
    
    # تخصيص عروض الأعمدة
    invoice_manager.set_header_column_widths(
        qr_width=1.4,
        logo_width=2.1,
        title_width=1.5
    )
    
    # تخصيص الملاحظات
    invoice_manager.set_notes_position_and_alignment('right', 'left')
    invoice_manager.set_notes_spacing(
        top_spacing=0.3,
        bottom_spacing=0.2,
        between_spacing=0.1,
        left_indent=25
    )
    invoice_manager.set_notes_text_style(
        font_size=9,
        text_color='#566573',
        leading=13
    )
    
    # بيانات الفاتورة
    invoice_data = {
        "invoice_number": "8089999999",
        "user_name": "عميل مميز",
        "wallet_number": "9099999999",
        "transaction_type": "إضافة رصيد",
        "amount": 500,
        "currency": "إكسا",
        "previous_balance": 1000,
        "new_balance": 1500
    }
    
    # إنشاء الفاتورة
    success, pdf_path, message = invoice_manager.create_invoice_pdf(invoice_data)
    
    if success:
        print(f"✅ تم إنشاء الفاتورة المخصصة: {pdf_path}")
        return pdf_path
    else:
        print(f"❌ فشل في إنشاء الفاتورة: {message}")
        return None

# تشغيل المثال
custom_invoice_path = create_custom_invoice()
```

## نصائح للاستخدام الأمثل

### الأداء
```python
# إنشاء مدير واحد لعدة فواتير
invoice_manager = InvoiceManager()

# تطبيق الإعدادات مرة واحدة
invoice_manager.apply_preset_header_layout('title_right_left')

# إنشاء عدة فواتير بنفس الإعدادات
for i in range(10):
    invoice_data['invoice_number'] = f"808{1000000 + i}"
    success, pdf_path, message = invoice_manager.create_invoice_pdf(invoice_data)
```

### معالجة الأخطاء
```python
try:
    invoice_manager = InvoiceManager()
    success, pdf_path, message = invoice_manager.create_invoice_pdf(invoice_data)
    
    if success:
        print(f"نجح: {pdf_path}")
    else:
        print(f"فشل: {message}")
        
except Exception as e:
    print(f"خطأ غير متوقع: {e}")
```

### التحقق من الإعدادات
```python
# عرض الإعدادات الحالية
settings = invoice_manager.get_all_settings()
print("الإعدادات الحالية:")
for category, values in settings.items():
    print(f"\n{category}:")
    for key, value in values.items():
        print(f"  {key}: {value}")
```
