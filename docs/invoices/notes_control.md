# التحكم في الملاحظات
# Notes Control

## نظرة عامة

نظام شامل للتحكم في ملاحظات الفاتورة مع إمكانية تخصيص الموقع والمحاذاة والتباعد والنمط.

## الموقع والمحاذاة

### تعيين الموقع والمحاذاة الداخلية
```python
# تعيين موقع الملاحظات والمحاذاة الداخلية
invoice_manager.set_notes_position_and_alignment(
    position='center',           # 'center', 'right', 'left'
    internal_alignment='center'  # 'center', 'right', 'left'
)
```

## التباعد والإزاحة

### التحكم في التباعد
```python
# تعيين التباعد للملاحظات
invoice_manager.set_notes_spacing(
    top_spacing=0.5,        # المسافة قبل الملاحظات (بالإنش)
    bottom_spacing=0.0,     # المسافة بعد الملاحظات (بالإنش)
    between_spacing=0.05,   # المسافة بين الملاحظات (بالإنش)
    left_indent=0,          # الإزاحة من اليسار (بالنقاط)
    right_indent=7.5        # الإزاحة من اليمين (بالنقاط)
)
```

## النمط والتصميم

### تخصيص النص
```python
# تعيين نمط النص للملاحظات
invoice_manager.set_notes_text_style(
    font_size=9,            # حجم الخط
    text_color='#7F8C8D',   # لون النص
    leading=12              # المسافة بين الأسطر
)
```

### الخلفية والحدود
```python
# تعيين خلفية وحدود للملاحظات (اختياري)
invoice_manager.set_notes_background_and_border(
    background_color='#F8F9FA',  # لون الخلفية
    border_color='#BDC3C7',      # لون الحدود
    border_width=1,              # سماكة الحدود
    padding=8                    # التباعد الداخلي
)
```

## الدوال المختصرة

### ملاحظات على اليمين مع محاذاة يسار
```python
invoice_manager.set_notes_to_right_with_left_alignment(left_indent=30)
```

### ملاحظات على اليسار مع محاذاة يمين
```python
invoice_manager.set_notes_to_left_with_right_alignment(right_indent=30)
```

### تعديل التباعد العمودي
```python
invoice_manager.adjust_notes_vertical_spacing(
    top_increase=0.1,      # زيادة المسافة العلوية
    bottom_increase=0.2    # زيادة المسافة السفلية
)
```

## الإعدادات المسبقة

### الإعداد الافتراضي
```python
invoice_manager.apply_preset_notes_settings('default')
```

### ملاحظات على اليمين مع محاذاة يسار
```python
invoice_manager.apply_preset_notes_settings('right_left')
```

### ملاحظات على اليسار مع محاذاة يمين
```python
invoice_manager.apply_preset_notes_settings('left_right')
```

### ملاحظات كبيرة في الوسط
```python
invoice_manager.apply_preset_notes_settings('center_large')
```

### ملاحظات مصغرة
```python
invoice_manager.apply_preset_notes_settings('minimal')
```

## أمثلة عملية

### المثال الأساسي: ملاحظات على اليمين مع محاذاة يسار
```python
from shared.invoices.invoice_manager import InvoiceManager

# إنشاء مدير الفواتير
invoice_manager = InvoiceManager()

# تطبيق الإعداد المطلوب
invoice_manager.set_notes_to_right_with_left_alignment(left_indent=30)

# تخصيص التباعد العمودي
invoice_manager.set_notes_spacing(
    top_spacing=0.2,      # مسافة قبل الملاحظات
    bottom_spacing=0.3,   # مسافة بعد الملاحظات
    between_spacing=0.12  # مسافة بين الملاحظات
)
```

### تخصيص شامل
```python
# تخصيص كامل للملاحظات
invoice_manager.set_notes_position_and_alignment('right', 'left')
invoice_manager.set_notes_spacing(
    top_spacing=0.25, 
    bottom_spacing=0.35, 
    between_spacing=0.15,
    left_indent=40, 
    right_indent=5
)
invoice_manager.set_notes_text_style(
    font_size=10, 
    text_color='#34495E', 
    leading=14
)
invoice_manager.set_notes_background_and_border(
    background_color='#F8F9FA', 
    border_color='#BDC3C7', 
    border_width=1, 
    padding=8
)
```

### استخدام الإعدادات المسبقة
```python
# تطبيق إعدادات مسبقة مختلفة
presets = ['default', 'right_left', 'left_right', 'center_large', 'minimal']

for preset in presets:
    invoice_manager.apply_preset_notes_settings(preset)
    # إنشاء فاتورة مع كل إعداد
    success, pdf_path, message = invoice_manager.create_invoice_pdf(invoice_data)
```

## إدارة الإعدادات

### الحصول على الإعدادات الحالية
```python
# عرض الإعدادات الحالية
current_settings = invoice_manager.get_notes_settings()
for key, value in current_settings.items():
    print(f"{key}: {value}")
```

### إعادة تعيين الإعدادات
```python
# إعادة تعيين جميع إعدادات الملاحظات إلى القيم الافتراضية
invoice_manager.reset_notes_settings()
```

### التحقق من صحة الإعدادات
```python
# التحقق من صحة الإعدادات وإصلاح القيم الخاطئة
invoice_manager.validate_and_fix_notes_settings()
```

## نصائح الاستخدام

### التباعد
- استخدم قيم صغيرة (0.05-0.3) للتباعد العمودي
- استخدم قيم متوسطة (10-50) للإزاحة الأفقية
- اختبر التباعد مع محتوى مختلف

### المحاذاة
- `right + left`: ملاحظات على اليمين مع نص محاذي لليسار
- `center + center`: ملاحظات في الوسط مع نص في الوسط
- `left + right`: ملاحظات على اليسار مع نص محاذي لليمين

### الألوان
- استخدم ألوان فاتحة للملاحظات (#7F8C8D, #95A5A6)
- تأكد من التباين الكافي مع الخلفية
- اختبر الألوان في الطباعة

### الخطوط
- استخدم أحجام خطوط صغيرة (7-11) للملاحظات
- زد المسافة بين الأسطر للوضوح
- تأكد من قابلية القراءة

## الملاحظات الافتراضية

النظام يضيف تلقائياً الملاحظات التالية:
- معلومات الاتصال
- شروط الخدمة
- معلومات إضافية حسب نوع المعاملة

يمكن تخصيص هذه الملاحظات من خلال إعدادات النظام.
