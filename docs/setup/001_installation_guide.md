# ⚙️ دليل التثبيت الشامل

**رقم التوثيق**: ST-001  
**التاريخ**: 2025-07-10  
**الإصدار**: 1.0.0  
**المطور**: صلاح الدين الدروبي  

---

## 📖 المحتويات

1. [متطلبات النظام](#متطلبات-النظام)
2. [تثبيت Python](#تثبيت-python)
3. [تحميل المشروع](#تحميل-المشروع)
4. [تثبيت المكتبات](#تثبيت-المكتبات)
5. [إعداد البيئة](#إعداد-البيئة)

---

## 💻 متطلبات النظام

### الحد الأدنى:
- **نظام التشغيل**: Windows 10/11, macOS 10.14+, Ubuntu 18.04+
- **Python**: 3.8 أو أحدث
- **الذاكرة**: 2 GB RAM
- **التخزين**: 1 GB مساحة فارغة
- **الإنترنت**: اتصال مستقر

### المستحسن:
- **Python**: 3.11 أو أحدث
- **الذاكرة**: 4 GB RAM أو أكثر
- **التخزين**: 5 GB مساحة فارغة
- **المعالج**: متعدد النوى

---

## 🐍 تثبيت Python

### Windows:
```bash
# تحميل من الموقع الرسمي
https://www.python.org/downloads/windows/

# أو باستخدام Chocolatey
choco install python

# أو باستخدام winget
winget install Python.Python.3.11
```

### macOS:
```bash
# باستخدام Homebrew
brew install python@3.11

# أو تحميل من الموقع الرسمي
https://www.python.org/downloads/macos/
```

### Ubuntu/Debian:
```bash
# تحديث قائمة الحزم
sudo apt update

# تثبيت Python
sudo apt install python3.11 python3.11-pip python3.11-venv

# التحقق من التثبيت
python3.11 --version
```

### CentOS/RHEL:
```bash
# تثبيت Python
sudo dnf install python3.11 python3.11-pip

# أو باستخدام yum
sudo yum install python3.11 python3.11-pip
```

---

## 📥 تحميل المشروع

### الطريقة الأولى: Git Clone
```bash
# تحميل المشروع
git clone https://github.com/username/Salah_Bot.git

# الانتقال للمجلد
cd Salah_Bot

# التحقق من الملفات
ls -la
```

### الطريقة الثانية: تحميل ZIP
```bash
# تحميل ملف ZIP من GitHub
# فك الضغط
unzip Salah_Bot-main.zip

# إعادة تسمية المجلد
mv Salah_Bot-main Salah_Bot

# الانتقال للمجلد
cd Salah_Bot
```

### الطريقة الثالثة: إنشاء مشروع جديد
```bash
# إنشاء مجلد المشروع
mkdir Salah_Bot
cd Salah_Bot

# إنشاء الهيكل الأساسي
mkdir -p main_bot/core main_bot/features
mkdir -p admin_bot/core admin_bot/management admin_bot/monitoring
mkdir -p shared/config shared/database shared/data_processing
mkdir -p docs logs backups
```

---

## 📦 تثبيت المكتبات

### إنشاء بيئة افتراضية:
```bash
# إنشاء البيئة الافتراضية
python -m venv venv

# تفعيل البيئة (Windows)
venv\Scripts\activate

# تفعيل البيئة (macOS/Linux)
source venv/bin/activate

# التحقق من التفعيل
which python
```

### تثبيت المكتبات الأساسية:
```bash
# تحديث pip
python -m pip install --upgrade pip

# تثبيت المكتبات الأساسية
pip install python-telegram-bot==20.7
pip install requests==2.31.0
pip install aiohttp==3.9.1
pip install asyncio-mqtt==0.16.1
```

### تثبيت مكتبات قاعدة البيانات:
```bash
# SQLite (مدمج مع Python)
# لا يحتاج تثبيت إضافي

# PostgreSQL (اختياري)
pip install psycopg2-binary==2.9.9

# MySQL (اختياري)
pip install PyMySQL==1.1.0
```

### تثبيت مكتبات الأمان:
```bash
# تشفير البيانات
pip install cryptography==41.0.8

# JWT للجلسات
pip install PyJWT==2.8.0

# التحقق من كلمات المرور
pip install bcrypt==4.1.2
```

### تثبيت مكتبات إضافية:
```bash
# معالجة الصور
pip install Pillow==10.1.0

# معالجة ملفات PDF
pip install PyPDF2==3.0.1

# معالجة ملفات Excel
pip install openpyxl==3.1.2

# معالجة التواريخ
pip install python-dateutil==2.8.2

# متغيرات البيئة
pip install python-dotenv==1.0.0
```

### تثبيت من ملف requirements.txt الموحد:
```bash
# تثبيت جميع المكتبات المطلوبة للنظام الكامل
pip install -r requirements.txt

# ملاحظة: ملف requirements.txt موحد في الجذر
# يحتوي على جميع المكتبات المطلوبة لجميع أجزاء النظام
```

---

## 🔧 إعداد البيئة

### إنشاء ملف .env:
```bash
# إنشاء ملف متغيرات البيئة
touch .env

# إضافة المتغيرات (Windows)
echo BOT_TOKEN=your_bot_token_here >> .env
echo ADMIN_BOT_TOKEN=your_admin_bot_token_here >> .env
echo EXA_AI_TOKEN=your_exa_ai_token_here >> .env
```

### محتوى ملف .env:
```env
# توكنات البوتات
BOT_TOKEN=1234567890:ABCdefGHIjklMNOpqrsTUVwxyz
ADMIN_BOT_TOKEN=0987654321:ZYXwvuTSRqponMLKjihgFEDcba
EXA_AI_TOKEN=sk-1234567890abcdef

# إعدادات قاعدة البيانات
DATABASE_URL=sqlite:///bot_database.db
DATABASE_NAME=salah_bot_db

# إعدادات الأمان
SECRET_KEY=your-secret-key-here
ENCRYPTION_KEY=your-encryption-key-here

# إعدادات الإدارة
ADMIN_CHAT_ID=123456789
ADMIN_USERNAME=admin_username

# إعدادات النظام
DEBUG=False
LOG_LEVEL=INFO
TIMEZONE=Asia/Riyadh
```

### إعداد ملف .gitignore:
```gitignore
# ملفات البيئة
.env
.env.local
.env.production

# مجلدات Python
__pycache__/
*.py[cod]
*$py.class
venv/
env/

# ملفات قاعدة البيانات
*.db
*.sqlite
*.sqlite3

# ملفات السجلات
logs/
*.log

# ملفات النسخ الاحتياطية
backups/
*.backup

# ملفات IDE
.vscode/
.idea/
*.swp
*.swo

# ملفات النظام
.DS_Store
Thumbs.db
```

### إنشاء هيكل المجلدات:
```bash
# إنشاء المجلدات الأساسية
mkdir -p main_bot/{core,features,media}
mkdir -p admin_bot/{core,management,monitoring}
mkdir -p shared/{config,database,data_processing,security,utils}
mkdir -p docs/{main,admin,features,security,setup}
mkdir -p logs/{main_bot,admin_bot,system}
mkdir -p backups/{daily,weekly,monthly}

# إنشاء ملفات __init__.py
touch main_bot/__init__.py
touch admin_bot/__init__.py
touch shared/__init__.py
touch shared/config/__init__.py
touch shared/database/__init__.py
touch shared/data_processing/__init__.py
```

### إعداد ملفات التكوين:
```python
# shared/config/settings.py
import os
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

# إعدادات البوت
BOT_TOKEN = os.getenv('BOT_TOKEN')
ADMIN_BOT_TOKEN = os.getenv('ADMIN_BOT_TOKEN')
EXA_AI_TOKEN = os.getenv('EXA_AI_TOKEN')

# إعدادات قاعدة البيانات
DATABASE_URL = os.getenv('DATABASE_URL', 'sqlite:///bot_database.db')

# إعدادات الأمان
SECRET_KEY = os.getenv('SECRET_KEY')
ENCRYPTION_KEY = os.getenv('ENCRYPTION_KEY')

# إعدادات الإدارة
ADMIN_CHAT_ID = int(os.getenv('ADMIN_CHAT_ID', 0))

# إعدادات النظام
DEBUG = os.getenv('DEBUG', 'False').lower() == 'true'
LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
```

---

## ✅ التحقق من التثبيت

### اختبار Python:
```bash
# التحقق من إصدار Python
python --version

# اختبار استيراد المكتبات
python -c "import telegram; print('✅ python-telegram-bot installed')"
python -c "import requests; print('✅ requests installed')"
python -c "import sqlite3; print('✅ SQLite available')"
```

### اختبار البيئة:
```bash
# اختبار متغيرات البيئة
python -c "
import os
from dotenv import load_dotenv
load_dotenv()
print('✅ BOT_TOKEN:', 'Found' if os.getenv('BOT_TOKEN') else 'Missing')
print('✅ ADMIN_BOT_TOKEN:', 'Found' if os.getenv('ADMIN_BOT_TOKEN') else 'Missing')
"
```

### اختبار الهيكل:
```bash
# التحقق من وجود الملفات الأساسية
ls -la main_bot/
ls -la admin_bot/
ls -la shared/
ls -la docs/

# التحقق من الأذونات
ls -la .env
```

---

## 🚀 الخطوات التالية

بعد إكمال التثبيت:

1. **إعداد التوكنات**: راجع [دليل الإعداد](002_configuration_guide.md)
2. **تشغيل البوت**: راجع [دليل التشغيل](003_deployment_guide.md)
3. **إعداد الأمان**: راجع [دليل الأمان](../security/001_security_overview.md)
4. **إعداد قاعدة البيانات**: راجع [دليل قاعدة البيانات](../database/001_database_overview.md)

---

## 🆘 حل المشاكل الشائعة

### مشكلة: Python غير موجود
```bash
# Windows
# تأكد من إضافة Python لـ PATH
# أعد تثبيت Python مع تحديد "Add to PATH"

# macOS/Linux
which python3
# إذا لم يوجد، أعد التثبيت
```

### مشكلة: pip غير موجود
```bash
# تثبيت pip
python -m ensurepip --upgrade

# أو تحميل get-pip.py
curl https://bootstrap.pypa.io/get-pip.py -o get-pip.py
python get-pip.py
```

### مشكلة: فشل تثبيت المكتبات
```bash
# تحديث pip
python -m pip install --upgrade pip

# تثبيت مع تجاهل الكاش
pip install --no-cache-dir package_name

# تثبيت مع صلاحيات المستخدم
pip install --user package_name
```

---

## 🎯 الخلاصة

تم إكمال التثبيت بنجاح! الآن لديك:

- ✅ **Python مثبت** مع جميع المكتبات المطلوبة
- ✅ **بيئة افتراضية** معدة ومفعلة
- ✅ **هيكل المشروع** منظم ومرتب
- ✅ **ملفات التكوين** جاهزة للإعداد
- ✅ **نظام الأمان** مهيأ للحماية

---

**آخر تحديث**: 2025-07-10  
**المرجع التالي**: [ST-002](002_configuration_guide.md)
