# ⌨️ معالج الأوامر - دليل شامل

**رقم التوثيق**: DP-003  
**التاريخ**: 2025-07-10  
**الإصدار**: 1.0.0  
**المطور**: صلاح الدين الدروبي  
**المرجع السابق**: [DP-002](002_buttons_processor.md)

---

## 📖 المحتويات

1. [نظرة عامة](#نظرة-عامة)
2. [أنواع الأوامر](#أنواع-الأوامر)
3. [خرائط الأوامر](#خرائط-الأوامر)
4. [الوظائف الرئيسية](#الوظائف-الرئيسية)
5. [أمثلة الاستخدام](#أمثلة-الاستخدام)

---

## 🎯 نظرة عامة

معالج الأوامر (`commands.py`) هو المسؤول عن معالجة جميع الأوامر النصية في النظام:

- **الأوامر الأساسية** (`/start`, `/help`)
- **الأوامر الإدارية** (`/admin`, `/stats`)
- **الأوامر المخصصة**
- **دعم متعدد اللغات**

---

## ⌨️ أنواع الأوامر

### 1. **الأوامر الأساسية**

#### للمستخدمين العاديين:
```python
basic_commands = {
    'ar': {
        "/start": "start",
        "/بداية": "start", 
        "/help": "help",
        "/مساعدة": "help",
        "/update": "update",
        "/تحديث": "update"
    },
    'en': {
        "/start": "start",
        "/help": "help", 
        "/update": "update"
    }
}
```

### 2. **الأوامر الإدارية**

#### لبوت الإدارة:
```python
admin_commands = {
    'ar': {
        "/admin": "admin_panel",
        "/إدارة": "admin_panel",
        "/users": "users_list", 
        "/مستخدمين": "users_list",
        "/stats": "statistics",
        "/إحصائيات": "statistics",
        "/broadcast": "broadcast",
        "/إذاعة": "broadcast",
        "/status": "bot_status",
        "/حالة": "bot_status",
        "/settings": "settings",
        "/إعدادات": "settings"
    }
}
```

### 3. **الأوامر المخصصة**

#### أوامر خاصة:
```python
custom_commands = {
    'ar': {
        "/exa": "ai_assistant",
        "/إكسا": "ai_assistant",
        "/works": "works",
        "/أعمال": "works",
        "/about": "about",
        "/نبذة": "about"
    }
}
```

---

## 🗺️ خرائط الأوامر

### الخريطة الموحدة:
```python
class CommandProcessor:
    def __init__(self):
        self.command_mapping = {
            'ar': {
                # الأوامر الأساسية
                "/start": {"type": "basic", "action": "start"},
                "/بداية": {"type": "basic", "action": "start"},
                "/help": {"type": "basic", "action": "help"},
                "/مساعدة": {"type": "basic", "action": "help"},
                
                # الأوامر الإدارية
                "/admin": {"type": "admin", "action": "admin_panel"},
                "/إدارة": {"type": "admin", "action": "admin_panel"},
                "/users": {"type": "admin", "action": "users_list"},
                "/مستخدمين": {"type": "admin", "action": "users_list"},
                
                # الأوامر المخصصة
                "/exa": {"type": "custom", "action": "ai_assistant"},
                "/إكسا": {"type": "custom", "action": "ai_assistant"}
            },
            'en': {
                # نفس الهيكل للإنجليزية
            }
        }
```

---

## ⚙️ الوظائف الرئيسية

### 1. **معالجة الأوامر**
```python
def process_command(self, command_text: str, language: str = 'ar') -> Dict[str, Any]:
    """معالجة الأوامر النصية"""
    # تنظيف النص
    clean_command = command_text.strip().lower()
    
    # البحث في خريطة الأوامر
    command_config = self.command_mapping.get(language, {}).get(clean_command)
    
    if command_config:
        return {
            "type": command_config["type"],
            "action": command_config["action"],
            "command": clean_command,
            "success": True
        }
    else:
        return {
            "type": "unknown",
            "action": "unknown_command",
            "success": False
        }
```

### 2. **التحقق من الأوامر**
```python
def is_command(self, text: str) -> bool:
    """التحقق من كون النص أمر"""
    return text.strip().startswith('/')

def is_valid_command(self, command: str, language: str = 'ar') -> bool:
    """التحقق من صحة الأمر"""
    return command in self.command_mapping.get(language, {})
```

### 3. **الحصول على معلومات الأمر**
```python
def get_command_info(self, command: str, language: str = 'ar') -> Dict[str, Any]:
    """الحصول على معلومات الأمر"""
    command_config = self.command_mapping.get(language, {}).get(command)
    
    if command_config:
        return {
            "command": command,
            "type": command_config["type"],
            "action": command_config["action"],
            "description": self.get_command_description(command, language)
        }
    else:
        return {"error": "Command not found"}
```

### 4. **الحصول على قائمة الأوامر**
```python
def get_available_commands(self, command_type: str = None, language: str = 'ar') -> List[str]:
    """الحصول على قائمة الأوامر المتاحة"""
    commands = self.command_mapping.get(language, {})
    
    if command_type:
        return [cmd for cmd, config in commands.items() 
                if config["type"] == command_type]
    else:
        return list(commands.keys())
```

---

## 💡 أمثلة الاستخدام

### مثال 1: معالجة أمر أساسي
```python
from data_processing.commands import CommandProcessor

processor = CommandProcessor()

# معالجة أمر /start
result = processor.process_command("/start", "ar")
print(result)
# Output: {"type": "basic", "action": "start", "success": True}
```

### مثال 2: معالجة أمر إداري
```python
# معالجة أمر /admin
result = processor.process_command("/admin", "ar")
print(result)
# Output: {"type": "admin", "action": "admin_panel", "success": True}
```

### مثال 3: التحقق من الأوامر
```python
# التحقق من كون النص أمر
is_cmd = processor.is_command("/help")
print(is_cmd)  # Output: True

# التحقق من صحة الأمر
is_valid = processor.is_valid_command("/help", "ar")
print(is_valid)  # Output: True
```

### مثال 4: الحصول على قائمة الأوامر
```python
# الحصول على جميع الأوامر
all_commands = processor.get_available_commands(language="ar")
print(all_commands)

# الحصول على الأوامر الإدارية فقط
admin_commands = processor.get_available_commands("admin", "ar")
print(admin_commands)
```

---

## 🔧 إضافة أوامر جديدة

### خطوات إضافة أمر جديد:

1. **إضافة الأمر للخريطة**:
```python
self.command_mapping['ar']["/أمر_جديد"] = {
    "type": "custom",
    "action": "new_action"
}
```

2. **إضافة وصف الأمر**:
```python
command_descriptions = {
    "/أمر_جديد": "وصف الأمر الجديد"
}
```

3. **إضافة المعالجة** (في البوت):
```python
elif command_result['action'] == 'new_action':
    await self.handle_new_command(update, context)
```

---

## 📊 إحصائيات الأوامر

- **عدد الأوامر الأساسية**: 6 أوامر
- **عدد الأوامر الإدارية**: 14 أمر
- **عدد الأوامر المخصصة**: 8 أوامر
- **اللغات المدعومة**: العربية والإنجليزية
- **إجمالي الأوامر**: 28+ أمر

---

## 🚨 ملاحظات مهمة

1. **حساسية الأحرف**: جميع الأوامر غير حساسة للأحرف الكبيرة/الصغيرة
2. **المسافات**: يتم تجاهل المسافات الزائدة
3. **الأمان**: الأوامر الإدارية محمية بنظام صلاحيات
4. **التوافق**: دعم كامل للغة العربية والإنجليزية
5. **الأخطاء**: معالجة شاملة للأوامر غير المعروفة

---

## 🔄 تدفق معالجة الأوامر

```
نص مدخل
    ↓
التحقق من كونه أمر (يبدأ بـ /)
    ↓
تنظيف النص وتحويله لأحرف صغيرة
    ↓
البحث في خريطة الأوامر
    ↓
إرجاع معلومات الأمر أو خطأ
    ↓
توجيه للمعالج المناسب في البوت
```

---

**آخر تحديث**: 2025-07-10  
**المرجع السابق**: [DP-002](002_buttons_processor.md)  
**المرجع التالي**: [DP-004](004_message_templates.md)
