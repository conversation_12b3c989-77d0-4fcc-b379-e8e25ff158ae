# 🔘 معالج الأزرار - دليل شامل

**رقم التوثيق**: DP-002  
**التاريخ**: 2025-07-10  
**الإصدار**: 1.0.0  
**المطور**: صلاح الدين الدروبي  
**المرجع السابق**: [DP-001](001_overview.md)

---

## 📖 المحتويات

1. [نظرة عامة](#نظرة-عامة)
2. [أنواع الأزرار](#أنواع-الأزرار)
3. [خرائط الأزرار](#خرائط-الأزرار)
4. [الوظائف الرئيسية](#الوظائف-الرئيسية)
5. [أمثلة الاستخدام](#أمثلة-الاستخدام)

---

## 🎯 نظرة عامة

معالج الأزرار (`buttons.py`) هو المسؤول عن معالجة جميع أنواع الأزرار في النظام:

- **الأزرار السفلية** (Reply Keyboard)
- **الأزرار المضمنة** (Inline Keyboard)
- **أزرار التنقل**
- **أزرار الإجراءات**

---

## 🔘 أنواع الأزرار

### 1. **الأزرار السفلية** (Reply Buttons)

#### الأزرار الأساسية:
```python
{
    "📍 موقعي": "location",
    "👨‍💼 نبذة عني": "about", 
    "💼 أعمالي": "works",
    "🎯 خبرتي": "experience",
    "🏆 إنجازاتي": "achievements",
    "🤖 إكسا الذكي": "ai_assistant",
    "❓ المساعدة": "help"
}
```

### 2. **الأزرار المضمنة** (Inline Buttons)

#### أزرار التنقل:
```python
{
    "menu_main": {"action": "menu_navigation", "target": "main_menu"},
    "back_main": {"action": "navigation", "target": "main_menu"}
}
```

#### أزرار إكسا الذكي:
```python
{
    "exa_normal": {"action": "ai_mode", "target": "normal"},
    "exa_pro": {"action": "ai_mode", "target": "pro"},
    "end_exa_normal": {"action": "end_conversation", "target": "normal"},
    "end_exa_pro": {"action": "end_conversation", "target": "pro"}
}
```

#### أزرار الملفات:
```python
{
    "view_works_pdf": {"action": "send_pdf", "target": "works"}
}
```

---

## 🗺️ خرائط الأزرار

### خريطة الأزرار العربية:
```python
button_mapping = {
    'ar': {
        "📍 موقعي": "location",
        "👨‍💼 نبذة عني": "about", 
        "💼 أعمالي": "works",
        "🎯 خبرتي": "experience",
        "🏆 إنجازاتي": "achievements",
        "🤖 إكسا الذكي": "ai_assistant",
        "❓ المساعدة": "help"
    }
}
```

### خريطة الأزرار الإنجليزية:
```python
button_mapping = {
    'en': {
        "📍 My Location": "location",
        "👨‍💼 About Me": "about",
        "💼 My Works": "works", 
        "🎯 My Experience": "experience",
        "🏆 My Achievements": "achievements",
        "🤖 Exa AI": "ai_assistant",
        "❓ Help": "help"
    }
}
```

---

## ⚙️ الوظائف الرئيسية

### 1. **معالجة الأزرار السفلية**
```python
def process_reply_button(self, button_text: str, language: str = 'ar') -> Dict[str, Any]:
    """معالجة الأزرار السفلية"""
    button_type = self.button_mapping.get(language, {}).get(button_text)
    
    if button_type:
        return {
            "action": button_type,
            "button_text": button_text,
            "language": language,
            "success": True
        }
    else:
        return {
            "action": "unknown",
            "success": False
        }
```

### 2. **معالجة الأزرار المضمنة**
```python
def process_inline_button(self, callback_data: str, language: str = 'ar') -> Dict[str, Any]:
    """معالجة الأزرار المضمنة"""
    button_config = self.inline_button_mapping.get(language, {}).get(callback_data)
    
    if button_config:
        return {
            "action": button_config["action"],
            "target": button_config["target"],
            "callback_data": callback_data,
            "success": True
        }
    else:
        return {
            "action": "unknown",
            "success": False
        }
```

### 3. **التحقق من نوع الزر**
```python
def is_button_text(self, text: str, language: str = 'ar') -> bool:
    """التحقق من كون النص زر سفلي"""
    return text in self.button_mapping.get(language, {})
```

### 4. **الحصول على نوع الزر**
```python
def get_button_type(self, button_text: str, language: str = 'ar') -> str:
    """الحصول على نوع الزر"""
    return self.button_mapping.get(language, {}).get(button_text, "unknown")
```

---

## 💡 أمثلة الاستخدام

### مثال 1: معالجة زر سفلي
```python
from data_processing.buttons import ButtonProcessor

processor = ButtonProcessor()

# معالجة زر "نبذة عني"
result = processor.process_reply_button("👨‍💼 نبذة عني", "ar")
print(result)
# Output: {"action": "about", "success": True, ...}
```

### مثال 2: معالجة زر مضمن
```python
# معالجة زر إكسا الذكي برو
result = processor.process_inline_button("exa_pro", "ar")
print(result)
# Output: {"action": "ai_mode", "target": "pro", "success": True}
```

### مثال 3: التحقق من الزر
```python
# التحقق من كون النص زر
is_button = processor.is_button_text("🤖 إكسا الذكي", "ar")
print(is_button)  # Output: True

# الحصول على نوع الزر
button_type = processor.get_button_type("🤖 إكسا الذكي", "ar")
print(button_type)  # Output: "ai_assistant"
```

---

## 🔧 إضافة أزرار جديدة

### خطوات إضافة زر سفلي جديد:

1. **إضافة الزر للخريطة**:
```python
button_mapping = {
    'ar': {
        # الأزرار الموجودة...
        "🆕 زر جديد": "new_button_type"
    }
}
```

2. **إضافة قالب الرسالة** (في `templates.py`):
```python
"new_button_type": {
    'ar': "محتوى الرسالة الجديدة...",
    'en': "New message content..."
}
```

3. **إضافة المعالجة** (في البوت):
```python
elif message_result['type'] == 'new_button_type':
    await self.handle_new_button(update, context)
```

---

## 📊 إحصائيات الأزرار

- **عدد الأزرار السفلية**: 7 أزرار
- **عدد الأزرار المضمنة**: 15+ زر
- **اللغات المدعومة**: العربية والإنجليزية
- **أنواع الإجراءات**: 6 أنواع رئيسية

---

## 🚨 ملاحظات مهمة

1. **ترتيب الأزرار**: يجب الحفاظ على ترتيب منطقي
2. **الرموز التعبيرية**: استخدام رموز واضحة ومفهومة
3. **التوافق**: جميع الأزرار تعمل على البوت الرئيسي وبوت الإدارة
4. **الأخطاء**: معالجة شاملة للأخطاء والحالات الاستثنائية

---

**آخر تحديث**: 2025-07-10  
**المرجع السابق**: [DP-001](001_overview.md)  
**المرجع التالي**: [DP-003](003_commands_processor.md)
