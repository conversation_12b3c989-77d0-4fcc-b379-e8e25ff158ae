# 🐍 Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 🔧 PyInstaller
*.manifest
*.spec

# 🧪 Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# 🌐 Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# 🔑 Configuration files with sensitive data
main/core/config.py
admin/core/admin_config.py
shared/config/secrets.json
*.key
*.pem

# 📊 Logs
*.log
logs/*.log
main/logs/*.log
admin/logs/*.log

# 💾 Database files
*.db
*.sqlite
*.sqlite3

# 📁 Temporary files
*.tmp
*.temp
temp/
tmp/

# 🗂️ OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 💼 IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# 📋 Reports and temporary files
*_report.md
*_report.json
test_*.py.bak
cleanup_*.py

# 🔄 Backup files
*.bak
*.backup
backups/temp/
