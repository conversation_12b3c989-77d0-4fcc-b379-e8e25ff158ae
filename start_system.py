#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🤖 نظام صلاح الدين الدروبي - ملف التشغيل الموحد
📱 البوت الرئيسي | 🛡️ بوت الإدارة والمراقبة
🚀 تشغيل مبسط وفعال للبوتين المترابطين
"""

import threading
import time
import sys
import os
import subprocess
from datetime import datetime

# إضافة مسار المكتبات المشتركة
sys.path.append(os.path.join(os.path.dirname(__file__), 'shared'))

# استيراد نظام السجلات الموحد
from utils.logging_config import get_system_logger, log_startup, log_shutdown

# الحصول على سجل النظام
logger = get_system_logger()

class SystemManager:
    """مدير النظام الموحد المبسط"""
    
    def __init__(self):
        self.main_bot_status = "غير مشغل"
        self.admin_bot_status = "غير مشغل"
        self.main_bot_process = None
        self.admin_bot_process = None
        self.start_time = datetime.now()
        self.running = True
        self.active_threads = []

    def run_main_bot(self):
        """تشغيل البوت الرئيسي"""
        try:
            logger.info("🚀︙بدء تشغيل البوت الرئيسي...")
            self.main_bot_status = "يتم التشغيل"

            # تشغيل البوت الرئيسي
            self.main_bot_process = subprocess.Popen([
                sys.executable, 'main/main.py'
            ], cwd=os.getcwd())

            self.main_bot_status = "يعمل"
            logger.info("✅︙البوت الرئيسي يعمل بنجاح")
            logger.info(f"🆔︙معرف العملية : {self.main_bot_process.pid}")

            # انتظار العملية
            self.main_bot_process.wait()
            
            if self.main_bot_process.returncode != 0:
                self.main_bot_status = "خطأ"
                logger.error(f"❌︙البوت الرئيسي توقف برمز خطأ : {self.main_bot_process.returncode}")
            else:
                self.main_bot_status = "متوقف"
                logger.info("ℹ️︙البوت الرئيسي توقف بشكل طبيعي")

        except Exception as e:
            self.main_bot_status = "خطأ"
            logger.error(f"❌︙فشل تشغيل البوت الرئيسي : {e}")
    
    def run_admin_bot(self):
        """تشغيل بوت الإدارة والمراقبة"""
        try:
            logger.info("🛡️︙بدء تشغيل بوت الإدارة والمراقبة...")
            self.admin_bot_status = "يتم التشغيل"

            # تشغيل بوت الإدارة
            self.admin_bot_process = subprocess.Popen([
                sys.executable, 'admin/admin.py'
            ], cwd=os.getcwd())

            self.admin_bot_status = "يعمل"
            logger.info("✅︙بوت الإدارة والمراقبة يعمل بنجاح")
            logger.info(f"🆔︙معرف العملية: {self.admin_bot_process.pid}")

            # انتظار العملية
            self.admin_bot_process.wait()
            
            if self.admin_bot_process.returncode != 0:
                self.admin_bot_status = "خطأ"
                logger.error(f"❌︙بوت الإدارة توقف برمز خطأ : {self.admin_bot_process.returncode}")
            else:
                self.admin_bot_status = "متوقف"
                logger.info("ℹ️︙بوت الإدارة توقف بشكل طبيعي")

        except Exception as e:
            self.admin_bot_status = "خطأ"
            logger.error(f"❌︙فشل تشغيل بوت الإدارة والمراقبة : {e}")

    def start_token_monitoring(self):
        """بدء نظام مراقبة التوكن"""
        try:
            logger.info("🤖 بدء تشغيل نظام مراقبة التوكن...")

            # استيراد نظام الإشعارات
            from ai_billing.admin_notifications import AdminNotificationManager

            # إنشاء مدير الإشعارات
            notification_manager = AdminNotificationManager()

            # بدء المراقبة في الخلفية
            notification_manager.start_background_monitoring()

            logger.info("✅ تم بدء نظام مراقبة التوكن بنجاح")

        except Exception as e:
            logger.error(f"❌ خطأ في بدء نظام مراقبة التوكن: {e}")
            logger.warning("⚠️ سيتم تشغيل النظام بدون مراقبة التوكن")

    def monitor_status(self):
        """مراقبة حالة البوتات والخيوط"""
        while self.running:
            try:
                # فحص حالة العمليات
                main_running = self.main_bot_process and self.main_bot_process.poll() is None
                admin_running = self.admin_bot_process and self.admin_bot_process.poll() is None

                # فحص حالة الخيوط
                active_count = 0
                for thread in self.active_threads:
                    if thread.is_alive():
                        active_count += 1
                    else:
                        logger.warning(f"⚠️ الخيط {thread.name} متوقف")

                # تسجيل معلومات الخيوط كل 5 دقائق
                if int(time.time()) % 300 == 0:  # كل 5 دقائق
                    logger.info(f"📊 الخيوط النشطة: {active_count}/{len(self.active_threads)}")
                    for i, thread in enumerate(self.active_threads, 1):
                        status = "نشط" if thread.is_alive() else "متوقف"
                        logger.info(f"   {i}. {thread.name}: {status}")

                if not main_running and self.main_bot_status == "يعمل":
                    self.main_bot_status = "متوقف"
                    logger.warning("⚠️ البوت الرئيسي توقف")

                if not admin_running and self.admin_bot_status == "يعمل":
                    self.admin_bot_status = "متوقف"
                    logger.warning("⚠️ بوت الإدارة توقف")

                # إذا توقف كلا البوتين، أوقف المراقبة
                if not main_running and not admin_running:
                    logger.info("ℹ️ كلا البوتين متوقف، إيقاف المراقبة...")
                    self.running = False
                    break

                time.sleep(30)  # انتظار 30 ثانية

            except KeyboardInterrupt:
                logger.info("🛑 تم طلب إيقاف النظام...")
                self.running = False
                break
            except Exception as e:
                logger.error(f"خطأ في المراقبة: {e}")
                time.sleep(10)

    def stop_bots(self):
        """إيقاف البوتات"""
        logger.info("🛑︙بدء إيقاف البوتات...")
        
        # إيقاف البوت الرئيسي
        if self.main_bot_process and self.main_bot_process.poll() is None:
            try:
                self.main_bot_process.terminate()
                self.main_bot_process.wait(timeout=10)
                logger.info("✅︙تم إيقاف البوت الرئيسي")
            except:
                try:
                    self.main_bot_process.kill()
                    logger.info("💀︙تم الإيقاف القسري للبوت الرئيسي")
                except:
                    pass
        
        # إيقاف بوت الإدارة
        if self.admin_bot_process and self.admin_bot_process.poll() is None:
            try:
                self.admin_bot_process.terminate()
                self.admin_bot_process.wait(timeout=10)
                logger.info("✅︙تم إيقاف بوت الإدارة")
            except:
                try:
                    self.admin_bot_process.kill()
                    logger.info("💀︙تم الإيقاف القسري لبوت الإدارة")
                except:
                    pass

    def start_system(self):
        """تشغيل النظام الموحد"""
        try:
            # تسجيل بدء التشغيل
            log_startup("نظام صلاح الدين الدروبي الموحد")
            
            print("=" * 70)
            print("🤖 نظام صلاح الدين الدروبي - التشغيل الموحد")
            print("📱 البوت الرئيسي | 🛡️ بوت الإدارة والمراقبة")
            print("🔗 البوتان مترابطان ويعملان معاً")
            print("=" * 70)
            
            # تشغيل البوت الرئيسي في خيط منفصل
            main_thread = threading.Thread(target=self.run_main_bot, daemon=True, name="MainBot")
            main_thread.start()
            self.active_threads.append(main_thread)
            logger.info("🧵 تم بدء خيط البوت الرئيسي")
            
            # انتظار قليل قبل تشغيل بوت الإدارة
            time.sleep(3)
            
            # تشغيل بوت الإدارة في خيط منفصل
            admin_thread = threading.Thread(target=self.run_admin_bot, daemon=True, name="AdminBot")
            admin_thread.start()
            self.active_threads.append(admin_thread)
            logger.info("🧵 تم بدء خيط بوت الإدارة")
            
            # انتظار قليل للتأكد من بدء التشغيل
            time.sleep(5)
            
            logger.info("🚀 تم بدء النظام الموحد بنجاح!")

            # بدء نظام مراقبة التوكن
            self.start_token_monitoring()

            # بدء مراقبة الحالة
            self.monitor_status()
            
        except KeyboardInterrupt:
            logger.info("🛑 تم طلب إيقاف النظام...")
        except Exception as e:
            logger.error(f"❌ خطأ في تشغيل النظام: {e}")
        finally:
            # إيقاف البوتات
            self.stop_bots()
            
            # تسجيل إيقاف النظام
            log_shutdown("نظام صلاح الدين الدروبي الموحد")
            
            # رسالة إيقاف بسيطة
            print("\n👋 تم إيقاف نظام صلاح الدين الدروبي بنجاح")

def main():
    """الدالة الرئيسية"""
    try:
        system = SystemManager()
        system.start_system()
    except Exception as e:
        logger.error(f"خطأ في تشغيل النظام: {e}")
        print(f"❌ خطأ في تشغيل النظام: {e}")

if __name__ == "__main__":
    main()
