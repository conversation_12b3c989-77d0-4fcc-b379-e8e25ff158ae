#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت اختبار نظام SQLite الجديد
يتحقق من عمل جميع الوظائف بشكل صحيح
"""

import os
import sys
import time
import threading
from datetime import datetime

# إضافة مسار المجلد المشترك
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'shared'))

def test_basic_operations():
    """اختبار العمليات الأساسية"""
    print("🔧 اختبار العمليات الأساسية...")
    
    try:
        from database import db_api
        
        # اختبار إنشاء مستخدم
        test_user_data = {
            'user_id': 999999999,
            'username': 'test_user',
            'display_name': 'مستخدم تجريبي',
            'language_code': 'ar'
        }
        
        # حذف المستخدم التجريبي إذا كان موجوداً
        db_api.delete_user(999999999)
        
        # إنشاء مستخدم جديد
        success = db_api.create_user(test_user_data)
        assert success, "فشل في إنشاء المستخدم"
        
        # الحصول على المستخدم
        user = db_api.get_user(999999999)
        assert user is not None, "فشل في الحصول على المستخدم"
        assert user['username'] == 'test_user', "بيانات المستخدم غير صحيحة"
        
        # تحديث المستخدم
        update_success = db_api.update_user(999999999, {'display_name': 'مستخدم محدث'})
        assert update_success, "فشل في تحديث المستخدم"
        
        # التحقق من التحديث
        updated_user = db_api.get_user(999999999)
        assert updated_user['display_name'] == 'مستخدم محدث', "لم يتم تحديث البيانات"
        
        # حذف المستخدم التجريبي
        delete_success = db_api.delete_user(999999999)
        assert delete_success, "فشل في حذف المستخدم"
        
        print("✅ اختبار العمليات الأساسية نجح")
        return True
        
    except Exception as e:
        print(f"❌ فشل اختبار العمليات الأساسية: {e}")
        return False

def test_concurrent_operations():
    """اختبار العمليات المتزامنة"""
    print("🔄 اختبار العمليات المتزامنة...")
    
    try:
        from database import db_api
        import threading
        
        # إنشاء مستخدمين تجريبيين
        test_users = []
        for i in range(5):
            user_id = 888888880 + i
            user_data = {
                'user_id': user_id,
                'username': f'concurrent_user_{i}',
                'display_name': f'مستخدم متزامن {i}',
                'language_code': 'ar'
            }
            
            # حذف إذا كان موجوداً
            db_api.delete_user(user_id)
            
            # إنشاء المستخدم
            db_api.create_user(user_data)
            test_users.append(user_id)
        
        # دالة تحديث متزامنة
        def concurrent_update(user_id, thread_id):
            for i in range(10):
                db_api.update_user_activity(user_id)
                time.sleep(0.01)  # محاكاة عملية
        
        # تشغيل خيوط متعددة
        threads = []
        for i, user_id in enumerate(test_users):
            thread = threading.Thread(target=concurrent_update, args=(user_id, i))
            threads.append(thread)
            thread.start()
        
        # انتظار انتهاء جميع الخيوط
        for thread in threads:
            thread.join()
        
        # التحقق من النتائج
        for user_id in test_users:
            user = db_api.get_user(user_id)
            assert user is not None, f"المستخدم {user_id} مفقود"
            assert user['visit_count'] == 10, f"عدد الزيارات غير صحيح للمستخدم {user_id}"
        
        # تنظيف المستخدمين التجريبيين
        for user_id in test_users:
            db_api.delete_user(user_id)
        
        print("✅ اختبار العمليات المتزامنة نجح")
        return True
        
    except Exception as e:
        print(f"❌ فشل اختبار العمليات المتزامنة: {e}")
        return False

def test_wallet_operations():
    """اختبار عمليات المحافظ"""
    print("💰 اختبار عمليات المحافظ...")
    
    try:
        from database import db_api
        
        # إنشاء مستخدم تجريبي
        user_id = 777777777
        user_data = {
            'user_id': user_id,
            'username': 'wallet_test_user',
            'display_name': 'مستخدم اختبار المحفظة',
            'language_code': 'ar'
        }
        
        # حذف إذا كان موجوداً
        db_api.delete_user(user_id)
        db_api.create_user(user_data)
        
        # إنشاء محفظة
        wallet_data = {
            'wallet_number': '9090000001',
            'user_id': user_id,
            'user_name': 'مستخدم اختبار المحفظة',
            'username': '@wallet_test_user',
            'balance': 100.0,
            'currency': 'إكسا',
            'status': 'active'
        }
        
        # حذف المحفظة إذا كانت موجودة
        existing_wallet = db_api.get_wallet('9090000001')
        if existing_wallet:
            db_api.db_manager.delete('wallets', 'wallet_number = ?', ('9090000001',))
        
        # إنشاء المحفظة
        success = db_api.create_wallet(wallet_data)
        assert success, "فشل في إنشاء المحفظة"
        
        # الحصول على المحفظة
        wallet = db_api.get_wallet('9090000001')
        assert wallet is not None, "فشل في الحصول على المحفظة"
        assert wallet['balance'] == 100.0, "رصيد المحفظة غير صحيح"
        
        # تحديث الرصيد
        update_success = db_api.update_wallet_balance('9090000001', 150.0, 'deposit')
        assert update_success, "فشل في تحديث رصيد المحفظة"
        
        # التحقق من التحديث
        updated_wallet = db_api.get_wallet('9090000001')
        assert updated_wallet['balance'] == 150.0, "لم يتم تحديث الرصيد"
        
        # الحصول على المعاملات
        transactions = db_api.get_wallet_transactions('9090000001')
        assert len(transactions) > 0, "لا توجد معاملات"
        
        # تنظيف البيانات التجريبية
        db_api.db_manager.delete('wallets', 'wallet_number = ?', ('9090000001',))
        db_api.delete_user(user_id)
        
        print("✅ اختبار عمليات المحافظ نجح")
        return True
        
    except Exception as e:
        print(f"❌ فشل اختبار عمليات المحافظ: {e}")
        return False

def test_backup_system():
    """اختبار نظام النسخ الاحتياطي"""
    print("💾 اختبار نظام النسخ الاحتياطي...")
    
    try:
        from database import db_api
        
        # إنشاء نسخة احتياطية تجريبية
        backup_path = db_api.create_backup("test")
        assert os.path.exists(backup_path), "فشل في إنشاء النسخة الاحتياطية"
        
        # عرض النسخ الاحتياطية
        backups = db_api.list_backups()
        assert len(backups) > 0, "لا توجد نسخ احتياطية"
        
        # البحث عن النسخة التجريبية
        test_backup = None
        for backup in backups:
            if 'test' in backup['name']:
                test_backup = backup
                break
        
        assert test_backup is not None, "لم يتم العثور على النسخة التجريبية"
        
        # حذف النسخة التجريبية
        if os.path.exists(backup_path):
            os.remove(backup_path)
        
        print("✅ اختبار نظام النسخ الاحتياطي نجح")
        return True
        
    except Exception as e:
        print(f"❌ فشل اختبار نظام النسخ الاحتياطي: {e}")
        return False

def test_statistics():
    """اختبار الإحصائيات"""
    print("📊 اختبار الإحصائيات...")
    
    try:
        from database import db_api
        
        # الحصول على إحصائيات النظام
        stats = db_api.get_system_statistics()
        
        # التحقق من وجود البيانات المطلوبة
        assert 'database' in stats, "إحصائيات قاعدة البيانات مفقودة"
        assert 'users' in stats, "إحصائيات المستخدمين مفقودة"
        assert 'wallets' in stats, "إحصائيات المحافظ مفقودة"
        
        # التحقق من البيانات الرقمية
        assert isinstance(stats['users']['total'], int), "إجمالي المستخدمين ليس رقماً"
        assert isinstance(stats['wallets']['total'], int), "إجمالي المحافظ ليس رقماً"
        
        print(f"📈 إجمالي المستخدمين: {stats['users']['total']}")
        print(f"💰 إجمالي المحافظ: {stats['wallets']['total']}")
        print(f"💾 حجم قاعدة البيانات: {stats['database']['database_size']} بايت")
        
        print("✅ اختبار الإحصائيات نجح")
        return True
        
    except Exception as e:
        print(f"❌ فشل اختبار الإحصائيات: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("="*60)
    print("🧪 اختبار نظام SQLite الجديد")
    print("="*60)
    print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    tests = [
        ("العمليات الأساسية", test_basic_operations),
        ("العمليات المتزامنة", test_concurrent_operations),
        ("عمليات المحافظ", test_wallet_operations),
        ("نظام النسخ الاحتياطي", test_backup_system),
        ("الإحصائيات", test_statistics)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n🔍 تشغيل اختبار: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            failed += 1
    
    print("\n" + "="*60)
    print("📋 نتائج الاختبار")
    print("="*60)
    print(f"✅ نجح: {passed}")
    print(f"❌ فشل: {failed}")
    print(f"📊 المجموع: {passed + failed}")
    
    if failed == 0:
        print("\n🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.")
        return True
    else:
        print(f"\n⚠️ فشل {failed} اختبار. يرجى مراجعة الأخطاء.")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع في الاختبار: {e}")
        sys.exit(1)
