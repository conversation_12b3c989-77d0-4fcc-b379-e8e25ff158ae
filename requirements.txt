# 🤖 نظام صلاح الدين الدروبي المتقدم - المتطلبات الموحدة
# آخر تحديث: 2025-08-04
# الإصدار: 3.1.0
#
# ملف متطلبات موحد لجميع أجزاء النظام:
# - البوت الرئيسي (main/)
# - بوت الإدارة (admin/)
# - المكونات المشتركة (shared/)

# ===== المكتبات الأساسية =====
python-telegram-bot==20.7
openai==1.98.0
httpx==0.28.1

# ===== معالجة الملفات والوسائط =====
reportlab==4.0.7
qrcode[pil]==7.4.2
Pillow==10.1.0
PyMuPDF==1.23.26

# ===== دعم اللغة العربية =====
arabic-reshaper==3.0.0
python-bidi==0.4.2

# ===== مكتبات OpenAI الداعمة =====
jiter==0.9.0
pydantic==2.11.1
typing-extensions==4.14.1
tqdm==4.67.1
distro==1.9.0
anyio==4.9.0
sniffio==1.3.1

# ===== مكتبات الشبكة والأمان =====
requests==2.32.3
cryptography==44.0.0
certifi==2025.6.15
httpcore==1.0.9
h11==0.16.0

# ===== مكتبات قاعدة البيانات والنسخ الاحتياطي =====
schedule==1.2.0

# ===== مكتبات التحقق والتطوير =====
annotated-types==0.7.0
pydantic-core==2.33.0
typing-inspection==0.4.0
colorama==0.4.6
idna==3.10

# ===== ملاحظات التثبيت =====
# للتثبيت السريع:
# pip install -r requirements.txt
#
# للتحديث:
# pip install --upgrade -r requirements.txt
#
# للتحقق من الإصدارات:
# pip list
#
# ===== ملاحظات مهمة =====
# هذا الملف موحد لجميع أجزاء النظام
# لا تقم بإنشاء ملفات requirements منفصلة في المجلدات الفرعية
# جميع المكتبات المطلوبة للنظام الكامل موجودة هنا
