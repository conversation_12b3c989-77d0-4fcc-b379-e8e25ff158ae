#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الأرقام الموحد
مدير مركزي لتوليد وإدارة جميع أنواع الأرقام في النظام
"""

import random
import json
import os
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from enum import Enum

class IDType(Enum):
    """أنواع الأرقام المدعومة مقسمة حسب البوت"""

    # ═══════════════════════════════════════════════════════════════
    # 🔧 البوت الإداري والمراقبة (Admin & Monitoring Bot)
    # ═══════════════════════════════════════════════════════════════

    # إشعارات المراقبة والإدارة (1XX)
    ADMIN_MONITORING_GENERAL = "101"    # إشعارات المراقبة العامة
    ADMIN_SYSTEM_INTERNAL = "102"       # إشعارات النظام الداخلية
    ADMIN_SECURITY_ALERTS = "103"       # إشعارات الأمان والحماية
    ADMIN_USER_ACTIONS = "104"          # مراقبة أنشطة المستخدمين
    ADMIN_BOT_STATUS = "105"            # حالة وأداء البوت

    # العمليات الإدارية (8XX)
    ADMIN_BALANCE_ADD = "808"           # عمليات إضافة الرصيد من الإدارة
    ADMIN_USER_MANAGEMENT = "809"       # عمليات إدارة المستخدمين
    ADMIN_LOAN_MANAGEMENT = "810"       # عمليات إدارة السلف من الإدارة
    ADMIN_WALLET_MANAGEMENT = "811"     # عمليات إدارة المحافظ
    ADMIN_SYSTEM_COMMANDS = "812"       # أوامر النظام الإدارية

    # فواتير إدارية (3XX)
    ADMIN_LOAN_ADD_INVOICE = "303"      # فواتير إضافة رصيد السلف
    ADMIN_LOAN_DEDUCT_INVOICE = "304"   # فواتير خصم رصيد السلف
    ADMIN_BALANCE_INVOICE = "306"       # فواتير إضافة الرصيد العادي
    ADMIN_MANUAL_INVOICE = "307"        # فواتير العمليات اليدوية

    # عمليات النظام الإدارية (7XX)
    ADMIN_BACKUP_OPERATIONS = "701"     # عمليات النسخ الاحتياطي
    ADMIN_MAINTENANCE = "702"           # عمليات الصيانة والتنظيف
    ADMIN_REPORTS = "703"               # تقارير النظام والإحصائيات
    ADMIN_DATABASE_OPS = "704"          # عمليات قاعدة البيانات

    # ═══════════════════════════════════════════════════════════════
    # 👥 البوت الرئيسي (Main Bot)
    # ═══════════════════════════════════════════════════════════════

    # إشعارات المستخدمين (2XX)
    MAIN_WALLET_CREATION = "202"        # إشعارات إنشاء المحافظ
    MAIN_LOAN_OPERATIONS = "203"        # إشعارات عمليات السلف
    MAIN_BALANCE_OPERATIONS = "204"     # إشعارات عمليات الرصيد
    MAIN_USER_NOTIFICATIONS = "205"     # إشعارات عامة للمستخدمين
    MAIN_TRANSACTION_ALERTS = "206"     # تنبيهات المعاملات

    # عمليات المستخدمين (4XX)
    MAIN_AI_REQUESTS = "401"            # طلبات الذكاء الاصطناعي
    MAIN_LOAN_REQUESTS = "402"          # طلبات السلف من المستخدمين
    MAIN_BALANCE_REQUESTS = "403"       # طلبات إضافة الرصيد
    MAIN_WALLET_OPERATIONS = "404"      # عمليات المحفظة العامة
    MAIN_USER_INTERACTIONS = "405"      # تفاعلات المستخدمين

    # فواتير المستخدمين (5XX)
    MAIN_AUTO_LOAN_INVOICE = "501"      # فواتير السداد التلقائي للسلف
    MAIN_USER_INVOICE = "502"           # فواتير المستخدمين العامة
    MAIN_TRANSACTION_INVOICE = "503"    # فواتير المعاملات
    MAIN_SERVICE_INVOICE = "504"        # فواتير الخدمات

    # أرقام المحافظ والحسابات (9XX)
    MAIN_WALLET_NUMBERS = "909"         # أرقام المحافظ الرئيسية
    MAIN_TEMP_WALLETS = "910"           # محافظ مؤقتة للاختبار
    MAIN_USER_SESSIONS = "911"          # جلسات المستخدمين
    MAIN_TRANSACTION_IDS = "912"        # معرفات المعاملات

class UnifiedIDManager:
    """مدير الأرقام الموحد"""
    
    def __init__(self, data_dir: str = "shared/database"):
        """
        تهيئة مدير الأرقام الموحد

        Args:
            data_dir: مجلد حفظ البيانات
        """
        self.data_dir = data_dir
        self.ids_file = os.path.join(data_dir, "unified_ids.json")
        self.ensure_data_dir()
        self.used_ids = self.load_used_ids()
        
        # وصف أنواع الأرقام مقسمة حسب البوت
        self.id_descriptions = {
            # البوت الإداري والمراقبة
            IDType.ADMIN_MONITORING_GENERAL: "إشعارات المراقبة الإدارية",
            IDType.ADMIN_SYSTEM_INTERNAL: "تنبيهات النظام الإدارية",
            IDType.ADMIN_SECURITY_ALERTS: "إشعارات الأمان الإدارية",
            IDType.ADMIN_USER_ACTIONS: "مراقبة أنشطة المستخدمين",
            IDType.ADMIN_BOT_STATUS: "حالة وأداء البوت",
            IDType.ADMIN_BALANCE_ADD: "عمليات إضافة الرصيد (إدارية)",
            IDType.ADMIN_USER_MANAGEMENT: "عمليات إدارة المستخدمين",
            IDType.ADMIN_LOAN_MANAGEMENT: "عمليات إدارة السلف (إدارية)",
            IDType.ADMIN_WALLET_MANAGEMENT: "عمليات إدارة المحافظ",
            IDType.ADMIN_SYSTEM_COMMANDS: "أوامر النظام الإدارية",
            IDType.ADMIN_LOAN_ADD_INVOICE: "فواتير إضافة رصيد السلف (إدارية)",
            IDType.ADMIN_LOAN_DEDUCT_INVOICE: "فواتير خصم رصيد السلف (إدارية)",
            IDType.ADMIN_BALANCE_INVOICE: "فواتير إضافة الرصيد العادي (إدارية)",
            IDType.ADMIN_MANUAL_INVOICE: "فواتير العمليات اليدوية",
            IDType.ADMIN_BACKUP_OPERATIONS: "عمليات النسخ الاحتياطي",
            IDType.ADMIN_MAINTENANCE: "عمليات الصيانة والتنظيف",
            IDType.ADMIN_DATABASE_OPS: "عمليات قاعدة البيانات",
            IDType.ADMIN_REPORTS: "تقارير النظام والإحصائيات",

            # البوت الرئيسي
            IDType.MAIN_WALLET_CREATION: "إشعارات إنشاء المحافظ",
            IDType.MAIN_LOAN_OPERATIONS: "إشعارات عمليات السلف",
            IDType.MAIN_BALANCE_OPERATIONS: "إشعارات عمليات الرصيد",
            IDType.MAIN_USER_NOTIFICATIONS: "إشعارات عامة للمستخدمين",
            IDType.MAIN_TRANSACTION_ALERTS: "تنبيهات المعاملات",
            IDType.MAIN_AI_REQUESTS: "طلبات الذكاء الاصطناعي",
            IDType.MAIN_LOAN_REQUESTS: "طلبات السلف من المستخدمين",
            IDType.MAIN_BALANCE_REQUESTS: "طلبات إضافة الرصيد",
            IDType.MAIN_WALLET_OPERATIONS: "عمليات المحفظة العامة",
            IDType.MAIN_USER_INTERACTIONS: "تفاعلات المستخدمين",
            IDType.MAIN_AUTO_LOAN_INVOICE: "فواتير السداد التلقائي للسلف",
            IDType.MAIN_USER_INVOICE: "فواتير المستخدمين العامة",
            IDType.MAIN_TRANSACTION_INVOICE: "فواتير المعاملات",
            IDType.MAIN_SERVICE_INVOICE: "فواتير الخدمات",
            IDType.MAIN_WALLET_NUMBERS: "أرقام المحافظ الرئيسية",
            IDType.MAIN_TEMP_WALLETS: "محافظ مؤقتة للاختبار",
            IDType.MAIN_USER_SESSIONS: "جلسات المستخدمين",
            IDType.MAIN_TRANSACTION_IDS: "معرفات المعاملات"
        }
    
    def ensure_data_dir(self):
        """التأكد من وجود مجلد البيانات"""
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
    
    def load_used_ids(self) -> Dict[str, List[str]]:
        """تحميل الأرقام المستخدمة"""
        try:
            if os.path.exists(self.ids_file):
                with open(self.ids_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            print(f"خطأ في تحميل الأرقام المستخدمة: {e}")
            return {}
    
    def save_used_ids(self):
        """حفظ الأرقام المستخدمة"""
        try:
            with open(self.ids_file, 'w', encoding='utf-8') as f:
                json.dump(self.used_ids, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ الأرقام المستخدمة: {e}")
    
    def generate_id(self, id_type: IDType, max_attempts: int = 100) -> str:
        """
        توليد رقم جديد من النوع المحدد
        
        Args:
            id_type: نوع الرقم
            max_attempts: عدد المحاولات القصوى لتجنب التكرار
            
        Returns:
            str: الرقم المولد (10 خانات)
        """
        prefix = id_type.value
        
        # التأكد من وجود قائمة للنوع
        if prefix not in self.used_ids:
            self.used_ids[prefix] = []
        
        for attempt in range(max_attempts):
            # توليد 7 أرقام عشوائية
            random_part = random.randint(1000000, 9999999)
            new_id = f"{prefix}{random_part}"
            
            # التحقق من عدم التكرار
            if new_id not in self.used_ids[prefix]:
                # إضافة الرقم للقائمة المستخدمة
                self.used_ids[prefix].append(new_id)
                
                # حفظ البيانات
                self.save_used_ids()
                
                return new_id
        
        # في حالة فشل التوليد بعد المحاولات القصوى
        raise Exception(f"فشل في توليد رقم فريد من النوع {prefix} بعد {max_attempts} محاولة")
    
    def is_id_used(self, id_number: str) -> bool:
        """
        التحقق من استخدام رقم معين
        
        Args:
            id_number: الرقم المراد التحقق منه
            
        Returns:
            bool: True إذا كان مستخدم، False إذا لم يكن مستخدم
        """
        if len(id_number) != 10:
            return False
        
        prefix = id_number[:3]
        return prefix in self.used_ids and id_number in self.used_ids[prefix]
    
    def get_id_type_info(self, id_number: str) -> Optional[Dict[str, str]]:
        """
        الحصول على معلومات نوع الرقم
        
        Args:
            id_number: الرقم المراد تحليله
            
        Returns:
            dict: معلومات النوع أو None إذا كان غير معروف
        """
        if len(id_number) != 10:
            return None
        
        prefix = id_number[:3]
        
        # البحث عن النوع المطابق
        for id_type in IDType:
            if id_type.value == prefix:
                return {
                    'prefix': prefix,
                    'type': id_type.name,
                    'description': self.id_descriptions.get(id_type, "غير محدد"),
                    'category': self._get_category(prefix)
                }
        
        return None
    
    def _get_category(self, prefix: str) -> str:
        """تحديد فئة الرقم حسب البادئة مع التمييز بين البوتين"""
        if prefix.startswith('1'):
            return "🔧 البوت الإداري - إشعارات ومراقبة"
        elif prefix.startswith('2'):
            return "👤 البوت الرئيسي - إشعارات المستخدمين"
        elif prefix.startswith('3'):
            return "🔧 البوت الإداري - فواتير إدارية"
        elif prefix.startswith('4'):
            return "👤 البوت الرئيسي - عمليات المستخدمين"
        elif prefix.startswith('5'):
            return "👤 البوت الرئيسي - فواتير المستخدمين"
        elif prefix.startswith('7'):
            return "🔧 البوت الإداري - صيانة ونسخ احتياطي"
        elif prefix.startswith('8'):
            return "🔧 البوت الإداري - عمليات إدارية"
        elif prefix.startswith('9'):
            return "👤 البوت الرئيسي - المحافظ والمعاملات"
        else:
            return "❓ غير محدد"
    
    def get_statistics(self) -> Dict[str, any]:
        """الحصول على إحصائيات الأرقام المستخدمة"""
        stats = {
            'total_ids': 0,
            'by_type': {},
            'by_category': {}
        }
        
        category_counts = {}
        
        for prefix, ids in self.used_ids.items():
            count = len(ids)
            stats['total_ids'] += count
            
            # إحصائيات حسب النوع
            id_type_info = self.get_id_type_info(f"{prefix}0000000")
            if id_type_info:
                stats['by_type'][id_type_info['description']] = count
                
                # إحصائيات حسب الفئة
                category = id_type_info['category']
                category_counts[category] = category_counts.get(category, 0) + count
        
        stats['by_category'] = category_counts
        return stats
    
    def cleanup_old_ids(self, keep_count: int = 1000) -> int:
        """
        تنظيف الأرقام القديمة للحفاظ على الأداء
        
        Args:
            keep_count: عدد الأرقام المراد الاحتفاظ بها لكل نوع
            
        Returns:
            int: عدد الأرقام المحذوفة
        """
        deleted_count = 0
        
        for prefix in self.used_ids:
            ids_list = self.used_ids[prefix]
            if len(ids_list) > keep_count:
                # الاحتفاظ بآخر keep_count رقم فقط
                self.used_ids[prefix] = ids_list[-keep_count:]
                deleted_count += len(ids_list) - keep_count
        
        if deleted_count > 0:
            self.save_used_ids()
        
        return deleted_count
    
    def export_ids_report(self, output_file: str = None) -> str:
        """
        تصدير تقرير بجميع الأرقام المستخدمة
        
        Args:
            output_file: مسار ملف التصدير (اختياري)
            
        Returns:
            str: محتوى التقرير
        """
        stats = self.get_statistics()
        
        report = f"""# تقرير الأرقام الموحدة
تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## الإحصائيات العامة
- إجمالي الأرقام: {stats['total_ids']}

## الإحصائيات حسب الفئة
"""
        
        for category, count in stats['by_category'].items():
            report += f"- {category}: {count} رقم\n"
        
        report += "\n## الإحصائيات حسب النوع\n"
        
        for type_desc, count in stats['by_type'].items():
            report += f"- {type_desc}: {count} رقم\n"
        
        report += "\n## تفاصيل الأرقام\n"
        
        for prefix, ids in self.used_ids.items():
            id_type_info = self.get_id_type_info(f"{prefix}0000000")
            type_name = id_type_info['description'] if id_type_info else f"نوع {prefix}"
            
            report += f"\n### {type_name} ({prefix})\n"
            report += f"العدد: {len(ids)}\n"
            
            if ids:
                report += f"أول رقم: {min(ids)}\n"
                report += f"آخر رقم: {max(ids)}\n"
        
        # حفظ التقرير إذا تم تحديد مسار
        if output_file:
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(report)
            except Exception as e:
                print(f"خطأ في حفظ التقرير: {e}")
        
        return report

# مثيل عام للاستخدام
unified_id_manager = UnifiedIDManager()

# ═══════════════════════════════════════════════════════════════
# دوال مساعدة للبوت الإداري والمراقبة
# ═══════════════════════════════════════════════════════════════

def generate_admin_monitoring_id() -> str:
    """توليد رقم إشعار مراقبة إدارية"""
    return unified_id_manager.generate_id(IDType.ADMIN_MONITORING_GENERAL)

def generate_admin_system_alert_id() -> str:
    """توليد رقم تنبيه نظام إداري"""
    return unified_id_manager.generate_id(IDType.ADMIN_SYSTEM_INTERNAL)

def generate_admin_security_alert_id() -> str:
    """توليد رقم إشعار أمان إداري"""
    return unified_id_manager.generate_id(IDType.ADMIN_SECURITY_ALERTS)

def generate_admin_loan_add_invoice_id() -> str:
    """توليد رقم فاتورة إضافة رصيد سلف (إدارية)"""
    return unified_id_manager.generate_id(IDType.ADMIN_LOAN_ADD_INVOICE)

def generate_admin_loan_deduct_invoice_id() -> str:
    """توليد رقم فاتورة خصم رصيد سلف (إدارية)"""
    return unified_id_manager.generate_id(IDType.ADMIN_LOAN_DEDUCT_INVOICE)

def generate_admin_balance_add_id() -> str:
    """توليد رقم عملية إضافة رصيد (إدارية)"""
    return unified_id_manager.generate_id(IDType.ADMIN_BALANCE_ADD)

def generate_admin_user_management_id() -> str:
    """توليد رقم عملية إدارة مستخدمين"""
    return unified_id_manager.generate_id(IDType.ADMIN_USER_MANAGEMENT)

def generate_admin_loan_management_id() -> str:
    """توليد رقم عملية إدارة سلف (إدارية)"""
    return unified_id_manager.generate_id(IDType.ADMIN_LOAN_MANAGEMENT)

def generate_admin_report_id() -> str:
    """توليد رقم تقرير إداري"""
    return unified_id_manager.generate_id(IDType.ADMIN_REPORTS)

def generate_admin_backup_id() -> str:
    """توليد رقم عملية نسخ احتياطي"""
    return unified_id_manager.generate_id(IDType.ADMIN_BACKUP_OPERATIONS)

# ═══════════════════════════════════════════════════════════════
# دوال مساعدة للبوت الرئيسي
# ═══════════════════════════════════════════════════════════════

def generate_user_wallet_creation_id() -> str:
    """توليد رقم إشعار إنشاء محفظة (مستخدم)"""
    return unified_id_manager.generate_id(IDType.MAIN_WALLET_CREATION)

def generate_user_loan_operation_id() -> str:
    """توليد رقم إشعار عملية سلف (مستخدم)"""
    return unified_id_manager.generate_id(IDType.MAIN_LOAN_OPERATIONS)

def generate_user_balance_operation_id() -> str:
    """توليد رقم إشعار عملية رصيد (مستخدم)"""
    return unified_id_manager.generate_id(IDType.MAIN_BALANCE_OPERATIONS)

def generate_user_loan_invoice_id() -> str:
    """توليد رقم فاتورة سلف (مستخدم)"""
    return unified_id_manager.generate_id(IDType.MAIN_LOAN_REQUESTS)

def generate_user_balance_invoice_id() -> str:
    """توليد رقم فاتورة رصيد (مستخدم)"""
    return unified_id_manager.generate_id(IDType.MAIN_BALANCE_REQUESTS)

def generate_user_exa_ai_id() -> str:
    """توليد رقم استعلام Exa AI عادي"""
    return unified_id_manager.generate_id(IDType.MAIN_AI_REQUESTS)

def generate_user_exa_ai_pro_id() -> str:
    """توليد رقم استعلام Exa AI Pro"""
    return unified_id_manager.generate_id(IDType.MAIN_AI_REQUESTS)

def generate_user_auto_loan_payment_id() -> str:
    """توليد رقم سداد تلقائي للسلف"""
    return unified_id_manager.generate_id(IDType.MAIN_AUTO_LOAN_INVOICE)

# ═══════════════════════════════════════════════════════════════
# دوال مساعدة مشتركة
# ═══════════════════════════════════════════════════════════════

def generate_wallet_number() -> str:
    """توليد رقم محفظة"""
    return unified_id_manager.generate_id(IDType.MAIN_WALLET_NUMBERS)

def generate_temp_wallet_number() -> str:
    """توليد رقم محفظة مؤقتة"""
    return unified_id_manager.generate_id(IDType.MAIN_TEMP_WALLETS)

# ═══════════════════════════════════════════════════════════════
# دوال للتوافق مع النظام الحالي (Backward Compatibility)
# ═══════════════════════════════════════════════════════════════

def generate_monitoring_id() -> str:
    """توليد رقم إشعار مراقبة (للتوافق مع النظام الحالي)"""
    return generate_admin_monitoring_id()

def generate_wallet_creation_id() -> str:
    """توليد رقم إشعار إنشاء محفظة (للتوافق مع النظام الحالي)"""
    return generate_user_wallet_creation_id()

def generate_loan_operation_id() -> str:
    """توليد رقم إشعار عملية سلف (للتوافق مع النظام الحالي)"""
    return generate_user_loan_operation_id()

def generate_loan_add_invoice_id() -> str:
    """توليد رقم فاتورة إضافة رصيد سلف (للتوافق مع النظام الحالي)"""
    return generate_admin_loan_add_invoice_id()

def generate_loan_deduct_invoice_id() -> str:
    """توليد رقم فاتورة خصم رصيد سلف (للتوافق مع النظام الحالي)"""
    return generate_admin_loan_deduct_invoice_id()

def generate_balance_add_id() -> str:
    """توليد رقم عملية إضافة رصيد (للتوافق مع النظام الحالي)"""
    return generate_admin_balance_add_id()

def get_id_info(id_number: str) -> Optional[Dict[str, str]]:
    """الحصول على معلومات رقم معين"""
    return unified_id_manager.get_id_type_info(id_number)

# مثال على الاستخدام
if __name__ == "__main__":
    # اختبار النظام
    print("🧪 اختبار نظام الترقيم الموحد")

    # توليد أرقام مختلفة
    monitoring_id = generate_monitoring_id()
    wallet_id = generate_wallet_number()
    invoice_id = generate_loan_add_invoice_id()

    print(f"📊 رقم مراقبة: {monitoring_id}")
    print(f"💳 رقم محفظة: {wallet_id}")
    print(f"🧾 رقم فاتورة: {invoice_id}")

    # تحليل الأرقام
    for test_id in [monitoring_id, wallet_id, invoice_id]:
        info = get_id_info(test_id)
        if info:
            print(f"🔍 {test_id}: {info['description']} ({info['category']})")

    # إحصائيات
    stats = unified_id_manager.get_statistics()
    print(f"📈 إجمالي الأرقام: {stats['total_ids']}")

    # تقرير
    report = unified_id_manager.export_ids_report()
    print("📋 تقرير الأرقام:")
    print(report[:500] + "..." if len(report) > 500 else report)
