#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عرض بيانات المستخدمين المحفوظة
"""

import json
import os
from datetime import datetime

def load_users_data():
    """تحميل بيانات المستخدمين"""
    users_file = "users_data.json"
    try:
        if os.path.exists(users_file):
            with open(users_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            print("❌ ملف البيانات غير موجود")
            return {}
    except Exception as e:
        print(f"❌ خطأ في تحميل البيانات: {e}")
        return {}

def display_users_data():
    """عرض بيانات المستخدمين بشكل منظم"""
    users_data = load_users_data()
    
    if not users_data:
        print("📭 لا توجد بيانات مستخدمين محفوظة")
        return
    
    print(f"\n{'='*80}")
    print(f"📊 بيانات مستخدمي البوت - إجمالي: {len(users_data)} مستخدم")
    print(f"{'='*80}")
    
    # ترتيب المستخدمين حسب تاريخ أول دخول
    sorted_users = sorted(
        users_data.items(), 
        key=lambda x: x[1].get('تاريخ أول دخول', ''), 
        reverse=True
    )
    
    for i, (user_id, user_info) in enumerate(sorted_users, 1):
        user_id_display = user_info.get('أيدي المستخدم', user_info.get('ID', user_id))
        username_display = user_info.get('اسم المستخدم', 'غير محدد')
        user_handle = user_info.get('معرف المستخدم', 'غير محدد')
        print(f"\n{i}. 👤 المستخدم رقم {i}")
        print(f"   📝 اسم المستخدم: {username_display}")
        print(f"   @ معرف المستخدم: @{user_handle}")
        print(f"   🆔 أيدي المستخدم: {user_id_display}")
        print(f"   🌍 اللغة: {user_info.get('اللغة', 'غير محدد')}")
        print(f"   📅 أول دخول: {user_info.get('تاريخ أول دخول', 'غير محدد')}")
        print(f"   🕐 آخر نشاط: {user_info.get('آخر نشاط', 'غير محدد')}")
        print(f"   📈 عدد الزيارات: {user_info.get('عدد الزيارات', 1)}")
        print(f"   {'-'*50}")

def export_to_text():
    """تصدير البيانات إلى ملف نصي"""
    users_data = load_users_data()
    
    if not users_data:
        print("❌ لا توجد بيانات للتصدير")
        return
    
    filename = f"users_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"بيانات مستخدمي البوت - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"إجمالي المستخدمين: {len(users_data)}\n")
            f.write("="*80 + "\n\n")
            
            # ترتيب المستخدمين
            sorted_users = sorted(
                users_data.items(), 
                key=lambda x: x[1].get('تاريخ أول دخول', ''), 
                reverse=True
            )
            
            for i, (user_id, user_info) in enumerate(sorted_users, 1):
                user_id_display = user_info.get('أيدي المستخدم', user_info.get('ID', user_id))
                username_display = user_info.get('اسم المستخدم', 'غير محدد')
                user_handle = user_info.get('معرف المستخدم', 'غير محدد')
                f.write(f"{i}. المستخدم رقم {i}\n")
                f.write(f"   اسم المستخدم: {username_display}\n")
                f.write(f"   معرف المستخدم: @{user_handle}\n")
                f.write(f"   أيدي المستخدم: {user_id_display}\n")
                f.write(f"   اللغة: {user_info.get('اللغة', 'غير محدد')}\n")
                f.write(f"   أول دخول: {user_info.get('تاريخ أول دخول', 'غير محدد')}\n")
                f.write(f"   آخر نشاط: {user_info.get('آخر نشاط', 'غير محدد')}\n")
                f.write(f"   عدد الزيارات: {user_info.get('عدد الزيارات', 1)}\n")
                f.write("-"*50 + "\n\n")
        
        print(f"✅ تم تصدير البيانات إلى: {filename}")
        
    except Exception as e:
        print(f"❌ خطأ في التصدير: {e}")

def get_statistics():
    """عرض إحصائيات سريعة"""
    users_data = load_users_data()
    
    if not users_data:
        print("❌ لا توجد بيانات للإحصائيات")
        return
    
    total_users = len(users_data)
    total_visits = sum(user.get('عدد الزيارات', 1) for user in users_data.values())
    
    # إحصائيات اللغات
    languages = {}
    for user in users_data.values():
        lang = user.get('اللغة', 'غير محدد')
        languages[lang] = languages.get(lang, 0) + 1
    
    # المستخدمون النشطون (زاروا أكثر من مرة)
    active_users = sum(1 for user in users_data.values() if user.get('عدد الزيارات', 1) > 1)
    
    print(f"\n{'='*50}")
    print("📊 إحصائيات سريعة")
    print(f"{'='*50}")
    print(f"👥 إجمالي المستخدمين: {total_users}")
    print(f"📈 إجمالي الزيارات: {total_visits}")
    print(f"🔄 المستخدمون النشطون: {active_users}")
    print(f"📊 متوسط الزيارات: {total_visits/total_users:.1f}")
    
    print(f"\n🌍 توزيع اللغات:")
    for lang, count in sorted(languages.items(), key=lambda x: x[1], reverse=True):
        percentage = (count/total_users)*100
        print(f"   {lang}: {count} ({percentage:.1f}%)")

def main():
    """القائمة الرئيسية"""
    while True:
        print(f"\n{'='*50}")
        print("📊 عارض بيانات المستخدمين")
        print(f"{'='*50}")
        print("1. عرض جميع المستخدمين")
        print("2. عرض الإحصائيات")
        print("3. تصدير البيانات إلى ملف نصي")
        print("4. خروج")
        
        choice = input("\nاختر رقم الخيار: ").strip()
        
        if choice == "1":
            display_users_data()
        elif choice == "2":
            get_statistics()
        elif choice == "3":
            export_to_text()
        elif choice == "4":
            print("👋 وداعاً!")
            break
        else:
            print("❌ خيار غير صحيح، حاول مرة أخرى")
        
        input("\nاضغط Enter للمتابعة...")

if __name__ == "__main__":
    main()
