#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة للحصول على معرف المحادثة (Chat ID)
استخدم هذا الملف لمعرفة معرف المحادثة الخاص بك
"""

import logging
from telegram import Update
from telegram.ext import Application, CommandHandler, MessageHandler, filters, ContextTypes
from config import BOT_TOKEN

# إعداد التسجيل
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

async def get_chat_id(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """الحصول على معرف المحادثة"""
    user = update.effective_user
    chat = update.effective_chat
    
    message = f"""
🆔 **معلومات المحادثة**

👤 **اسمك**: {user.full_name}
🆔 **معرف المستخدم**: `{user.id}`
💬 **معرف المحادثة**: `{chat.id}`
📱 **نوع المحادثة**: {chat.type}

📋 **لتفعيل المراقبة:**
1. انسخ معرف المحادثة: `{chat.id}`
2. افتح ملف `config.py`
3. استبدل `YOUR_CHAT_ID_HERE` بالمعرف المنسوخ
4. احفظ الملف وأعد تشغيل البوت

مثال:
```
ADMIN_CHAT_ID = "{chat.id}"
```
    """
    
    await update.message.reply_text(
        message,
        parse_mode='Markdown'
    )
    
    # طباعة المعلومات في وحدة التحكم أيضاً
    print(f"\n{'='*50}")
    print(f"معرف المحادثة الخاص بك: {chat.id}")
    print(f"اسم المستخدم: {user.full_name}")
    print(f"معرف المستخدم: {user.id}")
    print(f"{'='*50}\n")

def main():
    """تشغيل بوت الحصول على معرف المحادثة"""
    print("🤖 بوت الحصول على معرف المحادثة يعمل الآن...")
    print("💡 أرسل أي رسالة للبوت للحصول على معرف المحادثة الخاص بك")
    print("🛑 اضغط Ctrl+C لإيقاف البوت")
    
    application = Application.builder().token(BOT_TOKEN).build()
    
    # إضافة معالجات
    application.add_handler(CommandHandler("start", get_chat_id))
    application.add_handler(MessageHandler(filters.TEXT, get_chat_id))
    
    # تشغيل البوت
    application.run_polling(allowed_updates=Update.ALL_TYPES)

if __name__ == "__main__":
    main()
