#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام السجلات الموحد
يدير جميع سجلات النظام بطريقة منظمة ومركزية
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from pathlib import Path

class LoggingConfig:
    """إعداد نظام السجلات الموحد"""
    
    def __init__(self):
        self.logs_dir = Path("logs")
        self.logs_dir.mkdir(exist_ok=True)
        
        # إنشاء ملفات السجلات
        self.system_log = self.logs_dir / "system.log"
        self.admin_log = self.logs_dir / "admin.log"
        self.main_log = self.logs_dir / "main.log"
        
        # إعداد التنسيق
        self.log_format = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # إعداد التنسيق المفصل
        self.detailed_format = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(funcName)s() - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
    
    def setup_system_logger(self):
        """إعداد سجل النظام الموحد"""
        logger = logging.getLogger('system')
        logger.setLevel(logging.INFO)
        
        # تجنب إضافة معالجات متكررة
        if logger.handlers:
            return logger
        
        # معالج الملف مع دوران
        file_handler = logging.handlers.RotatingFileHandler(
            self.system_log,
            maxBytes=10*1024*1024,  # 10 MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setFormatter(self.detailed_format)
        file_handler.setLevel(logging.INFO)
        
        # معالج وحدة التحكم
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(self.log_format)
        console_handler.setLevel(logging.INFO)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    def setup_admin_logger(self):
        """إعداد سجل بوت الإدارة والمراقبة"""
        logger = logging.getLogger('admin')
        logger.setLevel(logging.INFO)
        
        if logger.handlers:
            return logger
        
        # معالج الملف مع دوران
        file_handler = logging.handlers.RotatingFileHandler(
            self.admin_log,
            maxBytes=5*1024*1024,  # 5 MB
            backupCount=3,
            encoding='utf-8'
        )
        file_handler.setFormatter(self.detailed_format)
        file_handler.setLevel(logging.INFO)
        
        # معالج وحدة التحكم للأخطاء فقط
        console_handler = logging.StreamHandler(sys.stderr)
        console_handler.setFormatter(self.log_format)
        console_handler.setLevel(logging.ERROR)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    def setup_main_logger(self):
        """إعداد سجل البوت الرئيسي"""
        logger = logging.getLogger('main')
        logger.setLevel(logging.INFO)
        
        if logger.handlers:
            return logger
        
        # معالج الملف مع دوران
        file_handler = logging.handlers.RotatingFileHandler(
            self.main_log,
            maxBytes=5*1024*1024,  # 5 MB
            backupCount=3,
            encoding='utf-8'
        )
        file_handler.setFormatter(self.detailed_format)
        file_handler.setLevel(logging.INFO)
        
        # معالج وحدة التحكم للأخطاء فقط
        console_handler = logging.StreamHandler(sys.stderr)
        console_handler.setFormatter(self.log_format)
        console_handler.setLevel(logging.ERROR)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    def get_logger(self, logger_type: str):
        """الحصول على السجل المناسب"""
        if logger_type == 'system':
            return self.setup_system_logger()
        elif logger_type == 'admin':
            return self.setup_admin_logger()
        elif logger_type == 'main':
            return self.setup_main_logger()
        else:
            raise ValueError(f"نوع السجل غير مدعوم: {logger_type}")
    
    def log_startup(self, component: str):
        """تسجيل بدء تشغيل المكون"""
        logger = self.get_logger('system')
        logger.info(f"🚀 بدء تشغيل {component}")
        logger.info(f"📅 التاريخ والوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info(f"🐍 إصدار Python: {sys.version}")
        logger.info(f"📁 مجلد العمل: {os.getcwd()}")
        logger.info("=" * 50)
    
    def log_shutdown(self, component: str):
        """تسجيل إيقاف المكون"""
        logger = self.get_logger('system')
        logger.info("=" * 50)
        logger.info(f"🛑 إيقاف {component}")
        logger.info(f"📅 التاريخ والوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("=" * 50)
    
    def log_error(self, component: str, error: Exception, context: str = ""):
        """تسجيل الأخطاء"""
        logger = self.get_logger('system')
        logger.error(f"❌ خطأ في {component}: {str(error)}")
        if context:
            logger.error(f"📝 السياق: {context}")
        logger.error(f"🔍 نوع الخطأ: {type(error).__name__}")
        
        # تسجيل تفاصيل إضافية للأخطاء الحرجة
        import traceback
        logger.error(f"📋 تفاصيل الخطأ:\n{traceback.format_exc()}")
    
    def log_user_activity(self, component: str, user_id: int, action: str, details: dict = None):
        """تسجيل أنشطة المستخدمين"""
        if component == 'admin':
            logger = self.get_logger('admin')
        elif component == 'main':
            logger = self.get_logger('main')
        else:
            logger = self.get_logger('system')
        
        logger.info(f"👤 نشاط مستخدم - ID: {user_id}, الإجراء: {action}")
        if details:
            for key, value in details.items():
                logger.info(f"   📋 {key}: {value}")
    
    def log_system_event(self, event: str, details: dict = None):
        """تسجيل أحداث النظام"""
        logger = self.get_logger('system')
        logger.info(f"🔔 حدث نظام: {event}")
        if details:
            for key, value in details.items():
                logger.info(f"   📋 {key}: {value}")

# إنشاء مثيل عام للاستخدام
logging_config = LoggingConfig()

# دوال مساعدة للاستخدام السريع
def get_system_logger():
    """الحصول على سجل النظام"""
    return logging_config.get_logger('system')

def get_admin_logger():
    """الحصول على سجل الإدارة"""
    return logging_config.get_logger('admin')

def get_main_logger():
    """الحصول على سجل البوت الرئيسي"""
    return logging_config.get_logger('main')

def log_startup(component: str):
    """تسجيل بدء التشغيل"""
    logging_config.log_startup(component)

def log_shutdown(component: str):
    """تسجيل الإيقاف"""
    logging_config.log_shutdown(component)

def log_error(component: str, error: Exception, context: str = ""):
    """تسجيل الأخطاء"""
    logging_config.log_error(component, error, context)

def log_user_activity(component: str, user_id: int, action: str, details: dict = None):
    """تسجيل أنشطة المستخدمين"""
    logging_config.log_user_activity(component, user_id, action, details)

def log_system_event(event: str, details: dict = None):
    """تسجيل أحداث النظام"""
    logging_config.log_system_event(event, details)

if __name__ == "__main__":
    # اختبار نظام السجلات
    print("🧪 اختبار نظام السجلات...")
    
    # اختبار سجل النظام
    system_logger = get_system_logger()
    system_logger.info("اختبار سجل النظام")
    
    # اختبار سجل الإدارة
    admin_logger = get_admin_logger()
    admin_logger.info("اختبار سجل الإدارة")
    
    # اختبار سجل البوت الرئيسي
    main_logger = get_main_logger()
    main_logger.info("اختبار سجل البوت الرئيسي")
    
    # اختبار الدوال المساعدة
    log_startup("نظام الاختبار")
    log_user_activity("main", 123456, "ضغط زر", {"button": "نبذة عني"})
    log_system_event("اختبار مكتمل", {"status": "نجح"})
    log_shutdown("نظام الاختبار")
    
    print("✅ تم اختبار نظام السجلات بنجاح!")
