#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
منظم الملفات - إنشاء هيكل ملفات احترافي للبوت
"""

import os
import shutil
from pathlib import Path

class FileOrganizer:
    """منظم الملفات للبوت"""
    
    def __init__(self):
        self.base_dir = Path(".")
        self.structure = {
            "core": ["main.py", "telegram_bot.py", "config.py"],
            "features": ["ai_assistant.py"],
            "monitoring": ["bot_logger.py"],
            "utils": ["view_users.py", "get_chat_id.py"],
            "data": [],  # للملفات المُنشأة تلقائياً
            "docs": [],  # للتوثيق
            "backups": [],  # للنسخ الاحتياطية
            "logs": []  # لسجلات النظام
        }
    
    def create_directories(self):
        """إنشاء المجلدات المطلوبة"""
        directories = [
            "core",
            "features", 
            "monitoring",
            "utils",
            "data",
            "docs",
            "backups",
            "logs"
        ]
        
        for directory in directories:
            dir_path = self.base_dir / directory
            dir_path.mkdir(exist_ok=True)
            print(f"✅ تم إنشاء مجلد: {directory}")
    
    def organize_existing_files(self):
        """تنظيم الملفات الموجودة"""
        for category, files in self.structure.items():
            for file_name in files:
                source = self.base_dir / file_name
                if source.exists():
                    destination = self.base_dir / category / file_name
                    try:
                        shutil.move(str(source), str(destination))
                        print(f"✅ تم نقل {file_name} إلى {category}/")
                    except Exception as e:
                        print(f"❌ خطأ في نقل {file_name}: {e}")
    
    def create_init_files(self):
        """إنشاء ملفات __init__.py للمجلدات"""
        directories = ["core", "features", "monitoring", "utils"]
        
        for directory in directories:
            init_file = self.base_dir / directory / "__init__.py"
            if not init_file.exists():
                init_file.write_text("# -*- coding: utf-8 -*-\n")
                print(f"✅ تم إنشاء __init__.py في {directory}/")
    
    def create_gitignore(self):
        """إنشاء ملف .gitignore"""
        gitignore_content = """
# البيانات الحساسة
config.py
*.json
*.log

# ملفات النظام
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
*.so

# البيئة الافتراضية
venv/
env/
ENV/

# ملفات IDE
.vscode/
.idea/
*.swp
*.swo

# ملفات النسخ الاحتياطي
backups/
*.backup

# ملفات مؤقتة
temp/
tmp/
*.tmp

# سجلات النظام
logs/
*.log
        """
        
        gitignore_file = self.base_dir / ".gitignore"
        gitignore_file.write_text(gitignore_content.strip())
        print("✅ تم إنشاء ملف .gitignore")
    
    def organize_all(self):
        """تنظيم شامل للملفات"""
        print("🔧 بدء تنظيم الملفات...")
        
        self.create_directories()
        self.organize_existing_files()
        self.create_init_files()
        self.create_gitignore()
        
        print("✅ تم تنظيم جميع الملفات بنجاح!")
        print("\n📁 الهيكل الجديد:")
        print("├── core/           # الملفات الأساسية")
        print("├── features/       # الميزات والوظائف")
        print("├── monitoring/     # المراقبة والتتبع")
        print("├── utils/          # الأدوات المساعدة")
        print("├── data/           # البيانات المُنشأة")
        print("├── docs/           # التوثيق")
        print("├── backups/        # النسخ الاحتياطية")
        print("└── logs/           # سجلات النظام")

if __name__ == "__main__":
    organizer = FileOrganizer()
    organizer.organize_all()
