[{"session_id": "system_test_1753123456789", "user_id": "system_test", "user_name": "System Performance Test", "message_type": "comprehensive_performance_test", "total_duration": 2.8451234567890125, "timestamp": "2025-07-22T12:00:00.000000", "steps": [{"name": "session_start", "duration": 2e-06, "details": {"timestamp": "2025-07-22T12:00:00.000000", "user_id": "system_test", "user_name": "System Performance Test", "message_type": "comprehensive_performance_test"}}, {"name": "system_status_check", "duration": 0.095432, "details": {"main_bot_status": "يعمل", "admin_bot_status": "يعمل", "system_uptime_seconds": 3600.0, "active_threads": 2, "system_healthy": true}}, {"name": "telegram_api_connectivity", "duration": 1.234567, "details": {"test_endpoint": "https://api.telegram.org/bot", "timeout": 5.0, "status_code": 200, "response_time_ms": 1234.567, "api_accessible": true, "success": true}}, {"name": "message_processing_simulation", "duration": 0.456789, "details": {"simulation_type": "complete_message_flow", "simulated_steps": 8, "step_details": {"receive_message": 45.123, "parse_message": 32.456, "check_user_permissions": 18.789, "process_text": 67.234, "generate_response": 89.567, "format_response": 34.89, "send_response": 45.123, "log_interaction": 23.456}, "total_simulated_time": 356.638, "success": true}}, {"name": "ai_processing_simulation", "duration": 1.058234, "details": {"simulation_type": "ai_response_generation", "simulated_ai_model": "deepseek-chat", "simulated_tokens": 200, "processing_time_ms": 1058.234, "success": true}}, {"name": "session_end", "duration": 5e-06, "details": {"test_completed": true, "test_type": "comprehensive_system_performance_test", "all_tests_passed": true, "system_status": "healthy", "performance_rating": "excellent"}}]}, {"session_id": "591967813_1753123500000", "user_id": "591967813", "user_name": "صلاح الدين الدروبي", "message_type": "button_click", "total_duration": 0.56789, "timestamp": "2025-07-22T12:05:00.000000", "steps": [{"name": "session_start", "duration": 1e-06, "details": {"timestamp": "2025-07-22T12:05:00.000000", "user_id": "591967813", "user_name": "صلاح الدين الدروبي", "message_type": "button_click"}}, {"name": "button_processing", "duration": 0.123456, "details": {"button_name": "about", "button_text": "👨‍💼 نبذة عني", "user_status": "existing", "processing_type": "standard"}}, {"name": "media_loading", "duration": 0.234567, "details": {"media_type": "image", "file_path": "main/media/imagery/about.jpg", "file_size_kb": 245.6, "loading_success": true}}, {"name": "response_generation", "duration": 0.098765, "details": {"response_type": "text_with_image", "text_length": 156, "formatting_applied": true}}, {"name": "monitoring_notification", "duration": 0.111102, "details": {"notification_sent": true, "notification_id": "1012345678", "admin_notified": true}}, {"name": "session_end", "duration": 3e-06, "details": {"message_processed": true, "conversation_type": "button_interaction", "success": true}}]}, {"session_id": "591967813_1753123600000", "user_id": "591967813", "user_name": "صلاح الدين الدروبي", "message_type": "ai_conversation", "total_duration": 3.456789, "timestamp": "2025-07-22T12:10:00.000000", "steps": [{"name": "session_start", "duration": 1e-06, "details": {"timestamp": "2025-07-22T12:10:00.000000", "user_id": "591967813", "user_name": "صلاح الدين الدروبي", "message_type": "ai_conversation"}}, {"name": "message_received", "duration": 0.012345, "details": {"message_text": "ما هي أفضل طريقة لتعلم البرمجة؟", "message_length": 35, "language_detected": "ar"}}, {"name": "ai_api_call", "duration": 2.987654, "details": {"api_endpoint": "deepseek-chat", "tokens_sent": 45, "tokens_received": 234, "model_response_time": 2987.654, "success": true}}, {"name": "response_formatting", "duration": 0.234567, "details": {"original_length": 456, "formatted_length": 445, "formatting_applied": ["arabic_reshaping", "text_cleanup"], "success": true}}, {"name": "monitoring_notification", "duration": 0.222222, "details": {"notification_sent": true, "notification_id": "1012345679", "admin_notified": true, "ai_conversation_logged": true}}, {"name": "session_end", "duration": 4e-06, "details": {"message_processed": true, "conversation_type": "ai_interaction", "success": true}}]}]