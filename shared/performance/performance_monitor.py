#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام مراقبة الأداء المتقدم
يراقب أداء النظام ويحفظ البيانات لتحليل الأداء
"""

import os
import json
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import asyncio
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)

@dataclass
class PerformanceStep:
    """خطوة في قياس الأداء"""
    name: str
    duration: float
    details: Dict[str, Any]

@dataclass
class PerformanceSession:
    """جلسة قياس أداء كاملة"""
    session_id: str
    user_id: str
    user_name: str
    message_type: str
    total_duration: float
    timestamp: str
    steps: List[PerformanceStep]

class PerformanceMonitor:
    """مراقب الأداء المتقدم"""
    
    def __init__(self):
        """تهيئة مراقب الأداء"""
        self.base_dir = os.path.dirname(__file__)
        self.data_dir = os.path.join(self.base_dir, 'data')
        os.makedirs(self.data_dir, exist_ok=True)
        
        # ملفات البيانات
        self.daily_file_pattern = os.path.join(self.data_dir, 'performance_{date}.json')
        self.summary_file = os.path.join(self.data_dir, 'performance_summary.json')
        
        # جلسة الأداء الحالية
        self.current_session: Optional[Dict[str, Any]] = None
        self.session_start_time: Optional[float] = None
        self.steps: List[PerformanceStep] = []
        
        # إحصائيات الأداء
        self.performance_stats = {
            'total_sessions': 0,
            'average_response_time': 0.0,
            'fastest_response': float('inf'),
            'slowest_response': 0.0,
            'error_count': 0,
            'success_count': 0
        }
        
        self.load_performance_summary()
    
    def start_session(self, user_id: str, user_name: str, message_type: str) -> str:
        """بدء جلسة مراقبة أداء جديدة"""
        try:
            session_id = f"{user_id}_{int(time.time() * 1000)}"
            self.session_start_time = time.perf_counter()
            self.steps = []
            
            self.current_session = {
                'session_id': session_id,
                'user_id': user_id,
                'user_name': user_name,
                'message_type': message_type,
                'start_time': self.session_start_time,
                'timestamp': datetime.now().isoformat()
            }
            
            # تسجيل بداية الجلسة
            self.add_step('session_start', {
                'timestamp': datetime.now().isoformat(),
                'user_id': user_id,
                'user_name': user_name,
                'message_type': message_type
            })
            
            return session_id
            
        except Exception as e:
            logger.error(f"خطأ في بدء جلسة الأداء: {e}")
            return ""
    
    def add_step(self, step_name: str, details: Dict[str, Any] = None) -> None:
        """إضافة خطوة لقياس الأداء"""
        try:
            if not self.current_session:
                return
            
            step_start = time.perf_counter()
            
            # محاكاة وقت المعالجة (يمكن إزالتها في الإنتاج)
            if step_name != 'session_start' and step_name != 'session_end':
                time.sleep(0.001)  # 1ms للمحاكاة
            
            step_duration = time.perf_counter() - step_start
            
            step = PerformanceStep(
                name=step_name,
                duration=step_duration,
                details=details or {}
            )
            
            self.steps.append(step)
            
        except Exception as e:
            logger.error(f"خطأ في إضافة خطوة الأداء {step_name}: {e}")
    
    def end_session(self, success: bool = True) -> Optional[PerformanceSession]:
        """إنهاء جلسة مراقبة الأداء"""
        try:
            if not self.current_session or not self.session_start_time:
                return None
            
            # إضافة خطوة النهاية
            self.add_step('session_end', {
                'message_processed': success,
                'conversation_type': 'regular_message',
                'success': success
            })
            
            # حساب المدة الإجمالية
            total_duration = time.perf_counter() - self.session_start_time
            
            # إنشاء جلسة الأداء
            session = PerformanceSession(
                session_id=self.current_session['session_id'],
                user_id=self.current_session['user_id'],
                user_name=self.current_session['user_name'],
                message_type=self.current_session['message_type'],
                total_duration=total_duration,
                timestamp=self.current_session['timestamp'],
                steps=self.steps.copy()
            )
            
            # حفظ البيانات
            self.save_session_data(session)
            self.update_performance_stats(session, success)
            
            # إعادة تعيين الجلسة
            self.current_session = None
            self.session_start_time = None
            self.steps = []
            
            return session
            
        except Exception as e:
            logger.error(f"خطأ في إنهاء جلسة الأداء: {e}")
            return None
    
    def save_session_data(self, session: PerformanceSession) -> None:
        """حفظ بيانات جلسة الأداء"""
        try:
            # تحديد ملف اليوم
            today = datetime.now().strftime('%Y%m%d')
            daily_file = self.daily_file_pattern.format(date=today)
            
            # قراءة البيانات الحالية
            sessions_data = []
            if os.path.exists(daily_file):
                with open(daily_file, 'r', encoding='utf-8') as f:
                    sessions_data = json.load(f)
            
            # إضافة الجلسة الجديدة
            session_dict = asdict(session)
            sessions_data.append(session_dict)
            
            # حفظ البيانات
            with open(daily_file, 'w', encoding='utf-8') as f:
                json.dump(sessions_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"تم حفظ بيانات جلسة الأداء: {session.session_id}")
            
        except Exception as e:
            logger.error(f"خطأ في حفظ بيانات جلسة الأداء: {e}")
    
    def update_performance_stats(self, session: PerformanceSession, success: bool) -> None:
        """تحديث إحصائيات الأداء"""
        try:
            self.performance_stats['total_sessions'] += 1
            
            if success:
                self.performance_stats['success_count'] += 1
            else:
                self.performance_stats['error_count'] += 1
            
            # تحديث أوقات الاستجابة
            response_time = session.total_duration
            
            if response_time < self.performance_stats['fastest_response']:
                self.performance_stats['fastest_response'] = response_time
            
            if response_time > self.performance_stats['slowest_response']:
                self.performance_stats['slowest_response'] = response_time
            
            # حساب متوسط وقت الاستجابة
            total_sessions = self.performance_stats['total_sessions']
            current_avg = self.performance_stats['average_response_time']
            new_avg = ((current_avg * (total_sessions - 1)) + response_time) / total_sessions
            self.performance_stats['average_response_time'] = new_avg
            
            # حفظ الإحصائيات
            self.save_performance_summary()
            
        except Exception as e:
            logger.error(f"خطأ في تحديث إحصائيات الأداء: {e}")
    
    def load_performance_summary(self) -> None:
        """تحميل ملخص الأداء"""
        try:
            if os.path.exists(self.summary_file):
                with open(self.summary_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.performance_stats.update(data.get('stats', {}))
        except Exception as e:
            logger.error(f"خطأ في تحميل ملخص الأداء: {e}")
    
    def save_performance_summary(self) -> None:
        """حفظ ملخص الأداء"""
        try:
            summary_data = {
                'stats': self.performance_stats,
                'last_updated': datetime.now().isoformat(),
                'version': '1.0.0'
            }
            
            with open(self.summary_file, 'w', encoding='utf-8') as f:
                json.dump(summary_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"خطأ في حفظ ملخص الأداء: {e}")
    
    def get_daily_performance(self, date: str = None) -> Dict[str, Any]:
        """الحصول على أداء يوم محدد"""
        try:
            if not date:
                date = datetime.now().strftime('%Y%m%d')
            
            daily_file = self.daily_file_pattern.format(date=date)
            
            if not os.path.exists(daily_file):
                return {'sessions': [], 'summary': {}}
            
            with open(daily_file, 'r', encoding='utf-8') as f:
                sessions = json.load(f)
            
            # حساب ملخص اليوم
            if sessions:
                total_duration = sum(s['total_duration'] for s in sessions)
                avg_duration = total_duration / len(sessions)
                fastest = min(s['total_duration'] for s in sessions)
                slowest = max(s['total_duration'] for s in sessions)
            else:
                avg_duration = fastest = slowest = 0
            
            summary = {
                'total_sessions': len(sessions),
                'average_duration': avg_duration,
                'fastest_session': fastest,
                'slowest_session': slowest,
                'date': date
            }
            
            return {
                'sessions': sessions,
                'summary': summary
            }
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على أداء اليوم {date}: {e}")
            return {'sessions': [], 'summary': {}}
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الأداء العامة"""
        return self.performance_stats.copy()
    
    def cleanup_old_data(self, days_to_keep: int = 30) -> int:
        """تنظيف البيانات القديمة"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            deleted_count = 0
            
            for filename in os.listdir(self.data_dir):
                if filename.startswith('performance_') and filename.endswith('.json'):
                    try:
                        # استخراج التاريخ من اسم الملف
                        date_str = filename.replace('performance_', '').replace('.json', '')
                        file_date = datetime.strptime(date_str, '%Y%m%d')
                        
                        if file_date < cutoff_date:
                            file_path = os.path.join(self.data_dir, filename)
                            os.remove(file_path)
                            deleted_count += 1
                            
                    except ValueError:
                        # تجاهل الملفات التي لا تتبع نمط التاريخ
                        continue
            
            logger.info(f"تم حذف {deleted_count} ملف أداء قديم")
            return deleted_count
            
        except Exception as e:
            logger.error(f"خطأ في تنظيف بيانات الأداء القديمة: {e}")
            return 0

# مثيل عام لمراقب الأداء
performance_monitor = PerformanceMonitor()

# دوال مساعدة للاستخدام السهل
def start_performance_tracking(user_id: str, user_name: str, message_type: str) -> str:
    """بدء تتبع الأداء"""
    return performance_monitor.start_session(user_id, user_name, message_type)

def add_performance_step(step_name: str, details: Dict[str, Any] = None) -> None:
    """إضافة خطوة أداء"""
    performance_monitor.add_step(step_name, details)

def end_performance_tracking(success: bool = True) -> Optional[PerformanceSession]:
    """إنهاء تتبع الأداء"""
    return performance_monitor.end_session(success)

def get_performance_stats() -> Dict[str, Any]:
    """الحصول على إحصائيات الأداء"""
    return performance_monitor.get_performance_stats()
