# 📊 نظام مراقبة الأداء المتقدم

## 🎯 نظرة عامة

نظام شامل لمراقبة وتحليل أداء بوت صلاح الدين، يقوم بتتبع أوقات الاستجابة وتحليل الأداء وإنشاء تقارير مفصلة.

## 🏗️ هيكل النظام

```
shared/performance/
├── performance_monitor.py      # مراقب الأداء الرئيسي
├── data/                      # بيانات الأداء
│   ├── performance_YYYYMMDD.json  # بيانات يومية
│   └── performance_summary.json   # ملخص الأداء العام
└── README.md                  # هذا الملف
```

## 🚀 الاستخدام السريع

### 1. بدء تتبع الأداء
```python
from shared.performance.performance_monitor import start_performance_tracking

# بدء جلسة مراقبة
session_id = start_performance_tracking(
    user_id="591967813",
    user_name="صلاح الدين الدروبي", 
    message_type="button_click"
)
```

### 2. إضافة خطوات الأداء
```python
from shared.performance.performance_monitor import add_performance_step

# إضافة خطوة معالجة الزر
add_performance_step("button_processing", {
    "button_name": "about",
    "button_text": "👨‍💼 نبذة عني",
    "user_status": "existing"
})

# إضافة خطوة تحميل الوسائط
add_performance_step("media_loading", {
    "media_type": "image",
    "file_path": "main/media/imagery/about.jpg",
    "file_size_kb": 245.6
})
```

### 3. إنهاء التتبع
```python
from shared.performance.performance_monitor import end_performance_tracking

# إنهاء الجلسة
session = end_performance_tracking(success=True)
```

## 📈 أنواع القياسات

### 1. **قياسات الأزرار**
- وقت معالجة الزر
- تحميل الوسائط
- إرسال الإشعارات
- تنسيق الاستجابة

### 2. **قياسات المحادثات الذكية**
- استقبال الرسالة
- استدعاء API الذكاء الاصطناعي
- تنسيق الاستجابة
- إرسال الإشعارات

### 3. **قياسات النظام**
- فحص حالة النظام
- اختبار اتصال Telegram API
- محاكاة معالجة الرسائل
- تقييم صحة النظام

## 📊 البيانات المحفوظة

### بيانات الجلسة
```json
{
  "session_id": "591967813_1753123456789",
  "user_id": "591967813",
  "user_name": "صلاح الدين الدروبي",
  "message_type": "button_click",
  "total_duration": 0.567890,
  "timestamp": "2025-07-22T12:05:00.000000",
  "steps": [
    {
      "name": "session_start",
      "duration": 0.000001,
      "details": { ... }
    }
  ]
}
```

### إحصائيات الأداء
```json
{
  "total_sessions": 156,
  "average_response_time": 1.2345,
  "fastest_response": 0.234,
  "slowest_response": 4.567,
  "success_rate": 0.9807,
  "performance_by_type": { ... }
}
```

## 🔧 الميزات المتقدمة

### 1. **التنظيف التلقائي**
```python
from shared.performance.performance_monitor import performance_monitor

# حذف البيانات الأقدم من 30 يوم
deleted_count = performance_monitor.cleanup_old_data(days_to_keep=30)
```

### 2. **الحصول على إحصائيات يومية**
```python
# إحصائيات يوم محدد
daily_data = performance_monitor.get_daily_performance("20250722")
print(f"جلسات اليوم: {daily_data['summary']['total_sessions']}")
```

### 3. **مراقبة الأداء العامة**
```python
# الحصول على الإحصائيات العامة
stats = performance_monitor.get_performance_stats()
print(f"متوسط وقت الاستجابة: {stats['average_response_time']:.3f}s")
```

## 📋 أنواع الرسائل المدعومة

| النوع | الوصف | الخطوات المتوقعة |
|-------|--------|------------------|
| `button_click` | ضغط زر في البوت | معالجة الزر، تحميل الوسائط، الاستجابة |
| `ai_conversation` | محادثة مع الذكاء الاصطناعي | استقبال، API، تنسيق، إرسال |
| `text_message` | رسالة نصية عادية | معالجة النص، تحديد النوع، الاستجابة |
| `file_upload` | رفع ملف | استقبال الملف، التحليل، الحفظ |
| `system_test` | اختبار النظام | فحص شامل لجميع المكونات |

## 🎨 التكامل مع النظام

### في البوت الرئيسي
```python
# في معالج الأزرار
async def handle_button(update, context):
    user = update.effective_user
    
    # بدء تتبع الأداء
    session_id = start_performance_tracking(
        str(user.id), 
        user.first_name, 
        "button_click"
    )
    
    try:
        # معالجة الزر
        add_performance_step("button_processing", {...})
        
        # تحميل الوسائط
        add_performance_step("media_loading", {...})
        
        # إرسال الاستجابة
        add_performance_step("response_sent", {...})
        
        # إنهاء بنجاح
        end_performance_tracking(success=True)
        
    except Exception as e:
        # إنهاء مع خطأ
        end_performance_tracking(success=False)
        raise
```

### في نظام المراقبة
```python
# في بوت الإدارة - عرض إحصائيات الأداء
async def show_performance_stats(update, context):
    stats = get_performance_stats()
    
    message = f"""
📊 **إحصائيات الأداء**

🔢 إجمالي الجلسات: {stats['total_sessions']}
⏱️ متوسط وقت الاستجابة: {stats['average_response_time']:.3f}s
🚀 أسرع استجابة: {stats['fastest_response']:.3f}s
🐌 أبطأ استجابة: {stats['slowest_response']:.3f}s
✅ معدل النجاح: {stats['success_count']}/{stats['total_sessions']}
    """
    
    await update.message.reply_text(message)
```

## 🛡️ الأمان والخصوصية

### حماية البيانات
- **عدم حفظ المحتوى الحساس**: لا يتم حفظ نص الرسائل الكاملة
- **تشفير معرفات المستخدمين**: حماية هوية المستخدمين
- **تنظيف تلقائي**: حذف البيانات القديمة تلقائياً

### إعدادات الخصوصية
```python
# تعطيل تتبع الأداء للمستخدمين الحساسين
PERFORMANCE_TRACKING_ENABLED = True
EXCLUDE_USERS = ["admin_user_id"]  # مستخدمون مستثنون
```

## 📈 التحليلات والتقارير

### 1. **تقرير الأداء اليومي**
- عدد الجلسات
- متوسط وقت الاستجابة
- معدل النجاح
- أكثر الميزات استخداماً

### 2. **تحليل الاتجاهات**
- مقارنة الأداء عبر الأيام
- تحديد أوقات الذروة
- رصد التحسينات أو التراجع

### 3. **تنبيهات الأداء**
- تنبيه عند تجاوز حد معين لوقت الاستجابة
- إشعار عند ارتفاع معدل الأخطاء
- تقارير دورية للمديرين

## 🔧 الصيانة والتحسين

### مهام الصيانة الدورية
```bash
# تنظيف البيانات القديمة (شهرياً)
python -c "from shared.performance.performance_monitor import performance_monitor; performance_monitor.cleanup_old_data(30)"

# إنشاء تقرير شهري
python -c "from shared.performance.performance_monitor import performance_monitor; print(performance_monitor.get_performance_stats())"
```

### تحسين الأداء
- **ضغط البيانات**: ضغط الملفات القديمة
- **فهرسة البيانات**: تحسين البحث والاستعلام
- **تحسين الذاكرة**: تقليل استهلاك الذاكرة

## 📞 الدعم والمساعدة

للمساعدة في استخدام نظام مراقبة الأداء:
1. راجع الأمثلة في هذا الملف
2. تحقق من ملفات البيانات النموذجية
3. اتصل بفريق التطوير للدعم التقني

---

**📊 نظام مراقبة أداء متقدم لضمان أفضل تجربة مستخدم**
