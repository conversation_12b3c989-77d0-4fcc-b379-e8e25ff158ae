#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير التوكن والفوترة للذكاء الاصطناعي
يدير خصم التوكن مقابل استخدام إكسا الذكي برو
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional, Tuple
import sys

# إضافة مسار المكتبات المشتركة
current_dir = os.path.dirname(os.path.abspath(__file__))
shared_dir = os.path.dirname(current_dir)
sys.path.insert(0, shared_dir)

from database.wallet_manager import WalletManager

# إعداد نظام السجلات
logger = logging.getLogger(__name__)

class TokenManager:
    """مدير التوكن والفوترة للذكاء الاصطناعي"""
    
    def __init__(self):
        """تهيئة مدير التوكن"""
        self.wallet_manager = WalletManager()
        
        # معدل التحويل: 1 إكسا = 524,288 توكن
        self.TOKEN_RATE = 524288
        
        # الحد الأدنى للتحذير (0.5 إكسا)
        self.LOW_BALANCE_WARNING = 0.5
        
        # التكلفة التقديرية عند فشل الحساب (0.01 إكسا)
        self.FALLBACK_COST = 0.01
        
        # ملف سجل استهلاك التوكن
        self.usage_log_file = os.path.join(os.path.dirname(__file__), "..", "database", "token_usage_log.json")
        self._ensure_log_file_exists()
        
        logger.info("🤖 تم تهيئة مدير التوكن والفوترة")
    
    def _ensure_log_file_exists(self):
        """التأكد من وجود ملف سجل الاستهلاك"""
        if not os.path.exists(self.usage_log_file):
            with open(self.usage_log_file, 'w', encoding='utf-8') as f:
                json.dump([], f, ensure_ascii=False, indent=2)
            logger.info("📄 تم إنشاء ملف سجل استهلاك التوكن")
    
    def tokens_to_exa(self, tokens: int) -> float:
        """تحويل التوكن إلى إكسا"""
        return tokens / self.TOKEN_RATE
    
    def exa_to_tokens(self, exa: float) -> int:
        """تحويل الإكسا إلى توكن"""
        return int(exa * self.TOKEN_RATE)
    
    def check_balance_before_request(self, user_id: int) -> Tuple[bool, str, Dict]:
        """
        فحص الرصيد قبل إرسال طلب للذكاء الاصطناعي
        
        Args:
            user_id: معرف المستخدم
            
        Returns:
            Tuple[bool, str, Dict]: (يمكن الاستخدام, رسالة, معلومات إضافية)
        """
        try:
            # فحص إمكانية استخدام الذكاء الاصطناعي المتقدم
            can_use, message = self.wallet_manager.can_use_ai_pro(user_id)
            
            if not can_use:
                return False, message, {}
            
            # الحصول على معلومات المحفظة
            wallet_data = self.wallet_manager.get_user_wallet(user_id)
            if not wallet_data:
                return False, "لا توجد محفظة للمستخدم", {}
            
            balance = wallet_data.get("balance", 0.0)
            
            # تحذير عند انخفاض الرصيد
            warning_message = ""
            if balance < self.LOW_BALANCE_WARNING:
                if balance > 0:
                    warning_message = f"⚠️ تحذير: رصيدك منخفض ({balance:.3f} إكسا). يرجى شحن رصيدك قريباً."
                else:
                    warning_message = "⚠️ رصيدك صفر. سيتم إنشاء دين عند الاستخدام."
            
            return True, warning_message, {
                "balance": balance,
                "wallet_number": wallet_data["wallet_number"],
                "has_low_balance": balance < self.LOW_BALANCE_WARNING
            }
            
        except Exception as e:
            logger.error(f"خطأ في فحص الرصيد للمستخدم {user_id}: {e}")
            return False, f"خطأ في فحص الرصيد: {e}", {}
    
    def calculate_cost(self, input_tokens: int, output_tokens: int) -> Tuple[float, Dict]:
        """
        حساب تكلفة الاستخدام بناءً على التوكن المستهلك
        
        Args:
            input_tokens: توكن الطلب
            output_tokens: توكن الرد
            
        Returns:
            Tuple[float, Dict]: (التكلفة بالإكسا, تفاصيل الحساب)
        """
        try:
            total_tokens = input_tokens + output_tokens
            cost_in_exa = self.tokens_to_exa(total_tokens)
            
            calculation_details = {
                "input_tokens": input_tokens,
                "output_tokens": output_tokens,
                "total_tokens": total_tokens,
                "cost_in_exa": cost_in_exa,
                "token_rate": self.TOKEN_RATE,
                "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            logger.info(f"💰 حساب التكلفة: {total_tokens} توكن = {cost_in_exa:.6f} إكسا")
            return cost_in_exa, calculation_details
            
        except Exception as e:
            logger.error(f"خطأ في حساب التكلفة: {e}")
            # إرجاع التكلفة التقديرية
            return self.FALLBACK_COST, {
                "error": str(e),
                "fallback_cost": self.FALLBACK_COST,
                "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
    
    def process_ai_request_billing(self, user_id: int, input_tokens: int, output_tokens: int, 
                                 request_text: str = "", response_text: str = "") -> Tuple[bool, str, Dict]:
        """
        معالجة فوترة طلب الذكاء الاصطناعي
        
        Args:
            user_id: معرف المستخدم
            input_tokens: توكن الطلب
            output_tokens: توكن الرد
            request_text: نص الطلب (اختياري)
            response_text: نص الرد (اختياري)
            
        Returns:
            Tuple[bool, str, Dict]: (نجح الخصم, رسالة, بيانات الفوترة)
        """
        try:
            # حساب التكلفة
            cost, calculation_details = self.calculate_cost(input_tokens, output_tokens)
            
            # الحصول على معلومات المحفظة
            wallet_data = self.wallet_manager.get_user_wallet(user_id)
            if not wallet_data:
                return False, "لا توجد محفظة للمستخدم", {}
            
            wallet_number = wallet_data["wallet_number"]
            current_balance = wallet_data.get("balance", 0.0)
            
            billing_data = {
                "user_id": user_id,
                "wallet_number": wallet_number,
                "cost": cost,
                "calculation_details": calculation_details,
                "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            if current_balance >= cost:
                # خصم من الرصيد
                new_balance = current_balance - cost
                success = self.wallet_manager.update_wallet_balance(wallet_number, new_balance, "ai_usage")
                
                if success:
                    billing_data["payment_method"] = "balance_deduction"
                    billing_data["old_balance"] = current_balance
                    billing_data["new_balance"] = new_balance
                    
                    # تسجيل الاستخدام
                    self._log_token_usage(user_id, calculation_details, "balance_deduction")
                    
                    total_tokens = input_tokens + output_tokens
                    message = f"📊︙الطلب︙{input_tokens} توكن\n📊︙الرد︙{output_tokens} توكن\n📊︙الاجمالي︙{total_tokens} توكن\n💰︙تم خصم رصيد︙{cost:.6f} إكسا\n💳︙رصيدك الحالي︙{new_balance:.3f} إكسا"
                    return True, message, billing_data
                else:
                    return False, "فشل في خصم الرصيد", billing_data
            else:
                # إنشاء دين
                debt_success, debt_message, debt_data = self.wallet_manager.add_debt(
                    user_id, cost, f"استخدام إكسا الذكي برو ({input_tokens + output_tokens} توكن)"
                )
                
                if debt_success:
                    billing_data["payment_method"] = "debt_creation"
                    billing_data["debt_data"] = debt_data
                    
                    # تسجيل الاستخدام
                    self._log_token_usage(user_id, calculation_details, "debt_creation")
                    
                    total_tokens = input_tokens + output_tokens
                    message = f"📊︙الطلب︙{input_tokens} توكن\n📊︙الرد︙{output_tokens} توكن\n📊︙الاجمالي︙{total_tokens} توكن\n💰︙تم خصم رصيد︙{cost:.6f} إكسا (دين)\n⚠️︙تنبيه︙لديك دين قدره {debt_data.get('new_debt', 0):.3f} إكسا"
                    return True, message, billing_data
                else:
                    return False, f"فشل في إنشاء الدين: {debt_message}", billing_data
            
        except Exception as e:
            logger.error(f"خطأ في معالجة فوترة الذكاء الاصطناعي للمستخدم {user_id}: {e}")
            return False, f"خطأ في المعالجة: {e}", {}
    
    def _log_token_usage(self, user_id: int, calculation_details: Dict, payment_method: str):
        """تسجيل استهلاك التوكن في السجل"""
        try:
            # قراءة السجل الحالي
            with open(self.usage_log_file, 'r', encoding='utf-8') as f:
                usage_log = json.load(f)
            
            # إضافة السجل الجديد
            log_entry = {
                "user_id": user_id,
                "payment_method": payment_method,
                "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                **calculation_details
            }
            
            usage_log.append(log_entry)
            
            # الاحتفاظ بآخر 1000 سجل فقط
            if len(usage_log) > 1000:
                usage_log = usage_log[-1000:]
            
            # حفظ السجل
            with open(self.usage_log_file, 'w', encoding='utf-8') as f:
                json.dump(usage_log, f, ensure_ascii=False, indent=2)
            
            logger.info(f"📝 تم تسجيل استهلاك التوكن للمستخدم {user_id}")
            
        except Exception as e:
            logger.error(f"خطأ في تسجيل استهلاك التوكن: {e}")

    def estimate_tokens(self, text: str) -> int:
        """
        تقدير عدد التوكن في النص

        Args:
            text: النص المراد تقدير توكنه

        Returns:
            int: عدد التوكن المقدر
        """
        try:
            # تقدير تقريبي: كل 4 أحرف = 1 توكن (للنصوص العربية والإنجليزية)
            # هذا تقدير محافظ يأخذ في الاعتبار طبيعة النصوص المختلطة
            estimated_tokens = max(1, len(text) // 3)  # تقدير محافظ أكثر
            return estimated_tokens

        except Exception as e:
            logger.error(f"خطأ في تقدير التوكن: {e}")
            return len(text.split()) * 2  # تقدير احتياطي

    def get_accurate_token_usage(self, response_object) -> tuple:
        """
        الحصول على استهلاك التوكن الدقيق من رد API

        Args:
            response_object: كائن الرد من OpenAI API

        Returns:
            tuple: (input_tokens, output_tokens)
        """
        try:
            if hasattr(response_object, 'usage'):
                usage = response_object.usage
                input_tokens = getattr(usage, 'prompt_tokens', 0)
                output_tokens = getattr(usage, 'completion_tokens', 0)
                return input_tokens, output_tokens
            else:
                logger.warning("لا يمكن الحصول على معلومات استهلاك التوكن من الرد")
                return 0, 0

        except Exception as e:
            logger.error(f"خطأ في الحصول على استهلاك التوكن الدقيق: {e}")
            return 0, 0

    def calculate_cost_with_tiers(self, input_tokens: int, output_tokens: int) -> Tuple[float, Dict]:
        """
        حساب التكلفة مع نظام الطبقات (للمستقبل)

        Args:
            input_tokens: توكن الطلب
            output_tokens: توكن الرد

        Returns:
            Tuple[float, Dict]: (التكلفة بالإكسا, تفاصيل الحساب)
        """
        try:
            # نظام الطبقات (يمكن تفعيله لاحقاً)
            tiers = {
                "basic": {"rate": self.TOKEN_RATE, "max_tokens": 100000},
                "premium": {"rate": self.TOKEN_RATE * 0.9, "max_tokens": 500000},
                "enterprise": {"rate": self.TOKEN_RATE * 0.8, "max_tokens": float('inf')}
            }

            total_tokens = input_tokens + output_tokens

            # حالياً نستخدم المعدل الأساسي
            cost_in_exa = self.tokens_to_exa(total_tokens)

            calculation_details = {
                "input_tokens": input_tokens,
                "output_tokens": output_tokens,
                "total_tokens": total_tokens,
                "cost_in_exa": cost_in_exa,
                "token_rate": self.TOKEN_RATE,
                "tier": "basic",
                "discount": 0.0,
                "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            return cost_in_exa, calculation_details

        except Exception as e:
            logger.error(f"خطأ في حساب التكلفة مع الطبقات: {e}")
            return self.calculate_cost(input_tokens, output_tokens)

    def get_user_balance_warning(self, user_id: int) -> str:
        """
        الحصول على رسالة تحذير الرصيد المخصصة

        Args:
            user_id: معرف المستخدم

        Returns:
            str: رسالة التحذير
        """
        try:
            wallet_data = self.wallet_manager.get_user_wallet(user_id)
            if not wallet_data:
                return ""

            balance = wallet_data.get("balance", 0.0)
            debt_amount = wallet_data.get("debt_amount", 0.0)
            has_debt = wallet_data.get("has_debt", False)

            if has_debt and debt_amount > 0:
                return f"⚠️ تنبيه: لديك دين قدره {debt_amount:.3f} إكسا. يرجى سداد الدين لاستخدام إكسا الذكي برو."
            elif balance < self.LOW_BALANCE_WARNING:
                if balance > 0:
                    return f"⚠️ تحذير: رصيدك منخفض ({balance:.3f} إكسا). يرجى شحن رصيدك قريباً."
                else:
                    return "⚠️ رصيدك صفر. سيتم إنشاء دين عند الاستخدام (حد أقصى 1 إكسا)."

            return ""

        except Exception as e:
            logger.error(f"خطأ في الحصول على تحذير الرصيد: {e}")
            return ""
