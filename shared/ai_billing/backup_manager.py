#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام النسخ الاحتياطي لبيانات فوترة التوكن
يدير النسخ الاحتياطي والاستعادة لجميع بيانات النظام
"""

import os
import json
import shutil
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
import zipfile
import sys

# إضافة مسار المكتبات المشتركة
current_dir = os.path.dirname(os.path.abspath(__file__))
shared_dir = os.path.dirname(current_dir)
sys.path.insert(0, shared_dir)

# إعداد نظام السجلات
logger = logging.getLogger(__name__)

class BackupManager:
    """مدير النسخ الاحتياطي لنظام فوترة التوكن"""
    
    def __init__(self):
        """تهيئة مدير النسخ الاحتياطي"""
        
        # مجلدات البيانات
        self.database_dir = os.path.join(shared_dir, "database")
        self.ai_billing_dir = os.path.dirname(__file__)
        
        # مجلد النسخ الاحتياطي
        self.backup_dir = os.path.join(shared_dir, "backups")
        os.makedirs(self.backup_dir, exist_ok=True)
        
        # الملفات المهمة للنسخ الاحتياطي
        self.important_files = [
            os.path.join(self.database_dir, "wallets_data.json"),
            os.path.join(self.database_dir, "token_usage_log.json"),
            os.path.join(self.database_dir, "admin_notifications_log.json"),
            os.path.join(self.database_dir, "users_data.json"),
            os.path.join(self.database_dir, "monitoring_data.json")
        ]
        
        # إعدادات النسخ الاحتياطي
        self.settings = {
            "auto_backup_enabled": True,
            "backup_interval_hours": 6,  # كل 6 ساعات
            "max_backups_to_keep": 30,  # الاحتفاظ بـ 30 نسخة
            "compress_backups": True,
            "backup_on_critical_events": True
        }
        
        logger.info("💾 تم تهيئة مدير النسخ الاحتياطي")
    
    def create_backup(self, backup_name: str = None, include_logs: bool = True) -> Tuple[bool, str, str]:
        """
        إنشاء نسخة احتياطية
        
        Args:
            backup_name: اسم النسخة الاحتياطية (اختياري)
            include_logs: تضمين ملفات السجلات
            
        Returns:
            Tuple[bool, str, str]: (نجح الإنشاء, مسار النسخة, رسالة)
        """
        try:
            # إنشاء اسم النسخة الاحتياطية
            if not backup_name:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                backup_name = f"token_system_backup_{timestamp}"
            
            # مجلد النسخة الاحتياطية
            backup_path = os.path.join(self.backup_dir, backup_name)
            os.makedirs(backup_path, exist_ok=True)
            
            # نسخ الملفات المهمة
            copied_files = []
            for file_path in self.important_files:
                if os.path.exists(file_path):
                    filename = os.path.basename(file_path)
                    dest_path = os.path.join(backup_path, filename)
                    shutil.copy2(file_path, dest_path)
                    copied_files.append(filename)
                    logger.info(f"📄 تم نسخ: {filename}")
            
            # نسخ ملفات السجلات إذا طُلب ذلك
            if include_logs:
                logs_dir = os.path.join(shared_dir, "..", "logs")
                if os.path.exists(logs_dir):
                    backup_logs_dir = os.path.join(backup_path, "logs")
                    try:
                        shutil.copytree(logs_dir, backup_logs_dir)
                    except Exception as e:
                        logger.warning(f"تحذير في نسخ ملفات السجلات: {e}")
                    logger.info("📋 تم نسخ ملفات السجلات")
            
            # إنشاء ملف معلومات النسخة الاحتياطية
            backup_info = {
                "backup_name": backup_name,
                "created_at": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "files_included": copied_files,
                "include_logs": include_logs,
                "system_version": "1.0",
                "backup_size_mb": self._calculate_folder_size(backup_path)
            }
            
            info_file = os.path.join(backup_path, "backup_info.json")
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(backup_info, f, ensure_ascii=False, indent=2)
            
            # ضغط النسخة الاحتياطية إذا كان مفعلاً
            final_path = backup_path
            if self.settings["compress_backups"]:
                zip_path = f"{backup_path}.zip"
                self._create_zip_archive(backup_path, zip_path)
                shutil.rmtree(backup_path)  # حذف المجلد غير المضغوط
                final_path = zip_path
                logger.info(f"🗜️ تم ضغط النسخة الاحتياطية: {os.path.basename(zip_path)}")
            
            # تنظيف النسخ القديمة
            self._cleanup_old_backups()
            
            success_message = f"تم إنشاء النسخة الاحتياطية بنجاح: {os.path.basename(final_path)}"
            logger.info(f"✅ {success_message}")
            
            return True, final_path, success_message
            
        except Exception as e:
            error_message = f"خطأ في إنشاء النسخة الاحتياطية: {e}"
            logger.error(f"❌ {error_message}")
            return False, "", error_message
    
    def restore_backup(self, backup_path: str, confirm: bool = False) -> Tuple[bool, str]:
        """
        استعادة نسخة احتياطية
        
        Args:
            backup_path: مسار النسخة الاحتياطية
            confirm: تأكيد الاستعادة
            
        Returns:
            Tuple[bool, str]: (نجحت الاستعادة, رسالة)
        """
        try:
            if not confirm:
                return False, "يجب تأكيد الاستعادة لتجنب فقدان البيانات الحالية"
            
            if not os.path.exists(backup_path):
                return False, f"النسخة الاحتياطية غير موجودة: {backup_path}"
            
            # إنشاء نسخة احتياطية من البيانات الحالية قبل الاستعادة
            current_backup_success, current_backup_path, _ = self.create_backup("pre_restore_backup")
            if current_backup_success:
                logger.info(f"✅ تم إنشاء نسخة احتياطية من البيانات الحالية: {current_backup_path}")
            
            # استخراج النسخة الاحتياطية إذا كانت مضغوطة
            restore_dir = backup_path
            if backup_path.endswith('.zip'):
                extract_dir = os.path.join(self.backup_dir, "temp_restore")
                if os.path.exists(extract_dir):
                    shutil.rmtree(extract_dir)
                
                with zipfile.ZipFile(backup_path, 'r') as zip_ref:
                    zip_ref.extractall(extract_dir)
                
                restore_dir = extract_dir
                logger.info("📦 تم استخراج النسخة الاحتياطية المضغوطة")
            
            # قراءة معلومات النسخة الاحتياطية
            info_file = os.path.join(restore_dir, "backup_info.json")
            if os.path.exists(info_file):
                with open(info_file, 'r', encoding='utf-8') as f:
                    backup_info = json.load(f)
                logger.info(f"📋 معلومات النسخة: {backup_info.get('backup_name', 'غير محدد')}")
            
            # استعادة الملفات
            restored_files = []
            for file_path in self.important_files:
                filename = os.path.basename(file_path)
                backup_file = os.path.join(restore_dir, filename)
                
                if os.path.exists(backup_file):
                    # إنشاء المجلد إذا لم يكن موجوداً
                    os.makedirs(os.path.dirname(file_path), exist_ok=True)
                    
                    # استعادة الملف
                    shutil.copy2(backup_file, file_path)
                    restored_files.append(filename)
                    logger.info(f"📄 تم استعادة: {filename}")
            
            # تنظيف الملفات المؤقتة
            if backup_path.endswith('.zip') and os.path.exists(extract_dir):
                shutil.rmtree(extract_dir)
            
            success_message = f"تم استعادة النسخة الاحتياطية بنجاح. الملفات المستعادة: {len(restored_files)}"
            logger.info(f"✅ {success_message}")
            
            return True, success_message
            
        except Exception as e:
            error_message = f"خطأ في استعادة النسخة الاحتياطية: {e}"
            logger.error(f"❌ {error_message}")
            return False, error_message
    
    def list_backups(self) -> List[Dict[str, Any]]:
        """
        الحصول على قائمة النسخ الاحتياطية
        
        Returns:
            list: قائمة النسخ الاحتياطية مع معلوماتها
        """
        try:
            backups = []
            
            if not os.path.exists(self.backup_dir):
                return backups
            
            for item in os.listdir(self.backup_dir):
                item_path = os.path.join(self.backup_dir, item)
                
                # تخطي الملفات المؤقتة
                if item.startswith('.') or item.startswith('temp_'):
                    continue
                
                backup_info = {
                    "name": item,
                    "path": item_path,
                    "is_compressed": item.endswith('.zip'),
                    "size_mb": 0,
                    "created_at": "",
                    "files_count": 0
                }
                
                # حساب الحجم
                if os.path.isfile(item_path):
                    backup_info["size_mb"] = os.path.getsize(item_path) / (1024 * 1024)
                elif os.path.isdir(item_path):
                    backup_info["size_mb"] = self._calculate_folder_size(item_path)
                
                # الحصول على تاريخ الإنشاء
                backup_info["created_at"] = datetime.fromtimestamp(
                    os.path.getctime(item_path)
                ).strftime('%Y-%m-%d %H:%M:%S')
                
                # قراءة معلومات إضافية إذا كانت متوفرة
                info_file = None
                if os.path.isdir(item_path):
                    info_file = os.path.join(item_path, "backup_info.json")
                elif item.endswith('.zip'):
                    try:
                        with zipfile.ZipFile(item_path, 'r') as zip_ref:
                            if "backup_info.json" in zip_ref.namelist():
                                info_content = zip_ref.read("backup_info.json").decode('utf-8')
                                info_data = json.loads(info_content)
                                backup_info["created_at"] = info_data.get("created_at", backup_info["created_at"])
                                backup_info["files_count"] = len(info_data.get("files_included", []))
                    except:
                        pass
                
                if info_file and os.path.exists(info_file):
                    try:
                        with open(info_file, 'r', encoding='utf-8') as f:
                            info_data = json.load(f)
                            backup_info["created_at"] = info_data.get("created_at", backup_info["created_at"])
                            backup_info["files_count"] = len(info_data.get("files_included", []))
                    except:
                        pass
                
                backups.append(backup_info)
            
            # ترتيب حسب تاريخ الإنشاء (الأحدث أولاً)
            backups.sort(key=lambda x: x["created_at"], reverse=True)
            
            return backups
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على قائمة النسخ الاحتياطية: {e}")
            return []
    
    def delete_backup(self, backup_path: str) -> Tuple[bool, str]:
        """
        حذف نسخة احتياطية
        
        Args:
            backup_path: مسار النسخة الاحتياطية
            
        Returns:
            Tuple[bool, str]: (نجح الحذف, رسالة)
        """
        try:
            if not os.path.exists(backup_path):
                return False, "النسخة الاحتياطية غير موجودة"
            
            if os.path.isfile(backup_path):
                os.remove(backup_path)
            elif os.path.isdir(backup_path):
                shutil.rmtree(backup_path)
            
            success_message = f"تم حذف النسخة الاحتياطية: {os.path.basename(backup_path)}"
            logger.info(f"🗑️ {success_message}")
            
            return True, success_message
            
        except Exception as e:
            error_message = f"خطأ في حذف النسخة الاحتياطية: {e}"
            logger.error(f"❌ {error_message}")
            return False, error_message
    
    def _calculate_folder_size(self, folder_path: str) -> float:
        """حساب حجم المجلد بالميجابايت"""
        try:
            total_size = 0
            for dirpath, dirnames, filenames in os.walk(folder_path):
                for filename in filenames:
                    file_path = os.path.join(dirpath, filename)
                    if os.path.exists(file_path):
                        total_size += os.path.getsize(file_path)
            return total_size / (1024 * 1024)  # تحويل إلى ميجابايت
        except:
            return 0.0
    
    def _create_zip_archive(self, source_dir: str, zip_path: str):
        """إنشاء أرشيف مضغوط"""
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zip_ref:
            for root, dirs, files in os.walk(source_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_name = os.path.relpath(file_path, source_dir)
                    zip_ref.write(file_path, arc_name)
    
    def _cleanup_old_backups(self):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            backups = self.list_backups()
            
            if len(backups) > self.settings["max_backups_to_keep"]:
                # حذف النسخ الزائدة (الأقدم)
                old_backups = backups[self.settings["max_backups_to_keep"]:]
                
                for backup in old_backups:
                    self.delete_backup(backup["path"])
                
                logger.info(f"🧹 تم حذف {len(old_backups)} نسخة احتياطية قديمة")
                
        except Exception as e:
            logger.error(f"خطأ في تنظيف النسخ الاحتياطية القديمة: {e}")
    
    def create_emergency_backup(self, reason: str = "طوارئ") -> Tuple[bool, str]:
        """
        إنشاء نسخة احتياطية طارئة
        
        Args:
            reason: سبب النسخة الطارئة
            
        Returns:
            Tuple[bool, str]: (نجح الإنشاء, رسالة)
        """
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = f"emergency_backup_{timestamp}_{reason}"
            
            success, backup_path, message = self.create_backup(backup_name, include_logs=True)
            
            if success:
                logger.warning(f"🚨 تم إنشاء نسخة احتياطية طارئة: {reason}")
            
            return success, message
            
        except Exception as e:
            error_message = f"خطأ في إنشاء النسخة الاحتياطية الطارئة: {e}"
            logger.error(f"❌ {error_message}")
            return False, error_message
    
    def get_backup_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات النسخ الاحتياطية"""
        try:
            backups = self.list_backups()
            
            if not backups:
                return {
                    "total_backups": 0,
                    "total_size_mb": 0,
                    "latest_backup": None,
                    "oldest_backup": None
                }
            
            total_size = sum(backup["size_mb"] for backup in backups)
            latest_backup = backups[0] if backups else None
            oldest_backup = backups[-1] if backups else None
            
            return {
                "total_backups": len(backups),
                "total_size_mb": total_size,
                "latest_backup": latest_backup,
                "oldest_backup": oldest_backup,
                "avg_size_mb": total_size / len(backups) if backups else 0
            }
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات النسخ الاحتياطية: {e}")
            return {"error": str(e)}
