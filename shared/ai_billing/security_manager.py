#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام الأمان والتشفير لفوترة التوكن
يوفر حماية متقدمة للبيانات الحساسة والمعاملات
"""

import os
import json
import hashlib
import hmac
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import secrets
import sys

# إضافة مسار المكتبات المشتركة
current_dir = os.path.dirname(os.path.abspath(__file__))
shared_dir = os.path.dirname(current_dir)
sys.path.insert(0, shared_dir)

# إعداد نظام السجلات
logger = logging.getLogger(__name__)

class SecurityManager:
    """مدير الأمان والتشفير لنظام فوترة التوكن"""
    
    def __init__(self):
        """تهيئة مدير الأمان"""
        
        # مجلد مفاتيح التشفير
        self.keys_dir = os.path.join(shared_dir, "security")
        os.makedirs(self.keys_dir, exist_ok=True)
        
        # ملف المفتاح الرئيسي
        self.master_key_file = os.path.join(self.keys_dir, "master.key")
        
        # إعدادات الأمان
        self.settings = {
            "encrypt_sensitive_data": True,
            "hash_user_ids": True,
            "audit_log_enabled": True,
            "session_timeout_minutes": 30,
            "max_failed_attempts": 5,
            "rate_limit_requests_per_minute": 60
        }
        
        # تهيئة التشفير
        self.cipher_suite = self._initialize_encryption()
        
        # سجل الأمان
        self.security_log_file = os.path.join(shared_dir, "database", "security_audit.json")
        self._ensure_security_log_exists()
        
        # تتبع المحاولات الفاشلة
        self.failed_attempts = {}
        
        # تتبع معدل الطلبات
        self.request_tracking = {}
        
        logger.info("🔒 تم تهيئة مدير الأمان والتشفير")
    
    def _initialize_encryption(self) -> Fernet:
        """تهيئة نظام التشفير"""
        try:
            # تحميل أو إنشاء المفتاح الرئيسي
            if os.path.exists(self.master_key_file):
                with open(self.master_key_file, 'rb') as f:
                    key = f.read()
            else:
                # إنشاء مفتاح جديد
                key = Fernet.generate_key()
                with open(self.master_key_file, 'wb') as f:
                    f.write(key)
                logger.info("🔑 تم إنشاء مفتاح تشفير جديد")
            
            return Fernet(key)
            
        except Exception as e:
            logger.error(f"خطأ في تهيئة التشفير: {e}")
            # إنشاء مفتاح مؤقت للطوارئ
            return Fernet(Fernet.generate_key())
    
    def _ensure_security_log_exists(self):
        """التأكد من وجود ملف سجل الأمان"""
        if not os.path.exists(self.security_log_file):
            with open(self.security_log_file, 'w', encoding='utf-8') as f:
                json.dump([], f, ensure_ascii=False, indent=2)
            logger.info("📄 تم إنشاء ملف سجل الأمان")
    
    def encrypt_data(self, data: str) -> str:
        """
        تشفير البيانات
        
        Args:
            data: البيانات المراد تشفيرها
            
        Returns:
            str: البيانات المشفرة
        """
        try:
            if not self.settings["encrypt_sensitive_data"]:
                return data
            
            encrypted_data = self.cipher_suite.encrypt(data.encode('utf-8'))
            return base64.b64encode(encrypted_data).decode('utf-8')
            
        except Exception as e:
            logger.error(f"خطأ في تشفير البيانات: {e}")
            return data  # إرجاع البيانات الأصلية في حالة الخطأ
    
    def decrypt_data(self, encrypted_data: str) -> str:
        """
        فك تشفير البيانات
        
        Args:
            encrypted_data: البيانات المشفرة
            
        Returns:
            str: البيانات المفكوكة التشفير
        """
        try:
            if not self.settings["encrypt_sensitive_data"]:
                return encrypted_data
            
            decoded_data = base64.b64decode(encrypted_data.encode('utf-8'))
            decrypted_data = self.cipher_suite.decrypt(decoded_data)
            return decrypted_data.decode('utf-8')
            
        except Exception as e:
            logger.error(f"خطأ في فك تشفير البيانات: {e}")
            return encrypted_data  # إرجاع البيانات الأصلية في حالة الخطأ
    
    def hash_user_id(self, user_id: int) -> str:
        """
        تشفير معرف المستخدم بـ hash
        
        Args:
            user_id: معرف المستخدم
            
        Returns:
            str: معرف المستخدم المشفر
        """
        try:
            if not self.settings["hash_user_ids"]:
                return str(user_id)
            
            # استخدام HMAC مع مفتاح سري
            secret_key = b"user_id_secret_key_2024"
            user_id_bytes = str(user_id).encode('utf-8')
            
            hashed = hmac.new(secret_key, user_id_bytes, hashlib.sha256)
            return hashed.hexdigest()[:16]  # أول 16 حرف للاختصار
            
        except Exception as e:
            logger.error(f"خطأ في تشفير معرف المستخدم: {e}")
            return str(user_id)
    
    def generate_transaction_signature(self, transaction_data: Dict[str, Any]) -> str:
        """
        إنشاء توقيع رقمي للمعاملة
        
        Args:
            transaction_data: بيانات المعاملة
            
        Returns:
            str: التوقيع الرقمي
        """
        try:
            # تحويل البيانات إلى نص مرتب
            sorted_data = json.dumps(transaction_data, sort_keys=True, ensure_ascii=False)
            
            # إنشاء التوقيع
            secret_key = b"transaction_signature_key_2024"
            signature = hmac.new(secret_key, sorted_data.encode('utf-8'), hashlib.sha256)
            
            return signature.hexdigest()
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء توقيع المعاملة: {e}")
            return ""
    
    def verify_transaction_signature(self, transaction_data: Dict[str, Any], signature: str) -> bool:
        """
        التحقق من توقيع المعاملة
        
        Args:
            transaction_data: بيانات المعاملة
            signature: التوقيع المراد التحقق منه
            
        Returns:
            bool: صحة التوقيع
        """
        try:
            expected_signature = self.generate_transaction_signature(transaction_data)
            return hmac.compare_digest(expected_signature, signature)
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من توقيع المعاملة: {e}")
            return False
    
    def log_security_event(self, event_type: str, user_id: int = None, details: Dict[str, Any] = None):
        """
        تسجيل حدث أمني
        
        Args:
            event_type: نوع الحدث
            user_id: معرف المستخدم (اختياري)
            details: تفاصيل إضافية
        """
        try:
            if not self.settings["audit_log_enabled"]:
                return
            
            # قراءة السجل الحالي
            with open(self.security_log_file, 'r', encoding='utf-8') as f:
                security_log = json.load(f)
            
            # إضافة الحدث الجديد
            event = {
                "event_type": event_type,
                "user_id": self.hash_user_id(user_id) if user_id else None,
                "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "details": details or {},
                "ip_address": "unknown",  # يمكن تحسينه لاحقاً
                "user_agent": "unknown"   # يمكن تحسينه لاحقاً
            }
            
            security_log.append(event)
            
            # الاحتفاظ بآخر 1000 حدث فقط
            if len(security_log) > 1000:
                security_log = security_log[-1000:]
            
            # حفظ السجل
            with open(self.security_log_file, 'w', encoding='utf-8') as f:
                json.dump(security_log, f, ensure_ascii=False, indent=2)
            
            logger.info(f"🔍 تم تسجيل حدث أمني: {event_type}")
            
        except Exception as e:
            logger.error(f"خطأ في تسجيل الحدث الأمني: {e}")
    
    def check_rate_limit(self, user_id: int) -> Tuple[bool, str]:
        """
        فحص معدل الطلبات
        
        Args:
            user_id: معرف المستخدم
            
        Returns:
            Tuple[bool, str]: (مسموح, رسالة)
        """
        try:
            current_time = datetime.now()
            user_key = str(user_id)
            
            # تنظيف الطلبات القديمة
            if user_key in self.request_tracking:
                self.request_tracking[user_key] = [
                    req_time for req_time in self.request_tracking[user_key]
                    if current_time - req_time < timedelta(minutes=1)
                ]
            else:
                self.request_tracking[user_key] = []
            
            # فحص الحد الأقصى
            if len(self.request_tracking[user_key]) >= self.settings["rate_limit_requests_per_minute"]:
                self.log_security_event("rate_limit_exceeded", user_id, {
                    "requests_count": len(self.request_tracking[user_key])
                })
                return False, "تم تجاوز الحد الأقصى للطلبات. يرجى المحاولة لاحقاً."
            
            # إضافة الطلب الحالي
            self.request_tracking[user_key].append(current_time)
            
            return True, "مسموح"
            
        except Exception as e:
            logger.error(f"خطأ في فحص معدل الطلبات: {e}")
            return True, "مسموح"  # السماح في حالة الخطأ
    
    def track_failed_attempt(self, user_id: int, attempt_type: str = "authentication"):
        """
        تتبع المحاولات الفاشلة
        
        Args:
            user_id: معرف المستخدم
            attempt_type: نوع المحاولة
        """
        try:
            user_key = str(user_id)
            current_time = datetime.now()
            
            if user_key not in self.failed_attempts:
                self.failed_attempts[user_key] = []
            
            # تنظيف المحاولات القديمة (آخر ساعة)
            self.failed_attempts[user_key] = [
                attempt for attempt in self.failed_attempts[user_key]
                if current_time - attempt["time"] < timedelta(hours=1)
            ]
            
            # إضافة المحاولة الفاشلة
            self.failed_attempts[user_key].append({
                "time": current_time,
                "type": attempt_type
            })
            
            # تسجيل الحدث
            self.log_security_event("failed_attempt", user_id, {
                "attempt_type": attempt_type,
                "total_attempts": len(self.failed_attempts[user_key])
            })
            
            # تحذير إذا تجاوز الحد الأقصى
            if len(self.failed_attempts[user_key]) >= self.settings["max_failed_attempts"]:
                self.log_security_event("suspicious_activity", user_id, {
                    "reason": "too_many_failed_attempts",
                    "attempts_count": len(self.failed_attempts[user_key])
                })
                logger.warning(f"🚨 نشاط مشبوه: محاولات فاشلة متعددة من المستخدم {user_id}")
            
        except Exception as e:
            logger.error(f"خطأ في تتبع المحاولة الفاشلة: {e}")
    
    def is_user_blocked(self, user_id: int) -> Tuple[bool, str]:
        """
        فحص ما إذا كان المستخدم محظور
        
        Args:
            user_id: معرف المستخدم
            
        Returns:
            Tuple[bool, str]: (محظور, سبب الحظر)
        """
        try:
            user_key = str(user_id)
            current_time = datetime.now()
            
            if user_key not in self.failed_attempts:
                return False, ""
            
            # تنظيف المحاولات القديمة
            self.failed_attempts[user_key] = [
                attempt for attempt in self.failed_attempts[user_key]
                if current_time - attempt["time"] < timedelta(hours=1)
            ]
            
            # فحص الحظر
            if len(self.failed_attempts[user_key]) >= self.settings["max_failed_attempts"]:
                return True, f"تم حظرك مؤقتاً بسبب {len(self.failed_attempts[user_key])} محاولة فاشلة"
            
            return False, ""
            
        except Exception as e:
            logger.error(f"خطأ في فحص حظر المستخدم: {e}")
            return False, ""
    
    def generate_secure_token(self, length: int = 32) -> str:
        """
        إنشاء رمز آمن عشوائي
        
        Args:
            length: طول الرمز
            
        Returns:
            str: الرمز الآمن
        """
        try:
            return secrets.token_urlsafe(length)
        except Exception as e:
            logger.error(f"خطأ في إنشاء الرمز الآمن: {e}")
            return hashlib.sha256(str(datetime.now()).encode()).hexdigest()[:length]
    
    def get_security_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الأمان"""
        try:
            # قراءة سجل الأمان
            with open(self.security_log_file, 'r', encoding='utf-8') as f:
                security_log = json.load(f)
            
            # تحليل الأحداث
            event_types = {}
            recent_events = []
            
            for event in security_log:
                event_type = event.get("event_type", "unknown")
                event_types[event_type] = event_types.get(event_type, 0) + 1
                
                # الأحداث الأخيرة (آخر 24 ساعة)
                try:
                    event_time = datetime.strptime(event.get("timestamp", ""), '%Y-%m-%d %H:%M:%S')
                    if datetime.now() - event_time < timedelta(hours=24):
                        recent_events.append(event)
                except:
                    pass
            
            return {
                "total_events": len(security_log),
                "event_types": event_types,
                "recent_events_24h": len(recent_events),
                "failed_attempts_tracking": len(self.failed_attempts),
                "rate_limit_tracking": len(self.request_tracking),
                "encryption_enabled": self.settings["encrypt_sensitive_data"],
                "audit_log_enabled": self.settings["audit_log_enabled"],
                "last_updated": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات الأمان: {e}")
            return {"error": str(e)}
    
    def cleanup_old_security_data(self):
        """تنظيف بيانات الأمان القديمة"""
        try:
            current_time = datetime.now()
            
            # تنظيف المحاولات الفاشلة القديمة
            for user_key in list(self.failed_attempts.keys()):
                self.failed_attempts[user_key] = [
                    attempt for attempt in self.failed_attempts[user_key]
                    if current_time - attempt["time"] < timedelta(hours=24)
                ]
                
                if not self.failed_attempts[user_key]:
                    del self.failed_attempts[user_key]
            
            # تنظيف تتبع معدل الطلبات
            for user_key in list(self.request_tracking.keys()):
                self.request_tracking[user_key] = [
                    req_time for req_time in self.request_tracking[user_key]
                    if current_time - req_time < timedelta(minutes=5)
                ]
                
                if not self.request_tracking[user_key]:
                    del self.request_tracking[user_key]
            
            logger.info("🧹 تم تنظيف بيانات الأمان القديمة")
            
        except Exception as e:
            logger.error(f"خطأ في تنظيف بيانات الأمان: {e}")
