#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إشعارات المدير لفوترة التوكن
يرسل تنبيهات للمدير عند حدوث ديون أو استهلاك غير طبيعي
"""

import os
import json
import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import sys

# إضافة مسار المكتبات المشتركة
current_dir = os.path.dirname(os.path.abspath(__file__))
shared_dir = os.path.dirname(current_dir)
sys.path.insert(0, shared_dir)

from database.wallet_manager import WalletManager
from .usage_tracker import UsageTracker

# إعداد نظام السجلات
logger = logging.getLogger(__name__)

class AdminNotificationManager:
    """مدير إشعارات المدير لنظام فوترة التوكن"""
    
    def __init__(self, admin_bot_token: str = None, admin_chat_id: int = None):
        """
        تهيئة مدير الإشعارات
        
        Args:
            admin_bot_token: توكن بوت الإدارة
            admin_chat_id: معرف محادثة المدير
        """
        self.admin_bot_token = admin_bot_token
        self.admin_chat_id = admin_chat_id
        
        # تحميل الإعدادات من ملف الإعدادات إذا لم تُمرر
        if not self.admin_bot_token or not self.admin_chat_id:
            self._load_admin_config()
        
        self.wallet_manager = WalletManager()
        self.usage_tracker = UsageTracker()
        
        # ملف تتبع الإشعارات المرسلة
        self.notifications_log_file = os.path.join(
            os.path.dirname(__file__), "..", "database", "admin_notifications_log.json"
        )
        self._ensure_log_file_exists()
        
        # إعدادات التنبيهات
        self.settings = {
            "debt_threshold": 0.1,  # تنبيه عند دين أكبر من 0.1 إكسا
            "unusual_usage_multiplier": 3.0,  # تنبيه عند استهلاك 3 أضعاف المتوسط
            "daily_report_enabled": True,  # تقرير يومي
            "debt_notification_enabled": True,  # تنبيهات الديون
            "unusual_usage_notification_enabled": True  # تنبيهات الاستهلاك غير الطبيعي
        }
        
        logger.info("📢 تم تهيئة مدير إشعارات المدير")
    
    def _load_admin_config(self):
        """تحميل إعدادات المدير"""
        try:
            # محاولة تحميل من ملف الإعدادات
            config_paths = [
                os.path.join(os.path.dirname(__file__), "..", "..", "admin", "core", "admin_config.py"),
                os.path.join(os.path.dirname(__file__), "..", "..", "admin", "admin_config.py")
            ]
            
            for config_path in config_paths:
                if os.path.exists(config_path):
                    # قراءة الملف وتحليله
                    with open(config_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                    # استخراج التوكن ومعرف المدير
                    import re
                    token_match = re.search(r'ADMIN_BOT_TOKEN\s*=\s*["\']([^"\']+)["\']', content)
                    chat_id_match = re.search(r'ADMIN_USER_ID\s*=\s*(\d+)', content)
                    
                    if token_match:
                        self.admin_bot_token = token_match.group(1)
                    if chat_id_match:
                        self.admin_chat_id = int(chat_id_match.group(1))
                    
                    break
            
            if not self.admin_bot_token or not self.admin_chat_id:
                logger.warning("⚠️ لم يتم العثور على إعدادات المدير، الإشعارات معطلة")
                
        except Exception as e:
            logger.error(f"خطأ في تحميل إعدادات المدير: {e}")
    
    def _ensure_log_file_exists(self):
        """التأكد من وجود ملف سجل الإشعارات"""
        if not os.path.exists(self.notifications_log_file):
            with open(self.notifications_log_file, 'w', encoding='utf-8') as f:
                json.dump([], f, ensure_ascii=False, indent=2)
            logger.info("📄 تم إنشاء ملف سجل إشعارات المدير")
    
    def _load_notifications_log(self) -> List[Dict]:
        """تحميل سجل الإشعارات"""
        try:
            with open(self.notifications_log_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"خطأ في تحميل سجل الإشعارات: {e}")
            return []
    
    def _save_notification_log(self, notification_data: Dict):
        """حفظ إشعار في السجل"""
        try:
            notifications_log = self._load_notifications_log()
            
            # إضافة الإشعار الجديد
            notification_data["timestamp"] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            notifications_log.append(notification_data)
            
            # الاحتفاظ بآخر 100 إشعار فقط
            if len(notifications_log) > 100:
                notifications_log = notifications_log[-100:]
            
            # حفظ السجل
            with open(self.notifications_log_file, 'w', encoding='utf-8') as f:
                json.dump(notifications_log, f, ensure_ascii=False, indent=2)
            
        except Exception as e:
            logger.error(f"خطأ في حفظ سجل الإشعار: {e}")
    
    async def send_admin_notification(self, message: str, notification_type: str = "info"):
        """
        إرسال إشعار للمدير
        
        Args:
            message: نص الإشعار
            notification_type: نوع الإشعار (info, warning, error, debt, usage)
        """
        try:
            if not self.admin_bot_token or not self.admin_chat_id:
                logger.warning("⚠️ إعدادات المدير غير متوفرة، لا يمكن إرسال الإشعار")
                return False
            
            # إضافة رمز حسب نوع الإشعار
            emoji_map = {
                "info": "ℹ️",
                "warning": "⚠️",
                "error": "❌",
                "debt": "💳",
                "usage": "🤖",
                "daily_report": "📊"
            }
            
            emoji = emoji_map.get(notification_type, "📢")
            formatted_message = f"{emoji} **إشعار نظام التوكن**\n\n{message}"
            
            # إرسال الإشعار باستخدام requests (بدلاً من telegram bot)
            import requests
            
            url = f"https://api.telegram.org/bot{self.admin_bot_token}/sendMessage"
            data = {
                "chat_id": self.admin_chat_id,
                "text": formatted_message,
                "parse_mode": "Markdown"
            }
            
            response = requests.post(url, data=data, timeout=10)
            
            if response.status_code == 200:
                # حفظ الإشعار في السجل
                self._save_notification_log({
                    "type": notification_type,
                    "message": message,
                    "status": "sent"
                })
                logger.info(f"✅ تم إرسال إشعار المدير: {notification_type}")
                return True
            else:
                logger.error(f"❌ فشل في إرسال إشعار المدير: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"خطأ في إرسال إشعار المدير: {e}")
            return False
    
    async def check_and_notify_debts(self):
        """فحص الديون وإرسال تنبيهات"""
        try:
            if not self.settings["debt_notification_enabled"]:
                return
            
            debt_stats = self.wallet_manager.get_debt_statistics()
            users_with_debt = debt_stats.get("users_with_debt", [])
            
            # تصفية المستخدمين الذين لديهم ديون كبيرة
            significant_debts = [
                user for user in users_with_debt 
                if user["debt_amount"] >= self.settings["debt_threshold"]
            ]
            
            if significant_debts:
                message = f"💳 **تنبيه الديون**\n\n"
                message += f"عدد المستخدمين المدينين: {len(significant_debts)}\n"
                message += f"إجمالي الديون: {sum(user['debt_amount'] for user in significant_debts):.3f} إكسا\n\n"
                
                message += "**المستخدمون:**\n"
                for user in significant_debts[:5]:  # أول 5 مستخدمين
                    message += f"• {user['user_name']}: {user['debt_amount']:.3f} إكسا\n"
                
                if len(significant_debts) > 5:
                    message += f"... و {len(significant_debts) - 5} مستخدم آخر"
                
                await self.send_admin_notification(message, "debt")
            
        except Exception as e:
            logger.error(f"خطأ في فحص الديون: {e}")
    
    async def check_and_notify_unusual_usage(self):
        """فحص الاستهلاك غير الطبيعي وإرسال تنبيهات"""
        try:
            if not self.settings["unusual_usage_notification_enabled"]:
                return
            
            unusual_users = self.usage_tracker.detect_unusual_usage(
                self.settings["unusual_usage_multiplier"]
            )
            
            if unusual_users:
                message = f"🤖 **تنبيه الاستهلاك غير الطبيعي**\n\n"
                message += f"عدد المستخدمين: {len(unusual_users)}\n\n"
                
                message += "**المستخدمون:**\n"
                for user in unusual_users[:3]:  # أول 3 مستخدمين
                    message += f"• {user['user_name']}: {user['max_daily_tokens']:,} توكن/يوم\n"
                    message += f"  النسبة: {user['ratio']:.1f}x المتوسط\n"
                
                await self.send_admin_notification(message, "usage")
            
        except Exception as e:
            logger.error(f"خطأ في فحص الاستهلاك غير الطبيعي: {e}")
    
    async def send_daily_report(self):
        """إرسال التقرير اليومي"""
        try:
            if not self.settings["daily_report_enabled"]:
                return
            
            # الحصول على إحصائيات اليوم
            daily_stats = self.usage_tracker.get_daily_usage_stats(1)
            overall_stats = self.usage_tracker.get_overall_usage_stats()
            debt_stats = self.wallet_manager.get_debt_statistics()
            
            today = datetime.now().strftime('%Y-%m-%d')
            today_data = daily_stats.get("daily_stats", {}).get(today, {})
            
            message = f"📊 **التقرير اليومي - {today}**\n\n"
            
            # إحصائيات اليوم
            message += "**إحصائيات اليوم:**\n"
            message += f"• الطلبات: {today_data.get('requests', 0)}\n"
            message += f"• المستخدمين النشطين: {today_data.get('unique_users', 0)}\n"
            message += f"• إجمالي التوكن: {today_data.get('total_tokens', 0):,}\n"
            message += f"• إجمالي التكلفة: {today_data.get('total_cost', 0):.6f} إكسا\n\n"
            
            # إحصائيات الديون
            message += "**إحصائيات الديون:**\n"
            message += f"• المستخدمين المدينين: {debt_stats.get('total_users_with_debt', 0)}\n"
            message += f"• إجمالي الديون: {debt_stats.get('total_debt_amount', 0):.3f} إكسا\n\n"
            
            # إحصائيات عامة
            message += "**الإحصائيات العامة:**\n"
            message += f"• إجمالي الطلبات: {overall_stats.get('total_requests', 0)}\n"
            message += f"• إجمالي المستخدمين: {overall_stats.get('total_users', 0)}\n"
            message += f"• إجمالي التكلفة: {overall_stats.get('total_cost', 0):.6f} إكسا"
            
            await self.send_admin_notification(message, "daily_report")
            
        except Exception as e:
            logger.error(f"خطأ في إرسال التقرير اليومي: {e}")
    
    async def run_periodic_checks(self):
        """تشغيل الفحوصات الدورية"""
        try:
            logger.info("🔄 بدء الفحوصات الدورية لنظام التوكن")
            
            while True:
                try:
                    # فحص الديون كل 30 دقيقة
                    await self.check_and_notify_debts()
                    
                    # فحص الاستهلاك غير الطبيعي كل ساعة
                    current_time = datetime.now()
                    if current_time.minute == 0:  # كل ساعة عند الدقيقة 0
                        await self.check_and_notify_unusual_usage()
                    
                    # إرسال التقرير اليومي في الساعة 9 صباحاً
                    if current_time.hour == 9 and current_time.minute == 0:
                        await self.send_daily_report()
                    
                    # انتظار 30 دقيقة
                    await asyncio.sleep(1800)  # 30 دقيقة
                    
                except Exception as e:
                    logger.error(f"خطأ في الفحص الدوري: {e}")
                    await asyncio.sleep(300)  # انتظار 5 دقائق عند الخطأ
                    
        except Exception as e:
            logger.error(f"خطأ في تشغيل الفحوصات الدورية: {e}")
    
    def start_background_monitoring(self):
        """بدء المراقبة في الخلفية"""
        try:
            # تشغيل الفحوصات الدورية في thread منفصل
            import threading
            
            def run_async_monitoring():
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(self.run_periodic_checks())
            
            monitoring_thread = threading.Thread(target=run_async_monitoring, daemon=True)
            monitoring_thread.start()
            
            logger.info("✅ تم بدء المراقبة في الخلفية")
            
        except Exception as e:
            logger.error(f"خطأ في بدء المراقبة في الخلفية: {e}")
