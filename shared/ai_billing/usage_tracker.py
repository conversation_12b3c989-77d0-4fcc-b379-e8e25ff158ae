#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
متتبع استخدام الذكاء الاصطناعي والإحصائيات
يوفر إحصائيات مفصلة عن استهلاك التوكن والتكاليف
"""

import os
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from collections import defaultdict
import sys

# إضافة مسار المكتبات المشتركة
current_dir = os.path.dirname(os.path.abspath(__file__))
shared_dir = os.path.dirname(current_dir)
sys.path.insert(0, shared_dir)

from database.wallet_manager import WalletManager

# إعداد نظام السجلات
logger = logging.getLogger(__name__)

class UsageTracker:
    """متتبع استخدام الذكاء الاصطناعي والإحصائيات"""
    
    def __init__(self):
        """تهيئة متتبع الاستخدام"""
        self.wallet_manager = WalletManager()
        
        # ملف سجل الاستهلاك
        self.usage_log_file = os.path.join(os.path.dirname(__file__), "..", "database", "token_usage_log.json")
        
        logger.info("📊 تم تهيئة متتبع استخدام الذكاء الاصطناعي")
    
    def load_usage_log(self) -> List[Dict]:
        """تحميل سجل الاستهلاك"""
        try:
            if os.path.exists(self.usage_log_file):
                with open(self.usage_log_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return []
        except Exception as e:
            logger.error(f"خطأ في تحميل سجل الاستهلاك: {e}")
            return []
    
    def get_user_usage_stats(self, user_id: int, days: int = 30) -> Dict[str, Any]:
        """
        الحصول على إحصائيات استخدام المستخدم
        
        Args:
            user_id: معرف المستخدم
            days: عدد الأيام للإحصائيات (افتراضي 30 يوم)
            
        Returns:
            dict: إحصائيات الاستخدام
        """
        try:
            usage_log = self.load_usage_log()
            
            # تصفية السجلات للمستخدم والفترة المحددة
            cutoff_date = datetime.now() - timedelta(days=days)
            user_logs = []
            
            for log_entry in usage_log:
                if log_entry.get("user_id") == user_id:
                    try:
                        log_date = datetime.strptime(log_entry.get("timestamp", ""), '%Y-%m-%d %H:%M:%S')
                        if log_date >= cutoff_date:
                            user_logs.append(log_entry)
                    except:
                        continue
            
            # حساب الإحصائيات
            total_requests = len(user_logs)
            total_input_tokens = sum(log.get("input_tokens", 0) for log in user_logs)
            total_output_tokens = sum(log.get("output_tokens", 0) for log in user_logs)
            total_tokens = total_input_tokens + total_output_tokens
            total_cost = sum(log.get("cost_in_exa", 0) for log in user_logs)
            
            # تصنيف حسب طريقة الدفع
            balance_payments = [log for log in user_logs if log.get("payment_method") == "balance_deduction"]
            debt_payments = [log for log in user_logs if log.get("payment_method") == "debt_creation"]
            
            # متوسط الاستخدام
            avg_tokens_per_request = total_tokens / total_requests if total_requests > 0 else 0
            avg_cost_per_request = total_cost / total_requests if total_requests > 0 else 0
            
            return {
                "user_id": user_id,
                "period_days": days,
                "total_requests": total_requests,
                "total_input_tokens": total_input_tokens,
                "total_output_tokens": total_output_tokens,
                "total_tokens": total_tokens,
                "total_cost": total_cost,
                "balance_payments": len(balance_payments),
                "debt_payments": len(debt_payments),
                "avg_tokens_per_request": avg_tokens_per_request,
                "avg_cost_per_request": avg_cost_per_request,
                "last_updated": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات المستخدم {user_id}: {e}")
            return {}
    
    def get_daily_usage_stats(self, days: int = 7) -> Dict[str, Any]:
        """
        الحصول على إحصائيات الاستخدام اليومية
        
        Args:
            days: عدد الأيام (افتراضي 7 أيام)
            
        Returns:
            dict: إحصائيات يومية
        """
        try:
            usage_log = self.load_usage_log()
            
            # تجميع البيانات حسب التاريخ
            daily_stats = defaultdict(lambda: {
                "requests": 0,
                "input_tokens": 0,
                "output_tokens": 0,
                "total_tokens": 0,
                "total_cost": 0.0,
                "unique_users": set(),
                "balance_payments": 0,
                "debt_payments": 0
            })
            
            cutoff_date = datetime.now() - timedelta(days=days)
            
            for log_entry in usage_log:
                try:
                    log_date = datetime.strptime(log_entry.get("timestamp", ""), '%Y-%m-%d %H:%M:%S')
                    if log_date >= cutoff_date:
                        date_key = log_date.strftime('%Y-%m-%d')
                        
                        daily_stats[date_key]["requests"] += 1
                        daily_stats[date_key]["input_tokens"] += log_entry.get("input_tokens", 0)
                        daily_stats[date_key]["output_tokens"] += log_entry.get("output_tokens", 0)
                        daily_stats[date_key]["total_tokens"] += log_entry.get("total_tokens", 0)
                        daily_stats[date_key]["total_cost"] += log_entry.get("cost_in_exa", 0)
                        daily_stats[date_key]["unique_users"].add(log_entry.get("user_id"))
                        
                        if log_entry.get("payment_method") == "balance_deduction":
                            daily_stats[date_key]["balance_payments"] += 1
                        elif log_entry.get("payment_method") == "debt_creation":
                            daily_stats[date_key]["debt_payments"] += 1
                except:
                    continue
            
            # تحويل sets إلى أعداد
            formatted_stats = {}
            for date, stats in daily_stats.items():
                formatted_stats[date] = {
                    "requests": stats["requests"],
                    "input_tokens": stats["input_tokens"],
                    "output_tokens": stats["output_tokens"],
                    "total_tokens": stats["total_tokens"],
                    "total_cost": stats["total_cost"],
                    "unique_users": len(stats["unique_users"]),
                    "balance_payments": stats["balance_payments"],
                    "debt_payments": stats["debt_payments"]
                }
            
            return {
                "period_days": days,
                "daily_stats": formatted_stats,
                "last_updated": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على الإحصائيات اليومية: {e}")
            return {}
    
    def get_overall_usage_stats(self) -> Dict[str, Any]:
        """الحصول على الإحصائيات العامة للاستخدام"""
        try:
            usage_log = self.load_usage_log()
            
            if not usage_log:
                return {
                    "total_requests": 0,
                    "total_users": 0,
                    "total_tokens": 0,
                    "total_cost": 0.0,
                    "last_updated": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
            
            # حساب الإحصائيات العامة
            total_requests = len(usage_log)
            unique_users = set(log.get("user_id") for log in usage_log)
            total_input_tokens = sum(log.get("input_tokens", 0) for log in usage_log)
            total_output_tokens = sum(log.get("output_tokens", 0) for log in usage_log)
            total_tokens = total_input_tokens + total_output_tokens
            total_cost = sum(log.get("cost_in_exa", 0) for log in usage_log)
            
            # تصنيف حسب طريقة الدفع
            balance_payments = sum(1 for log in usage_log if log.get("payment_method") == "balance_deduction")
            debt_payments = sum(1 for log in usage_log if log.get("payment_method") == "debt_creation")
            
            # أول وآخر استخدام
            timestamps = [log.get("timestamp") for log in usage_log if log.get("timestamp")]
            first_usage = min(timestamps) if timestamps else None
            last_usage = max(timestamps) if timestamps else None
            
            return {
                "total_requests": total_requests,
                "total_users": len(unique_users),
                "total_input_tokens": total_input_tokens,
                "total_output_tokens": total_output_tokens,
                "total_tokens": total_tokens,
                "total_cost": total_cost,
                "balance_payments": balance_payments,
                "debt_payments": debt_payments,
                "avg_tokens_per_request": total_tokens / total_requests if total_requests > 0 else 0,
                "avg_cost_per_request": total_cost / total_requests if total_requests > 0 else 0,
                "first_usage": first_usage,
                "last_usage": last_usage,
                "last_updated": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على الإحصائيات العامة: {e}")
            return {}
    
    def get_top_users_by_usage(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        الحصول على أكثر المستخدمين استخداماً
        
        Args:
            limit: عدد المستخدمين المطلوب عرضهم
            
        Returns:
            list: قائمة بأكثر المستخدمين استخداماً
        """
        try:
            usage_log = self.load_usage_log()
            
            # تجميع البيانات حسب المستخدم
            user_stats = defaultdict(lambda: {
                "requests": 0,
                "total_tokens": 0,
                "total_cost": 0.0
            })
            
            for log_entry in usage_log:
                user_id = log_entry.get("user_id")
                if user_id:
                    user_stats[user_id]["requests"] += 1
                    user_stats[user_id]["total_tokens"] += log_entry.get("total_tokens", 0)
                    user_stats[user_id]["total_cost"] += log_entry.get("cost_in_exa", 0)
            
            # ترتيب المستخدمين حسب الاستخدام
            sorted_users = sorted(
                user_stats.items(),
                key=lambda x: x[1]["total_tokens"],
                reverse=True
            )[:limit]
            
            # إضافة معلومات المستخدم من المحفظة
            top_users = []
            for user_id, stats in sorted_users:
                wallet_data = self.wallet_manager.get_user_wallet(user_id)
                user_info = {
                    "user_id": user_id,
                    "user_name": wallet_data.get("user_name", "غير محدد") if wallet_data else "غير محدد",
                    "requests": stats["requests"],
                    "total_tokens": stats["total_tokens"],
                    "total_cost": stats["total_cost"],
                    "avg_tokens_per_request": stats["total_tokens"] / stats["requests"] if stats["requests"] > 0 else 0
                }
                top_users.append(user_info)
            
            return top_users
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على أكثر المستخدمين استخداماً: {e}")
            return []
    
    def detect_unusual_usage(self, threshold_multiplier: float = 3.0) -> List[Dict[str, Any]]:
        """
        اكتشاف الاستخدام غير الطبيعي
        
        Args:
            threshold_multiplier: مضاعف العتبة للكشف عن الاستخدام غير الطبيعي
            
        Returns:
            list: قائمة بالمستخدمين ذوي الاستخدام غير الطبيعي
        """
        try:
            usage_log = self.load_usage_log()
            
            # حساب متوسط الاستخدام لكل مستخدم
            user_daily_usage = defaultdict(lambda: defaultdict(int))
            
            for log_entry in usage_log:
                user_id = log_entry.get("user_id")
                timestamp = log_entry.get("timestamp", "")
                
                if user_id and timestamp:
                    try:
                        date = datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
                        user_daily_usage[user_id][date] += log_entry.get("total_tokens", 0)
                    except:
                        continue
            
            # حساب المتوسط العام
            all_daily_usage = []
            for user_usage in user_daily_usage.values():
                all_daily_usage.extend(user_usage.values())
            
            if not all_daily_usage:
                return []
            
            avg_daily_usage = sum(all_daily_usage) / len(all_daily_usage)
            threshold = avg_daily_usage * threshold_multiplier
            
            # البحث عن الاستخدام غير الطبيعي
            unusual_users = []
            for user_id, daily_usage in user_daily_usage.items():
                max_daily_usage = max(daily_usage.values()) if daily_usage else 0
                
                if max_daily_usage > threshold:
                    wallet_data = self.wallet_manager.get_user_wallet(user_id)
                    unusual_users.append({
                        "user_id": user_id,
                        "user_name": wallet_data.get("user_name", "غير محدد") if wallet_data else "غير محدد",
                        "max_daily_tokens": max_daily_usage,
                        "threshold": threshold,
                        "ratio": max_daily_usage / avg_daily_usage if avg_daily_usage > 0 else 0
                    })
            
            # ترتيب حسب النسبة
            unusual_users.sort(key=lambda x: x["ratio"], reverse=True)
            
            return unusual_users
            
        except Exception as e:
            logger.error(f"خطأ في اكتشاف الاستخدام غير الطبيعي: {e}")
            return []
