#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام التقارير المتقدمة لفوترة التوكن
يوفر تقارير مفصلة وتحليلات متقدمة للاستخدام والتكاليف
"""

import os
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from collections import defaultdict
import sys

# إضافة مسار المكتبات المشتركة
current_dir = os.path.dirname(os.path.abspath(__file__))
shared_dir = os.path.dirname(current_dir)
sys.path.insert(0, shared_dir)

from database.wallet_manager import WalletManager
from .usage_tracker import UsageTracker

# إعداد نظام السجلات
logger = logging.getLogger(__name__)

class AdvancedReportsManager:
    """مدير التقارير المتقدمة لنظام فوترة التوكن"""
    
    def __init__(self):
        """تهيئة مدير التقارير"""
        self.wallet_manager = WalletManager()
        self.usage_tracker = UsageTracker()
        
        # مجلد التقارير
        self.reports_dir = os.path.join(os.path.dirname(__file__), "..", "reports")
        os.makedirs(self.reports_dir, exist_ok=True)
        
        logger.info("📊 تم تهيئة مدير التقارير المتقدمة")
    
    def generate_financial_report(self, days: int = 30) -> Dict[str, Any]:
        """
        إنشاء تقرير مالي شامل
        
        Args:
            days: عدد الأيام للتقرير
            
        Returns:
            dict: التقرير المالي
        """
        try:
            # الحصول على البيانات
            usage_log = self.usage_tracker.load_usage_log()
            wallet_stats = self.wallet_manager.get_wallet_statistics()
            debt_stats = self.wallet_manager.get_debt_statistics()
            
            # تصفية البيانات للفترة المحددة
            cutoff_date = datetime.now() - timedelta(days=days)
            period_usage = []
            
            for log_entry in usage_log:
                try:
                    log_date = datetime.strptime(log_entry.get("timestamp", ""), '%Y-%m-%d %H:%M:%S')
                    if log_date >= cutoff_date:
                        period_usage.append(log_entry)
                except:
                    continue
            
            # حساب الإحصائيات المالية
            total_revenue = sum(log.get("cost_in_exa", 0) for log in period_usage)
            total_tokens_consumed = sum(log.get("total_tokens", 0) for log in period_usage)
            total_requests = len(period_usage)
            
            # تحليل طرق الدفع
            balance_payments = [log for log in period_usage if log.get("payment_method") == "balance_deduction"]
            debt_payments = [log for log in period_usage if log.get("payment_method") == "debt_creation"]
            
            balance_revenue = sum(log.get("cost_in_exa", 0) for log in balance_payments)
            debt_amount = sum(log.get("cost_in_exa", 0) for log in debt_payments)
            
            # تحليل المستخدمين
            unique_users = set(log.get("user_id") for log in period_usage)
            avg_revenue_per_user = total_revenue / len(unique_users) if unique_users else 0
            
            # تحليل يومي
            daily_revenue = defaultdict(float)
            daily_requests = defaultdict(int)
            
            for log in period_usage:
                try:
                    date = datetime.strptime(log.get("timestamp", ""), '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
                    daily_revenue[date] += log.get("cost_in_exa", 0)
                    daily_requests[date] += 1
                except:
                    continue
            
            # أفضل الأيام
            best_day = max(daily_revenue.items(), key=lambda x: x[1]) if daily_revenue else ("", 0)
            
            report = {
                "report_type": "financial",
                "period_days": days,
                "generated_at": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                
                # الإحصائيات الأساسية
                "total_revenue": total_revenue,
                "total_tokens_consumed": total_tokens_consumed,
                "total_requests": total_requests,
                "unique_users": len(unique_users),
                
                # تحليل الدفع
                "balance_revenue": balance_revenue,
                "debt_amount": debt_amount,
                "balance_percentage": (balance_revenue / total_revenue * 100) if total_revenue > 0 else 0,
                "debt_percentage": (debt_amount / total_revenue * 100) if total_revenue > 0 else 0,
                
                # متوسطات
                "avg_revenue_per_user": avg_revenue_per_user,
                "avg_revenue_per_request": total_revenue / total_requests if total_requests > 0 else 0,
                "avg_tokens_per_request": total_tokens_consumed / total_requests if total_requests > 0 else 0,
                
                # تحليل يومي
                "daily_avg_revenue": total_revenue / days if days > 0 else 0,
                "daily_avg_requests": total_requests / days if days > 0 else 0,
                "best_day": {"date": best_day[0], "revenue": best_day[1]},
                
                # حالة المحافظ
                "total_wallets": wallet_stats.get("total_wallets", 0),
                "total_balance": wallet_stats.get("total_balance", 0),
                "total_debt": debt_stats.get("total_debt_amount", 0),
                "users_with_debt": debt_stats.get("total_users_with_debt", 0),
                
                # نسب مهمة
                "debt_to_revenue_ratio": (debt_stats.get("total_debt_amount", 0) / total_revenue * 100) if total_revenue > 0 else 0,
                "active_users_percentage": (len(unique_users) / wallet_stats.get("total_wallets", 1) * 100)
            }
            
            return report
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء التقرير المالي: {e}")
            return {"error": str(e)}
    
    def generate_usage_analysis_report(self, days: int = 7) -> Dict[str, Any]:
        """
        إنشاء تقرير تحليل الاستخدام
        
        Args:
            days: عدد الأيام للتحليل
            
        Returns:
            dict: تقرير تحليل الاستخدام
        """
        try:
            usage_log = self.usage_tracker.load_usage_log()
            
            # تصفية البيانات
            cutoff_date = datetime.now() - timedelta(days=days)
            period_usage = []
            
            for log_entry in usage_log:
                try:
                    log_date = datetime.strptime(log_entry.get("timestamp", ""), '%Y-%m-%d %H:%M:%S')
                    if log_date >= cutoff_date:
                        period_usage.append(log_entry)
                except:
                    continue
            
            # تحليل الاستخدام حسب الساعة
            hourly_usage = defaultdict(lambda: {"requests": 0, "tokens": 0, "cost": 0.0})
            
            for log in period_usage:
                try:
                    hour = datetime.strptime(log.get("timestamp", ""), '%Y-%m-%d %H:%M:%S').hour
                    hourly_usage[hour]["requests"] += 1
                    hourly_usage[hour]["tokens"] += log.get("total_tokens", 0)
                    hourly_usage[hour]["cost"] += log.get("cost_in_exa", 0)
                except:
                    continue
            
            # أكثر الساعات نشاطاً
            peak_hour = max(hourly_usage.items(), key=lambda x: x[1]["requests"]) if hourly_usage else (0, {"requests": 0})
            
            # تحليل المستخدمين
            user_analysis = defaultdict(lambda: {"requests": 0, "tokens": 0, "cost": 0.0})
            
            for log in period_usage:
                user_id = log.get("user_id")
                if user_id:
                    user_analysis[user_id]["requests"] += 1
                    user_analysis[user_id]["tokens"] += log.get("total_tokens", 0)
                    user_analysis[user_id]["cost"] += log.get("cost_in_exa", 0)
            
            # ترتيب المستخدمين
            top_users_by_requests = sorted(user_analysis.items(), key=lambda x: x[1]["requests"], reverse=True)[:5]
            top_users_by_tokens = sorted(user_analysis.items(), key=lambda x: x[1]["tokens"], reverse=True)[:5]
            top_users_by_cost = sorted(user_analysis.items(), key=lambda x: x[1]["cost"], reverse=True)[:5]
            
            # تحليل أنماط الاستخدام
            request_sizes = [log.get("total_tokens", 0) for log in period_usage]
            avg_request_size = sum(request_sizes) / len(request_sizes) if request_sizes else 0
            
            small_requests = len([r for r in request_sizes if r < 100])
            medium_requests = len([r for r in request_sizes if 100 <= r < 500])
            large_requests = len([r for r in request_sizes if r >= 500])
            
            report = {
                "report_type": "usage_analysis",
                "period_days": days,
                "generated_at": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                
                # إحصائيات عامة
                "total_requests": len(period_usage),
                "total_tokens": sum(log.get("total_tokens", 0) for log in period_usage),
                "total_users": len(user_analysis),
                "avg_request_size": avg_request_size,
                
                # تحليل الساعات
                "peak_hour": {"hour": peak_hour[0], "requests": peak_hour[1]["requests"]},
                "hourly_distribution": dict(hourly_usage),
                
                # أحجام الطلبات
                "request_size_distribution": {
                    "small": {"count": small_requests, "percentage": small_requests / len(request_sizes) * 100 if request_sizes else 0},
                    "medium": {"count": medium_requests, "percentage": medium_requests / len(request_sizes) * 100 if request_sizes else 0},
                    "large": {"count": large_requests, "percentage": large_requests / len(request_sizes) * 100 if request_sizes else 0}
                },
                
                # أفضل المستخدمين
                "top_users": {
                    "by_requests": [(uid, data["requests"]) for uid, data in top_users_by_requests],
                    "by_tokens": [(uid, data["tokens"]) for uid, data in top_users_by_tokens],
                    "by_cost": [(uid, data["cost"]) for uid, data in top_users_by_cost]
                }
            }
            
            return report
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء تقرير تحليل الاستخدام: {e}")
            return {"error": str(e)}
    
    def generate_debt_analysis_report(self) -> Dict[str, Any]:
        """
        إنشاء تقرير تحليل الديون
        
        Returns:
            dict: تقرير تحليل الديون
        """
        try:
            debt_stats = self.wallet_manager.get_debt_statistics()
            wallets_data = self.wallet_manager.load_wallets_data()
            
            # تحليل الديون
            debt_analysis = {
                "users_with_debt": [],
                "debt_by_amount": {"small": 0, "medium": 0, "large": 0},
                "debt_age_analysis": {"new": 0, "old": 0, "very_old": 0},
                "total_potential_loss": 0.0
            }
            
            current_time = datetime.now()
            
            for wallet_number, wallet_data in wallets_data.items():
                if wallet_data.get("has_debt", False):
                    debt_amount = wallet_data.get("debt_amount", 0.0)
                    debt_created_at = wallet_data.get("debt_created_at")
                    
                    # تحليل المبلغ
                    if debt_amount < 0.1:
                        debt_analysis["debt_by_amount"]["small"] += 1
                    elif debt_amount < 0.5:
                        debt_analysis["debt_by_amount"]["medium"] += 1
                    else:
                        debt_analysis["debt_by_amount"]["large"] += 1
                    
                    # تحليل العمر
                    if debt_created_at:
                        try:
                            debt_date = datetime.strptime(debt_created_at, '%Y-%m-%d %H:%M:%S')
                            days_old = (current_time - debt_date).days
                            
                            if days_old < 7:
                                debt_analysis["debt_age_analysis"]["new"] += 1
                            elif days_old < 30:
                                debt_analysis["debt_age_analysis"]["old"] += 1
                            else:
                                debt_analysis["debt_age_analysis"]["very_old"] += 1
                        except:
                            pass
                    
                    # إضافة للقائمة
                    debt_analysis["users_with_debt"].append({
                        "wallet_number": wallet_number,
                        "user_name": wallet_data.get("user_name", "غير محدد"),
                        "debt_amount": debt_amount,
                        "debt_created_at": debt_created_at,
                        "days_old": (current_time - datetime.strptime(debt_created_at, '%Y-%m-%d %H:%M:%S')).days if debt_created_at else 0
                    })
                    
                    debt_analysis["total_potential_loss"] += debt_amount
            
            # ترتيب حسب المبلغ
            debt_analysis["users_with_debt"].sort(key=lambda x: x["debt_amount"], reverse=True)
            
            report = {
                "report_type": "debt_analysis",
                "generated_at": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                
                # إحصائيات أساسية
                "total_users_with_debt": len(debt_analysis["users_with_debt"]),
                "total_debt_amount": debt_stats.get("total_debt_amount", 0),
                "average_debt": debt_stats.get("average_debt_amount", 0),
                "total_potential_loss": debt_analysis["total_potential_loss"],
                
                # تحليل المبالغ
                "debt_distribution": debt_analysis["debt_by_amount"],
                
                # تحليل العمر
                "debt_age_distribution": debt_analysis["debt_age_analysis"],
                
                # قائمة المدينين
                "users_with_debt": debt_analysis["users_with_debt"][:10],  # أول 10
                
                # توصيات
                "recommendations": self._generate_debt_recommendations(debt_analysis)
            }
            
            return report
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء تقرير تحليل الديون: {e}")
            return {"error": str(e)}
    
    def _generate_debt_recommendations(self, debt_analysis: Dict) -> List[str]:
        """إنشاء توصيات بناءً على تحليل الديون"""
        recommendations = []
        
        total_debt = debt_analysis["total_potential_loss"]
        very_old_debts = debt_analysis["debt_age_analysis"]["very_old"]
        large_debts = debt_analysis["debt_by_amount"]["large"]
        
        if total_debt > 10:
            recommendations.append("إجمالي الديون مرتفع، يُنصح بمراجعة سياسة الائتمان")
        
        if very_old_debts > 0:
            recommendations.append(f"يوجد {very_old_debts} دين قديم (أكثر من 30 يوم)، يُنصح بالمتابعة")
        
        if large_debts > 5:
            recommendations.append("عدد كبير من الديون الكبيرة، يُنصح بتقليل الحد الأقصى")
        
        if not recommendations:
            recommendations.append("وضع الديون جيد، لا توجد مشاكل كبيرة")
        
        return recommendations
    
    def save_report_to_file(self, report: Dict[str, Any], filename: str = None) -> str:
        """
        حفظ التقرير في ملف
        
        Args:
            report: بيانات التقرير
            filename: اسم الملف (اختياري)
            
        Returns:
            str: مسار الملف المحفوظ
        """
        try:
            if not filename:
                report_type = report.get("report_type", "report")
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"{report_type}_{timestamp}.json"
            
            filepath = os.path.join(self.reports_dir, filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            logger.info(f"✅ تم حفظ التقرير: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"خطأ في حفظ التقرير: {e}")
            return ""
    
    def generate_comprehensive_report(self, days: int = 30) -> Dict[str, Any]:
        """
        إنشاء تقرير شامل يجمع جميع التحليلات
        
        Args:
            days: عدد الأيام للتقرير
            
        Returns:
            dict: التقرير الشامل
        """
        try:
            financial_report = self.generate_financial_report(days)
            usage_report = self.generate_usage_analysis_report(days)
            debt_report = self.generate_debt_analysis_report()
            
            comprehensive_report = {
                "report_type": "comprehensive",
                "period_days": days,
                "generated_at": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                
                "financial_analysis": financial_report,
                "usage_analysis": usage_report,
                "debt_analysis": debt_report,
                
                "executive_summary": {
                    "total_revenue": financial_report.get("total_revenue", 0),
                    "total_requests": financial_report.get("total_requests", 0),
                    "active_users": financial_report.get("unique_users", 0),
                    "total_debt": debt_report.get("total_debt_amount", 0),
                    "debt_users": debt_report.get("total_users_with_debt", 0),
                    "system_health": "جيد" if debt_report.get("total_debt_amount", 0) < 5 else "يحتاج مراجعة"
                }
            }
            
            return comprehensive_report
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء التقرير الشامل: {e}")
            return {"error": str(e)}
