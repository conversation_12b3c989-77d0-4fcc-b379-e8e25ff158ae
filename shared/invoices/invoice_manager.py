#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مدير الفواتير - Invoice Manager
يدير إنشاء وحفظ الفواتير للمعاملات المالية
"""

import os
import json
import qrcode
import warnings
from datetime import datetime
from typing import Dict, Any, Optional, Tuple
from PIL import Image as PILImage
import logging

# مكتبات ReportLab
from reportlab.lib.pagesizes import A5
from reportlab.lib.units import cm, inch
from reportlab.lib.colors import HexColor
from reportlab.platypus import BaseDocTemplate, Frame, PageTemplate, Paragraph, Table, TableStyle, Image, Spacer, Flowable
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.graphics.shapes import Drawing, Rect, Line
from reportlab.graphics import renderPDF

# تجاهل تحذيرات ReportLab المتعلقة بالخطوط العربية
warnings.filterwarnings("ignore", message=".*font.*")
warnings.filterwarnings("ignore", message=".*glyph.*")

# مكتبات معالجة النصوص العربية
try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_SUPPORT = True
except ImportError:
    ARABIC_SUPPORT = False

# استيراد نظام الرسائل الموحد للفواتير
try:
    import sys
    # إضافة مسار مجلد data_processing
    data_processing_path = os.path.join(os.path.dirname(__file__), '..', 'data_processing')
    if data_processing_path not in sys.path:
        sys.path.append(data_processing_path)
    from invoice_messages import invoice_message_manager
except ImportError as e:
    invoice_message_manager = None
    print(f"تحذير: لا يمكن تحميل نظام الرسائل الموحد: {e}")

# إعداد نظام السجلات
logger = logging.getLogger(__name__)

class ColoredPageTemplate(PageTemplate):
    """قالب صفحة مع خلفية ملونة"""
    def __init__(self, id, frames, pagesize, background_color='#f4f5f1'):
        PageTemplate.__init__(self, id, frames, pagesize=pagesize)
        self.background_color = background_color

    def beforeDrawPage(self, canvas, doc):
        """رسم الخلفية الملونة قبل المحتوى"""
        canvas.saveState()
        canvas.setFillColor(HexColor(self.background_color))
        canvas.rect(0, 0, self.pagesize[0], self.pagesize[1], fill=1)
        canvas.restoreState()

class QRFrameFlowable(Flowable):
    """عنصر لرسم إطار QR مع زوايا مدورة مع إمكانية التحكم في التباعد"""
    def __init__(self, qr_path, frame_size=100, qr_size=90, border_width=1.5, border_color='#F0522E',
                 corner_radius=4, background_color='#f4f5f1', internal_padding_x=0, internal_padding_y=0):
        """
        تهيئة عنصر الإطار والـ QR

        Args:
            qr_path: مسار ملف QR Code
            frame_size: حجم الإطار الخارجي
            qr_size: حجم QR Code
            border_width: سماكة الإطار
            border_color: لون الإطار
            corner_radius: نصف قطر الزوايا المدورة
            background_color: لون خلفية الإطار
            internal_padding_x: التباعد الداخلي الأفقي بين الإطار والـ QR (موجب = توسيط أكثر، سالب = تقريب أكثر)
            internal_padding_y: التباعد الداخلي العمودي بين الإطار والـ QR (موجب = توسيط أكثر، سالب = تقريب أكثر)
        """
        self.qr_path = qr_path
        self.frame_size = frame_size
        self.qr_size = qr_size
        self.border_width = border_width
        self.border_color = border_color
        self.corner_radius = corner_radius
        self.background_color = background_color
        self.internal_padding_x = internal_padding_x  # التحكم الداخلي الأفقي
        self.internal_padding_y = internal_padding_y  # التحكم الداخلي العمودي
        self.width = frame_size
        self.height = frame_size

    def draw(self):
        """رسم الإطار والـ QR Code مع التباعد المخصص"""
        canvas = self.canv
        canvas.saveState()

        # رسم الإطار مع الزوايا المدورة
        canvas.setStrokeColor(HexColor(self.border_color))
        canvas.setLineWidth(self.border_width)
        canvas.setFillColor(HexColor(self.background_color))  # خلفية قابلة للتخصيص

        # رسم مستطيل مع زوايا مدورة
        canvas.roundRect(0, 0, self.frame_size, self.frame_size, self.corner_radius, stroke=1, fill=1)

        # رسم QR Code مع التباعد المخصص
        if self.qr_path and os.path.exists(self.qr_path):
            # حساب الموقع الأساسي (الوسط)
            base_qr_x = (self.frame_size - self.qr_size) / 2
            base_qr_y = (self.frame_size - self.qr_size) / 2

            # تطبيق التباعد الداخلي المخصص
            qr_x = base_qr_x + self.internal_padding_x
            qr_y = base_qr_y + self.internal_padding_y

            # التأكد من أن QR لا يخرج من حدود الإطار
            qr_x = max(0, min(qr_x, self.frame_size - self.qr_size))
            qr_y = max(0, min(qr_y, self.frame_size - self.qr_size))

            canvas.drawImage(self.qr_path, qr_x, qr_y, width=self.qr_size, height=self.qr_size, mask='auto')

        canvas.restoreState()

class InvoiceManager:
    """مدير الفواتير"""
    
    def __init__(self):
        """تهيئة مدير الفواتير"""
        self.base_dir = os.path.dirname(os.path.abspath(__file__))
        self.generated_dir = os.path.join(self.base_dir, "generated")
        self.pending_file = os.path.join(self.base_dir, "pending_invoices.json")
        self.assets_dir = os.path.join(os.path.dirname(self.base_dir), "assets")
        self.fonts_dir = os.path.join(os.path.dirname(self.base_dir), "fonts")

        # إنشاء المجلدات الفرعية المنظمة
        self.qr_dir = os.path.join(self.generated_dir, 'QR Code')
        self.pdf_dir = os.path.join(self.generated_dir, 'PDF')
        self.img_dir = os.path.join(self.generated_dir, 'Image')

        # إنشاء جميع المجلدات إذا لم تكن موجودة
        os.makedirs(self.generated_dir, exist_ok=True)
        os.makedirs(self.qr_dir, exist_ok=True)
        os.makedirs(self.pdf_dir, exist_ok=True)
        os.makedirs(self.img_dir, exist_ok=True)

        # تحميل الخطوط العربية إذا كانت متوفرة
        self._load_arabic_fonts()

        # إعدادات التحكم في الإطار والـ QR
        self._init_frame_settings()
        
    def _load_arabic_fonts(self):
        """تحميل الخطوط العربية"""
        try:
            # تحميل خطوط مختلفة بترتيب الأولوية
            font_options = [
                # خطوط Noto Sans Arabic (الأولوية الأولى - أكثر توافقاً)
                {
                    'NotoSansArabic-Regular': 'NotoSansArabic-Regular.ttf',
                    'NotoSansArabic-Bold': 'NotoSansArabic-Bold.ttf',
                },
                # خطوط Amiri (بديل)
                {
                    'Amiri-Regular': 'Amiri-Regular.ttf',
                    'Amiri-Bold': 'Amiri-Bold.ttf',
                }
            ]

            self.loaded_fonts = {}
            self.font_paths = {}
            self.default_font = None
            self.bold_font = None

            # جرب كل مجموعة خطوط
            for font_group in font_options:
                group_loaded = True
                temp_paths = {}
                
                for font_name, font_file in font_group.items():
                    font_path = os.path.join(self.fonts_dir, font_file)
                    if os.path.exists(font_path):
                        try:
                            # تسجيل الخط في ReportLab
                            pdfmetrics.registerFont(TTFont(font_name, font_path))
                            temp_paths[font_name] = font_path
                            self.loaded_fonts[font_name] = True
                            logger.info(f"تم تحميل الخط {font_name}")
                        except Exception as e:
                            logger.warning(f"فشل في تحميل الخط {font_name}: {e}")
                            group_loaded = False
                            break
                    else:
                        group_loaded = False
                        break
                
                # إذا تم العثور على مجموعة كاملة، استخدمها
                if group_loaded and temp_paths:
                    self.font_paths.update(temp_paths)
                    font_names = list(font_group.keys())
                    self.default_font = font_names[0]
                    self.bold_font = font_names[1] if len(font_names) > 1 else font_names[0]
                    logger.info(f"تم تعيين مجموعة الخطوط: {self.default_font}")
                    break

            if not self.default_font:
                self.default_font = 'Helvetica'
                self.bold_font = 'Helvetica-Bold'
                logger.warning("لم يتم العثور على خطوط عربية، سيتم استخدام الخط الافتراضي")

        except Exception as e:
            logger.error(f"خطأ في تحميل الخطوط العربية: {e}")
            self.default_font = 'Helvetica'
            self.bold_font = 'Helvetica-Bold'

    def _init_frame_settings(self):
        """تهيئة إعدادات التحكم في الإطار والـ QR"""
        # إعدادات الحجم (بالبكسل)
        self.frame_size_pixels = 100
        self.qr_size_pixels = 90

        # إعدادات الألوان
        self.frame_border_color = '#F0522E'
        self.frame_background_color = '#f4f5f1'

        # إعدادات الحدود والزوايا (بالبكسل)
        self.frame_border_width = 2.0
        self.frame_corner_radius_pixels = 4

        # التحكم الداخلي - التباعد بين الإطار والـ QR (بالنقاط)
        self.qr_internal_padding_x = 0  # أفقي
        self.qr_internal_padding_y = 0  # عمودي

        # التحكم الخارجي - موقع الإطار والـ QR معاً في الجدول (بالنقاط)
        self.frame_external_offset_x = -30  # إزاحة أفقية للإطار كاملاً
        self.frame_external_offset_y = 0  # إزاحة عمودية للإطار كاملاً

        # إعدادات التباعد في الجدول
        self.frame_table_padding_horizontal = 5
        self.frame_table_padding_vertical = 10

        # إعدادات التحكم في عنوان الفاتورة
        self._init_invoice_title_settings()

        # إعدادات التحكم في الشعار
        self._init_logo_settings()

        # إعدادات تخطيط الرأس
        self._init_header_layout_settings()

        # إعدادات التحكم في الملاحظات
        self._init_notes_settings()

    def _init_notes_settings(self):
        """تهيئة إعدادات التحكم في الملاحظات"""
        # إعدادات الموقع والتباعد
        self.notes_position = 'right'  # 'center', 'right', 'left'
        self.notes_internal_alignment = TA_RIGHT  # TA_CENTER, TA_RIGHT, TA_LEFT

        # التباعد العمودي والأفقي
        self.notes_top_spacing = 0.5  # المسافة قبل الملاحظات (بالإنش)
        self.notes_bottom_spacing = 0.0  # المسافة بعد الملاحظات (بالإنش)
        self.notes_between_spacing = 0.05  # المسافة بين الملاحظات (بالإنش)
        self.notes_left_indent = 0  # الإزاحة من اليسار (بالنقاط)
        self.notes_right_indent = 7.5  # الإزاحة من اليمين (بالنقاط)

        # إعدادات النص
        self.notes_font_size = 9
        self.notes_text_color = '#7F8C8D'
        self.notes_leading = 12  # المسافة بين الأسطر

        # إعدادات الخلفية والحدود (اختيارية)
        self.notes_background_color = None  # None أو لون hex
        self.notes_border_color = None  # None أو لون hex
        self.notes_border_width = 0  # سماكة الحدود
        self.notes_padding = 0  # التباعد الداخلي حول النص

    def _init_invoice_title_settings(self):
        """تهيئة إعدادات التحكم في عنوان الفاتورة"""
        # إعدادات الموقع والمحاذاة
        self.invoice_title_position = 'right'  # 'center', 'right', 'left'
        self.invoice_title_internal_alignment = TA_RIGHT  # TA_CENTER, TA_RIGHT, TA_LEFT

        # التباعد العمودي والأفقي
        self.invoice_title_vertical_spacing = 0.2  # المسافة بين السطرين (بالإنش)
        self.invoice_title_horizontal_offset = 0  # الإزاحة الأفقية (بالنقاط)
        self.invoice_title_vertical_offset = 0  # الإزاحة العمودية (بالنقاط)
        self.invoice_title_left_indent = 0  # الإزاحة من اليسار (بالنقاط)
        self.invoice_title_right_indent = -7  # الإزاحة من اليمين (بالنقاط)

        # إعدادات النص والألوان
        self.invoice_title_line1_text = "فـاتـورة رصـيـد"
        self.invoice_title_line2_text = "إلـكــتــرونــيــة"
        self.invoice_title_line1_font_size = 15
        self.invoice_title_line2_font_size = 15
        self.invoice_title_line1_color = '#2A3C73'
        self.invoice_title_line2_color = '#F0522E'
        self.invoice_title_leading = 14  # المسافة بين الأسطر

    def _init_logo_settings(self):
        """تهيئة إعدادات التحكم في الشعار"""
        # إعدادات الموقع والتباعد
        self.logo_position = 'center'  # 'center', 'right', 'left'
        self.logo_horizontal_offset = 0  # الإزاحة الأفقية (بالنقاط)
        self.logo_vertical_offset = 0  # الإزاحة العمودية (بالنقاط)
        self.logo_left_indent = 0  # الإزاحة من اليسار (بالنقاط)
        self.logo_right_indent = 0  # الإزاحة من اليمين (بالنقاط)
        self.logo_top_padding = 10  # التباعد العلوي (بالنقاط)
        self.logo_bottom_padding = 10  # التباعد السفلي (بالنقاط)

        # إعدادات الحجم
        self.logo_width = 1.4  # العرض (بالإنش)
        self.logo_height = 1.0703  # الارتفاع (بالإنش)

    def _init_header_layout_settings(self):
        """تهيئة إعدادات تخطيط الرأس"""
        # إعدادات عروض الأعمدة (بدلاً من التقسيم المتساوي)
        self.header_qr_column_width = 1.5  # عرض عمود QR (بالإنش)
        self.header_logo_column_width = 2.0  # عرض عمود الشعار (بالإنش)
        self.header_title_column_width = 1.5  # عرض عمود العنوان (بالإنش)

        # إعدادات المحاذاة
        self.header_qr_alignment = 'CENTER'
        self.header_logo_alignment = 'CENTER'
        self.header_title_alignment = 'RIGHT'

    def process_arabic_text(self, text: str) -> str:
        """معالجة النص العربي - إصلاح الاتجاه والتشكيل"""
        if not ARABIC_SUPPORT or not text:
            return text

        try:
            # تنظيف النص أولاً
            cleaned_text = text.strip()
            if not cleaned_text:
                return text
            
            # التحقق من وجود نص عربي
            has_arabic = any('\u0600' <= char <= '\u06FF' for char in cleaned_text)
            if not has_arabic:
                return cleaned_text
            
            # معالجة خاصة للنصوص المختلطة (عربي + أرقام)
            # 1. إعادة تشكيل الحروف العربية المتصلة
            reshaped_text = arabic_reshaper.reshape(cleaned_text)
            # 2. تطبيق خوارزمية BiDi مع إعدادات محسنة للنصوص المختلطة
            bidi_text = get_display(reshaped_text, base_dir='R')
            return bidi_text
        except Exception as e:
            logger.warning(f"فشل في معالجة النص العربي: {e}")
            # إذا فشلت المعالجة، أرجع النص كما هو
            return text
    
    def format_currency_text(self, amount: float, currency: str = "إكسا") -> str:
        """تنسيق النص المالي بشكل صحيح"""
        try:
            # تنسيق الرقم
            formatted_amount = f"{amount:.0f}"
            # تكوين النص بالترتيب الصحيح: رقم + مسافة + عملة
            text = f"{formatted_amount} {currency}"
            # معالجة النص العربي
            return self.process_arabic_text(text)
        except Exception as e:
            logger.warning(f"فشل في تنسيق النص المالي: {e}")
            return f"{amount:.0f} {currency}"

    def format_token_text(self, token_amount: float) -> str:
        """تنسيق التوكن بالآلاف والملايين بشكل ذكي"""
        try:
            # تحويل القيمة إلى آلاف أولاً (لأن 524.288 هي بالآلاف)
            token_in_thousands = token_amount

            if token_in_thousands >= 1000000:  # مليون ألف أو أكثر (مليار)
                billions = token_in_thousands / 1000000
                formatted_text = f"{billions:.1f} مليار توكن"
            elif token_in_thousands >= 1000:  # ألف ألف أو أكثر (مليون)
                millions = token_in_thousands / 1000
                formatted_text = f"{millions:.1f} مليون توكن"
            else:
                formatted_text = f"{token_in_thousands:.1f} ألف توكن"

            return self.process_arabic_text(formatted_text)
        except Exception as e:
            logger.warning(f"فشل في تنسيق التوكن: {e}")
            return f"{token_amount:.1f} ألف توكن"

    def process_mixed_text(self, text: str) -> str:
        """معالجة النصوص المختلطة بطريقة بسيطة"""
        # استخدام نفس المعالجة البسيطة للجميع
        return self.process_arabic_text(text)
    
    def generate_invoice_number(self) -> str:
        """توليد رقم فاتورة فريد بالشكل الجديد (808xxxxxxx)"""
        import random
        return f"808{random.randint(1000000, 9999999)}"

    # ==================== دوال التحكم في إعدادات الإطار والـ QR ====================

    def set_frame_size(self, frame_size_pixels: int, qr_size_pixels: int = None):
        """
        تعيين حجم الإطار والـ QR

        Args:
            frame_size_pixels: حجم الإطار بالبكسل
            qr_size_pixels: حجم QR بالبكسل (إذا لم يُحدد، سيكون أصغر من الإطار بـ 10 بكسل)
        """
        self.frame_size_pixels = frame_size_pixels
        if qr_size_pixels is None:
            self.qr_size_pixels = frame_size_pixels - 10
        else:
            self.qr_size_pixels = qr_size_pixels

        logger.info(f"تم تعيين حجم الإطار: {self.frame_size_pixels}px، حجم QR: {self.qr_size_pixels}px")

    def set_qr_internal_position(self, horizontal_offset: float = 0, vertical_offset: float = 0):
        """
        تعيين موقع QR داخل الإطار (التحكم الداخلي)

        Args:
            horizontal_offset: الإزاحة الأفقية بالنقاط (موجب = يمين، سالب = يسار)
            vertical_offset: الإزاحة العمودية بالنقاط (موجب = أعلى، سالب = أسفل)
        """
        self.qr_internal_padding_x = horizontal_offset
        self.qr_internal_padding_y = vertical_offset

        logger.info(f"تم تعيين موقع QR الداخلي: أفقي={horizontal_offset}, عمودي={vertical_offset}")

    def set_frame_external_position(self, horizontal_offset: float = 0, vertical_offset: float = 0):
        """
        تعيين موقع الإطار والـ QR معاً (التحكم الخارجي)

        Args:
            horizontal_offset: الإزاحة الأفقية للإطار كاملاً بالنقاط (موجب = يمين، سالب = يسار)
            vertical_offset: الإزاحة العمودية للإطار كاملاً بالنقاط (موجب = أعلى، سالب = أسفل)
        """
        self.frame_external_offset_x = horizontal_offset
        self.frame_external_offset_y = vertical_offset

        logger.info(f"تم تعيين موقع الإطار الخارجي: أفقي={horizontal_offset}, عمودي={vertical_offset}")

    def set_frame_colors(self, border_color: str = None, background_color: str = None):
        """
        تعيين ألوان الإطار

        Args:
            border_color: لون الحدود (hex color)
            background_color: لون الخلفية (hex color)
        """
        if border_color:
            self.frame_border_color = border_color
        if background_color:
            self.frame_background_color = background_color

        logger.info(f"تم تعيين ألوان الإطار: حدود={self.frame_border_color}, خلفية={self.frame_background_color}")

    def set_frame_border(self, border_width: float = None, corner_radius_pixels: int = None):
        """
        تعيين خصائص حدود الإطار

        Args:
            border_width: سماكة الحدود
            corner_radius_pixels: نصف قطر الزوايا بالبكسل
        """
        if border_width is not None:
            self.frame_border_width = border_width
        if corner_radius_pixels is not None:
            self.frame_corner_radius_pixels = corner_radius_pixels

        logger.info(f"تم تعيين حدود الإطار: سماكة={self.frame_border_width}, زوايا={self.frame_corner_radius_pixels}px")

    def set_frame_table_padding(self, horizontal_padding: int = None, vertical_padding: int = None):
        """
        تعيين التباعد حول الإطار في الجدول

        Args:
            horizontal_padding: التباعد الأفقي
            vertical_padding: التباعد العمودي
        """
        if horizontal_padding is not None:
            self.frame_table_padding_horizontal = horizontal_padding
        if vertical_padding is not None:
            self.frame_table_padding_vertical = vertical_padding

        logger.info(f"تم تعيين تباعد الجدول: أفقي={self.frame_table_padding_horizontal}, عمودي={self.frame_table_padding_vertical}")

    def get_frame_settings(self) -> Dict[str, Any]:
        """
        الحصول على جميع إعدادات الإطار الحالية

        Returns:
            dict: قاموس يحتوي على جميع الإعدادات
        """
        return {
            'frame_size_pixels': self.frame_size_pixels,
            'qr_size_pixels': self.qr_size_pixels,
            'border_color': self.frame_border_color,
            'background_color': self.frame_background_color,
            'border_width': self.frame_border_width,
            'corner_radius_pixels': self.frame_corner_radius_pixels,
            'qr_internal_padding_x': self.qr_internal_padding_x,
            'qr_internal_padding_y': self.qr_internal_padding_y,
            'frame_external_offset_x': self.frame_external_offset_x,
            'frame_external_offset_y': self.frame_external_offset_y,
            'table_padding_horizontal': self.frame_table_padding_horizontal,
            'table_padding_vertical': self.frame_table_padding_vertical
        }

    def get_all_settings(self) -> Dict[str, Any]:
        """
        الحصول على جميع الإعدادات (الإطار والملاحظات)

        Returns:
            dict: قاموس يحتوي على جميع الإعدادات
        """
        frame_settings = self.get_frame_settings()
        notes_settings = self.get_notes_settings()

        return {
            'frame': frame_settings,
            'notes': notes_settings
        }

    def reset_frame_settings(self):
        """إعادة تعيين جميع إعدادات الإطار إلى القيم الافتراضية"""
        self._init_frame_settings()
        logger.info("تم إعادة تعيين جميع إعدادات الإطار إلى القيم الافتراضية")

    def apply_preset_frame_settings(self, preset_name: str):
        """
        تطبيق إعدادات مسبقة للإطار

        Args:
            preset_name: اسم الإعداد المسبق ('default', 'large', 'compact', 'spacious')
        """
        presets = {
            'default': {
                'frame_size': 100, 'qr_size': 90, 'border_width': 2.0, 'corner_radius': 4,
                'internal_x': 0, 'internal_y': 0, 'external_x': 0, 'external_y': 0,
                'table_h': 5, 'table_v': 10
            },
            'large': {
                'frame_size': 120, 'qr_size': 105, 'border_width': 2.5, 'corner_radius': 6,
                'internal_x': 0, 'internal_y': 0, 'external_x': 0, 'external_y': 0,
                'table_h': 8, 'table_v': 12
            },
            'compact': {
                'frame_size': 80, 'qr_size': 70, 'border_width': 1.5, 'corner_radius': 3,
                'internal_x': 0, 'internal_y': 0, 'external_x': 0, 'external_y': 0,
                'table_h': 3, 'table_v': 6
            },
            'spacious': {
                'frame_size': 110, 'qr_size': 85, 'border_width': 2.0, 'corner_radius': 8,
                'internal_x': 0, 'internal_y': 0, 'external_x': 0, 'external_y': 0,
                'table_h': 12, 'table_v': 18
            }
        }

        if preset_name not in presets:
            logger.warning(f"إعداد مسبق غير معروف: {preset_name}")
            return

        preset = presets[preset_name]
        self.set_frame_size(preset['frame_size'], preset['qr_size'])
        self.set_frame_border(preset['border_width'], preset['corner_radius'])
        self.set_qr_internal_position(preset['internal_x'], preset['internal_y'])
        self.set_frame_external_position(preset['external_x'], preset['external_y'])
        self.set_frame_table_padding(preset['table_h'], preset['table_v'])

        logger.info(f"تم تطبيق الإعداد المسبق: {preset_name}")

    # ==================== دوال مساعدة للتحكم المتقدم ====================

    def set_qr_position(self, horizontal_offset: float, vertical_offset: float):
        """
        دالة مختصرة لتعيين موقع QR داخل الإطار (للتوافق مع الكود القديم)

        Args:
            horizontal_offset: الإزاحة الأفقية
            vertical_offset: الإزاحة العمودية
        """
        self.set_qr_internal_position(horizontal_offset, vertical_offset)

    def move_frame_to_logo(self, distance_pixels: int = 20):
        """
        تحريك الإطار والـ QR أقرب إلى الشعار

        Args:
            distance_pixels: المسافة بالبكسل للتحريك نحو الشعار
        """
        # تحويل البكسل إلى نقاط وتحريك الإطار يميناً (نحو الشعار)
        offset_points = distance_pixels * 0.75
        self.set_frame_external_position(offset_points, 0)
        logger.info(f"تم تحريك الإطار نحو الشعار بمسافة {distance_pixels} بكسل")

    def adjust_frame_thickness_and_qr_size(self, thickness_increase: float, qr_size_increase: int):
        """
        تكبير سماكة الإطار مع تكبير حجم QR

        Args:
            thickness_increase: زيادة سماكة الإطار
            qr_size_increase: زيادة حجم QR بالبكسل
        """
        new_border_width = self.frame_border_width + thickness_increase
        new_qr_size = self.qr_size_pixels + qr_size_increase

        self.set_frame_border(border_width=new_border_width)
        self.set_frame_size(self.frame_size_pixels, new_qr_size)

        logger.info(f"تم تكبير سماكة الإطار إلى {new_border_width} وحجم QR إلى {new_qr_size}px")

    def create_custom_spacing_profile(self, profile_name: str, **kwargs):
        """
        إنشاء ملف تعريف مخصص للتباعد

        Args:
            profile_name: اسم الملف التعريفي
            **kwargs: الإعدادات المخصصة
        """
        # تطبيق الإعدادات المخصصة
        if 'frame_size' in kwargs:
            self.set_frame_size(kwargs['frame_size'], kwargs.get('qr_size'))
        if 'internal_x' in kwargs or 'internal_y' in kwargs:
            self.set_qr_internal_position(
                kwargs.get('internal_x', 0),
                kwargs.get('internal_y', 0)
            )
        if 'external_x' in kwargs or 'external_y' in kwargs:
            self.set_frame_external_position(
                kwargs.get('external_x', 0),
                kwargs.get('external_y', 0)
            )
        if 'border_color' in kwargs or 'background_color' in kwargs:
            self.set_frame_colors(
                kwargs.get('border_color'),
                kwargs.get('background_color')
            )
        if 'border_width' in kwargs or 'corner_radius' in kwargs:
            self.set_frame_border(
                kwargs.get('border_width'),
                kwargs.get('corner_radius')
            )

        logger.info(f"تم إنشاء ملف التعريف المخصص: {profile_name}")

    # ==================== دوال التحكم في إعدادات الملاحظات ====================

    def set_notes_position_and_alignment(self, position: str = 'center', internal_alignment: str = 'center'):
        """
        تعيين موقع الملاحظات والمحاذاة الداخلية

        Args:
            position: موقع الملاحظات ('center', 'right', 'left')
            internal_alignment: المحاذاة الداخلية للنص ('center', 'right', 'left')
        """
        # تحويل النصوص إلى قيم ReportLab
        position_map = {
            'center': 'center',
            'right': 'right',
            'left': 'left'
        }

        alignment_map = {
            'center': TA_CENTER,
            'right': TA_RIGHT,
            'left': TA_LEFT
        }

        if position in position_map:
            self.notes_position = position_map[position]
        else:
            logger.warning(f"موقع غير صحيح: {position}. استخدام 'center'")
            self.notes_position = 'center'

        if internal_alignment in alignment_map:
            self.notes_internal_alignment = alignment_map[internal_alignment]
        else:
            logger.warning(f"محاذاة غير صحيحة: {internal_alignment}. استخدام 'center'")
            self.notes_internal_alignment = TA_CENTER

        logger.info(f"تم تعيين موقع الملاحظات: {position}, المحاذاة الداخلية: {internal_alignment}")

    def set_notes_spacing(self, top_spacing: float = None, bottom_spacing: float = None,
                         between_spacing: float = None, left_indent: float = None, right_indent: float = None):
        """
        تعيين التباعد العمودي والأفقي للملاحظات

        Args:
            top_spacing: المسافة قبل الملاحظات (بالإنش)
            bottom_spacing: المسافة بعد الملاحظات (بالإنش)
            between_spacing: المسافة بين الملاحظات (بالإنش)
            left_indent: الإزاحة من اليسار (بالنقاط)
            right_indent: الإزاحة من اليمين (بالنقاط)
        """
        if top_spacing is not None:
            self.notes_top_spacing = top_spacing
        if bottom_spacing is not None:
            self.notes_bottom_spacing = bottom_spacing
        if between_spacing is not None:
            self.notes_between_spacing = between_spacing
        if left_indent is not None:
            self.notes_left_indent = left_indent
        if right_indent is not None:
            self.notes_right_indent = right_indent

        logger.info(f"تم تعيين تباعد الملاحظات: أعلى={self.notes_top_spacing}, أسفل={self.notes_bottom_spacing}, بين={self.notes_between_spacing}")

    def set_notes_text_style(self, font_size: int = None, text_color: str = None, leading: int = None):
        """
        تعيين نمط النص للملاحظات

        Args:
            font_size: حجم الخط
            text_color: لون النص (hex color)
            leading: المسافة بين الأسطر
        """
        if font_size is not None:
            self.notes_font_size = font_size
        if text_color is not None:
            self.notes_text_color = text_color
        if leading is not None:
            self.notes_leading = leading

        logger.info(f"تم تعيين نمط نص الملاحظات: حجم={self.notes_font_size}, لون={self.notes_text_color}")

    def set_notes_background_and_border(self, background_color: str = None, border_color: str = None,
                                       border_width: float = None, padding: float = None):
        """
        تعيين خلفية وحدود الملاحظات (اختياري)

        Args:
            background_color: لون الخلفية (hex color أو None)
            border_color: لون الحدود (hex color أو None)
            border_width: سماكة الحدود
            padding: التباعد الداخلي حول النص
        """
        if background_color is not None:
            self.notes_background_color = background_color
        if border_color is not None:
            self.notes_border_color = border_color
        if border_width is not None:
            self.notes_border_width = border_width
        if padding is not None:
            self.notes_padding = padding

        logger.info(f"تم تعيين خلفية وحدود الملاحظات: خلفية={self.notes_background_color}, حدود={self.notes_border_color}")

    def get_notes_settings(self) -> Dict[str, Any]:
        """
        الحصول على جميع إعدادات الملاحظات الحالية

        Returns:
            dict: قاموس يحتوي على جميع إعدادات الملاحظات
        """
        return {
            'position': self.notes_position,
            'internal_alignment': self.notes_internal_alignment,
            'top_spacing': self.notes_top_spacing,
            'bottom_spacing': self.notes_bottom_spacing,
            'between_spacing': self.notes_between_spacing,
            'left_indent': self.notes_left_indent,
            'right_indent': self.notes_right_indent,
            'font_size': self.notes_font_size,
            'text_color': self.notes_text_color,
            'leading': self.notes_leading,
            'background_color': self.notes_background_color,
            'border_color': self.notes_border_color,
            'border_width': self.notes_border_width,
            'padding': self.notes_padding
        }

    def reset_notes_settings(self):
        """إعادة تعيين جميع إعدادات الملاحظات إلى القيم الافتراضية"""
        self._init_notes_settings()
        logger.info("تم إعادة تعيين جميع إعدادات الملاحظات إلى القيم الافتراضية")

    def apply_preset_notes_settings(self, preset_name: str):
        """
        تطبيق إعدادات مسبقة للملاحظات

        Args:
            preset_name: اسم الإعداد المسبق ('default', 'right_left', 'left_right', 'center_large', 'minimal')
        """
        presets = {
            'default': {
                'position': 'center', 'alignment': 'center',
                'top_spacing': 0.0, 'bottom_spacing': 0.5, 'between_spacing': 0.1,
                'left_indent': 0, 'right_indent': 0,
                'font_size': 9, 'text_color': '#7F8C8D', 'leading': 12
            },
            'right_left': {  # ملاحظات على اليمين مع محاذاة يسار
                'position': 'right', 'alignment': 'left',
                'top_spacing': 0.1, 'bottom_spacing': 0.3, 'between_spacing': 0.08,
                'left_indent': 20, 'right_indent': 0,
                'font_size': 8, 'text_color': '#5D6D7E', 'leading': 11
            },
            'left_right': {  # ملاحظات على اليسار مع محاذاة يمين
                'position': 'left', 'alignment': 'right',
                'top_spacing': 0.1, 'bottom_spacing': 0.3, 'between_spacing': 0.08,
                'left_indent': 0, 'right_indent': 20,
                'font_size': 8, 'text_color': '#5D6D7E', 'leading': 11
            },
            'center_large': {  # ملاحظات وسط بخط كبير
                'position': 'center', 'alignment': 'center',
                'top_spacing': 0.2, 'bottom_spacing': 0.4, 'between_spacing': 0.15,
                'left_indent': 0, 'right_indent': 0,
                'font_size': 11, 'text_color': '#34495E', 'leading': 16
            },
            'minimal': {  # ملاحظات مصغرة
                'position': 'center', 'alignment': 'center',
                'top_spacing': 0.05, 'bottom_spacing': 0.2, 'between_spacing': 0.05,
                'left_indent': 0, 'right_indent': 0,
                'font_size': 7, 'text_color': '#95A5A6', 'leading': 9
            }
        }

        if preset_name not in presets:
            logger.warning(f"إعداد مسبق غير معروف للملاحظات: {preset_name}")
            return

        preset = presets[preset_name]
        self.set_notes_position_and_alignment(preset['position'], preset['alignment'])
        self.set_notes_spacing(
            preset['top_spacing'], preset['bottom_spacing'], preset['between_spacing'],
            preset['left_indent'], preset['right_indent']
        )
        self.set_notes_text_style(preset['font_size'], preset['text_color'], preset['leading'])

        logger.info(f"تم تطبيق الإعداد المسبق للملاحظات: {preset_name}")

    # ==================== دوال مساعدة للتحكم في الملاحظات ====================

    def set_notes_to_right_with_left_alignment(self, left_indent: float = 20):
        """
        دالة مختصرة: وضع الملاحظات على اليمين مع محاذاة يسار

        Args:
            left_indent: الإزاحة من اليسار بالنقاط
        """
        self.set_notes_position_and_alignment('right', 'left')
        self.set_notes_spacing(left_indent=left_indent)
        logger.info(f"تم وضع الملاحظات على اليمين مع محاذاة يسار، إزاحة: {left_indent}")

    def set_notes_to_left_with_right_alignment(self, right_indent: float = 20):
        """
        دالة مختصرة: وضع الملاحظات على اليسار مع محاذاة يمين

        Args:
            right_indent: الإزاحة من اليمين بالنقاط
        """
        self.set_notes_position_and_alignment('left', 'right')
        self.set_notes_spacing(right_indent=right_indent)
        logger.info(f"تم وضع الملاحظات على اليسار مع محاذاة يمين، إزاحة: {right_indent}")

    def adjust_notes_vertical_spacing(self, top_increase: float, bottom_increase: float):
        """
        تعديل التباعد العمودي للملاحظات

        Args:
            top_increase: زيادة المسافة العلوية (بالإنش)
            bottom_increase: زيادة المسافة السفلية (بالإنش)
        """
        new_top = self.notes_top_spacing + top_increase
        new_bottom = self.notes_bottom_spacing + bottom_increase
        self.set_notes_spacing(top_spacing=new_top, bottom_spacing=new_bottom)
        logger.info(f"تم تعديل التباعد العمودي: أعلى={new_top}, أسفل={new_bottom}")

    def validate_and_fix_notes_settings(self):
        """
        التحقق من صحة إعدادات الملاحظات وإصلاح القيم الخاطئة
        """
        # التحقق من التباعد العمودي
        if self.notes_top_spacing < 0 or self.notes_top_spacing > 3.0:
            logger.warning(f"قيمة التباعد العلوي غير صحيحة: {self.notes_top_spacing}. إعادة تعيين إلى 0.1")
            self.notes_top_spacing = 0.1

        if self.notes_bottom_spacing < 0 or self.notes_bottom_spacing > 3.0:
            logger.warning(f"قيمة التباعد السفلي غير صحيحة: {self.notes_bottom_spacing}. إعادة تعيين إلى 0.5")
            self.notes_bottom_spacing = 0.5

        if self.notes_between_spacing < 0 or self.notes_between_spacing > 1.0:
            logger.warning(f"قيمة التباعد بين الملاحظات غير صحيحة: {self.notes_between_spacing}. إعادة تعيين إلى 0.1")
            self.notes_between_spacing = 0.1

        # التحقق من الإزاحة الأفقية
        if self.notes_left_indent < 0 or self.notes_left_indent > 300:
            logger.warning(f"قيمة الإزاحة اليسرى غير صحيحة: {self.notes_left_indent}. إعادة تعيين إلى 0")
            self.notes_left_indent = 0

        if self.notes_right_indent < 0 or self.notes_right_indent > 300:
            logger.warning(f"قيمة الإزاحة اليمنى غير صحيحة: {self.notes_right_indent}. إعادة تعيين إلى 0")
            self.notes_right_indent = 0

        # التحقق من نمط النص
        if self.notes_font_size < 5 or self.notes_font_size > 25:
            logger.warning(f"حجم الخط غير صحيح: {self.notes_font_size}. إعادة تعيين إلى 9")
            self.notes_font_size = 9

        if self.notes_leading < 6 or self.notes_leading > 40:
            logger.warning(f"المسافة بين الأسطر غير صحيحة: {self.notes_leading}. إعادة تعيين إلى 12")
            self.notes_leading = 12

        # التحقق من الحدود والتباعد
        if self.notes_border_width < 0 or self.notes_border_width > 15:
            logger.warning(f"سماكة الحدود غير صحيحة: {self.notes_border_width}. إعادة تعيين إلى 0")
            self.notes_border_width = 0

        if self.notes_padding < 0 or self.notes_padding > 100:
            logger.warning(f"التباعد الداخلي غير صحيح: {self.notes_padding}. إعادة تعيين إلى 0")
            self.notes_padding = 0

        logger.info("تم التحقق من صحة إعدادات الملاحظات وإصلاح القيم الخاطئة")

    # ==================== دوال التحكم في عنوان الفاتورة ====================

    def set_invoice_title_position_and_alignment(self, position: str = 'right', internal_alignment: str = 'right'):
        """
        تعيين موقع عنوان الفاتورة والمحاذاة الداخلية

        Args:
            position: موقع العنوان ('center', 'right', 'left')
            internal_alignment: المحاذاة الداخلية للنص ('center', 'right', 'left')
        """
        # تحويل النصوص إلى قيم ReportLab
        position_map = {
            'center': 'center',
            'right': 'right',
            'left': 'left'
        }

        alignment_map = {
            'center': TA_CENTER,
            'right': TA_RIGHT,
            'left': TA_LEFT
        }

        if position in position_map:
            self.invoice_title_position = position_map[position]
        else:
            logger.warning(f"موقع غير صحيح: {position}. استخدام 'right'")
            self.invoice_title_position = 'right'

        if internal_alignment in alignment_map:
            self.invoice_title_internal_alignment = alignment_map[internal_alignment]
        else:
            logger.warning(f"محاذاة غير صحيحة: {internal_alignment}. استخدام 'right'")
            self.invoice_title_internal_alignment = TA_RIGHT

        logger.info(f"تم تعيين موقع عنوان الفاتورة: {position} مع محاذاة: {internal_alignment}")

    def set_invoice_title_spacing(self, vertical_spacing: float = None, horizontal_offset: float = None,
                                 vertical_offset: float = None, left_indent: float = None, right_indent: float = None):
        """
        تعيين التباعد لعنوان الفاتورة

        Args:
            vertical_spacing: المسافة بين السطرين (بالإنش)
            horizontal_offset: الإزاحة الأفقية (بالنقاط)
            vertical_offset: الإزاحة العمودية (بالنقاط)
            left_indent: الإزاحة من اليسار (بالنقاط)
            right_indent: الإزاحة من اليمين (بالنقاط)
        """
        if vertical_spacing is not None:
            self.invoice_title_vertical_spacing = vertical_spacing
        if horizontal_offset is not None:
            self.invoice_title_horizontal_offset = horizontal_offset
        if vertical_offset is not None:
            self.invoice_title_vertical_offset = vertical_offset
        if left_indent is not None:
            self.invoice_title_left_indent = left_indent
        if right_indent is not None:
            self.invoice_title_right_indent = right_indent

        logger.info(f"تم تعيين تباعد عنوان الفاتورة: عمودي={vertical_spacing}, أفقي={horizontal_offset}")

    def set_invoice_title_text_style(self, line1_font_size: int = None, line1_color: str = None,
                                    line2_font_size: int = None, line2_color: str = None, leading: int = None):
        """
        تعيين نمط النص لعنوان الفاتورة

        Args:
            line1_font_size: حجم خط السطر الأول
            line1_color: لون السطر الأول
            line2_font_size: حجم خط السطر الثاني
            line2_color: لون السطر الثاني
            leading: المسافة بين الأسطر
        """
        if line1_font_size is not None:
            self.invoice_title_line1_font_size = line1_font_size
        if line1_color is not None:
            self.invoice_title_line1_color = line1_color
        if line2_font_size is not None:
            self.invoice_title_line2_font_size = line2_font_size
        if line2_color is not None:
            self.invoice_title_line2_color = line2_color
        if leading is not None:
            self.invoice_title_leading = leading

        logger.info(f"تم تعيين نمط نص عنوان الفاتورة")

    def set_invoice_title_text(self, line1_text: str = None, line2_text: str = None):
        """
        تعيين نص عنوان الفاتورة

        Args:
            line1_text: نص السطر الأول
            line2_text: نص السطر الثاني
        """
        if line1_text is not None:
            self.invoice_title_line1_text = line1_text
        if line2_text is not None:
            self.invoice_title_line2_text = line2_text

        logger.info(f"تم تعيين نص عنوان الفاتورة: '{line1_text}' - '{line2_text}'")

    # ==================== دوال التحكم في الشعار ====================

    def set_logo_position_and_spacing(self, position: str = None, horizontal_offset: float = None,
                                     vertical_offset: float = None, left_indent: float = None,
                                     right_indent: float = None, top_padding: float = None, bottom_padding: float = None):
        """
        تعيين موقع وتباعد الشعار

        Args:
            position: موقع الشعار ('center', 'right', 'left')
            horizontal_offset: الإزاحة الأفقية (بالنقاط)
            vertical_offset: الإزاحة العمودية (بالنقاط)
            left_indent: الإزاحة من اليسار (بالنقاط)
            right_indent: الإزاحة من اليمين (بالنقاط)
            top_padding: التباعد العلوي (بالنقاط)
            bottom_padding: التباعد السفلي (بالنقاط)
        """
        if position is not None:
            position_map = {'center': 'center', 'right': 'right', 'left': 'left'}
            if position in position_map:
                self.logo_position = position_map[position]
            else:
                logger.warning(f"موقع شعار غير صحيح: {position}. استخدام 'center'")
                self.logo_position = 'center'

        if horizontal_offset is not None:
            self.logo_horizontal_offset = horizontal_offset
        if vertical_offset is not None:
            self.logo_vertical_offset = vertical_offset
        if left_indent is not None:
            self.logo_left_indent = left_indent
        if right_indent is not None:
            self.logo_right_indent = right_indent
        if top_padding is not None:
            self.logo_top_padding = top_padding
        if bottom_padding is not None:
            self.logo_bottom_padding = bottom_padding

        logger.info(f"تم تعيين موقع وتباعد الشعار: {position}")

    def set_logo_size(self, width: float = None, height: float = None):
        """
        تعيين حجم الشعار

        Args:
            width: العرض (بالإنش)
            height: الارتفاع (بالإنش)
        """
        if width is not None:
            self.logo_width = width
        if height is not None:
            self.logo_height = height

        logger.info(f"تم تعيين حجم الشعار: {width}x{height} إنش")

    def set_header_column_widths(self, qr_width: float = None, logo_width: float = None, title_width: float = None):
        """
        تعيين عروض أعمدة الرأس

        Args:
            qr_width: عرض عمود QR (بالإنش)
            logo_width: عرض عمود الشعار (بالإنش)
            title_width: عرض عمود العنوان (بالإنش)
        """
        if qr_width is not None:
            self.header_qr_column_width = qr_width
        if logo_width is not None:
            self.header_logo_column_width = logo_width
        if title_width is not None:
            self.header_title_column_width = title_width

        logger.info(f"تم تعيين عروض أعمدة الرأس: QR={qr_width}, شعار={logo_width}, عنوان={title_width}")

    # ==================== دوال مختصرة للاستخدام السريع ====================

    def set_title_to_right_with_left_alignment(self, left_indent: float = 20):
        """
        دالة مختصرة: وضع عنوان الفاتورة على اليمين مع محاذاة يسار

        Args:
            left_indent: الإزاحة من اليسار بالنقاط
        """
        self.set_invoice_title_position_and_alignment('right', 'left')
        self.set_invoice_title_spacing(left_indent=left_indent)
        logger.info(f"تم وضع عنوان الفاتورة على اليمين مع محاذاة يسار، إزاحة: {left_indent}")

    def set_title_to_center_with_large_font(self, font_size: int = 18):
        """
        دالة مختصرة: وضع عنوان الفاتورة في الوسط مع خط كبير

        Args:
            font_size: حجم الخط
        """
        self.set_invoice_title_position_and_alignment('center', 'center')
        self.set_invoice_title_text_style(line1_font_size=font_size, line2_font_size=font_size-2)
        logger.info(f"تم وضع عنوان الفاتورة في الوسط مع خط كبير: {font_size}")

    def set_logo_to_right_with_spacing(self, horizontal_offset: float = 20):
        """
        دالة مختصرة: وضع الشعار على اليمين مع تباعد

        Args:
            horizontal_offset: الإزاحة الأفقية بالنقاط
        """
        self.set_logo_position_and_spacing('right', horizontal_offset=horizontal_offset)
        logger.info(f"تم وضع الشعار على اليمين مع إزاحة: {horizontal_offset}")

    def apply_preset_header_layout(self, preset_name: str):
        """
        تطبيق إعدادات مسبقة لتخطيط الرأس

        Args:
            preset_name: اسم الإعداد المسبق ('default', 'title_right_left', 'title_center', 'compact')
        """
        presets = {
            'default': {
                'title_position': 'right', 'title_alignment': 'right',
                'title_vertical_spacing': 0.2, 'title_horizontal_offset': 0,
                'logo_position': 'center', 'logo_horizontal_offset': 0,
                'qr_width': 1.5, 'logo_width': 2.0, 'title_width': 1.5
            },
            'title_right_left': {  # عنوان على اليمين مع محاذاة يسار
                'title_position': 'right', 'title_alignment': 'left',
                'title_vertical_spacing': 0.15, 'title_horizontal_offset': 0,
                'title_left_indent': 30,
                'logo_position': 'center', 'logo_horizontal_offset': 0,
                'qr_width': 1.3, 'logo_width': 2.2, 'title_width': 1.5
            },
            'title_center': {  # عنوان في الوسط
                'title_position': 'center', 'title_alignment': 'center',
                'title_vertical_spacing': 0.25, 'title_horizontal_offset': 0,
                'logo_position': 'center', 'logo_horizontal_offset': 0,
                'qr_width': 1.4, 'logo_width': 2.0, 'title_width': 1.6
            },
            'compact': {  # تخطيط مضغوط
                'title_position': 'right', 'title_alignment': 'right',
                'title_vertical_spacing': 0.1, 'title_horizontal_offset': 0,
                'logo_position': 'center', 'logo_horizontal_offset': 0,
                'qr_width': 1.2, 'logo_width': 1.8, 'title_width': 1.3
            }
        }

        if preset_name not in presets:
            logger.warning(f"إعداد مسبق غير معروف لتخطيط الرأس: {preset_name}")
            return

        preset = presets[preset_name]

        # تطبيق إعدادات العنوان
        self.set_invoice_title_position_and_alignment(
            preset['title_position'],
            preset['title_alignment']
        )
        self.set_invoice_title_spacing(
            vertical_spacing=preset['title_vertical_spacing'],
            horizontal_offset=preset['title_horizontal_offset'],
            left_indent=preset.get('title_left_indent', 0)
        )

        # تطبيق إعدادات الشعار
        self.set_logo_position_and_spacing(
            preset['logo_position'],
            horizontal_offset=preset['logo_horizontal_offset']
        )

        # تطبيق عروض الأعمدة
        self.set_header_column_widths(
            preset['qr_width'],
            preset['logo_width'],
            preset['title_width']
        )

        logger.info(f"تم تطبيق الإعداد المسبق لتخطيط الرأس: {preset_name}")

    def create_qr_code(self, data: str, filename: str) -> str:
        """إنشاء QR Code"""
        try:
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(data)
            qr.make(fit=True)
            
            # إنشاء صورة QR مع خلفية شفافة ولون #2A3C73
            qr_image = qr.make_image(fill_color="#2A3C73", back_color="transparent")
            qr_path = os.path.join(self.qr_dir, f"{filename}_qr.png")
            qr_image.save(qr_path)
            
            return qr_path
        except Exception as e:
            logger.error(f"خطأ في إنشاء QR Code: {e}")
            return ""

    def create_invoice_pdf(self, invoice_data: Dict[str, Any]) -> Tuple[bool, str, str]:
        """إنشاء فاتورة PDF باستخدام ReportLab"""
        try:
            invoice_number = invoice_data.get("invoice_number", self.generate_invoice_number())
            filename = f"invoice_{invoice_number}.pdf"
            filepath = os.path.join(self.pdf_dir, filename)

            # إنشاء مستند PDF بحجم A5 عمودي مع هوامش 15 بكسل
            margin_15px = 15 * 72 / 96  # تحويل 15 بكسل إلى نقاط
            doc = BaseDocTemplate(
                filepath,
                pagesize=A5,
                rightMargin=margin_15px,
                leftMargin=margin_15px,
                topMargin=margin_15px,
                bottomMargin=margin_15px
            )

            # إنشاء إطار للمحتوى
            frame = Frame(
                margin_15px, margin_15px,
                A5[0] - 2*margin_15px, A5[1] - 2*margin_15px,
                leftPadding=0, bottomPadding=0,
                rightPadding=0, topPadding=0
            )

            # إضافة قالب الصفحة مع الخلفية الملونة #f4f5f1
            template = ColoredPageTemplate('normal', [frame], A5, '#f4f5f1')
            doc.addPageTemplates([template])

            # إعداد الأنماط العربية
            styles = getSampleStyleSheet()

            # نمط للعناوين العربية الرئيسية
            arabic_main_title_style = ParagraphStyle(
                'ArabicMainTitle',
                parent=styles['Title'],
                fontName=self.bold_font,
                fontSize=16,
                alignment=TA_CENTER,
                spaceAfter=15,
                textColor=HexColor('#1B4F72'),
                leading=20
            )

            # نمط للعناوين الفرعية العربية
            arabic_subtitle_style = ParagraphStyle(
                'ArabicSubtitle',
                parent=styles['Heading2'],
                fontName=self.bold_font,
                fontSize=12,
                alignment=TA_RIGHT,
                spaceAfter=10,
                textColor=HexColor('#2E86AB'),
                leading=16
            )

            # نمط للنص العربي العادي
            arabic_normal_style = ParagraphStyle(
                'ArabicNormal',
                parent=styles['Normal'],
                fontName=self.default_font,
                fontSize=10,
                alignment=TA_RIGHT,
                spaceAfter=8,
                leading=14
            )

            # نمط للجداول العربية
            arabic_table_style = ParagraphStyle(
                'ArabicTable',
                parent=styles['Normal'],
                fontName=self.default_font,
                fontSize=9,
                alignment=TA_RIGHT,
                leading=12
            )

            # بناء محتوى الفاتورة
            story = []

            # إنشاء QR Code أولاً مع البيانات المنسقة
            qr_data = self.create_formatted_qr_data(invoice_data)
            qr_path = self.create_qr_code(qr_data, invoice_number)

            # إنشاء رأس الصفحة الجديد - الترتيب الصحيح
            header_data = []
            header_row = []

            # يسار الورقة - QR Code مع إطار (العمود الأول)
            if qr_path and os.path.exists(qr_path):
                # تحويل البكسل إلى نقاط (1 بكسل = 0.75 نقطة تقريباً)
                qr_frame = QRFrameFlowable(
                    qr_path=qr_path,
                    frame_size=self.frame_size_pixels * 0.75,  # استخدام الحجم المخصص
                    qr_size=self.qr_size_pixels * 0.75,        # استخدام حجم QR المخصص
                    border_width=self.frame_border_width,       # استخدام سماكة الحدود المخصصة
                    border_color=self.frame_border_color,       # استخدام لون الإطار المخصص
                    corner_radius=self.frame_corner_radius_pixels * 0.75,  # استخدام نصف قطر الزوايا المخصص
                    background_color=self.frame_background_color,  # استخدام لون الخلفية المخصص
                    internal_padding_x=self.qr_internal_padding_x,  # التحكم الداخلي الأفقي
                    internal_padding_y=self.qr_internal_padding_y   # التحكم الداخلي العمودي
                )
                header_row.append(qr_frame)
            else:
                header_row.append("")

            # الشعار - استخدام الإعدادات المخصصة (العمود الثاني)
            logo_path = os.path.join(self.assets_dir, "logo.png")
            if os.path.exists(logo_path):
                try:
                    # شعار بحجم مخصص
                    logo = Image(logo_path, width=self.logo_width*inch, height=self.logo_height*inch)
                    header_row.append(logo)
                except Exception as e:
                    logger.warning(f"تعذر إضافة الشعار: {e}")
                    header_row.append("")
            else:
                header_row.append("")

            # عنوان الفاتورة - استخدام الإعدادات المخصصة (العمود الثالث)
            title_text_style_line1 = ParagraphStyle(
                'TitleTextLine1',
                parent=styles['Normal'],
                fontName='NotoSansArabic-ExtraBold' if 'NotoSansArabic-ExtraBold' in self.loaded_fonts else self.bold_font,
                fontSize=self.invoice_title_line1_font_size,
                alignment=self.invoice_title_internal_alignment,
                textColor=HexColor(self.invoice_title_line1_color),
                leading=self.invoice_title_leading,
                leftIndent=self.invoice_title_left_indent,
                rightIndent=self.invoice_title_right_indent
            )

            title_text_style_line2 = ParagraphStyle(
                'TitleTextLine2',
                parent=styles['Normal'],
                fontName='NotoSansArabic-ExtraBold' if 'NotoSansArabic-ExtraBold' in self.loaded_fonts else self.bold_font,
                fontSize=self.invoice_title_line2_font_size,
                alignment=self.invoice_title_internal_alignment,
                textColor=HexColor(self.invoice_title_line2_color),
                leading=self.invoice_title_leading,
                leftIndent=self.invoice_title_left_indent,
                rightIndent=self.invoice_title_right_indent
            )

            title_content = [
                Paragraph(self.process_arabic_text(self.invoice_title_line1_text), title_text_style_line1),
                Spacer(1, self.invoice_title_vertical_spacing*inch),  # تباعد عمودي مخصص بين السطرين
                Paragraph(self.process_arabic_text(self.invoice_title_line2_text), title_text_style_line2)
            ]
            header_row.append(title_content)

            header_data.append(header_row)

            # إنشاء جدول الرأس مع عروض أعمدة مخصصة
            # استخدام الإعدادات المخصصة بدلاً من التقسيم المتساوي
            qr_col_width = self.header_qr_column_width * inch
            logo_col_width = self.header_logo_column_width * inch
            title_col_width = self.header_title_column_width * inch

            header_table = Table(header_data, colWidths=[qr_col_width, logo_col_width, title_col_width])
            # تحديد المحاذاة بناءً على الإعدادات المخصصة
            title_alignment_map = {
                'right': 'RIGHT',
                'center': 'CENTER',
                'left': 'LEFT'
            }
            title_table_alignment = title_alignment_map.get(self.invoice_title_position, 'RIGHT')

            header_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (0, 0), self.header_qr_alignment),  # QR - محاذاة مخصصة
                ('ALIGN', (1, 0), (1, 0), self.header_logo_alignment),  # الشعار - محاذاة مخصصة
                ('ALIGN', (2, 0), (2, 0), title_table_alignment),   # العنوان - محاذاة مخصصة
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                # استخدام التباعد المخصص للإطار
                ('LEFTPADDING', (0, 0), (0, 0), self.frame_table_padding_horizontal + self.frame_external_offset_x),  # QR مع الإزاحة الخارجية
                ('RIGHTPADDING', (0, 0), (0, 0), self.frame_table_padding_horizontal - self.frame_external_offset_x),
                ('TOPPADDING', (0, 0), (0, 0), self.frame_table_padding_vertical + self.frame_external_offset_y),
                ('BOTTOMPADDING', (0, 0), (0, 0), self.frame_table_padding_vertical - self.frame_external_offset_y),
                # تباعد مخصص للشعار
                ('LEFTPADDING', (1, 0), (1, 0), self.logo_left_indent),
                ('RIGHTPADDING', (1, 0), (1, 0), self.logo_right_indent),
                ('TOPPADDING', (1, 0), (1, 0), self.logo_top_padding),
                ('BOTTOMPADDING', (1, 0), (1, 0), self.logo_bottom_padding),
                # تباعد مخصص لعنوان الفاتورة
                ('LEFTPADDING', (2, 0), (2, 0), self.invoice_title_left_indent),
                ('RIGHTPADDING', (2, 0), (2, 0), self.invoice_title_right_indent),
                ('TOPPADDING', (2, 0), (2, 0), self.invoice_title_vertical_offset),
                ('BOTTOMPADDING', (2, 0), (2, 0), -self.invoice_title_vertical_offset),
            ]))

            story.append(header_table)

            # حساب عرض الصفحة للخط الفاصل
            page_width = A5[0] - 2*margin_15px  # العرض المتاح

            # متغير للتحكم في الموقع العمودي للخط الفاصل
            # يمكن تعديل هذه القيم لرفع الخط أو خفضه
            line_top_spacing = 0.05*inch    # المسافة قبل الخط (يمكن تقليلها لرفع الخط)
            line_bottom_spacing = 0.25*inch # المسافة بعد الخط (يمكن تقليلها لخفض الخط)

            story.append(Spacer(1, line_top_spacing))

            # إضافة خط فاصل بعد رأس الورقة بسماكة 4 بكسل ولون الإطار
            # إنشاء رسم للخط
            line_drawing = Drawing(page_width, 3)  # عرض الصفحة وارتفاع أكبر للخط السميك

            # إنشاء الخط بسماكة 2 بكسل (3 نقاط) ولون الإطار
            separator_line = Line(0, 3, page_width, 3)
            separator_line.strokeColor = HexColor('#F0522E')  # نفس لون الإطار
            separator_line.strokeWidth = 3 * 0.75  # تحويل 4 بكسل إلى نقاط

            line_drawing.add(separator_line)
            story.append(line_drawing)
            story.append(Spacer(1, line_bottom_spacing))

            # متغيرات التحكم في موقع عنوان "تفاصيل المعاملة"
            # يمكن تعديل هذه القيم لتحريك العنوان عمودياً وأفقياً
            title_top_spacing = -0.3*inch      # المسافة قبل العنوان (تقليلها = رفع العنوان)
            title_bottom_spacing = 0.0*inch  # المسافة بعد العنوان (تقليلها = تقريب الجدول)
            title_alignment = TA_CENTER       # محاذاة العنوان (TA_CENTER, TA_RIGHT, TA_LEFT)
            title_left_indent = 0*inch        # إزاحة أفقية من اليسار (زيادتها = تحريك يميناً)

            # إنشاء نمط العنوان مع إمكانية التحكم في المحاذاة واللون المطلوب
            custom_title_style = ParagraphStyle(
                'CustomTitleStyle',
                parent=arabic_subtitle_style,
                alignment=title_alignment,
                leftIndent=title_left_indent,
                textColor=HexColor('#2A3C73')  # لون تفاصيل الفاتورة
            )

            story.append(Spacer(1, title_top_spacing))
            # عنوان قسم تفاصيل الفاتورة
            story.append(Paragraph(self.process_arabic_text("تفاصيل العملية"), custom_title_style))
            story.append(Spacer(1, title_bottom_spacing))

            # إنشاء أنماط للعناوين والقيم
            header_style = ParagraphStyle(
                'HeaderStyle',
                parent=styles['Normal'],
                fontName=self.bold_font,
                fontSize=11,
                alignment=TA_CENTER,
                textColor=HexColor('#F0522E'),
                leading=16
            )

            value_style = ParagraphStyle(
                'ValueStyle',
                parent=styles['Normal'],
                fontName=self.default_font,
                fontSize=11,
                alignment=TA_CENTER,
                textColor=HexColor('#2A3C73'),
                leading=16
            )

            # تفاصيل المعاملة بالتنسيق المطلوب
            # السطر الأول: العناوين - نوع العملية
            headers_row_operation = [
                self.process_arabic_text('نوع العملية'),
                self.process_arabic_text('')  # عمود فارغ للتوازن
            ]

            # السطر الثاني: القيم - نوع العملية
            operation_type = invoice_data.get('transaction_type', 'غير محدد')
            values_row_operation = [
                self.process_arabic_text(operation_type),
                self.process_arabic_text('')  # عمود فارغ للتوازن
            ]

            # السطر الثالث: العناوين (معكوسة)
            headers_row = [
                self.process_arabic_text('تاريخ الفاتورة'),
                self.process_arabic_text('رقم الفاتورة')
            ]

            # استخدام رقم الفاتورة الموجود في البيانات أو إنشاء رقم جديد
            existing_invoice_number = invoice_data.get('invoice_number', '')
            if existing_invoice_number and len(existing_invoice_number) >= 10:
                # استخدام الرقم الموجود مهما كان نوعه (808, 502, 303, 304, 305)
                invoice_number_display = existing_invoice_number
            else:
                # إنشاء رقم جديد بالشكل الافتراضي (808xxxxxxx) فقط إذا لم يتم تمرير رقم
                import random
                invoice_number_display = f"808{random.randint(1000000, 9999999)}"

            # تنسيق التاريخ بدون وقت
            date_only = datetime.now().strftime('%Y-%m-%d')

            # السطر الرابع: القيم (معكوسة)
            values_row_1 = [
                date_only,
                invoice_number_display
            ]

            # السطر الخامس: العناوين (معكوسة)
            headers_row_2 = [
                self.process_arabic_text('اسم العميل'),
                self.process_arabic_text('رقم المحفظة')
            ]

            # السطر السادس: القيم (معكوسة)
            values_row_2 = [
                self.process_arabic_text(invoice_data.get('user_name', 'غير محدد')),
                invoice_data.get('wallet_number', 'غير محدد')
            ]

            # إنشاء الجدول بالتنسيق الجديد مع صف نوع العملية
            details_data = [
                [Paragraph(headers_row_operation[0], header_style), Paragraph(headers_row_operation[1], header_style)],
                [Paragraph(values_row_operation[0], value_style), Paragraph(values_row_operation[1], value_style)],
                [Paragraph(headers_row[0], header_style), Paragraph(headers_row[1], header_style)],
                [Paragraph(values_row_1[0], value_style), Paragraph(values_row_1[1], value_style)],
                [Paragraph(headers_row_2[0], header_style), Paragraph(headers_row_2[1], header_style)],
                [Paragraph(values_row_2[0], value_style), Paragraph(values_row_2[1], value_style)]
            ]

            # متغيرات التحكم في موقع وحجم جدول البيانات
            # يمكن تعديل هذه القيم لتحريك الجدول وتغيير أبعاده

            # التحكم في أبعاد الجدول الخارجية
            table_col_width_1 = 2.2*inch      # عرض العمود الأول (رقم الفاتورة/رقم المحفظة)
            table_col_width_2 = 2.2*inch      # عرض العمود الثاني (تاريخ الفاتورة/اسم العميل)
            table_row_height = 0.3*inch       # ارتفاع كل صف (تقليلها = صفوف أقصر، زيادتها = صفوف أطول)

            # التحكم في التباعد الداخلي للجدول
            table_left_padding = 15           # مسافة أفقية داخلية من اليسار
            table_right_padding = 15          # مسافة أفقية داخلية من اليمين
            table_top_padding = 8            # مسافة عمودية داخلية من الأعلى (تقليلها = محتوى أقرب للحدود)
            table_bottom_padding = 8         # مسافة عمودية داخلية من الأسفل (تقليلها = محتوى أقرب للحدود)

            # التحكم في موقع الجدول العمودي
            table_bottom_spacing = 0.25*inch  # المسافة بعد الجدول (تقليلها = تقريب العناصر التالية)

            # إنشاء الجدول مع الأبعاد المخصصة
            details_table = Table(details_data,
                                colWidths=[table_col_width_1, table_col_width_2],
                                rowHeights=[table_row_height] * len(details_data))

            details_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, -1), HexColor('#FFFFFF')),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 1.5, HexColor('#E8E8E8')),
                # تباعد عمودي وأفقي قابل للتخصيص
                ('LEFTPADDING', (0, 0), (-1, -1), table_left_padding),
                ('RIGHTPADDING', (0, 0), (-1, -1), table_right_padding),
                ('TOPPADDING', (0, 0), (-1, -1), table_top_padding),
                ('BOTTOMPADDING', (0, 0), (-1, -1), table_bottom_padding),
                # خلفيات مختلفة للعناوين والقيم مع الصفوف الجديدة
                ('BACKGROUND', (0, 0), (-1, 0), HexColor('#FFF5F5')),  # خلفية عنوان نوع العملية
                ('BACKGROUND', (0, 1), (-1, 1), HexColor('#F0F8FF')),  # خلفية قيمة نوع العملية
                ('BACKGROUND', (0, 2), (-1, 2), HexColor('#FFF5F5')),  # خلفية العناوين الثانية
                ('BACKGROUND', (0, 3), (-1, 3), HexColor('#F0F8FF')),  # خلفية القيم الثانية
                ('BACKGROUND', (0, 4), (-1, 4), HexColor('#FFF5F5')),  # خلفية العناوين الثالثة
                ('BACKGROUND', (0, 5), (-1, 5), HexColor('#F0F8FF')),  # خلفية القيم الثالثة
                # دمج خلايا نوع العملية
                ('SPAN', (0, 0), (1, 0)),  # دمج خلايا عنوان نوع العملية
                ('SPAN', (0, 1), (1, 1)),  # دمج خلايا قيمة نوع العملية
            ]))

            story.append(details_table)
            story.append(Spacer(1, table_bottom_spacing))

            # إضافة الجدول الثاني - تفاصيل الرصيد
            # متغيرات التحكم في موقع عنوان "تفاصيل الرصيد"
            balance_title_top_spacing = -0.2*inch      # المسافة قبل العنوان
            balance_title_bottom_spacing = 0.0*inch   # المسافة بعد العنوان
            balance_title_alignment = TA_CENTER       # محاذاة العنوان

            # إنشاء نمط العنوان الثاني
            balance_title_style = ParagraphStyle(
                'BalanceTitleStyle',
                parent=arabic_subtitle_style,
                alignment=balance_title_alignment,
                textColor=HexColor('#2A3C73')  # نفس لون العنوان الأول
            )

            story.append(Spacer(1, balance_title_top_spacing))
            # عنوان قسم تفاصيل الرصيد
            story.append(Paragraph(self.process_arabic_text("تفاصيل الرصيد"), balance_title_style))
            story.append(Spacer(1, balance_title_bottom_spacing))

            # حساب قيمة التوكن (1 إكسا = 524,288 ألف توكن)
            amount = invoice_data.get('amount', 0)
            previous_balance = invoice_data.get('previous_balance', 0)
            new_balance = invoice_data.get('new_balance', 0)
            usd_value = amount * 3  # 1 إكسا = 3 دولار
            token_value = amount * 524.288  # 1 إكسا = 524,288 ألف توكن

            # السطر الأول: العناوين - الرصيد المضاف، الرصيد السابق، الرصيد الجديد
            balance_headers_row_1 = [
                self.process_arabic_text('الرصيد الجديد'),
                self.process_arabic_text('الرصيد السابق'),
                self.process_arabic_text('الرصيد المضاف')
            ]

            # السطر الثاني: القيم - الأرصدة
            balance_values_row_1 = [
                self.format_currency_text(new_balance, "إكسا"),
                self.format_currency_text(previous_balance, "إكسا"),
                self.format_currency_text(amount, "إكسا")
            ]

            # السطر الثالث: عنوان القيمة بالدولار
            balance_headers_row_2 = [
                self.process_arabic_text('القيمة بالدولار'),
                self.process_arabic_text('')  # عمود فارغ
            ]

            # السطر الرابع: قيمة الدولار
            balance_values_row_2 = [
                self.process_arabic_text(f"{usd_value:.0f} دولار"),
                self.process_arabic_text('')  # عمود فارغ
            ]

            # السطر الخامس: عنوان القيمة بالتوكن
            balance_headers_row_3 = [
                self.process_arabic_text('القيمة بالتوكن'),
                self.process_arabic_text('')  # عمود فارغ
            ]

            # السطر السادس: قيمة التوكن
            balance_values_row_3 = [
                self.format_token_text(token_value),
                self.process_arabic_text('')  # عمود فارغ
            ]

            # إنشاء بيانات الجدول الثاني
            balance_data = [
                [Paragraph(balance_headers_row_1[0], header_style), Paragraph(balance_headers_row_1[1], header_style), Paragraph(balance_headers_row_1[2], header_style)],
                [Paragraph(balance_values_row_1[0], value_style), Paragraph(balance_values_row_1[1], value_style), Paragraph(balance_values_row_1[2], value_style)],
                [Paragraph(balance_headers_row_2[0], header_style), Paragraph(balance_headers_row_2[1], header_style), Paragraph('', header_style)],
                [Paragraph(balance_values_row_2[0], value_style), Paragraph(balance_values_row_2[1], value_style), Paragraph('', value_style)],
                [Paragraph(balance_headers_row_3[0], header_style), Paragraph(balance_headers_row_3[1], header_style), Paragraph('', header_style)],
                [Paragraph(balance_values_row_3[0], value_style), Paragraph(balance_values_row_3[1], value_style), Paragraph('', value_style)]
            ]

            # إنشاء الجدول الثاني مع 3 أعمدة
            balance_table_col_width = (table_col_width_1 + table_col_width_2) / 3  # تقسيم العرض على 3 أعمدة

            balance_table = Table(balance_data,
                                colWidths=[balance_table_col_width, balance_table_col_width, balance_table_col_width],
                                rowHeights=[table_row_height] * len(balance_data))

            balance_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, -1), HexColor('#FFFFFF')),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 1.5, HexColor('#E8E8E8')),
                # تباعد عمودي وأفقي
                ('LEFTPADDING', (0, 0), (-1, -1), table_left_padding),
                ('RIGHTPADDING', (0, 0), (-1, -1), table_right_padding),
                ('TOPPADDING', (0, 0), (-1, -1), table_top_padding),
                ('BOTTOMPADDING', (0, 0), (-1, -1), table_bottom_padding),
                # خلفيات مختلفة للعناوين والقيم
                ('BACKGROUND', (0, 0), (-1, 0), HexColor('#FFF5F5')),  # خلفية عناوين الأرصدة
                ('BACKGROUND', (0, 1), (-1, 1), HexColor('#F0F8FF')),  # خلفية قيم الأرصدة
                ('BACKGROUND', (0, 2), (-1, 2), HexColor('#FFF5F5')),  # خلفية عنوان الدولار
                ('BACKGROUND', (0, 3), (-1, 3), HexColor('#F0F8FF')),  # خلفية قيمة الدولار
                ('BACKGROUND', (0, 4), (-1, 4), HexColor('#FFF5F5')),  # خلفية عنوان التوكن
                ('BACKGROUND', (0, 5), (-1, 5), HexColor('#F0F8FF')),  # خلفية قيمة التوكن
                # دمج خلايا القيمة بالدولار والتوكن
                ('SPAN', (0, 2), (2, 2)),  # دمج خلايا عنوان القيمة بالدولار
                ('SPAN', (0, 3), (2, 3)),  # دمج خلايا قيمة الدولار
                ('SPAN', (0, 4), (2, 4)),  # دمج خلايا عنوان القيمة بالتوكن
                ('SPAN', (0, 5), (2, 5)),  # دمج خلايا قيمة التوكن
            ]))

            story.append(balance_table)
            story.append(Spacer(1, table_bottom_spacing))

            # ملاحظات مع إعدادات قابلة للتخصيص
            # إضافة المسافة العلوية قبل الملاحظات
            # التحقق من صحة قيم التباعد وتطبيق حدود آمنة
            safe_top_spacing = max(0, min(self.notes_top_spacing, 2.0))  # حد أقصى 2 إنش
            if safe_top_spacing > 0:
                story.append(Spacer(1, safe_top_spacing*inch))

            # إنشاء نمط الملاحظات باستخدام الإعدادات المخصصة مع حدود آمنة
            safe_left_indent = max(0, min(self.notes_left_indent, 200))  # حد أقصى 200 نقطة
            safe_right_indent = max(0, min(self.notes_right_indent, 200))  # حد أقصى 200 نقطة
            safe_font_size = max(6, min(self.notes_font_size, 20))  # حد أدنى 6 وأقصى 20
            safe_leading = max(8, min(self.notes_leading, 30))  # حد أدنى 8 وأقصى 30

            notes_style = ParagraphStyle(
                'Notes',
                parent=styles['Normal'],
                fontName=self.default_font,
                fontSize=safe_font_size,
                alignment=self.notes_internal_alignment,
                textColor=HexColor(self.notes_text_color),
                leading=safe_leading,
                leftIndent=safe_left_indent,
                rightIndent=safe_right_indent
            )

            # إضافة خلفية وحدود إذا كانت محددة (مع حماية من الأخطاء)
            try:
                if self.notes_background_color:
                    notes_style.backColor = HexColor(self.notes_background_color)
                if self.notes_border_color:
                    notes_style.borderColor = HexColor(self.notes_border_color)
                    notes_style.borderWidth = max(0, min(self.notes_border_width, 10))  # حد أقصى 10
                if self.notes_padding > 0:
                    safe_padding = max(0, min(self.notes_padding, 50))  # حد أقصى 50
                    notes_style.leftIndent += safe_padding
                    notes_style.rightIndent += safe_padding
            except Exception as e:
                logger.warning(f"تحذير في إعدادات خلفية الملاحظات: {e}")

            notes = [
                self.process_arabic_text("ملاحظات :"),
                self.process_arabic_text("• هذه فاتورة إلكترونية تم إنشاؤها تلقائياً من نظام إدارة المحافظ الإلكترونية"),
                self.process_arabic_text("• يرجى الاحتفاظ بهذه الفاتورة كإثبات للمعاملة"),
                self.process_arabic_text("• شكراً لك على استخدام خدماتنا"),
            ]

            # إضافة الملاحظات مع التباعد المخصص (مع حماية من الأخطاء)
            safe_between_spacing = max(0, min(self.notes_between_spacing, 0.5))  # حد أقصى 0.5 إنش
            safe_bottom_spacing = max(0, min(self.notes_bottom_spacing, 2.0))  # حد أقصى 2 إنش

            try:
                for i, note in enumerate(notes):
                    story.append(Paragraph(note, notes_style))
                    # إضافة تباعد بين الملاحظات (ما عدا الأخيرة)
                    if i < len(notes) - 1 and safe_between_spacing > 0:
                        story.append(Spacer(1, safe_between_spacing*inch))
            except Exception as e:
                logger.error(f"خطأ في إضافة الملاحظات: {e}")
                # في حالة الخطأ، استخدم النمط الافتراضي
                default_notes_style = ParagraphStyle(
                    'DefaultNotes',
                    parent=styles['Normal'],
                    fontName=self.default_font,
                    fontSize=9,
                    alignment=TA_CENTER,
                    textColor=HexColor('#7F8C8D'),
                    leading=12
                )
                for note in notes:
                    story.append(Paragraph(note, default_notes_style))
                    story.append(Spacer(1, 0.1*inch))

            # إضافة المسافة السفلية بعد الملاحظات (مع حماية من القيم الكبيرة)
            if safe_bottom_spacing > 0:
                story.append(Spacer(1, safe_bottom_spacing*inch))

            # بناء المستند
            doc.build(story)

            logger.info(f"تم إنشاء الفاتورة بنجاح: {filename}")
            return True, filepath, invoice_number

        except Exception as e:
            logger.error(f"خطأ في إنشاء فاتورة PDF: {e}")
            return False, "", ""

    def save_pending_invoice(self, invoice_data: Dict[str, Any]) -> bool:
        """حفظ فاتورة معلقة"""
        try:
            # تحميل الفواتير المعلقة
            pending_invoices = {}
            if os.path.exists(self.pending_file):
                with open(self.pending_file, 'r', encoding='utf-8') as f:
                    pending_invoices = json.load(f)

            # إضافة الفاتورة الجديدة
            invoice_number = invoice_data.get('invoice_number', self.generate_invoice_number())
            pending_invoices[invoice_number] = invoice_data

            # حفظ الملف
            with open(self.pending_file, 'w', encoding='utf-8') as f:
                json.dump(pending_invoices, f, ensure_ascii=False, indent=2)

            return True

        except Exception as e:
            logger.error(f"خطأ في حفظ الفاتورة المعلقة: {e}")
            return False

    def create_transaction_invoice(self, wallet_number: str, user_name: str, amount: float,
                                 transaction_type: str, previous_balance: float,
                                 new_balance: float, invoice_number: str = None) -> Tuple[bool, str, str]:
        """إنشاء فاتورة لمعاملة مالية"""
        try:
            # التحقق من صحة إعدادات الملاحظات قبل إنشاء الفاتورة
            self.validate_and_fix_notes_settings()

            # استخدام رقم الفاتورة المُمرر أو إنشاء رقم جديد
            if invoice_number is None:
                invoice_number = self.generate_invoice_number()

            # بيانات الفاتورة
            invoice_data = {
                'invoice_number': invoice_number,
                'wallet_number': wallet_number,
                'user_name': user_name,
                'amount': amount,
                'usd_value': amount * 3,  # 1 إكسا = 3 دولار
                'transaction_type': transaction_type,
                'previous_balance': previous_balance,
                'new_balance': new_balance,
                'date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'created_at': datetime.now().isoformat()
            }

            # حفظ الفاتورة المعلقة
            self.save_pending_invoice(invoice_data)

            # إنشاء PDF
            success, filepath, invoice_num = self.create_invoice_pdf(invoice_data)

            if success:
                logger.info(f"تم إنشاء فاتورة المعاملة بنجاح: {invoice_number}")
                return True, filepath, invoice_number
            else:
                return False, "", ""

        except Exception as e:
            logger.error(f"خطأ في إنشاء فاتورة المعاملة: {e}")
            return False, "", ""

    def check_pdf_exists(self, invoice_number: str) -> Tuple[bool, str]:
        """
        التحقق من وجود ملف PDF للفاتورة

        Args:
            invoice_number: رقم الفاتورة

        Returns:
            tuple: (هل الملف موجود, مسار الملف)
        """
        try:
            filename = f"invoice_{invoice_number}.pdf"
            filepath = os.path.join(self.pdf_dir, filename)

            if os.path.exists(filepath):
                logger.info(f"✅ تم العثور على ملف PDF موجود: {filepath}")
                return True, filepath
            else:
                logger.info(f"ℹ️ لم يتم العثور على ملف PDF: {filepath}")
                return False, ""

        except Exception as e:
            logger.error(f"❌ خطأ في التحقق من وجود ملف PDF: {e}")
            return False, ""

    def create_formatted_qr_data(self, invoice_data: Dict[str, Any]) -> str:
        """إنشاء بيانات QR Code منسقة بنفس تنسيق الفاتورة"""
        try:
            # استخراج البيانات
            invoice_number = invoice_data.get('invoice_number', 'غير محدد')
            transaction_type = invoice_data.get('transaction_type', 'معاملة مالية')
            user_name = invoice_data.get('user_name', 'غير محدد')
            wallet_number = invoice_data.get('wallet_number', 'غير محدد')
            amount = invoice_data.get('amount', 0)
            previous_balance = invoice_data.get('previous_balance', 0)
            new_balance = invoice_data.get('new_balance', 0)
            date = invoice_data.get('date', 'غير محدد')

            # حساب القيم
            usd_value = amount * 3  # 1 إكسا = 3 دولار
            token_value = amount * 524.288  # 1 إكسا = 524,288 ألف توكن

            # إنشاء النص المنسق باستخدام النظام الموحد
            if invoice_message_manager:
                # إعداد البيانات للنظام الموحد
                qr_invoice_data = {
                    'transaction_type': transaction_type,
                    'invoice_number': invoice_number,
                    'user_name': user_name,
                    'wallet_number': wallet_number,
                    'amount': amount,
                    'previous_balance': previous_balance,
                    'new_balance': new_balance,
                    'date': date
                }
                qr_data = invoice_message_manager.get_invoice_message_by_type(qr_invoice_data)
            else:
                # النظام القديم كاحتياطي
                # تنسيق التوكن
                if token_value >= 1000:  # مليون أو أكثر
                    token_text = f"{token_value/1000:.2f} مليون توكين"
                else:
                    token_text = f"{token_value:.2f} ألف توكين"

                # تحديد نوع الفاتورة للعنوان
                if "خصم" in transaction_type:
                    invoice_title = "🧾︙فاتورة خصم رصيد إلكترونية"
                    amount_label = "💰︙المبلغ المخصوم"
                    success_message = "✅︙تم خصم الرصيد بنجاح - شكراً لك على استخدام خدماتنا"
                else:
                    invoice_title = "🧾︙فاتورة إضافة رصيد إلكترونية"
                    amount_label = "💰︙المبلغ المضاف"
                    success_message = "✅︙تم إضافة الرصيد بنجاح - شكراً لك على استخدام خدماتنا"

                qr_data = f"""{invoice_title}
🔢︙رقم الفاتورة︙{invoice_number}

👤︙العميل︙{user_name}
🏦︙رقم المحفظة︙{wallet_number}

{amount_label}︙{amount:.0f} إكسا
📊︙الرصيد السابق︙{previous_balance:.0f} إكسا
💳︙الرصيد الجديد︙{new_balance:.0f} إكسا

💵︙القيمة بالدولار︙{usd_value:.0f} USD
🪙︙القيمة بالتوكين︙{token_text}

📅︙تاريخ العملية︙{date}
🏢︙مقدم الخدمة︙نظام المحفظة الإلكترونية

{success_message}"""

            return qr_data

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء بيانات QR منسقة: {e}")
            # في حالة الخطأ، إرجاع البيانات البسيطة
            return f"Invoice: {invoice_data.get('invoice_number', '')}\nWallet: {invoice_data.get('wallet_number', '')}\nAmount: {invoice_data.get('amount', 0):.0f} EXA"
