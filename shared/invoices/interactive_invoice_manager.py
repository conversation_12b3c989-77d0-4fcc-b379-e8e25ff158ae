"""
نظام إدارة الفواتير التفاعلية
يدير الفواتير مع الأزرار التفاعلية لعرض وطباعة الفواتير
"""

import os
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Tuple

logger = logging.getLogger(__name__)

class InteractiveInvoiceManager:
    """مدير الفواتير التفاعلية"""
    
    def __init__(self):
        """تهيئة مدير الفواتير التفاعلية"""
        self.base_dir = os.path.dirname(__file__)
        self.temp_invoices_dir = os.path.join(self.base_dir, 'temp_invoices')
        os.makedirs(self.temp_invoices_dir, exist_ok=True)

        self.button_invoices_file = os.path.join(self.temp_invoices_dir, 'button_invoices.json')
        
    def save_interactive_invoice(self, invoice_number: str, pdf_path: str, user_id: int, 
                               transaction_type: str, amount: float, 
                               expires_hours: int = 24) -> bool:
        """
        حفظ فاتورة تفاعلية للوصول إليها عبر الأزرار
        
        Args:
            invoice_number: رقم الفاتورة
            pdf_path: مسار ملف PDF
            user_id: معرف المستخدم
            transaction_type: نوع المعاملة
            amount: المبلغ
            expires_hours: ساعات انتهاء الصلاحية (افتراضي 24 ساعة)
            
        Returns:
            bool: نجح الحفظ أم لا
        """
        try:
            # قراءة الفواتير الحالية
            button_invoices = self._load_button_invoices()
            
            # إنشاء بيانات الفاتورة التفاعلية
            invoice_data = {
                'pdf_path': pdf_path,
                'user_id': user_id,
                'transaction_type': transaction_type,
                'amount': amount,
                'created_at': datetime.now().isoformat(),
                'expires_at': (datetime.now() + timedelta(hours=expires_hours)).isoformat()
            }
            
            # حفظ الفاتورة
            button_invoices[invoice_number] = invoice_data
            
            # كتابة الملف
            with open(self.button_invoices_file, 'w', encoding='utf-8') as f:
                json.dump(button_invoices, f, ensure_ascii=False, indent=2)
            
            logger.info(f"✅ تم حفظ فاتورة تفاعلية: {invoice_number}")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في حفظ الفاتورة التفاعلية {invoice_number}: {e}")
            return False
    
    def get_interactive_invoice(self, invoice_number: str) -> Optional[Dict[str, Any]]:
        """
        الحصول على بيانات فاتورة تفاعلية
        
        Args:
            invoice_number: رقم الفاتورة
            
        Returns:
            dict أو None: بيانات الفاتورة أو None إذا لم توجد
        """
        try:
            button_invoices = self._load_button_invoices()
            invoice_data = button_invoices.get(invoice_number)
            
            if not invoice_data:
                logger.warning(f"⚠️ فاتورة غير موجودة: {invoice_number}")
                return None
            
            # التحقق من انتهاء الصلاحية
            expires_at = datetime.fromisoformat(invoice_data['expires_at'])
            if datetime.now() > expires_at:
                logger.warning(f"⚠️ فاتورة منتهية الصلاحية: {invoice_number}")
                # حذف الفاتورة المنتهية الصلاحية
                self._remove_invoice(invoice_number)
                return None
            
            return invoice_data
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على الفاتورة التفاعلية {invoice_number}: {e}")
            return None
    
    def cleanup_expired_invoices(self) -> int:
        """
        تنظيف الفواتير منتهية الصلاحية
        
        Returns:
            int: عدد الفواتير المحذوفة
        """
        try:
            button_invoices = self._load_button_invoices()
            current_time = datetime.now()
            expired_invoices = []
            
            # البحث عن الفواتير منتهية الصلاحية
            for invoice_number, invoice_data in button_invoices.items():
                try:
                    expires_at = datetime.fromisoformat(invoice_data['expires_at'])
                    if current_time > expires_at:
                        expired_invoices.append(invoice_number)
                except Exception as e:
                    logger.error(f"❌ خطأ في فحص انتهاء صلاحية الفاتورة {invoice_number}: {e}")
                    expired_invoices.append(invoice_number)  # حذف الفواتير التالفة أيضاً
            
            # حذف الفواتير منتهية الصلاحية
            for invoice_number in expired_invoices:
                del button_invoices[invoice_number]
            
            # حفظ التغييرات
            if expired_invoices:
                with open(self.button_invoices_file, 'w', encoding='utf-8') as f:
                    json.dump(button_invoices, f, ensure_ascii=False, indent=2)
                
                logger.info(f"✅ تم حذف {len(expired_invoices)} فاتورة منتهية الصلاحية")
            
            return len(expired_invoices)
            
        except Exception as e:
            logger.error(f"❌ خطأ في تنظيف الفواتير منتهية الصلاحية: {e}")
            return 0
    
    def _load_button_invoices(self) -> Dict[str, Any]:
        """تحميل الفواتير التفاعلية من الملف"""
        try:
            if os.path.exists(self.button_invoices_file):
                with open(self.button_invoices_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            logger.error(f"❌ خطأ في تحميل الفواتير التفاعلية: {e}")
            return {}
    
    def _remove_invoice(self, invoice_number: str) -> bool:
        """حذف فاتورة من النظام"""
        try:
            button_invoices = self._load_button_invoices()
            if invoice_number in button_invoices:
                del button_invoices[invoice_number]
                
                with open(self.button_invoices_file, 'w', encoding='utf-8') as f:
                    json.dump(button_invoices, f, ensure_ascii=False, indent=2)
                
                logger.info(f"✅ تم حذف الفاتورة: {invoice_number}")
                return True
            return False
            
        except Exception as e:
            logger.error(f"❌ خطأ في حذف الفاتورة {invoice_number}: {e}")
            return False
    
    def get_invoice_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الفواتير التفاعلية"""
        try:
            button_invoices = self._load_button_invoices()
            current_time = datetime.now()
            
            total_invoices = len(button_invoices)
            active_invoices = 0
            expired_invoices = 0
            
            for invoice_data in button_invoices.values():
                try:
                    expires_at = datetime.fromisoformat(invoice_data['expires_at'])
                    if current_time <= expires_at:
                        active_invoices += 1
                    else:
                        expired_invoices += 1
                except:
                    expired_invoices += 1
            
            return {
                'total_invoices': total_invoices,
                'active_invoices': active_invoices,
                'expired_invoices': expired_invoices,
                'last_updated': current_time.isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على إحصائيات الفواتير: {e}")
            return {
                'total_invoices': 0,
                'active_invoices': 0,
                'expired_invoices': 0,
                'error': str(e)
            }

# إنشاء مثيل عام للاستخدام
interactive_invoice_manager = InteractiveInvoiceManager()
