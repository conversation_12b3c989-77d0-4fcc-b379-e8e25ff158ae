# نظام إدارة الفواتير الإلكترونية
# Electronic Invoice Management System

## نظرة عامة

نظام شامل لإدارة وإنشاء الفواتير الإلكترونية مع دعم كامل للغة العربية وميزات متقدمة للتحكم في التصميم والتخطيط.

## الميزات الرئيسية

### 🎯 **إنشاء الفواتير**
- إنشاء فواتير PDF عالية الجودة
- دعم كامل للنصوص العربية
- QR Code مع بيانات الفاتورة مشفرة
- تصميم احترافي مع ألوان مخصصة

### 🎨 **التحكم في التصميم**
- **عنوان الفاتورة**: تحكم كامل في الموقع والمحاذاة والتباعد
- **الشعار**: تخصيص الموقع والحجم والتباعد
- **تخطيط الرأس**: عروض أعمدة مخصصة (إزالة التقسيم المتساوي)
- **الملاحظات**: تحكم في الموقع والنمط والتباعد

### 💱 **العملات والمعاملات**
- إكسا (العملة الأساسية)
- دولار أمريكي (1 إكسا = 3 دولار)
- توكن (1 إكسا = 524,288 ألف توكن)
- دعم إضافة وخصم الرصيد

## الملفات الأساسية

- `invoice_manager.py`: الملف الرئيسي لإدارة الفواتير
- `interactive_invoice_manager.py`: واجهة تفاعلية لإنشاء الفواتير
- `pdf_to_image.py`: تحويل ملفات PDF إلى صور

## الاستخدام السريع

```python
from shared.invoices.invoice_manager import InvoiceManager

# إنشاء مدير الفواتير
invoice_manager = InvoiceManager()

# بيانات الفاتورة
invoice_data = {
    "invoice_number": "8081234567",
    "user_name": "أحمد محمد",
    "wallet_number": "9091234567",
    "transaction_type": "إضافة رصيد",
    "amount": 100,
    "currency": "إكسا",
    "previous_balance": 50,
    "new_balance": 150
}

# إنشاء الفاتورة
success, pdf_path, message = invoice_manager.create_invoice_pdf(invoice_data)

if success:
    print(f"تم إنشاء الفاتورة: {pdf_path}")
else:
    print(f"خطأ: {message}")
```

## التخصيص السريع

### عنوان على اليمين مع محاذاة يسار
```python
# المثال الأكثر طلباً
invoice_manager.set_title_to_right_with_left_alignment(left_indent=40)
invoice_manager.set_invoice_title_spacing(vertical_spacing=0.15)
```

### استخدام الإعدادات المسبقة
```python
# إعدادات جاهزة للاستخدام
invoice_manager.apply_preset_header_layout('title_right_left')
invoice_manager.apply_preset_notes_settings('right_left')
```

## التوثيق التفصيلي

📚 **للحصول على دليل شامل، راجع:**
- `docs/invoices/README.md`: الدليل الرئيسي
- `docs/invoices/header_control.md`: التحكم في رأس الفاتورة
- `docs/invoices/notes_control.md`: التحكم في الملاحظات
- `docs/invoices/examples.md`: أمثلة شاملة

## المتطلبات

```
Python 3.8+
reportlab>=4.0.0
qrcode[pil]>=7.4.0
python-bidi>=0.4.2
arabic-reshaper>=3.0.0
Pillow>=10.0.0
```

## هيكل المجلدات

```
shared/invoices/
├── invoice_manager.py              # مدير الفواتير الرئيسي
├── interactive_invoice_manager.py  # واجهة تفاعلية
├── pdf_to_image.py                # محول PDF إلى صورة
├── generated/                     # الملفات المُنشأة
│   ├── PDF/                      # ملفات PDF
│   ├── QR Code/                  # صور QR Code
│   └── Image/                    # صور الفواتير
├── temp_invoices/                 # الفواتير المؤقتة
│   └── button_invoices.json      # فواتير الأزرار التفاعلية
├── assets/                        # الأصول
│   ├── logo.png                  # الشعار
│   └── fonts/                    # الخطوط العربية
├── pending_invoices.json          # الفواتير المعلقة
└── README.md                     # هذا الملف
```

## الملفات المُنشأة

- `generated/PDF/`: ملفات PDF
- `generated/QR Code/`: صور QR Code
- `generated/Image/`: صور الفواتير
- `temp_invoices/button_invoices.json`: بيانات الفواتير التفاعلية المؤقتة

## الميزات المتقدمة

### التحكم في رأس الفاتورة
- تخصيص موقع ومحاذاة عنوان الفاتورة
- التحكم في التباعد العمودي والأفقي
- تخصيص موقع وحجم الشعار
- عروض أعمدة مخصصة

### التحكم في الملاحظات
- تخصيص موقع الملاحظات
- التحكم في المحاذاة والتباعد
- تخصيص النمط والألوان
- إضافة خلفية وحدود

### الإعدادات المسبقة
- إعدادات جاهزة للاستخدام
- دوال مختصرة لسهولة التطبيق
- إمكانية حفظ الإعدادات المخصصة

## الأمان والأداء

- تشفير بيانات QR Code
- تحسين استهلاك الذاكرة
- معالجة الأخطاء الشاملة
- تسجيل العمليات (Logging)
- تنظيف الملفات المؤقتة

---

**للمزيد من التفاصيل والأمثلة، راجع ملفات التوثيق في مجلد `docs/invoices/`**
