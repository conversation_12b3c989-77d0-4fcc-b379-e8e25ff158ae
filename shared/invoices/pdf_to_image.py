"""
نظام تحويل PDF إلى صورة عالية الجودة
يستخدم PyMuPDF لتحويل فواتير PDF إلى صور للعرض
"""

import os
import logging
from typing import Tuple, Optional
import tempfile

logger = logging.getLogger(__name__)

class PDFToImageConverter:
    """محول PDF إلى صورة عالية الجودة"""
    
    def __init__(self):
        """تهيئة المحول"""
        # استخدام مجلد Image المنظم بدلاً من temp_images
        self.base_dir = os.path.dirname(__file__)
        self.generated_dir = os.path.join(self.base_dir, 'generated')
        self.img_dir = os.path.join(self.generated_dir, 'Image')

        # إنشاء المجلدات إذا لم تكن موجودة
        os.makedirs(self.generated_dir, exist_ok=True)
        os.makedirs(self.img_dir, exist_ok=True)
        
    def convert_pdf_to_image(self, pdf_path: str, dpi: int = 300) -> Tuple[bool, Optional[str], str]:
        """
        تحويل PDF إلى صورة عالية الجودة
        
        Args:
            pdf_path: مسار ملف PDF
            dpi: دقة الصورة (افتراضي 300 للجودة العالية)
            
        Returns:
            tuple: (نجح التحويل, مسار الصورة, رسالة الخطأ)
        """
        try:
            # التحقق من وجود الملف
            if not os.path.exists(pdf_path):
                return False, None, f"ملف PDF غير موجود: {pdf_path}"
            
            # استيراد PyMuPDF
            try:
                import fitz  # PyMuPDF
            except ImportError:
                return False, None, "مكتبة PyMuPDF غير مثبتة. يرجى تثبيتها باستخدام: pip install PyMuPDF"
            
            # فتح ملف PDF
            pdf_document = fitz.open(pdf_path)
            
            if len(pdf_document) == 0:
                pdf_document.close()
                return False, None, "ملف PDF فارغ"
            
            # تحويل الصفحة الأولى إلى صورة
            page = pdf_document[0]  # الصفحة الأولى
            
            # تحديد معامل التكبير للحصول على الدقة المطلوبة
            zoom = dpi / 72.0  # 72 DPI هو الافتراضي
            mat = fitz.Matrix(zoom, zoom)
            
            # تحويل الصفحة إلى صورة
            pix = page.get_pixmap(matrix=mat)
            
            # إنشاء اسم ملف للصورة في المجلد المخصص
            pdf_filename = os.path.basename(pdf_path)
            image_filename = f"{os.path.splitext(pdf_filename)[0]}_preview.png"
            image_path = os.path.join(self.img_dir, image_filename)
            
            # حفظ الصورة
            pix.save(image_path)
            
            # إغلاق الموارد
            pix = None
            pdf_document.close()
            
            logger.info(f"✅ تم تحويل PDF إلى صورة بنجاح: {image_path}")
            return True, image_path, "تم التحويل بنجاح"
            
        except Exception as e:
            error_msg = f"خطأ في تحويل PDF إلى صورة: {str(e)}"
            logger.error(error_msg)
            return False, None, error_msg
    
    def convert_pdf_to_high_quality_image(self, pdf_path: str) -> Tuple[bool, Optional[str], str]:
        """
        تحويل PDF إلى صورة بجودة عالية (300 DPI)
        
        Args:
            pdf_path: مسار ملف PDF
            
        Returns:
            tuple: (نجح التحويل, مسار الصورة, رسالة الخطأ)
        """
        return self.convert_pdf_to_image(pdf_path, dpi=300)
    
    def cleanup_image(self, image_path: str) -> bool:
        """
        حذف صورة (اختياري - للتنظيف عند الحاجة)

        Args:
            image_path: مسار الصورة المراد حذفها

        Returns:
            bool: نجح الحذف أم لا
        """
        try:
            if os.path.exists(image_path):
                os.remove(image_path)
                logger.info(f"✅ تم حذف الصورة: {image_path}")
                return True
            else:
                logger.warning(f"⚠️ الصورة غير موجودة: {image_path}")
                return False
        except Exception as e:
            logger.error(f"❌ خطأ في حذف الصورة {image_path}: {e}")
            return False

    def check_image_exists(self, pdf_path: str) -> Tuple[bool, Optional[str]]:
        """
        التحقق من وجود صورة مستخرجة من PDF

        Args:
            pdf_path: مسار ملف PDF

        Returns:
            tuple: (هل الصورة موجودة, مسار الصورة إن وجدت)
        """
        try:
            pdf_filename = os.path.basename(pdf_path)
            image_filename = f"{os.path.splitext(pdf_filename)[0]}_preview.png"
            image_path = os.path.join(self.img_dir, image_filename)

            if os.path.exists(image_path):
                logger.info(f"✅ تم العثور على صورة موجودة: {image_path}")
                return True, image_path
            else:
                logger.info(f"ℹ️ لم يتم العثور على صورة موجودة: {image_path}")
                return False, None

        except Exception as e:
            logger.error(f"❌ خطأ في التحقق من وجود الصورة: {e}")
            return False, None

    def cleanup_all_temp_images(self) -> int:
        """
        حذف جميع الصور المؤقتة
        
        Returns:
            int: عدد الصور المحذوفة
        """
        deleted_count = 0
        try:
            if os.path.exists(self.temp_dir):
                for filename in os.listdir(self.temp_dir):
                    if filename.endswith('.png') or filename.endswith('.jpg'):
                        file_path = os.path.join(self.temp_dir, filename)
                        try:
                            os.remove(file_path)
                            deleted_count += 1
                        except Exception as e:
                            logger.error(f"❌ خطأ في حذف {file_path}: {e}")
                
                logger.info(f"✅ تم حذف {deleted_count} صورة مؤقتة")
            
        except Exception as e:
            logger.error(f"❌ خطأ في تنظيف الصور المؤقتة: {e}")
        
        return deleted_count
    
    def get_pdf_info(self, pdf_path: str) -> dict:
        """
        الحصول على معلومات ملف PDF
        
        Args:
            pdf_path: مسار ملف PDF
            
        Returns:
            dict: معلومات الملف
        """
        try:
            if not os.path.exists(pdf_path):
                return {"error": "الملف غير موجود"}
            
            import fitz
            pdf_document = fitz.open(pdf_path)
            
            info = {
                "page_count": len(pdf_document),
                "file_size": os.path.getsize(pdf_path),
                "metadata": pdf_document.metadata,
                "is_encrypted": pdf_document.needs_pass
            }
            
            pdf_document.close()
            return info
            
        except Exception as e:
            return {"error": f"خطأ في قراءة معلومات PDF: {str(e)}"}

# إنشاء مثيل عام للاستخدام
pdf_converter = PDFToImageConverter()
