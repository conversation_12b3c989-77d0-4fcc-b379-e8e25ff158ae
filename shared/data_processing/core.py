#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الدوال الأساسية لمعالجة النصوص
clean_text, detect_language...
"""

import logging
import re
from datetime import datetime
from typing import Dict, Any, Optional, List, Union

from .commands import CommandProcessor
from .buttons import ButtonProcessor
from .templates import MessageTemplates
from .monitoring import MonitoringProcessor
from .shared_utils import TextUtils

logger = logging.getLogger(__name__)

class SharedTextProcessor:
    """فئة معالجة النصوص المشتركة بين البوتات"""
    
    def __init__(self):
        """تهيئة معالج النصوص المشترك"""
        self.supported_languages = ['ar', 'en']
        self.default_language = 'ar'
        
        # تهيئة المعالجات الفرعية
        self.command_processor = CommandProcessor()
        self.button_processor = ButtonProcessor()
        self.message_templates = MessageTemplates()
        self.monitoring_processor = MonitoringProcessor()
        self.text_utils = TextUtils()
    
    def clean_text(self, text: str) -> str:
        """تنظيف النصوص من الرموز والمسافات الزائدة"""
        return self.text_utils.clean_text(text)
    
    def detect_language(self, text: str) -> str:
        """كشف لغة النص"""
        return self.text_utils.detect_language(text)
    
    def validate_message(self, message_text: str) -> tuple:
        """التحقق من صحة الرسالة"""
        return self.text_utils.validate_message(message_text)
    
    def truncate_text(self, text: str, max_length: int = 100) -> str:
        """اقتطاع النص إلى طول محدد"""
        return self.text_utils.truncate_text(text, max_length)
    
    def process_text_message(self, message_text: str, user, language: str = 'ar') -> Dict[str, Any]:
        """معالجة الرسائل النصية العادية"""
        try:
            # تنظيف النص
            clean_text = self.clean_text(message_text)
            
            # البحث في خريطة الأزرار
            button_type = self.button_processor.get_button_mapping(language).get(clean_text)
            
            if button_type and button_type in self.message_templates.get_all_templates():
                return {
                    "text": self.message_templates.get_message(button_type, language),
                    "type": button_type,
                    "language": language,
                    "has_image": button_type in ['location', 'about', 'works', 'experience', 'achievements'],
                    "image_key": button_type if button_type in ['location', 'about', 'works', 'experience', 'achievements'] else None
                }
            
            # رد افتراضي
            username = f"{user.first_name} {getattr(user, 'last_name', '')}".strip() if getattr(user, 'last_name', None) else user.first_name
            if not username:
                username = getattr(user, 'username', None) or "مستخدم غير محدد"
                
            default_messages = {
                'ar': f"مرحباً {username}! 👋\n\nيمكنك استخدام الأزرار أدناه للتنقل بين الأقسام المختلفة.\nأو يمكنك كتابة سؤال للمساعد الذكي إكسا.",
                'en': f"Hello {username}! 👋\n\nYou can use the buttons below to navigate between different sections.\nOr you can ask Exa AI assistant a question."
            }
            
            return {
                "text": default_messages.get(language, default_messages['ar']),
                "type": "default",
                "language": language,
                "has_image": False,
                "image_key": None
            }
                
        except Exception as e:
            logger.error(f"خطأ في معالجة النص: {e}")
            error_messages = {
                'ar': "عذراً، حدث خطأ في معالجة طلبك. يرجى المحاولة مرة أخرى.",
                'en': "Sorry, an error occurred while processing your request. Please try again."
            }
            return {
                "text": error_messages.get(language, error_messages['ar']),
                "type": "error",
                "language": language,
                "has_image": False,
                "image_key": None
            }
    
    def get_welcome_message(self, username: str, language: str = 'ar') -> str:
        """الحصول على رسالة الترحيب"""
        return self.message_templates.get_welcome_message(username, language)
    
    def process_command(self, command_text: str, language: str = 'ar') -> Optional[str]:
        """معالجة الأوامر"""
        return self.command_processor.process_command(command_text, language)

    def process_admin_message(self, message_text: str, language: str = 'ar') -> Dict[str, Any]:
        """معالجة رسائل البوت الإداري"""
        try:
            # تنظيف النص
            clean_text = self.clean_text(message_text)

            # خريطة الأوامر الإدارية
            admin_commands = {
                'ar': {
                    # أوامر البدء والإدارة
                    "start": ["مدير", "ادارة", "ابدا", "بدء", "/start", "/admin"],
                    "update": ["تحديث", "/update"],

                    # أوامر المراقبة والحالة
                    "main_bot_status": ["🟢 حالة البوت الرئيسي"],
                    "monitoring": ["📊 المراقبة المباشرة"],

                    # أوامر إدارة المستخدمين
                    "user_management": ["👥 إدارة المستخدمين"],
                    "users_list": ["👥 قائمة المستخدمين"],
                    "add_user": ["➕ إضافة مستخدم"],
                    "remove_user": ["➖ إزالة مستخدم"],
                    "ban_user": ["🚫 حظر مستخدم"],
                    "restrict_user": ["⚠️ تقييد مستخدم"],
                    "user_report": ["📊 تقرير مستخدم"],
                    "search_user": ["🔍 بحث مستخدم"],

                    # أوامر الإحصائيات والتقارير
                    "statistics": ["📈 الإحصائيات والتقارير"],
                    "broadcast": ["📰 إدارة النشرات"],
                    "backup": ["🗄️ النسخ الاحتياطية"],
                    "settings": ["⚙️ إعدادات النظام"],
                    "alerts": ["🚨 التنبيهات والإنذارات"],
                    "sync": ["🔄 مزامنة البيانات"],
                    "lists": ["📋 القوائم"],

                    # أوامر التنقل
                    "back_to_main": ["🔙 العودة للقائمة الرئيسية"]
                }
            }

            # البحث عن نوع الأمر
            for command_type, command_list in admin_commands.get(language, admin_commands['ar']).items():
                if clean_text in command_list:
                    return {
                        "command_type": command_type,
                        "original_text": message_text,
                        "clean_text": clean_text,
                        "language": language,
                        "is_admin_command": True
                    }

            # إذا لم يتم العثور على أمر محدد
            return {
                "command_type": "unknown",
                "original_text": message_text,
                "clean_text": clean_text,
                "language": language,
                "is_admin_command": False
            }

        except Exception as e:
            logger.error(f"خطأ في معالجة رسالة الإدارة: {e}")
            return {
                "command_type": "error",
                "original_text": message_text,
                "clean_text": message_text,
                "language": language,
                "is_admin_command": False,
                "error": str(e)
            }

    def process_main_bot_message(self, message_text: str, user, language: str = 'ar') -> Dict[str, Any]:
        """معالجة رسائل البوت الرئيسي"""
        try:
            # تنظيف النص
            clean_text = self.clean_text(message_text)

            # خريطة الأوامر الرئيسية
            main_commands = {
                'ar': {
                    # أوامر البدء والتحديث
                    "start": ["/ابدا", "ابدا", "/start"],
                    "update": ["/تحديث", "تحديث", "/update"],
                    "help": ["/مساعدة", "مساعدة", "/المساعدة", "المساعدة", "❓ المساعدة", "/help"],

                    # أوامر المحتوى الأساسي
                    "location": ["📍 موقعي", "/موقعي", "موقعي"],
                    "about": ["👨‍💼 نبذة عني", "/حول", "حول"],
                    "works": ["💼 أعمالي", "/أعمالي", "أعمالي"],
                    "experience": ["🎯 خبرتي", "/خبرتي", "خبرتي"],
                    "achievements": ["🏆 إنجازاتي", "/إنجازاتي", "إنجازاتي"],

                    # أوامر المساعد الذكي
                    "ai_assistant": ["🤖 إكسا الذكي", "/ذكي", "ذكي"],
                    "exa_ai_pro": ["🧠 إكسا برو"],

                    # أوامر المحفظة
                    "my_wallet": ["💰 محفظتي", "/محفظتي", "محفظتي"],
                    "my_balance": ["💰 عرض الرصيد", "/رصيد", "رصيد"],
                    "add_balance": ["➕ إضافة رصيد", "/إضافة_رصيد", "إضافة رصيد"],
                    "show_transactions": ["📊 عرض المعاملات", "/معاملات", "معاملات"],
                    "request_loan": ["💳 خدمة سلفني", "/سلفني", "سلفني"],
                    "usage_report": ["📈 تقرير الاستخدام", "/تقرير", "تقرير"],
                    "back_to_main": ["🔙 العودة للقائمة الرئيسية", "/عودة", "عودة"]
                }
            }

            # البحث عن نوع الأمر
            for command_type, command_list in main_commands.get(language, main_commands['ar']).items():
                if clean_text in command_list:
                    return {
                        "command_type": command_type,
                        "original_text": message_text,
                        "clean_text": clean_text,
                        "language": language,
                        "is_main_command": True,
                        "has_image": command_type in ['location', 'about', 'works', 'experience', 'achievements'],
                        "image_key": command_type if command_type in ['location', 'about', 'works', 'experience', 'achievements'] else None
                    }

            # إذا لم يتم العثور على أمر محدد، معالجة كرسالة عادية
            username = f"{user.first_name} {getattr(user, 'last_name', '')}".strip() if getattr(user, 'last_name', None) else user.first_name
            if not username:
                username = getattr(user, 'username', None) or "مستخدم غير محدد"

            return {
                "command_type": "general_message",
                "original_text": message_text,
                "clean_text": clean_text,
                "language": language,
                "is_main_command": False,
                "has_image": False,
                "image_key": None,
                "default_response": f"مرحباً {username}! 👋\n\nيمكنك استخدام الأزرار أدناه للتنقل بين الأقسام المختلفة.\nأو يمكنك كتابة سؤال للمساعد الذكي إكسا."
            }

        except Exception as e:
            logger.error(f"خطأ في معالجة رسالة البوت الرئيسي: {e}")
            return {
                "command_type": "error",
                "original_text": message_text,
                "clean_text": message_text,
                "language": language,
                "is_main_command": False,
                "has_image": False,
                "image_key": None,
                "error": str(e)
            }

    def get_available_restrictions(self, language: str = 'ar') -> Dict[str, Any]:
        """الحصول على القيود المتاحة في البوت الرئيسي"""
        restrictions = {
            'ar': {
                'ai_assistant': {
                    'name': 'إكسا الذكي',
                    'description': 'منع الوصول للمساعد الذكي إكسا',
                    'callback_data': 'restrict_ai_assistant'
                },
                'exa_ai_pro': {
                    'name': 'إكسا برو',
                    'description': 'منع الوصول لإكسا الذكي برو',
                    'callback_data': 'restrict_exa_ai_pro'
                },
                'location_access': {
                    'name': 'الموقع',
                    'description': 'منع الوصول لمعلومات الموقع',
                    'callback_data': 'restrict_location_access'
                },
                'about_access': {
                    'name': 'النبذة',
                    'description': 'منع الوصول للنبذة الشخصية',
                    'callback_data': 'restrict_about_access'
                },
                'works_access': {
                    'name': 'الأعمال',
                    'description': 'منع الوصول لقسم الأعمال',
                    'callback_data': 'restrict_works_access'
                },
                'experience_access': {
                    'name': 'الخبرة',
                    'description': 'منع الوصول لقسم الخبرة',
                    'callback_data': 'restrict_experience_access'
                },
                'achievements_access': {
                    'name': 'الإنجازات',
                    'description': 'منع الوصول لقسم الإنجازات',
                    'callback_data': 'restrict_achievements_access'
                },
                'file_sending': {
                    'name': 'إرسال الملفات',
                    'description': 'منع إرسال الملفات والوسائط',
                    'callback_data': 'restrict_file_sending'
                },
                'help_access': {
                    'name': 'المساعدة',
                    'description': 'منع الوصول لقسم المساعدة',
                    'callback_data': 'restrict_help_access'
                }
            }
        }

        return restrictions.get(language, restrictions['ar'])

    def format_restriction_selection_message(self, user_info: Dict[str, Any], current_restrictions: list = None, language: str = 'ar') -> Dict[str, Any]:
        """تنسيق رسالة اختيار القيود"""
        if current_restrictions is None:
            current_restrictions = []

        available_restrictions = self.get_available_restrictions(language)

        # تنسيق معلومات المستخدم
        name = user_info.get('اسم المستخدم', 'غير محدد')
        username = user_info.get('معرف المستخدم', 'غير محدد')
        user_id = user_info.get('أيدي المستخدم', 'غير محدد')

        message = f"""⚠️︙تقييد المستخدم

👤︙الأسم: {name}
📧︙المعرف: {username}
🆔︙الأيدي: {user_id}

🚫︙القيود المتاحة:
🤖 إكسا الذكي | 🧠 إكسا برو
📍 الموقع | 👨‍💼 النبذة
💼 الأعمال | 🎯 الخبرة
🏆 الإنجازات | 📎 إرسال الملفات
❓ المساعدة

"""

        # إضافة القيود الحالية
        if current_restrictions:
            message += "✅︙القيود المطبقة حالياً:\n"
            for restriction in current_restrictions:
                if restriction in available_restrictions:
                    restriction_info = available_restrictions[restriction]
                    icon = {'ai_assistant': '🤖', 'exa_ai_pro': '🧠', 'location_access': '📍',
                           'about_access': '👨‍💼', 'works_access': '💼', 'experience_access': '🎯',
                           'achievements_access': '🏆', 'file_sending': '📎', 'help_access': '❓'}.get(restriction, '🚫')
                    message += f"• {icon} منع {restriction_info['name']}\n"
            message += "\n"

        message += "💡︙ملاحظة: يمكنك اختيار عدة قيود للمستخدم الواحد\n"
        message += "🔄︙استخدم الأزرار أدناه لإضافة أو إزالة القيود"

        return {
            "message": message,
            "restrictions": available_restrictions,
            "current_restrictions": current_restrictions
        }

    def create_restriction_inline_keyboard(self, user_id: str, current_restrictions: list = None, language: str = 'ar'):
        """إنشاء لوحة مفاتيح مضمنة لنظام التقييد"""
        from telegram import InlineKeyboardButton, InlineKeyboardMarkup

        if current_restrictions is None:
            current_restrictions = []

        available_restrictions = self.get_available_restrictions(language)
        keyboard = []

        # خريطة الرموز للقيود
        restriction_icons = {
            'ai_assistant': '🤖',
            'exa_ai_pro': '🧠',
            'location_access': '📍',
            'about_access': '👨‍💼',
            'works_access': '💼',
            'experience_access': '🎯',
            'achievements_access': '🏆',
            'file_sending': '📎',
            'help_access': '❓'
        }

        # إضافة أزرار القيود (صفين في كل صف)
        restriction_buttons = []
        for restriction_key, restriction_info in available_restrictions.items():
            # تحديد ما إذا كان القيد مفعل أم لا
            is_active = restriction_key in current_restrictions
            icon = restriction_icons.get(restriction_key, '🚫')
            status_icon = '✅' if is_active else '❌'
            button_text = f"{status_icon} {icon} {restriction_info['name']}"
            callback_data = f"toggle_restriction_{user_id}_{restriction_key}"

            restriction_buttons.append(InlineKeyboardButton(button_text, callback_data=callback_data))

            # إضافة صف كل زرين
            if len(restriction_buttons) == 2:
                keyboard.append(restriction_buttons)
                restriction_buttons = []

        # إضافة الزر المتبقي إذا كان موجود
        if restriction_buttons:
            keyboard.append(restriction_buttons)

        # إضافة أزرار التحكم
        control_buttons = [
            InlineKeyboardButton("🚫 إزالة جميع القيود", callback_data=f"remove_all_restrictions_{user_id}"),
            InlineKeyboardButton("✅ تطبيق القيود", callback_data=f"apply_restrictions_{user_id}")
        ]
        keyboard.append(control_buttons)

        # زر الإلغاء
        keyboard.append([InlineKeyboardButton("❌ إلغاء", callback_data="cancel_restriction")])

        return InlineKeyboardMarkup(keyboard)

    def format_restriction_result_message(self, user_info: Dict[str, Any], applied_restrictions: list, language: str = 'ar') -> str:
        """تنسيق رسالة نتيجة تطبيق القيود"""
        available_restrictions = self.get_available_restrictions(language)

        # تنسيق معلومات المستخدم
        name = user_info.get('اسم المستخدم', 'غير محدد')
        username = user_info.get('معرف المستخدم', 'غير محدد')
        user_id = user_info.get('أيدي المستخدم', 'غير محدد')

        message = f"""✅︙تم تطبيق القيود بنجاح

👤︙الأسم: {name}
📧︙المعرف: {username}
🆔︙الأيدي: {user_id}

"""

        if applied_restrictions:
            message += "🚫︙القيود المطبقة:\n"
            for restriction in applied_restrictions:
                if restriction in available_restrictions:
                    restriction_info = available_restrictions[restriction]
                    icon = {'ai_assistant': '🤖', 'exa_ai_pro': '🧠', 'location_access': '📍',
                           'about_access': '👨‍💼', 'works_access': '💼', 'experience_access': '🎯',
                           'achievements_access': '🏆', 'file_sending': '📎', 'help_access': '❓'}.get(restriction, '🚫')
                    message += f"• {icon} منع {restriction_info['name']}\n"
        else:
            message += "✅︙تم إزالة جميع القيود من هذا المستخدم\n"

        message += f"\n📅︙تاريخ التطبيق: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

        return message
    
    def process_inline_button(self, button_data: str, language: str = 'ar') -> Dict[str, Any]:
        """معالجة الأزرار المضمنة"""
        return self.button_processor.process_inline_button(button_data, language)
    
    def process_reply_button(self, button_text: str, language: str = 'ar') -> Dict[str, Any]:
        """معالجة الأزرار السفلية"""
        return self.button_processor.process_reply_button(button_text, language)
    
    def get_button_text_mapping(self, language: str = 'ar') -> Dict[str, str]:
        """الحصول على خريطة النصوص والأزرار"""
        return self.button_processor.get_button_mapping(language)
    
    def get_message_type(self, message_text: str, language: str = 'ar') -> str:
        """تحديد نوع الرسالة"""
        button_mapping = self.get_button_text_mapping(language)
        return button_mapping.get(message_text, "unknown")
    
    def get_supported_commands(self, language: str = 'ar') -> List[str]:
        """الحصول على قائمة الأوامر المدعومة"""
        return self.command_processor.get_supported_commands(language)
    
    # دوال المراقبة
    def format_live_monitoring_message(self, notification_id: str, stats: dict) -> str:
        """تنسيق رسالة المراقبة المباشرة"""
        return self.monitoring_processor.format_live_monitoring_message(notification_id, stats)
    
    def generate_notification_id(self) -> str:
        """توليد رقم إشعار عشوائي"""
        return self.monitoring_processor.generate_notification_id()
    
    def get_current_stats(self) -> dict:
        """الحصول على الإحصائيات الحالية"""
        return self.monitoring_processor.get_current_stats()
    
    def process_monitoring_message(self, message_data: dict) -> str:
        """معالجة رسائل المراقبة"""
        return self.monitoring_processor.process_monitoring_message(message_data)

    def process_callback_query(self, callback_data: str, language: str = 'ar') -> Dict[str, Any]:
        """معالجة استدعاءات الأزرار المضمنة"""
        return self.button_processor.process_callback_query(callback_data, language)

    def get_pdf_file_path(self, file_type: str) -> Optional[str]:
        """الحصول على مسار ملف PDF"""
        try:
            import os
            import sys

            # إضافة مسار البوت الرئيسي الصحيح
            main_bot_path = os.path.join(os.path.dirname(__file__), '..', '..', 'main', 'core')
            sys.path.append(main_bot_path)

            from config import PDF_FILES
            pdf_path = PDF_FILES.get(file_type)

            # التحقق من وجود الملف
            if pdf_path and os.path.exists(pdf_path):
                logger.info(f"تم العثور على ملف PDF: {pdf_path}")
                return pdf_path
            else:
                logger.warning(f"ملف PDF غير موجود: {pdf_path}")
                return None

        except Exception as e:
            logger.error(f"خطأ في الحصول على مسار ملف PDF: {e}")
            return None
