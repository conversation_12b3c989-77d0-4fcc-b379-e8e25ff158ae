#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام الرسائل الموحد للفواتير
يدير جميع رسائل الفواتير بطريقة ذكية وموحدة
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class InvoiceMessageManager:
    """مدير الرسائل الموحد للفواتير"""
    
    def __init__(self):
        """تهيئة مدير رسائل الفواتير"""
        self.operation_types = {
            # عمليات الرصيد العادي
            "إضافة رصيد": {
                "amount_label": "الرصيد المضاف",
                "success_message": "تم إضافة الرصيد بنجاح"
            },
            "خصم رصيد": {
                "amount_label": "المبلغ المخصوم", 
                "success_message": "تم خصم الرصيد بنجاح"
            },
            
            # عمليات السلفة
            "إضافة سلفة": {
                "amount_label": "الرصيد السلفة",
                "success_message": "تم إضافة السلفة بنجاح"
            },
            "خصم سلفة": {
                "amount_label": "المبلغ المخصوم من السلفة",
                "success_message": "تم خصم السلفة بنجاح"
            },
            
            # عمليات منح وسداد السلف
            "منح سلفة": {
                "amount_label": "مبلغ السلفة الممنوحة",
                "success_message": "تم منح السلفة بنجاح"
            },
            "سداد سلفة": {
                "amount_label": "مبلغ السداد",
                "success_message": "تم سداد السلفة بنجاح"
            }
        }
    
    def format_currency(self, amount: float) -> str:
        """تنسيق العملة بدون .00"""
        if amount == int(amount):
            return f"{int(amount)} إكسا"
        return f"{amount} إكسا"
    
    def format_usd_value(self, amount: float) -> str:
        """تنسيق القيمة بالدولار"""
        usd_value = amount * 3  # 1 إكسا = 3 دولار
        if usd_value == int(usd_value):
            return f"{int(usd_value)} USD"
        return f"{usd_value} USD"
    
    def format_token_value(self, amount: float) -> str:
        """تنسيق القيمة بالتوكين مع الآلاف والملايين"""
        tokens = amount * 524.288  # 1 إكسا = 524.288 ألف توكين
        
        if tokens >= 1000:
            if tokens >= 1000000:
                return f"{tokens/1000000:.2f} مليون توكين"
            else:
                return f"{tokens/1000:.2f} الف توكين"
        else:
            return f"{tokens:.0f} توكين"
    
    def create_balance_invoice_message(self, invoice_data: Dict[str, Any]) -> str:
        """
        إنشاء رسالة فاتورة الرصيد الموحدة
        تتعرف تلقائياً على نوع العملية (إضافة أم خصم)
        """
        try:
            # استخراج البيانات
            transaction_type = invoice_data.get('transaction_type', 'إضافة رصيد')
            invoice_number = invoice_data.get('invoice_number', '')
            user_name = invoice_data.get('user_name', 'غير محدد')
            wallet_number = invoice_data.get('wallet_number', 'غير محدد')
            amount = float(invoice_data.get('amount', 0))
            previous_balance = float(invoice_data.get('previous_balance', 0))
            new_balance = float(invoice_data.get('new_balance', 0))
            date = invoice_data.get('date', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            
            # الحصول على تفاصيل نوع العملية
            operation_info = self.operation_types.get(transaction_type, self.operation_types["إضافة رصيد"])
            amount_label = operation_info["amount_label"]
            success_message = operation_info["success_message"]
            
            # بناء الرسالة الموحدة
            message = f"""🧾︙فاتورة رصيد إلكترونية
🧾︙نوع العملية︙{transaction_type}
🔢︙رقم الفاتورة︙{invoice_number}

👤︙العميل︙{user_name}
🏦︙رقم المحفظة︙{wallet_number}

💰︙{amount_label}︙{self.format_currency(amount)}
📊︙الرصيد السابق︙{self.format_currency(previous_balance)}
💳︙الرصيد الجديد︙{self.format_currency(new_balance)}

💵︙القيمة بالدولار︙{self.format_usd_value(amount)}
🪙︙القيمة بالتوكين︙{self.format_token_value(amount)}

📅︙تاريخ العملية︙{date}
🏢︙مقدم الخدمة︙نظام المحفظة الإلكترونية

✅︙{success_message} - شكراً لك على استخدام خدماتنا"""

            return message
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء رسالة فاتورة الرصيد: {e}")
            return "خطأ في إنشاء رسالة الفاتورة"
    
    def create_loan_invoice_message(self, invoice_data: Dict[str, Any]) -> str:
        """
        إنشاء رسالة فاتورة السلفة الموحدة
        تتعرف تلقائياً على نوع العملية (إضافة أم خصم سلفة)
        """
        try:
            # استخراج البيانات
            transaction_type = invoice_data.get('transaction_type', 'إضافة سلفة')
            invoice_number = invoice_data.get('invoice_number', '')
            user_name = invoice_data.get('user_name', 'غير محدد')
            wallet_number = invoice_data.get('wallet_number', 'غير محدد')
            amount = float(invoice_data.get('amount', 0))
            previous_balance = float(invoice_data.get('previous_balance', 0))
            new_balance = float(invoice_data.get('new_balance', 0))
            total_loan = float(invoice_data.get('total_loan', 0))
            loan_status = invoice_data.get('loan_status', 'نشط')
            date = invoice_data.get('date', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            
            # الحصول على تفاصيل نوع العملية
            operation_info = self.operation_types.get(transaction_type, self.operation_types["إضافة سلفة"])
            amount_label = operation_info["amount_label"]
            success_message = operation_info["success_message"]
            
            # بناء الرسالة الموحدة
            message = f"""🧾︙فاتورة رصيد إلكترونية
🧾︙نوع العملية︙{transaction_type}
🔢︙رقم الفاتورة︙{invoice_number}

👤︙العميل︙{user_name}
🏦︙رقم المحفظة︙{wallet_number}

💰︙{amount_label}︙{self.format_currency(amount)}
📊︙الرصيد السابق︙{self.format_currency(previous_balance)}
💳︙الرصيد الجديد︙{self.format_currency(new_balance)}
💸︙إجمالي السلف︙{self.format_currency(total_loan)}
🔄︙حالة السلفة︙{loan_status}

💵︙القيمة بالدولار︙{self.format_usd_value(amount)}
🪙︙القيمة بالتوكين︙{self.format_token_value(amount)}

📅︙تاريخ العملية︙{date}
🏢︙مقدم الخدمة︙نظام المحفظة الإلكترونية

✅︙{success_message} - شكراً لك على استخدام خدماتنا"""

            return message
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء رسالة فاتورة السلفة: {e}")
            return "خطأ في إنشاء رسالة الفاتورة"
    
    def create_loan_grant_payment_message(self, invoice_data: Dict[str, Any]) -> str:
        """
        إنشاء رسالة فاتورة منح/سداد السلف الموحدة
        تتعرف تلقائياً على نوع العملية (منح أم سداد)
        """
        try:
            # استخراج البيانات
            transaction_type = invoice_data.get('transaction_type', 'منح سلفة')
            invoice_number = invoice_data.get('invoice_number', '')
            user_name = invoice_data.get('user_name', 'غير محدد')
            wallet_number = invoice_data.get('wallet_number', 'غير محدد')
            amount = float(invoice_data.get('amount', 0))
            previous_balance = float(invoice_data.get('previous_balance', 0))
            new_balance = float(invoice_data.get('new_balance', 0))
            remaining_loan = float(invoice_data.get('remaining_loan', 0))
            loan_status = invoice_data.get('loan_status', 'نشط')
            date = invoice_data.get('date', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            
            # الحصول على تفاصيل نوع العملية
            operation_info = self.operation_types.get(transaction_type, self.operation_types["منح سلفة"])
            amount_label = operation_info["amount_label"]
            success_message = operation_info["success_message"]
            
            # بناء الرسالة الموحدة
            message = f"""🧾︙فاتورة رصيد إلكترونية
🧾︙نوع العملية︙{transaction_type}
🔢︙رقم الفاتورة︙{invoice_number}

👤︙العميل︙{user_name}
🏦︙رقم المحفظة︙{wallet_number}

💰︙{amount_label}︙{self.format_currency(amount)}
📊︙الرصيد السابق︙{self.format_currency(previous_balance)}
💳︙الرصيد الجديد︙{self.format_currency(new_balance)}
💸︙السلف المتبقي︙{self.format_currency(remaining_loan)}
🔄︙حالة السلفة︙{loan_status}

💵︙القيمة بالدولار︙{self.format_usd_value(amount)}
🪙︙القيمة بالتوكين︙{self.format_token_value(amount)}

📅︙تاريخ العملية︙{date}
🏢︙مقدم الخدمة︙نظام المحفظة الإلكترونية

✅︙{success_message} - شكراً لك على استخدام خدماتنا"""

            return message
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء رسالة فاتورة منح/سداد السلف: {e}")
            return "خطأ في إنشاء رسالة الفاتورة"

    def create_admin_notification_message(self, notification_data: Dict[str, Any]) -> str:
        """
        إنشاء رسالة إشعار إداري موحدة
        تتعرف تلقائياً على نوع العملية
        """
        try:
            # استخراج البيانات
            transaction_type = notification_data.get('transaction_type', 'إضافة رصيد')
            notification_id = notification_data.get('notification_id', '')
            wallet_number = notification_data.get('wallet_number', 'غير محدد')
            amount = float(notification_data.get('amount', 0))
            previous_balance = float(notification_data.get('previous_balance', 0))
            new_balance = float(notification_data.get('new_balance', 0))

            # الحصول على تفاصيل نوع العملية
            operation_info = self.operation_types.get(transaction_type, self.operation_types["إضافة رصيد"])
            success_message = operation_info["success_message"]

            # بناء الرسالة الإدارية المختصرة
            message = f"""✅︙{success_message}
🔢︙رقم الإشعار︙{notification_id}

👤︙رقم المحفظة︙{wallet_number}
💰︙الرصيد المضاف︙{self.format_currency(amount)}
📊︙الرصيد السابق︙{self.format_currency(previous_balance)}
💳︙الرصيد النهائي︙{self.format_currency(new_balance)}

💵︙القيمة المضافة بالدولار︙{self.format_usd_value(amount)}
🪙︙القيمة المضافة بالتوكين︙{self.format_token_value(amount)}"""

            return message

        except Exception as e:
            logger.error(f"خطأ في إنشاء رسالة الإشعار الإداري: {e}")
            return "خطأ في إنشاء رسالة الإشعار"

    def get_invoice_message_by_type(self, invoice_data: Dict[str, Any]) -> str:
        """
        الحصول على رسالة الفاتورة المناسبة حسب نوع العملية
        """
        try:
            transaction_type = invoice_data.get('transaction_type', '')

            # تحديد نوع الرسالة المطلوبة
            if transaction_type in ['إضافة رصيد', 'خصم رصيد']:
                return self.create_balance_invoice_message(invoice_data)
            elif transaction_type in ['إضافة سلفة', 'خصم سلفة']:
                return self.create_loan_invoice_message(invoice_data)
            elif transaction_type in ['منح سلفة', 'سداد سلفة']:
                return self.create_loan_grant_payment_message(invoice_data)
            else:
                # افتراضي: رسالة الرصيد
                return self.create_balance_invoice_message(invoice_data)

        except Exception as e:
            logger.error(f"خطأ في تحديد نوع رسالة الفاتورة: {e}")
            return self.create_balance_invoice_message(invoice_data)

    def add_operation_type(self, operation_name: str, amount_label: str, success_message: str):
        """
        إضافة نوع عملية جديد
        """
        self.operation_types[operation_name] = {
            "amount_label": amount_label,
            "success_message": success_message
        }
        logger.info(f"تم إضافة نوع عملية جديد: {operation_name}")

    def get_supported_operations(self) -> list:
        """
        الحصول على قائمة بأنواع العمليات المدعومة
        """
        return list(self.operation_types.keys())


# إنشاء مثيل عام للاستخدام
invoice_message_manager = InvoiceMessageManager()
