#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
معالجة الأوامر
"""

import logging
from typing import Dict, List, Optional

logger = logging.getLogger(__name__)

class CommandProcessor:
    """فئة معالجة الأوامر"""
    
    def __init__(self):
        """تهيئة معالج الأوامر"""
        self.commands = {
            'ar': {
                "start": ["/start", "/ابدا", "ابدا", "بدء"],
                "help": ["/help", "/مساعدة", "مساعدة", "❓ المساعدة"],
                "location": ["/location", "/موقع", "موقع", "📍 موقعي"],
                "about": ["/about", "/نبذة", "نبذة", "👨‍💼 نبذة عني"],
                "works": ["/works", "/اعمال", "اعمال", "💼 أعمالي"],
                "experience": ["/experience", "/خبرة", "خبرة", "🎯 خبرتي"],
                "achievements": ["/achievements", "/انجازات", "انجازات", "🏆 إنجازاتي"],
                "ai": ["/ai", "/ذكي", "ذكي", "🤖 إكسا الذكي"],
                "update": ["/update", "/تحديث", "تحديث"],
                "admin": ["/admin", "/ادارة", "ادارة"],
                "users": ["/users", "/مستخدمين", "مستخدمين"],
                "stats": ["/stats", "/احصائيات", "احصائيات"],
                "broadcast": ["/broadcast", "/اذاعة", "اذاعة"],
                "status": ["/status", "/حالة", "حالة"],
                "settings": ["/settings", "/اعدادات", "اعدادات"]
            },
            'en': {
                "start": ["/start", "start", "begin"],
                "help": ["/help", "help", "assistance"],
                "location": ["/location", "location", "where"],
                "about": ["/about", "about", "info"],
                "works": ["/works", "works", "projects"],
                "experience": ["/experience", "experience", "skills"],
                "achievements": ["/achievements", "achievements", "awards"],
                "ai": ["/ai", "ai", "assistant"],
                "update": ["/update", "update", "refresh"],
                "admin": ["/admin", "admin", "administration"],
                "users": ["/users", "users", "members"],
                "stats": ["/stats", "stats", "statistics"],
                "broadcast": ["/broadcast", "broadcast", "announce"],
                "status": ["/status", "status", "state"],
                "settings": ["/settings", "settings", "config"]
            }
        }
    
    def process_command(self, command_text: str, language: str = 'ar') -> Optional[str]:
        """معالجة الأوامر بشكل كامل مع دعم متعدد اللغات"""
        try:
            clean_command = command_text.strip().lower()
            
            # البحث في الأوامر المدعومة
            for command_type, command_variants in self.commands.get(language, {}).items():
                if clean_command in [variant.lower() for variant in command_variants]:
                    return command_type
            
            # البحث في اللغة الأخرى إذا لم توجد في اللغة المحددة
            other_lang = 'en' if language == 'ar' else 'ar'
            for command_type, command_variants in self.commands.get(other_lang, {}).items():
                if clean_command in [variant.lower() for variant in command_variants]:
                    return command_type
            
            return None
            
        except Exception as e:
            logger.error(f"خطأ في معالجة الأمر: {e}")
            return None
    
    def get_supported_commands(self, language: str = 'ar') -> List[str]:
        """الحصول على قائمة الأوامر المدعومة"""
        try:
            commands = []
            for command_type, command_variants in self.commands.get(language, {}).items():
                commands.extend(command_variants)
            return commands

        except Exception as e:
            logger.error(f"خطأ في الحصول على الأوامر المدعومة: {e}")
            return []
    
    def is_command(self, text: str) -> bool:
        """التحقق من كون النص أمراً"""
        try:
            return text.strip().startswith('/') or self.process_command(text) is not None
        except Exception as e:
            logger.error(f"خطأ في التحقق من الأمر: {e}")
            return False
    
    def get_command_help(self, language: str = 'ar') -> str:
        """الحصول على نص المساعدة للأوامر"""
        if language == 'ar':
            return """🤖 **الأوامر المتاحة:**

📍 **/موقع** أو **/location** - معلومات الموقع
👨‍💼 **/نبذة** أو **/about** - نبذة شخصية
💼 **/اعمال** أو **/works** - الأعمال والمشاريع
🎯 **/خبرة** أو **/experience** - الخبرات والمهارات
🏆 **/انجازات** أو **/achievements** - الإنجازات والجوائز
🤖 **/ذكي** أو **/ai** - المساعد الذكي
❓ **/مساعدة** أو **/help** - عرض هذه المساعدة
🔄 **/تحديث** أو **/update** - تحديث الأزرار

💡 **نصيحة:** يمكنك أيضاً استخدام الأزرار للتنقل!"""
        else:
            return """🤖 **Available Commands:**

📍 **/location** - Location information
👨‍💼 **/about** - Personal information
💼 **/works** - Works and projects
🎯 **/experience** - Experience and skills
🏆 **/achievements** - Achievements and awards
🤖 **/ai** - AI assistant
❓ **/help** - Show this help
🔄 **/update** - Update buttons

💡 **Tip:** You can also use buttons for navigation!"""
