#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
قوالب الرسائل
"""

import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

class MessageTemplates:
    """فئة قوالب الرسائل"""
    
    def __init__(self):
        """تهيئة قوالب الرسائل"""
        self.message_templates = {
            "welcome": {
                'ar': "🌟 مرحباً بك! {username} 🌟\n\nأهلاً وسهلاً بك في بوتي الشخصي\nاستخدم الأزرار أدناه للتعرف علي أكثر 👇",
                'en': "🌟 Welcome! {username} 🌟\n\nWelcome to my personal bot\nUse the buttons below to learn more about me 👇"
            },
            "location": {
                'ar': """📍 موقعي

🏠 أقيم في  [اكتب موقعك هنا]
🌍 المدينة  [اكتب مدينتك هنا]
🏢 مكان العمل  [اكتب مكان عملك هنا]

📞 للتواصل
• الهاتف  [رقم هاتفك]
• البريد الإلكتروني  [بريدك الإلكتروني]
• الموقع الإلكتروني  [موقعك الإلكتروني]

🗺️ يمكنك العثور علي في المنطقة المذكورة أعلاه""",
                'en': """📍 My Location

🏠 I live in  [Write your location here]
🌍 City  [Write your city here]
🏢 Workplace  [Write your workplace here]

📞 Contact
• Phone  [Your phone number]
• Email  [Your email]
• Website  [Your website]

🗺️ You can find me in the area mentioned above"""
            },
            "about": {
                'ar': """👨‍💼 نبذة عني

مرحباً! أنا **صلاح الدين منصور الدروبي**

🎯 مُلم بتصميم العلامات التجارية والشعارات والهويات البصرية
🎨 خبرة في التصميم الجرافيكي والإبداعي
💻 مطور ومصمم مواقع ويب احترافية
📱 متخصص في تطبيقات الهواتف الذكية

🌟 **رؤيتي**
تحويل الأفكار إلى تصاميم مبدعة تترك أثراً إيجابياً

💼 **خدماتي**
• تصميم الهويات البصرية والشعارات
• تطوير المواقع الإلكترونية
• تصميم التطبيقات
• الاستشارات التقنية

🎓 **التعليم**
• [اكتب مؤهلاتك التعليمية هنا]

📈 **الخبرة**
• [اكتب خبراتك المهنية هنا]""",
                'en': """👨‍💼 About Me

Hello! I'm **Salah Al-Din Mansour Al-Droubi**

🎯 Specialized in brand design, logos, and visual identities
🎨 Experience in graphic and creative design
💻 Professional web developer and designer
📱 Mobile app development specialist

🌟 **My Vision:**
Transforming ideas into creative designs that make a positive impact

💼 **My Services:**
• Visual identity and logo design
• Website development
• App design
• Technical consulting

🎓 **Education:**
• [Write your educational qualifications here]

📈 **Experience:**
• [Write your professional experience here]"""
            },
            "works": {
                'ar': """💼 أعمالي

🚀 المشاريع الحالية:
• [مشروع 1] - [وصف مختصر]
• [مشروع 2] - [وصف مختصر]
• [مشروع 3] - [وصف مختصر]

🎨 أعمال التصميم:
• تصميم هوية بصرية لـ [اسم الشركة]
• شعار [اسم المشروع]
• موقع إلكتروني لـ [اسم العميل]

💻 المشاريع التقنية:
• تطبيق [اسم التطبيق]
• نظام إدارة [نوع النظام]
• منصة [اسم المنصة]

🏆 مشاريع مميزة:
• [اسم المشروع المميز] - [الجائزة أو التقدير]
• [مشروع آخر] - [الإنجاز]

📊 الإحصائيات:
• أكثر من [X] مشروع مكتمل
• [X]+ عميل راضٍ
• [X] سنوات خبرة""",
                'en': """💼 My Works

🚀 Current Projects:
• [Project 1] - [Brief description]
• [Project 2] - [Brief description]
• [Project 3] - [Brief description]

🎨 Design Works:
• Visual identity design for [Company name]
• Logo for [Project name]
• Website for [Client name]

💻 Technical Projects:
• [App name] application
• [System type] management system
• [Platform name] platform

🏆 Featured Projects:
• [Featured project name] - [Award or recognition]
• [Another project] - [Achievement]

📊 Statistics:
• More than [X] completed projects
• [X]+ satisfied clients
• [X] years of experience"""
            },
            "experience": {
                'ar': """🎯 خبرتي

💻 المهارات التقنية:
• [مهارة 1] - [مستوى الخبرة]
• [مهارة 2] - [مستوى الخبرة]
• [مهارة 3] - [مستوى الخبرة]

🎨 مهارات التصميم:
• Adobe Photoshop - متقدم
• Adobe Illustrator - متقدم
• Figma - متوسط
• Canva - متقدم

🌐 تطوير الويب:
• HTML/CSS - متقدم
• JavaScript - متوسط
• Python - متقدم
• React - متوسط

📱 تطوير التطبيقات:
• Flutter - متوسط
• React Native - مبتدئ

🛠️ الأدوات والتقنيات:
• Git/GitHub
• VS Code
• Docker
• Linux

📈 سنوات الخبرة:
• التصميم الجرافيكي: [X] سنوات
• تطوير الويب: [X] سنوات
• إدارة المشاريع: [X] سنوات""",
                'en': """🎯 My Experience

💻 Technical Skills:
• [Skill 1] - [Experience level]
• [Skill 2] - [Experience level]
• [Skill 3] - [Experience level]

🎨 Design Skills:
• Adobe Photoshop - Advanced
• Adobe Illustrator - Advanced
• Figma - Intermediate
• Canva - Advanced

🌐 Web Development:
• HTML/CSS - Advanced
• JavaScript - Intermediate
• Python - Advanced
• React - Intermediate

📱 App Development:
• Flutter - Intermediate
• React Native - Beginner

🛠️ Tools & Technologies:
• Git/GitHub
• VS Code
• Docker
• Linux

📈 Years of Experience:
• Graphic Design: [X] years
• Web Development: [X] years
• Project Management: [X] years"""
            },
            "achievements": {
                'ar': """🏆 إنجازاتي

🥇 الجوائز والشهادات:
• [جائزة/شهادة 1] - [السنة]
• [جائزة/شهادة 2] - [السنة]
• [جائزة/شهادة 3] - [السنة]

🎓 الشهادات المهنية:
• شهادة في [اسم الدورة] من [اسم المؤسسة]
• دورة متقدمة في [المجال] 
• ورشة عمل في [الموضوع]

🌟 الإنجازات المهنية:
• تصميم هوية بصرية لأكثر من [X] شركة
• تطوير [X] موقع إلكتروني ناجح
• إدارة فريق من [X] مصممين

🥇 الجوائز والتقديرات:
• [اسم الجائزة] - [السنة]
• [اسم الجائزة] - [السنة]
• تقدير من [اسم المؤسسة]

📊 الإحصائيات:
• أكثر من [X] مشروع مكتمل
• [X]+ عميل راضٍ
• [X] سنوات خبرة
• تقييم [X]/5 من العملاء

🎓 الشهادات والدورات:
• شهادة في [اسم الدورة]
• دورة متقدمة في [المجال]
• ورشة عمل في [الموضوع]

🌟 المشاريع المميزة:
• مشروع [اسم المشروع] - [وصف مختصر]
• تصميم هوية [اسم الشركة]
• حملة إعلانية لـ [اسم العميل]

💼 العضويات المهنية:
• عضو في [اسم الجمعية]
• عضو في [اسم المنظمة]""",
                'en': """🏆 My Achievements

🥇 Awards and Certificates:
• [Award/Certificate 1] - [Year]
• [Award/Certificate 2] - [Year]
• [Award/Certificate 3] - [Year]

🎓 Professional Certificates:
• Certificate in [Course name] from [Institution]
• Advanced course in [Field]
• Workshop in [Topic]

🌟 Professional Achievements:
• Designed visual identity for more than [X] companies
• Developed [X] successful websites
• Managed a team of [X] designers

🥇 Awards and Recognition:
• [Award name] - [Year]
• [Award name] - [Year]
• Recognition from [Institution name]

📊 Statistics:
• More than [X] completed projects
• [X]+ satisfied clients
• [X] years of experience
• [X]/5 rating from clients

🎓 Certificates and Courses:
• Certificate in [Course name]
• Advanced course in [Field]
• Workshop in [Topic]

🌟 Featured Projects:
• [Project name] project - [Brief description]
• Identity design for [Company name]
• Advertising campaign for [Client name]

💼 Professional Memberships:
• Member of [Association name]
• Member of [Organization name]"""
            },
            "ai_assistant": {
                'ar': """🤖 مرحباً بك في إكسا الذكي!

اختر الوضع المناسب لك:

🧠 **إكسا الذكي برو**
• استشارات متقدمة ومعقدة
• تحليل عميق ومفصل
• مناسب للمشاريع والأعمال المهنية
• يستخدم نموذج ExaPro-Q1 المتقدم

🤖 **إكسا الذكي العادي**
• محادثات عامة وأسئلة بسيطة
• ردود سريعة ومفيدة
• مناسب للاستفسارات اليومية
• يستخدم نموذج Exa-X1

اختر الوضع الذي تريده:""",
                'en': """🤖 Welcome to Exa AI!

Choose the mode that suits you:

🧠 **Exa AI Pro**
• Advanced and complex consultations
• Deep and detailed analysis
• Suitable for professional projects and business
• Uses advanced ExaPro-Q1 model

🤖 **Exa AI Normal**
• General conversations and simple questions
• Quick and helpful responses
• Suitable for daily inquiries
• Uses Exa-X1 model

Choose the mode you want:"""
            }
        }
    
    def get_message(self, template_key: str, language: str = 'ar') -> str:
        """الحصول على رسالة من القالب"""
        try:
            template = self.message_templates.get(template_key, {})
            return template.get(language, template.get('ar', f"رسالة غير موجودة: {template_key}"))
        except Exception as e:
            logger.error(f"خطأ في الحصول على الرسالة: {e}")
            return f"خطأ في تحميل الرسالة: {template_key}"
    
    def get_welcome_message(self, username: str, language: str = 'ar') -> str:
        """الحصول على رسالة الترحيب مع اسم المستخدم"""
        try:
            template = self.get_message("welcome", language)
            return template.format(username=username)
        except Exception as e:
            logger.error(f"خطأ في تنسيق رسالة الترحيب: {e}")
            return f"مرحباً {username}! أهلاً وسهلاً بك في البوت."
    
    def get_all_templates(self) -> Dict[str, Any]:
        """الحصول على جميع القوالب"""
        return self.message_templates
    
    def add_template(self, key: str, template: Dict[str, str]):
        """إضافة قالب جديد"""
        try:
            self.message_templates[key] = template
            logger.info(f"تم إضافة قالب جديد: {key}")
        except Exception as e:
            logger.error(f"خطأ في إضافة القالب: {e}")
    
    def update_template(self, key: str, language: str, content: str):
        """تحديث قالب موجود"""
        try:
            if key not in self.message_templates:
                self.message_templates[key] = {}
            
            self.message_templates[key][language] = content
            logger.info(f"تم تحديث القالب: {key} - {language}")
        except Exception as e:
            logger.error(f"خطأ في تحديث القالب: {e}")
