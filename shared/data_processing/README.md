# 📁 مجلد معالجة النصوص - Text Processing Module

## 📋 نظرة عامة:

هذا المجلد يحتوي على نظام معالجة النصوص الموحد والمشترك بين جميع البوتات في النظام. تم تصميمه ليكون مركزياً ومنظماً لسهولة الصيانة والتطوير.

## 🗂️ هيكل الملفات:

```
shared/text_processing/
├── __init__.py          # ملف التهيئة والاستيرادات الرئيسية
├── core.py              # الفئة الرئيسية والدوال الأساسية
├── commands.py          # معالجة الأوامر (/start, /help, إلخ)
├── buttons.py           # معالجة الأزرار المضمنة والسفلية
├── templates.py         # قوالب الرسائل والمحتوى
├── monitoring.py        # رسائل المراقبة والإشعارات
├── shared_utils.py      # الأدوات المساعدة للنصوص
└── README.md           # هذا الملف - التوثيق الشامل
```

## 📄 وصف الملفات:

### 1. **`__init__.py`** - ملف التهيئة
**الغرض**: تهيئة المجلد كوحدة Python وتصدير الفئات الرئيسية

**المحتويات**:
- استيراد جميع الفئات الرئيسية
- تصدير الواجهات العامة
- إعداد المجلد للاستخدام الخارجي

**الاستيراد**:
```python
from text_processing import SharedTextProcessor
```

### 2. **`core.py`** - الدوال الأساسية
**الغرض**: الفئة الرئيسية التي تجمع جميع وظائف معالجة النصوص

**الفئات والدوال الرئيسية**:
- `SharedTextProcessor` - الفئة الرئيسية
- `clean_text()` - تنظيف النصوص
- `detect_language()` - كشف لغة النص
- `validate_message()` - التحقق من صحة الرسائل
- `process_text_message()` - معالجة الرسائل النصية

**مثال الاستخدام**:
```python
processor = SharedTextProcessor()
result = processor.process_text_message("📍 موقعي", user)
```

### 3. **`commands.py`** - معالجة الأوامر
**الغرض**: معالجة جميع أوامر البوت بلغتين (عربي/إنجليزي)

**الفئات والدوال**:
- `CommandProcessor` - فئة معالجة الأوامر
- `process_command()` - معالجة الأوامر
- `get_supported_commands()` - قائمة الأوامر المدعومة
- `is_command()` - التحقق من كون النص أمراً
- `get_command_help()` - نص المساعدة

**الأوامر المدعومة**:
- `/start`, `/ابدا` - بدء البوت
- `/help`, `/مساعدة` - المساعدة
- `/location`, `/موقع` - معلومات الموقع
- `/about`, `/نبذة` - نبذة شخصية
- `/works`, `/اعمال` - الأعمال والمشاريع
- `/experience`, `/خبرة` - الخبرات
- `/achievements`, `/انجازات` - الإنجازات

### 4. **`buttons.py`** - معالجة الأزرار
**الغرض**: معالجة الأزرار المضمنة والسفلية

**الفئات والدوال**:
- `ButtonProcessor` - فئة معالجة الأزرار
- `process_inline_button()` - معالجة الأزرار المضمنة
- `process_reply_button()` - معالجة الأزرار السفلية
- `get_button_mapping()` - خريطة النصوص والأزرار
- `is_button_text()` - التحقق من كون النص زر

**أنواع الأزرار**:
- **أزرار سفلية**: `📍 موقعي`, `👨‍💼 نبذة عني`, `💼 أعمالي`
- **أزرار مضمنة**: `menu_main`, `exa_normal`, `exa_pro`

### 5. **`templates.py`** - قوالب الرسائل
**الغرض**: إدارة جميع قوالب الرسائل والمحتوى

**الفئات والدوال**:
- `MessageTemplates` - فئة قوالب الرسائل
- `get_message()` - الحصول على رسالة من القالب
- `get_welcome_message()` - رسالة الترحيب
- `add_template()` - إضافة قالب جديد
- `update_template()` - تحديث قالب موجود

**القوالب المتاحة**:
- `welcome` - رسالة الترحيب
- `location` - معلومات الموقع
- `about` - نبذة شخصية
- `works` - الأعمال والمشاريع
- `experience` - الخبرات والمهارات
- `achievements` - الإنجازات والجوائز

### 6. **`monitoring.py`** - رسائل المراقبة
**الغرض**: معالجة وتنسيق رسائل المراقبة والإشعارات

**الفئات والدوال**:
- `MonitoringProcessor` - فئة معالجة المراقبة
- `format_live_monitoring_message()` - تنسيق رسالة المراقبة المباشرة
- `generate_notification_id()` - توليد أرقام الإشعارات
- `get_current_stats()` - الحصول على الإحصائيات
- `create_monitoring_data()` - إنشاء بيانات المراقبة

**تنسيق رسالة المراقبة**:
```
📊︙إشعار مراقبة مباشرة
🔢︙رقم الإشعار : 8460573126

📈 إحصائيات سريعة:
• إجمالي الإشعارات: 4
• ضغطات الأزرار: 1
• رسائل إدارة ومراقبة: 1
• رسائل الرئيسي: 1
• عدد المستخدمين: 1
• رسائل المستخدمين: 1
```

### 7. **`shared_utils.py`** - الأدوات المساعدة
**الغرض**: أدوات مساعدة لمعالجة النصوص والتحقق من صحتها

**الفئات والدوال**:
- `TextUtils` - فئة الأدوات المساعدة
- `clean_text()` - تنظيف النصوص
- `detect_language()` - كشف اللغة
- `validate_message()` - التحقق من صحة الرسائل
- `truncate_text()` - اقتطاع النصوص
- `extract_mentions()` - استخراج المنشنات
- `extract_hashtags()` - استخراج الهاشتاجات
- `get_text_stats()` - إحصائيات النص

## 🚀 كيفية الاستخدام:

### الاستيراد الأساسي:
```python
from text_processing import SharedTextProcessor

# إنشاء معالج النصوص
processor = SharedTextProcessor()
```

### معالجة النصوص:
```python
# معالجة رسالة نصية
result = processor.process_text_message("📍 موقعي", user)
print(result['type'])  # 'location'
print(result['text'])  # نص الموقع
```

### معالجة الأوامر:
```python
# معالجة أمر
command_type = processor.process_command("/start")
print(command_type)  # 'start'
```

### معالجة الأزرار:
```python
# معالجة زر سفلي
button_result = processor.process_reply_button("📍 موقعي")
print(button_result['action'])  # 'location'
```

### رسائل الترحيب:
```python
# رسالة ترحيب
username = "صلاح الدين الدروبي"
welcome = processor.get_welcome_message(username)
```

### المراقبة:
```python
# رسالة مراقبة
notification_id = processor.generate_notification_id()
stats = processor.get_current_stats()
monitoring_msg = processor.format_live_monitoring_message(notification_id, stats)
```

## 🔧 الميزات المتقدمة:

### دعم متعدد اللغات:
- العربية (افتراضي)
- الإنجليزية
- كشف تلقائي للغة

### معالجة الأخطاء:
- تسجيل شامل للأخطاء
- قيم افتراضية آمنة
- معالجة استثناءات شاملة

### الأداء:
- تحميل كسول للموارد
- ذاكرة تخزين مؤقت للقوالب
- معالجة فعالة للنصوص

## 🧪 الاختبار:

### اختبار شامل:
```bash
python test_text_processing_module.py
```

### اختبار مكونات محددة:
```python
# اختبار معالج النصوص
from text_processing import SharedTextProcessor
processor = SharedTextProcessor()

# اختبار الوظائف
assert processor.clean_text("  نص  ") == "نص"
assert processor.detect_language("Hello") == "en"
assert processor.detect_language("مرحبا") == "ar"
```

## 📊 الإحصائيات:

- **إجمالي الملفات**: 8 ملفات
- **إجمالي الفئات**: 6 فئات رئيسية
- **إجمالي الدوال**: 40+ دالة
- **اللغات المدعومة**: 2 (عربي/إنجليزي)
- **أنواع الرسائل**: 6 أنواع رئيسية

## 🔄 التحديثات المستقبلية:

### المخطط لها:
- دعم لغات إضافية
- قوالب رسائل متقدمة
- تحليل نصوص بالذكاء الاصطناعي
- إحصائيات متقدمة

### قيد التطوير:
- تحسين الأداء
- ذاكرة تخزين مؤقت محسنة
- واجهة برمجة تطبيقات REST

## 🛠️ المساهمة:

### إضافة ميزات جديدة:
1. إنشاء فئة جديدة في الملف المناسب
2. تحديث `__init__.py` لتصدير الفئة
3. إضافة اختبارات شاملة
4. تحديث التوثيق

### تحسين الكود:
1. اتباع معايير PEP 8
2. إضافة تعليقات شاملة
3. معالجة الأخطاء بشكل صحيح
4. اختبار جميع الحالات

## 📞 الدعم:

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- راجع ملفات الاختبار للأمثلة
- تحقق من ملفات السجل للأخطاء
- استخدم وضع التشخيص للتحليل المتقدم

---

**تم إنشاؤه بواسطة**: نظام البوتات الموحد  
**آخر تحديث**: 2025-07-10  
**الإصدار**: 1.0.0
