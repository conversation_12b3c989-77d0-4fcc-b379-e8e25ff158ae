#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
رسائل المراقبة
"""

import logging
import random
from typing import Dict, Any

logger = logging.getLogger(__name__)

class MonitoringProcessor:
    """فئة معالجة رسائل المراقبة"""
    
    def __init__(self):
        """تهيئة معالج المراقبة"""
        pass
    
    def format_live_monitoring_message(self, notification_id: str, stats: dict) -> str:
        """تنسيق رسالة المراقبة المباشرة الجديدة"""
        try:
            return f"""📊︙إشعار مراقبة مباشرة
💬︙{notification_id} : رقم الإشعار

📈︙إحصائيات سريعة:
• إجمالي الإشعارات: {stats.get('total_notifications', 0)}
• ضغطات الأزرار: {stats.get('button_clicks', 0)}
• رسائل إدارة ومراقبة: {stats.get('admin_messages', 0)}
• رسائل الرئيسي: {stats.get('main_bot_messages', 0)}
• عدد المستخدمين: {stats.get('total_users', 0)}
• رسائل المستخدمين: {stats.get('user_messages', 0)}"""
        except Exception as e:
            logger.error(f"خطأ في تنسيق رسالة المراقبة: {e}")
            return f"خطأ في تنسيق رسالة المراقبة: {notification_id}"
    
    def generate_notification_id(self) -> str:
        """توليد رقم إشعار عشوائي من 10 خانات يبدأ بـ 101"""
        try:
            # توليد 7 أرقام عشوائية لإكمال الـ 10 خانات مع بداية 101
            random_part = random.randint(1000000, 9999999)
            return f"101{random_part}"
        except Exception as e:
            logger.error(f"خطأ في توليد رقم الإشعار: {e}")
            return "1010000000"
    
    def get_current_stats(self) -> dict:
        """الحصول على الإحصائيات الحالية (يمكن تخصيصها حسب الحاجة)"""
        try:
            # هذه دالة أساسية - يمكن تطويرها لتتصل بقاعدة البيانات الفعلية
            return {
                'total_notifications': 4,
                'button_clicks': 1,
                'admin_messages': 1,
                'main_bot_messages': 1,
                'total_users': 1,
                'user_messages': 1
            }
        except Exception as e:
            logger.error(f"خطأ في الحصول على الإحصائيات: {e}")
            return {
                'total_notifications': 0,
                'button_clicks': 0,
                'admin_messages': 0,
                'main_bot_messages': 0,
                'total_users': 0,
                'user_messages': 0
            }
    
    def process_monitoring_message(self, message_data: dict) -> str:
        """معالجة رسائل المراقبة"""
        try:
            if message_data.get('type') == 'user_action':
                return self._format_user_action_message(message_data)
            elif message_data.get('type') == 'bot_response':
                return self._format_bot_response_message(message_data)
            else:
                return self._format_general_message(message_data)
        except Exception as e:
            logger.error(f"خطأ في معالجة رسالة المراقبة: {e}")
            return "خطأ في معالجة الرسالة"
    
    def _format_user_action_message(self, data: dict) -> str:
        """تنسيق رسالة إجراء المستخدم"""
        try:
            return f"""📊︙إشعار مراقبة
🔢︙رقم الإشعار︙{data.get('notification_id', 'غير محدد')}

🤖︙البوت الرئيسي
🤖︙الحالة︙{data.get('user_status', 'غير محدد')}
💬︙رسالة نصية
👤︙الاسم︙{data.get('username', 'غير محدد')}
📝︙النص︙{data.get('button_name', 'غير محدد')}



📅︙اليوم︙{data.get('day_name', 'غير محدد')}
📅︙التاريخ︙{data.get('date', 'غير محدد')}
⏰︙الوقت︙{data.get('time', 'غير محدد')}"""
        except Exception as e:
            logger.error(f"خطأ في تنسيق رسالة إجراء المستخدم: {e}")
            return "خطأ في تنسيق رسالة إجراء المستخدم"
    
    def _format_bot_response_message(self, data: dict) -> str:
        """تنسيق رسالة رد البوت"""
        try:
            return f"""📊︙إشعار مراقبة
🔢︙رقم الإشعار︙{data.get('notification_id', 'غير محدد')}

🤖︙البوت الرئيسي
🤖︙الحالة︙رد
💬︙رسالة نصية
👤︙الاسم︙{data.get('username', 'غير محدد')}
📝︙النص︙{data.get('response_text', 'غير محدد')}



📅︙اليوم︙{data.get('day_name', 'غير محدد')}
📅︙التاريخ︙{data.get('date', 'غير محدد')}
⏰︙الوقت︙{data.get('time', 'غير محدد')}"""
        except Exception as e:
            logger.error(f"خطأ في تنسيق رسالة رد البوت: {e}")
            return "خطأ في تنسيق رسالة رد البوت"
    
    def _format_general_message(self, data: dict) -> str:
        """تنسيق رسالة عامة"""
        try:
            return f"""📊︙إشعار عام
🔢︙رقم الإشعار : `{data.get('notification_id', 'غير محدد')}`

📝︙المحتوى : {data.get('content', 'غير محدد')}
📅︙التاريخ : {data.get('date', 'غير محدد')}
⏰︙الوقت : {data.get('time', 'غير محدد')}"""
        except Exception as e:
            logger.error(f"خطأ في تنسيق الرسالة العامة: {e}")
            return "خطأ في تنسيق الرسالة العامة"
    
    def create_monitoring_data(self, user, action_type: str, details: dict) -> dict:
        """إنشاء بيانات المراقبة"""
        try:
            from datetime import datetime
            
            now = datetime.now()
            
            return {
                'notification_id': self.generate_notification_id(),
                'user_id': str(user.id) if hasattr(user, 'id') else 'غير محدد',
                'username': f"{user.first_name} {user.last_name}".strip() if user.last_name else user.first_name,
                'user_handle': f"@{user.username}" if hasattr(user, 'username') and user.username else 'غير محدد',
                'action_type': action_type,
                'details': details,
                'timestamp': now.isoformat(),
                'date': now.strftime('%Y-%m-%d'),
                'time': now.strftime('%H:%M:%S'),
                'day_name': now.strftime('%A')
            }
        except Exception as e:
            logger.error(f"خطأ في إنشاء بيانات المراقبة: {e}")
            return {
                'notification_id': self.generate_notification_id(),
                'user_id': 'غير محدد',
                'username': 'غير محدد',
                'user_handle': 'غير محدد',
                'action_type': action_type,
                'details': details,
                'timestamp': 'غير محدد',
                'date': 'غير محدد',
                'time': 'غير محدد',
                'day_name': 'غير محدد'
            }
