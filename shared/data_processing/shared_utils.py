#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الأدوات المساعدة للنصوص
"""

import logging
import re
from typing import Dict, Any, Optional, List, Union

logger = logging.getLogger(__name__)

class TextUtils:
    """فئة الأدوات المساعدة للنصوص"""
    
    def __init__(self):
        """تهيئة أدوات النصوص"""
        self.arabic_pattern = re.compile(r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]')
        self.english_pattern = re.compile(r'[a-zA-Z]')
    
    def clean_text(self, text: str) -> str:
        """تنظيف النصوص من الرموز والمسافات الزائدة"""
        try:
            if not text:
                return ""
            
            # إزالة المسافات الزائدة من البداية والنهاية
            cleaned = text.strip()
            
            # إزالة المسافات المتعددة
            cleaned = re.sub(r'\s+', ' ', cleaned)
            
            # إزالة الرموز الخاصة غير المرغوب فيها (اختياري)
            # cleaned = re.sub(r'[^\w\s\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]', '', cleaned)
            
            return cleaned
            
        except Exception as e:
            logger.error(f"خطأ في تنظيف النص: {e}")
            return text if text else ""
    
    def detect_language(self, text: str) -> str:
        """كشف لغة النص (عربي أو إنجليزي)"""
        try:
            if not text:
                return 'ar'  # افتراضي
            
            # عد الأحرف العربية والإنجليزية
            arabic_chars = len(self.arabic_pattern.findall(text))
            english_chars = len(self.english_pattern.findall(text))
            
            # تحديد اللغة بناءً على الأحرف الأكثر
            if arabic_chars > english_chars:
                return 'ar'
            elif english_chars > arabic_chars:
                return 'en'
            else:
                return 'ar'  # افتراضي في حالة التساوي
                
        except Exception as e:
            logger.error(f"خطأ في كشف اللغة: {e}")
            return 'ar'
    
    def validate_message(self, message_text: str) -> tuple:
        """التحقق من صحة الرسالة"""
        try:
            if not message_text:
                return False, "الرسالة فارغة"
            
            if len(message_text.strip()) == 0:
                return False, "الرسالة تحتوي على مسافات فقط"
            
            if len(message_text) > 4096:  # حد تلجرام للرسائل
                return False, "الرسالة طويلة جداً"
            
            return True, "الرسالة صحيحة"
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من الرسالة: {e}")
            return False, "خطأ في التحقق من الرسالة"
    
    def truncate_text(self, text: str, max_length: int = 100) -> str:
        """اقتطاع النص إلى طول محدد"""
        try:
            if not text:
                return ""
            
            if len(text) <= max_length:
                return text
            
            # اقتطاع النص مع إضافة نقاط
            return text[:max_length-3] + "..."
            
        except Exception as e:
            logger.error(f"خطأ في اقتطاع النص: {e}")
            return text if text else ""
    
    def clean_username(self, username: str) -> str:
        """تنظيف اسم المستخدم من الرموز الإضافية"""
        try:
            if not username:
                return "مستخدم غير محدد"
            
            # إزالة الرموز الخاصة مثل | في النهاية
            cleaned = username.strip()
            
            # إزالة | من النهاية إذا كانت موجودة
            if cleaned.endswith('|'):
                cleaned = cleaned[:-1].strip()
            
            # إزالة المسافات المتعددة
            cleaned = re.sub(r'\s+', ' ', cleaned)
            
            return cleaned if cleaned else "مستخدم غير محدد"
            
        except Exception as e:
            logger.error(f"خطأ في تنظيف اسم المستخدم: {e}")
            return username if username else "مستخدم غير محدد"
    
    def validate_username(self, username: str) -> bool:
        """التحقق من صحة اسم المستخدم"""
        try:
            if not username:
                return False
            
            # التحقق من طول اسم المستخدم
            if len(username) < 3 or len(username) > 32:
                return False
            
            # التحقق من الأحرف المسموحة (أحرف وأرقام و_)
            if not re.match(r'^[a-zA-Z0-9_]+$', username):
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من اسم المستخدم: {e}")
            return False
    
    def format_number(self, number: Union[int, float], language: str = 'ar') -> str:
        """تنسيق الأرقام حسب اللغة"""
        try:
            if language == 'ar':
                # تحويل الأرقام الإنجليزية إلى عربية
                arabic_digits = '٠١٢٣٤٥٦٧٨٩'
                english_digits = '0123456789'
                
                number_str = str(number)
                for eng, ar in zip(english_digits, arabic_digits):
                    number_str = number_str.replace(eng, ar)
                
                return number_str
            else:
                return str(number)
                
        except Exception as e:
            logger.error(f"خطأ في تنسيق الرقم: {e}")
            return str(number)
    
    def extract_mentions(self, text: str) -> List[str]:
        """استخراج المنشنات (@username) من النص"""
        try:
            mentions = re.findall(r'@(\w+)', text)
            return mentions
        except Exception as e:
            logger.error(f"خطأ في استخراج المنشنات: {e}")
            return []
    
    def extract_hashtags(self, text: str) -> List[str]:
        """استخراج الهاشتاجات (#hashtag) من النص"""
        try:
            hashtags = re.findall(r'#(\w+)', text)
            return hashtags
        except Exception as e:
            logger.error(f"خطأ في استخراج الهاشتاجات: {e}")
            return []
    
    def is_emoji(self, text: str) -> bool:
        """التحقق من كون النص إيموجي"""
        try:
            # نمط بسيط للإيموجي
            emoji_pattern = re.compile(
                "["
                "\U0001F600-\U0001F64F"  # emoticons
                "\U0001F300-\U0001F5FF"  # symbols & pictographs
                "\U0001F680-\U0001F6FF"  # transport & map symbols
                "\U0001F1E0-\U0001F1FF"  # flags (iOS)
                "\U00002702-\U000027B0"
                "\U000024C2-\U0001F251"
                "]+", flags=re.UNICODE)
            
            return bool(emoji_pattern.search(text))
        except Exception as e:
            logger.error(f"خطأ في التحقق من الإيموجي: {e}")
            return False
    
    def count_words(self, text: str) -> int:
        """عد الكلمات في النص"""
        try:
            if not text:
                return 0
            
            # تنظيف النص وتقسيمه
            words = self.clean_text(text).split()
            return len(words)
        except Exception as e:
            logger.error(f"خطأ في عد الكلمات: {e}")
            return 0
    
    def get_text_stats(self, text: str) -> Dict[str, int]:
        """الحصول على إحصائيات النص"""
        try:
            return {
                'characters': len(text) if text else 0,
                'words': self.count_words(text),
                'lines': len(text.split('\n')) if text else 0,
                'arabic_chars': len(self.arabic_pattern.findall(text)) if text else 0,
                'english_chars': len(self.english_pattern.findall(text)) if text else 0
            }
        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات النص: {e}")
            return {
                'characters': 0,
                'words': 0,
                'lines': 0,
                'arabic_chars': 0,
                'english_chars': 0
            }
