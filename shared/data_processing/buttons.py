#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
معالجة الأزرار
"""

import logging
from typing import Dict, Any
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from telegram.constants import ParseMode

logger = logging.getLogger(__name__)

class ButtonProcessor:
    """فئة معالجة الأزرار"""

    def __init__(self, monitoring=None):
        """تهيئة معالج الأزرار"""
        self.monitoring = monitoring
        self.button_mapping = {
            'ar': {
                "📍 موقعي": "location",
                "👨‍💼 نبذة عني": "about",
                "💼 أعمالي": "works",
                "🎯 خبرتي": "experience",
                "🏆 إنجازاتي": "achievements",
                "🤖 إكسا الذكي": "ai_assistant",
                "💰 محفظتي": "my_wallet",
                "💰 عرض الرصيد": "my_balance",
                "➕ إضافة رصيد": "add_balance",
                "📊 عرض المعاملات": "show_transactions",
                "💳 خدمة سلفني": "request_loan",
                "📈 تقرير الاستخدام": "usage_report",
                "🔙 العودة للقائمة الرئيسية": "back_to_main",
                "❓ المساعدة": "help"
            },
            'en': {
                "📍 My Location": "location",
                "👨‍💼 About Me": "about",
                "💼 My Works": "works",
                "🎯 My Experience": "experience",
                "🏆 My Achievements": "achievements",
                "🤖 Exa AI": "ai_assistant",
                "💰 My Wallet": "my_wallet",
                "💰 Show Balance": "my_balance",
                "➕ Add Balance": "add_balance",
                "📊 Show Transactions": "show_transactions",
                "💳 Loan Service": "request_loan",
                "📈 Usage Report": "usage_report",
                "🔙 Back to Main Menu": "back_to_main",
                "❓ Help": "help"
            }
        }
        
        self.inline_button_mapping = {
            'ar': {
                "menu_main": {"action": "menu_navigation", "target": "main_menu"},
                "menu_about": {"action": "menu_navigation", "target": "about_menu"},
                "menu_works": {"action": "menu_navigation", "target": "works_menu"},
                "menu_contact": {"action": "menu_navigation", "target": "contact_menu"},
                "back_main": {"action": "navigation", "target": "main_menu"},
                "exa_normal": {"action": "ai_mode", "target": "normal"},
                "exa_pro": {"action": "ai_mode", "target": "pro"},
                "end_exa_normal": {"action": "end_conversation", "target": "normal"},
                "end_exa_pro": {"action": "end_conversation", "target": "pro"},
                "view_works_pdf": {"action": "send_pdf", "target": "works"}
            },
            'en': {
                "menu_main": {"action": "menu_navigation", "target": "main_menu"},
                "menu_about": {"action": "menu_navigation", "target": "about_menu"},
                "menu_works": {"action": "menu_navigation", "target": "works_menu"},
                "menu_contact": {"action": "menu_navigation", "target": "contact_menu"},
                "back_main": {"action": "navigation", "target": "main_menu"},
                "exa_normal": {"action": "ai_mode", "target": "normal"},
                "exa_pro": {"action": "ai_mode", "target": "pro"},
                "end_exa_normal": {"action": "end_conversation", "target": "normal"},
                "end_exa_pro": {"action": "end_conversation", "target": "pro"},
                "view_works_pdf": {"action": "send_pdf", "target": "works"}
            }
        }
    
    def process_inline_button(self, button_data: str, language: str = 'ar') -> Dict[str, Any]:
        """معالجة الأزرار المضمنة"""
        try:
            button_info = self.inline_button_mapping.get(language, {}).get(button_data)
            
            if button_info:
                return {
                    "action": button_info["action"],
                    "target": button_info["target"],
                    "button_data": button_data,
                    "language": language,
                    "success": True
                }
            else:
                return {
                    "action": "unknown",
                    "target": None,
                    "button_data": button_data,
                    "language": language,
                    "success": False
                }
                
        except Exception as e:
            logger.error(f"خطأ في معالجة الزر المضمن: {e}")
            return {
                "action": "error",
                "target": None,
                "button_data": button_data,
                "language": language,
                "success": False
            }
    
    def process_reply_button(self, button_text: str, language: str = 'ar') -> Dict[str, Any]:
        """معالجة الأزرار السفلية"""
        try:
            button_type = self.button_mapping.get(language, {}).get(button_text)
            
            if button_type:
                return {
                    "action": button_type,
                    "button_text": button_text,
                    "language": language,
                    "success": True
                }
            else:
                return {
                    "action": "unknown",
                    "button_text": button_text,
                    "language": language,
                    "success": False
                }
                
        except Exception as e:
            logger.error(f"خطأ في معالجة الزر السفلي: {e}")
            return {
                "action": "error",
                "button_text": button_text,
                "language": language,
                "success": False
            }
    
    def get_button_mapping(self, language: str = 'ar') -> Dict[str, str]:
        """الحصول على خريطة النصوص والأزرار"""
        return self.button_mapping.get(language, self.button_mapping['ar'])
    
    def get_inline_button_mapping(self, language: str = 'ar') -> Dict[str, Dict[str, str]]:
        """الحصول على خريطة الأزرار المضمنة"""
        return self.inline_button_mapping.get(language, self.inline_button_mapping['ar'])
    
    def is_button_text(self, text: str, language: str = 'ar') -> bool:
        """التحقق من كون النص زر سفلي"""
        try:
            return text in self.button_mapping.get(language, {})
        except Exception as e:
            logger.error(f"خطأ في التحقق من الزر: {e}")
            return False
    
    def get_button_type(self, button_text: str, language: str = 'ar') -> str:
        """الحصول على نوع الزر"""
        try:
            return self.button_mapping.get(language, {}).get(button_text, "unknown")
        except Exception as e:
            logger.error(f"خطأ في الحصول على نوع الزر: {e}")
            return "unknown"

    def process_callback_query(self, callback_data: str, language: str = 'ar') -> Dict[str, Any]:
        """معالجة استدعاءات الأزرار المضمنة"""
        try:
            # معالجة أزرار الفواتير التفاعلية
            if callback_data.startswith("view_invoice_"):
                invoice_number = callback_data.replace("view_invoice_", "")
                return {
                    "action": "view_invoice",
                    "target": invoice_number,
                    "callback_data": callback_data,
                    "language": language,
                    "success": True
                }
            elif callback_data.startswith("print_invoice_"):
                invoice_number = callback_data.replace("print_invoice_", "")
                return {
                    "action": "print_invoice",
                    "target": invoice_number,
                    "callback_data": callback_data,
                    "language": language,
                    "success": True
                }

            # معالجة الأزرار العادية
            button_info = self.inline_button_mapping.get(language, {}).get(callback_data)

            if button_info:
                return {
                    "action": button_info["action"],
                    "target": button_info["target"],
                    "callback_data": callback_data,
                    "language": language,
                    "success": True
                }
            else:
                return {
                    "action": "unknown",
                    "target": None,
                    "callback_data": callback_data,
                    "language": language,
                    "success": False
                }

        except Exception as e:
            logger.error(f"خطأ في معالجة استدعاء الزر: {e}")
            return {
                "action": "error",
                "target": None,
                "callback_data": callback_data,
                "language": language,
                "success": False
            }

    # ===== دوال الأزرار المضمنة =====

    def get_location_keyboard(self):
        """إنشاء لوحة مفاتيح الموقع مع أزرار البريد الإلكتروني والهاتف"""
        keyboard = [
            [InlineKeyboardButton("📧 البريد الإلكتروني", callback_data="copy_email"),
             InlineKeyboardButton("📱 الهاتف", callback_data="copy_phone")],
            [InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="main_menu")]
        ]
        return InlineKeyboardMarkup(keyboard)

    def get_works_keyboard(self):
        """إنشاء لوحة مفاتيح الأعمال"""
        keyboard = [
            [InlineKeyboardButton("📄 رؤية أعمالي", callback_data="view_works_pdf")],
            [InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="main_menu")]
        ]
        return InlineKeyboardMarkup(keyboard)

    def get_back_keyboard(self):
        """إنشاء لوحة مفاتيح العودة للقائمة الرئيسية"""
        keyboard = [[InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="main_menu")]]
        return InlineKeyboardMarkup(keyboard)

    async def copy_email(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """نسخ البريد الإلكتروني"""
        query = update.callback_query
        await query.answer()

        email = "<EMAIL>"

        try:
            # محاولة تحديث النص إذا كانت الرسالة نصية
            await query.edit_message_text(
                f"📧︙البريد الإلكتروني\n\n"
                f"<code>{email}</code>\n\n"
                f"💡︙اضغط على البريد الإلكتروني أعلاه لنسخه",
                parse_mode=ParseMode.HTML,
                reply_markup=self.get_location_keyboard()
            )
        except Exception:
            # إذا فشل التحديث، أرسل رسالة جديدة
            await query.message.reply_text(
                f"📧︙البريد الإلكتروني\n\n"
                f"<code>{email}</code>\n\n"
                f"💡︙اضغط على البريد الإلكتروني أعلاه لنسخه",
                parse_mode=ParseMode.HTML,
                reply_markup=self.get_location_keyboard()
            )

        # إرسال إشعار رد البوت
        if self.monitoring:
            await self.monitoring.send_bot_response_notification(
                f"تم عرض البريد الإلكتروني: {email}",
                "text"
            )

    async def copy_phone(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """نسخ رقم الهاتف"""
        query = update.callback_query
        await query.answer()

        phone1 = "+967772934757"
        phone2 = "+967739595505"

        try:
            # محاولة تحديث النص إذا كانت الرسالة نصية
            await query.edit_message_text(
                f"📱︙رقم الهاتف\n\n"
                f"<code>{phone1}</code>\n"
                f"<code>{phone2}</code>\n\n"
                f"💡︙اضغط على أي رقم أعلاه لنسخه",
                parse_mode=ParseMode.HTML,
                reply_markup=self.get_location_keyboard()
            )
        except Exception:
            # إذا فشل التحديث، أرسل رسالة جديدة
            await query.message.reply_text(
                f"📱︙رقم الهاتف\n\n"
                f"<code>{phone1}</code>\n"
                f"<code>{phone2}</code>\n\n"
                f"💡︙اضغط على أي رقم أعلاه لنسخه",
                parse_mode=ParseMode.HTML,
                reply_markup=self.get_location_keyboard()
            )

        # إرسال إشعار رد البوت
        if self.monitoring:
            await self.monitoring.send_bot_response_notification(
                f"تم عرض أرقام الهاتف: {phone1}, {phone2}",
                "text"
            )

    def get_keyboard_for_message_type(self, message_type: str):
        """الحصول على لوحة المفاتيح المناسبة حسب نوع الرسالة"""
        if message_type == "works":
            return self.get_works_keyboard()
        elif message_type == "location":
            return self.get_location_keyboard()
        else:
            return self.get_back_keyboard()

    async def handle_callback_query(self, update: Update, context: ContextTypes.DEFAULT_TYPE, callback_data: str):
        """معالجة استدعاءات الأزرار المضمنة"""
        try:
            if callback_data == "copy_email":
                await self.copy_email(update, context)
                return True
            elif callback_data == "copy_phone":
                await self.copy_phone(update, context)
                return True
            else:
                return False  # لم يتم التعامل مع هذا الزر

        except Exception as e:
            logger.error(f"خطأ في معالجة الزر المضمن {callback_data}: {e}")
            return False
