#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
حزمة قاعدة البيانات SQLite
توفر جميع الأدوات المطلوبة للتعامل مع قاعدة البيانات
"""

from .sqlite_manager import SQLiteManager
from .database_api import DatabaseAPI, db_api
from .backup_system import BackupSystem
from .migration_script import DataMigration
from .models import DatabaseModels

__version__ = "1.0.0"
__author__ = "صلاح الدين الدروبي"

# تصدير الفئات الرئيسية
__all__ = [
    'SQLiteManager',
    'DatabaseAPI', 
    'db_api',
    'BackupSystem',
    'DataMigration',
    'DatabaseModels'
]
