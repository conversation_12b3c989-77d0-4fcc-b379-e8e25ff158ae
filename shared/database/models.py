#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نماذج قاعدة البيانات SQLite
تحديد هيكل الجداول والعلاقات
"""

import sqlite3
from datetime import datetime
from typing import Dict, Any, Optional, List
import json

class DatabaseModels:
    """فئة نماذج قاعدة البيانات"""
    
    @staticmethod
    def create_tables(conn: sqlite3.Connection):
        """إنشاء جميع الجداول المطلوبة"""
        cursor = conn.cursor()
        
        # جدول المستخدمين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                user_id INTEGER PRIMARY KEY,
                username TEXT,
                display_name TEXT,
                language_code TEXT DEFAULT 'ar',
                registration_date TEXT,
                first_login_date TEXT,
                last_activity TEXT,
                visit_count INTEGER DEFAULT 0,
                wallet_number TEXT,
                wallet_status TEXT DEFAULT 'نشطة',
                is_active BOOLEAN DEFAULT 1,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول المحافظ
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS wallets (
                wallet_number TEXT PRIMARY KEY,
                user_id INTEGER,
                user_name TEXT,
                username TEXT,
                balance REAL DEFAULT 0.0,
                currency TEXT DEFAULT 'إكسا',
                status TEXT DEFAULT 'active',
                created_at TEXT,
                last_transaction TEXT,
                transaction_count INTEGER DEFAULT 0,
                is_verified BOOLEAN DEFAULT 0,
                verified_at TEXT,
                security_level TEXT DEFAULT 'basic',
                loan_amount REAL DEFAULT 0.0,
                has_active_loan BOOLEAN DEFAULT 0,
                loan_granted_at TEXT,
                debt_amount REAL DEFAULT 0.0,
                has_debt BOOLEAN DEFAULT 0,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (user_id)
            )
        ''')
        
        # جدول تاريخ القروض
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS loan_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                wallet_number TEXT,
                loan_type TEXT,
                amount REAL,
                granted_at TEXT,
                old_balance REAL,
                new_balance REAL,
                status TEXT DEFAULT 'active',
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (wallet_number) REFERENCES wallets (wallet_number)
            )
        ''')
        
        # جدول المعاملات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                wallet_number TEXT,
                transaction_type TEXT,
                amount REAL,
                description TEXT,
                status TEXT DEFAULT 'completed',
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (wallet_number) REFERENCES wallets (wallet_number)
            )
        ''')
        
        # جدول بيانات الإدارة
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS admin_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                key_name TEXT UNIQUE,
                value_data TEXT,
                data_type TEXT DEFAULT 'json',
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول بيانات المراقبة
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS monitoring_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                metric_name TEXT,
                metric_value TEXT,
                timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول إعدادات نماذج الذكاء الاصطناعي
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS ai_models (
                model_name TEXT PRIMARY KEY,
                status TEXT DEFAULT 'inactive',
                priority INTEGER,
                max_tokens INTEGER,
                temperature REAL,
                timeout INTEGER,
                fallback_enabled BOOLEAN DEFAULT 1,
                last_updated TEXT DEFAULT CURRENT_TIMESTAMP,
                config_data TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول سجل استخدام التوكن
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS token_usage (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                payment_method TEXT,
                input_tokens INTEGER,
                output_tokens INTEGER,
                total_tokens INTEGER,
                cost_in_exa REAL,
                token_rate REAL,
                timestamp TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (user_id)
            )
        ''')
        
        # جدول الجلسات الإدارية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS admin_sessions (
                session_id TEXT PRIMARY KEY,
                user_id INTEGER,
                username TEXT,
                login_time TEXT,
                last_activity TEXT,
                is_active BOOLEAN DEFAULT 1,
                ip_address TEXT,
                user_agent TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول التنبيهات والإشعارات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS alerts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                alert_type TEXT,
                title TEXT,
                message TEXT,
                severity TEXT DEFAULT 'info',
                is_read BOOLEAN DEFAULT 0,
                user_id INTEGER,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                read_at TEXT
            )
        ''')
        
        # إنشاء الفهارس لتحسين الأداء
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_users_user_id ON users(user_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_users_last_activity ON users(last_activity)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_wallets_user_id ON wallets(user_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_wallets_status ON wallets(status)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_transactions_wallet ON transactions(wallet_number)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(created_at)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_token_usage_user ON token_usage(user_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_token_usage_date ON token_usage(created_at)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_alerts_user ON alerts(user_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_alerts_read ON alerts(is_read)')
        
        conn.commit()
        print("✅ تم إنشاء جميع الجداول والفهارس بنجاح")

    @staticmethod
    def get_table_schema() -> Dict[str, List[str]]:
        """الحصول على مخطط الجداول"""
        return {
            'users': [
                'user_id', 'username', 'display_name', 'language_code',
                'registration_date', 'first_login_date', 'last_activity',
                'visit_count', 'wallet_number', 'wallet_status', 'is_active'
            ],
            'wallets': [
                'wallet_number', 'user_id', 'user_name', 'username', 'balance',
                'currency', 'status', 'created_at', 'last_transaction',
                'transaction_count', 'is_verified', 'verified_at', 'security_level',
                'loan_amount', 'has_active_loan', 'loan_granted_at',
                'debt_amount', 'has_debt'
            ],
            'transactions': [
                'id', 'wallet_number', 'transaction_type', 'amount',
                'description', 'status', 'created_at'
            ],
            'admin_data': [
                'id', 'key_name', 'value_data', 'data_type', 'created_at', 'updated_at'
            ],
            'monitoring_data': [
                'id', 'metric_name', 'metric_value', 'timestamp', 'created_at'
            ],
            'ai_models': [
                'model_name', 'status', 'priority', 'max_tokens', 'temperature',
                'timeout', 'fallback_enabled', 'last_updated', 'config_data'
            ],
            'token_usage': [
                'id', 'user_id', 'payment_method', 'input_tokens', 'output_tokens',
                'total_tokens', 'cost_in_exa', 'token_rate', 'timestamp'
            ]
        }

    @staticmethod
    def validate_data(table_name: str, data: Dict[str, Any]) -> bool:
        """التحقق من صحة البيانات قبل الإدراج"""
        schema = DatabaseModels.get_table_schema()
        
        if table_name not in schema:
            return False
        
        # التحقق من الحقول المطلوبة
        required_fields = {
            'users': ['user_id'],
            'wallets': ['wallet_number', 'user_id'],
            'transactions': ['wallet_number', 'transaction_type', 'amount'],
            'admin_data': ['key_name', 'value_data'],
            'ai_models': ['model_name'],
            'token_usage': ['user_id', 'total_tokens']
        }
        
        if table_name in required_fields:
            for field in required_fields[table_name]:
                if field not in data or data[field] is None:
                    return False
        
        return True
