#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت ترحيل البيانات من JSON إلى MySQL
يقوم بترحيل جميع البيانات الموجودة في ملفات JSON إلى قاعدة البيانات MySQL
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

from .mysql_manager import mysql_manager

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DataMigrator:
    """مرحل البيانات من JSON إلى MySQL"""
    
    def __init__(self):
        """تهيئة مرحل البيانات"""
        self.base_path = Path(__file__).parent
        self.backup_path = self.base_path / "migration_backups"
        self.backup_path.mkdir(exist_ok=True)
        
        # مسارات ملفات JSON
        self.json_files = {
            "users_data.json": "users",
            "wallets_database.json": "wallets", 
            "admin_data.json": "admin_data",
            "banned_users.json": "banned_users",
            "restricted_users.json": "restricted_users",
            "monitoring_data.json": "monitoring_data",
            "bot_stats.json": "bot_stats",
            "ai_models_config.json": "ai_models_config",
            "user_usage_limits.json": "user_sessions",
        }
        
        logger.info("🔄 تم تهيئة مرحل البيانات")
    
    def create_backup(self) -> bool:
        """إنشاء نسخة احتياطية من ملفات JSON الحالية"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_dir = self.backup_path / f"json_backup_{timestamp}"
            backup_dir.mkdir(exist_ok=True)
            
            for json_file in self.json_files.keys():
                source_path = self.base_path / json_file
                if source_path.exists():
                    backup_file = backup_dir / json_file
                    with open(source_path, 'r', encoding='utf-8') as src:
                        with open(backup_file, 'w', encoding='utf-8') as dst:
                            dst.write(src.read())
            
            logger.info(f"💾 تم إنشاء نسخة احتياطية في: {backup_dir}")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
            return False
    
    def load_json_file(self, filename: str) -> Optional[Dict]:
        """تحميل ملف JSON"""
        try:
            file_path = self.base_path / filename
            if not file_path.exists():
                logger.warning(f"⚠️ الملف غير موجود: {filename}")
                return None
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            logger.info(f"📂 تم تحميل الملف: {filename}")
            return data
            
        except Exception as e:
            logger.error(f"❌ خطأ في تحميل الملف {filename}: {e}")
            return None
    
    def migrate_users_data(self) -> bool:
        """ترحيل بيانات المستخدمين"""
        try:
            users_data = self.load_json_file("users_data.json")
            if not users_data:
                return True
            
            migrated_count = 0
            
            for user_id, user_info in users_data.items():
                # تحضير بيانات المستخدم
                user_data = {
                    'id': int(user_id),
                    'username': user_info.get('معرف المستخدم', '').replace('@', ''),
                    'first_name': user_info.get('اسم المستخدم', '').split(' | ')[0] if ' | ' in user_info.get('اسم المستخدم', '') else user_info.get('اسم المستخدم', ''),
                    'last_name': user_info.get('اسم المستخدم', '').split(' | ')[1] if ' | ' in user_info.get('اسم المستخدم', '') else '',
                    'language_code': user_info.get('اللغة', 'ar'),
                    'registration_date': user_info.get('تاريخ التسجيل', datetime.now().strftime('%Y-%m-%d %H:%M:%S')),
                    'first_login': user_info.get('تاريخ أول دخول', datetime.now().strftime('%Y-%m-%d %H:%M:%S')),
                    'last_activity': user_info.get('آخر نشاط', datetime.now().strftime('%Y-%m-%d %H:%M:%S')),
                    'visit_count': user_info.get('عدد الزيارات', 0),
                    'wallet_number': user_info.get('رقم المحفظة'),
                    'wallet_status': 'active' if user_info.get('حالة المحفظة') == 'نشطة' else 'inactive',
                    'user_data': json.dumps(user_info, ensure_ascii=False)
                }
                
                # إدراج البيانات في قاعدة البيانات
                query = """
                INSERT INTO users (
                    id, username, first_name, last_name, language_code,
                    registration_date, first_login, last_activity, visit_count,
                    wallet_number, wallet_status, user_data
                ) VALUES (
                    %(id)s, %(username)s, %(first_name)s, %(last_name)s, %(language_code)s,
                    %(registration_date)s, %(first_login)s, %(last_activity)s, %(visit_count)s,
                    %(wallet_number)s, %(wallet_status)s, %(user_data)s
                ) ON DUPLICATE KEY UPDATE
                    username = VALUES(username),
                    first_name = VALUES(first_name),
                    last_name = VALUES(last_name),
                    language_code = VALUES(language_code),
                    last_activity = VALUES(last_activity),
                    visit_count = VALUES(visit_count),
                    wallet_status = VALUES(wallet_status),
                    user_data = VALUES(user_data)
                """
                
                mysql_manager.execute_update(query, user_data)
                migrated_count += 1
            
            logger.info(f"👥 تم ترحيل {migrated_count} مستخدم")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في ترحيل بيانات المستخدمين: {e}")
            return False
    
    def migrate_wallets_data(self) -> bool:
        """ترحيل بيانات المحافظ"""
        try:
            wallets_data = self.load_json_file("wallets_database.json")
            if not wallets_data:
                return True
            
            migrated_count = 0
            
            for wallet_number, wallet_info in wallets_data.items():
                # تحضير بيانات المحفظة
                wallet_data = {
                    'wallet_number': wallet_number,
                    'user_id': wallet_info.get('user_id'),
                    'user_name': wallet_info.get('user_name', ''),
                    'username': wallet_info.get('username', '').replace('@', ''),
                    'balance': float(wallet_info.get('balance', 0.0)),
                    'currency': wallet_info.get('currency', 'إكسا'),
                    'status': wallet_info.get('status', 'active'),
                    'created_at': wallet_info.get('created_at', datetime.now().strftime('%Y-%m-%d %H:%M:%S')),
                    'last_transaction': wallet_info.get('last_transaction'),
                    'transaction_count': wallet_info.get('transaction_count', 0),
                    'is_verified': wallet_info.get('is_verified', False),
                    'security_level': wallet_info.get('security_level', 'basic'),
                    'verified_at': wallet_info.get('verified_at'),
                    'loan_amount': float(wallet_info.get('loan_amount', 0.0)),
                    'has_active_loan': wallet_info.get('has_active_loan', False),
                    'loan_granted_at': wallet_info.get('loan_granted_at'),
                    'debt_amount': float(wallet_info.get('debt_amount', 0.0)),
                    'has_debt': wallet_info.get('has_debt', False),
                    'debt_created_at': wallet_info.get('debt_created_at'),
                    'wallet_data': json.dumps(wallet_info, ensure_ascii=False)
                }
                
                # إدراج البيانات في قاعدة البيانات
                query = """
                INSERT INTO wallets (
                    wallet_number, user_id, user_name, username, balance, currency,
                    status, created_at, last_transaction, transaction_count,
                    is_verified, security_level, verified_at, loan_amount,
                    has_active_loan, loan_granted_at, debt_amount, has_debt,
                    debt_created_at, wallet_data
                ) VALUES (
                    %(wallet_number)s, %(user_id)s, %(user_name)s, %(username)s, %(balance)s, %(currency)s,
                    %(status)s, %(created_at)s, %(last_transaction)s, %(transaction_count)s,
                    %(is_verified)s, %(security_level)s, %(verified_at)s, %(loan_amount)s,
                    %(has_active_loan)s, %(loan_granted_at)s, %(debt_amount)s, %(has_debt)s,
                    %(debt_created_at)s, %(wallet_data)s
                ) ON DUPLICATE KEY UPDATE
                    user_name = VALUES(user_name),
                    username = VALUES(username),
                    balance = VALUES(balance),
                    status = VALUES(status),
                    last_transaction = VALUES(last_transaction),
                    transaction_count = VALUES(transaction_count),
                    is_verified = VALUES(is_verified),
                    loan_amount = VALUES(loan_amount),
                    has_active_loan = VALUES(has_active_loan),
                    debt_amount = VALUES(debt_amount),
                    has_debt = VALUES(has_debt),
                    wallet_data = VALUES(wallet_data)
                """
                
                mysql_manager.execute_update(query, wallet_data)
                migrated_count += 1
            
            logger.info(f"💰 تم ترحيل {migrated_count} محفظة")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في ترحيل بيانات المحافظ: {e}")
            return False
    
    def migrate_admin_data(self) -> bool:
        """ترحيل بيانات الإدارة"""
        try:
            admin_data = self.load_json_file("admin_data.json")
            if not admin_data:
                return True
            
            migrated_count = 0
            
            for key, value in admin_data.items():
                data = {
                    'data_key': key,
                    'data_value': json.dumps(value, ensure_ascii=False),
                    'description': f'بيانات إدارية: {key}'
                }
                
                query = """
                INSERT INTO admin_data (data_key, data_value, description)
                VALUES (%(data_key)s, %(data_value)s, %(description)s)
                ON DUPLICATE KEY UPDATE
                    data_value = VALUES(data_value),
                    description = VALUES(description)
                """
                
                mysql_manager.execute_update(query, data)
                migrated_count += 1
            
            logger.info(f"⚙️ تم ترحيل {migrated_count} عنصر من بيانات الإدارة")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في ترحيل بيانات الإدارة: {e}")
            return False
    
    def migrate_ai_models_config(self) -> bool:
        """ترحيل إعدادات نماذج الذكاء الاصطناعي"""
        try:
            ai_config = self.load_json_file("ai_models_config.json")
            if not ai_config:
                return True
            
            migrated_count = 0
            
            for model_name, config in ai_config.items():
                data = {
                    'model_name': model_name,
                    'status': config.get('status', 'inactive'),
                    'priority': config.get('priority', 1),
                    'max_tokens': config.get('max_tokens', 1000),
                    'temperature': float(config.get('temperature', 0.7)),
                    'timeout': config.get('timeout', 30),
                    'fallback_enabled': config.get('fallback_enabled', True),
                    'model_config': json.dumps(config, ensure_ascii=False)
                }
                
                query = """
                INSERT INTO ai_models_config (
                    model_name, status, priority, max_tokens, temperature,
                    timeout, fallback_enabled, model_config
                ) VALUES (
                    %(model_name)s, %(status)s, %(priority)s, %(max_tokens)s, %(temperature)s,
                    %(timeout)s, %(fallback_enabled)s, %(model_config)s
                ) ON DUPLICATE KEY UPDATE
                    status = VALUES(status),
                    priority = VALUES(priority),
                    max_tokens = VALUES(max_tokens),
                    temperature = VALUES(temperature),
                    timeout = VALUES(timeout),
                    fallback_enabled = VALUES(fallback_enabled),
                    model_config = VALUES(model_config)
                """
                
                mysql_manager.execute_update(query, data)
                migrated_count += 1
            
            logger.info(f"🤖 تم ترحيل {migrated_count} نموذج ذكاء اصطناعي")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في ترحيل إعدادات نماذج الذكاء الاصطناعي: {e}")
            return False
    
    def run_migration(self) -> bool:
        """تشغيل عملية الترحيل الكاملة"""
        try:
            logger.info("🚀 بدء عملية ترحيل البيانات من JSON إلى MySQL")
            
            # إنشاء نسخة احتياطية
            if not self.create_backup():
                logger.error("❌ فشل في إنشاء النسخة الاحتياطية")
                return False
            
            # ترحيل البيانات
            migrations = [
                ("المستخدمين", self.migrate_users_data),
                ("المحافظ", self.migrate_wallets_data),
                ("بيانات الإدارة", self.migrate_admin_data),
                ("نماذج الذكاء الاصطناعي", self.migrate_ai_models_config),
            ]
            
            success_count = 0
            for name, migration_func in migrations:
                logger.info(f"🔄 ترحيل {name}...")
                if migration_func():
                    success_count += 1
                    logger.info(f"✅ تم ترحيل {name} بنجاح")
                else:
                    logger.error(f"❌ فشل في ترحيل {name}")
            
            if success_count == len(migrations):
                logger.info("🎉 تم ترحيل جميع البيانات بنجاح!")
                return True
            else:
                logger.warning(f"⚠️ تم ترحيل {success_count} من {len(migrations)} مجموعات بيانات")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في عملية الترحيل: {e}")
            return False

def main():
    """تشغيل سكريبت الترحيل"""
    migrator = DataMigrator()
    success = migrator.run_migration()
    
    if success:
        print("✅ تم ترحيل البيانات بنجاح!")
    else:
        print("❌ فشل في ترحيل البيانات!")
    
    return success

if __name__ == "__main__":
    main()
