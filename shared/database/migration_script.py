#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت ترحيل البيانات من JSON إلى SQLite
يحافظ على جميع البيانات الموجودة أثناء عملية التحويل
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, Any, List
from .sqlite_manager import SQLiteManager

# إعداد نظام السجلات
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataMigration:
    """فئة ترحيل البيانات من JSON إلى SQLite"""
    
    def __init__(self, database_dir: str = None):
        """تهيئة نظام الترحيل"""
        self.database_dir = database_dir or os.path.dirname(__file__)
        self.db_manager = SQLiteManager()
        self.migration_log = []
        
        # مسارات ملفات JSON
        self.json_files = {
            'users': os.path.join(self.database_dir, 'users_data.json'),
            'wallets': os.path.join(self.database_dir, 'wallets_database.json'),
            'admin_data': os.path.join(self.database_dir, 'admin_data.json'),
            'monitoring': os.path.join(self.database_dir, 'monitoring_data.json'),
            'ai_models': os.path.join(self.database_dir, 'ai_models_config.json'),
            'token_usage': os.path.join(self.database_dir, '..', 'ai_billing', 'token_usage_log.json')
        }
        
        logger.info("🔄 تم تهيئة نظام ترحيل البيانات")
    
    def load_json_file(self, file_path: str) -> Dict[str, Any]:
        """تحميل ملف JSON"""
        try:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                logger.warning(f"⚠️ الملف غير موجود: {file_path}")
                return {}
        except Exception as e:
            logger.error(f"❌ خطأ في تحميل الملف {file_path}: {e}")
            return {}
    
    def backup_json_files(self):
        """إنشاء نسخة احتياطية من ملفات JSON قبل الترحيل"""
        backup_dir = os.path.join(self.database_dir, '..', '..', 'backups', 'migration_backup')
        os.makedirs(backup_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        for file_type, file_path in self.json_files.items():
            if os.path.exists(file_path):
                backup_path = os.path.join(backup_dir, f"{file_type}_{timestamp}.json")
                try:
                    import shutil
                    shutil.copy2(file_path, backup_path)
                    logger.info(f"✅ تم نسخ {file_type} إلى {backup_path}")
                except Exception as e:
                    logger.error(f"❌ خطأ في نسخ {file_type}: {e}")
    
    def migrate_users_data(self):
        """ترحيل بيانات المستخدمين"""
        logger.info("🔄 بدء ترحيل بيانات المستخدمين...")
        
        users_data = self.load_json_file(self.json_files['users'])
        if not users_data:
            logger.info("ℹ️ لا توجد بيانات مستخدمين للترحيل")
            return
        
        migrated_count = 0
        errors = []
        
        for user_id, user_info in users_data.items():
            try:
                # تحويل البيانات إلى تنسيق SQLite
                user_data = {
                    'user_id': int(user_id),
                    'username': user_info.get('معرف المستخدم', '').replace('@', ''),
                    'display_name': user_info.get('اسم المستخدم', ''),
                    'language_code': user_info.get('اللغة', 'ar'),
                    'registration_date': user_info.get('تاريخ التسجيل', ''),
                    'first_login_date': user_info.get('تاريخ أول دخول', ''),
                    'last_activity': user_info.get('آخر نشاط', ''),
                    'visit_count': user_info.get('عدد الزيارات', 0),
                    'wallet_number': user_info.get('رقم المحفظة', ''),
                    'wallet_status': user_info.get('حالة المحفظة', 'نشطة'),
                    'is_active': True
                }
                
                # التحقق من وجود المستخدم
                existing_user = self.db_manager.select_one('users', where_clause='user_id = ?', where_params=(int(user_id),))
                
                if existing_user:
                    # تحديث المستخدم الموجود
                    self.db_manager.update('users', user_data, 'user_id = ?', (int(user_id),))
                else:
                    # إدراج مستخدم جديد
                    self.db_manager.insert('users', user_data)
                
                migrated_count += 1
                
            except Exception as e:
                error_msg = f"خطأ في ترحيل المستخدم {user_id}: {e}"
                errors.append(error_msg)
                logger.error(f"❌ {error_msg}")
        
        logger.info(f"✅ تم ترحيل {migrated_count} مستخدم")
        if errors:
            logger.warning(f"⚠️ {len(errors)} أخطاء في الترحيل")
        
        self.migration_log.append({
            'table': 'users',
            'migrated': migrated_count,
            'errors': len(errors),
            'timestamp': datetime.now().isoformat()
        })
    
    def migrate_wallets_data(self):
        """ترحيل بيانات المحافظ"""
        logger.info("🔄 بدء ترحيل بيانات المحافظ...")
        
        wallets_data = self.load_json_file(self.json_files['wallets'])
        if not wallets_data:
            logger.info("ℹ️ لا توجد بيانات محافظ للترحيل")
            return
        
        migrated_count = 0
        errors = []
        
        for wallet_number, wallet_info in wallets_data.items():
            try:
                # تحويل البيانات إلى تنسيق SQLite
                wallet_data = {
                    'wallet_number': wallet_number,
                    'user_id': wallet_info.get('user_id', 0),
                    'user_name': wallet_info.get('user_name', ''),
                    'username': wallet_info.get('username', ''),
                    'balance': float(wallet_info.get('balance', 0.0)),
                    'currency': wallet_info.get('currency', 'إكسا'),
                    'status': wallet_info.get('status', 'active'),
                    'created_at': wallet_info.get('created_at', ''),
                    'last_transaction': wallet_info.get('last_transaction', ''),
                    'transaction_count': wallet_info.get('transaction_count', 0),
                    'is_verified': wallet_info.get('is_verified', False),
                    'verified_at': wallet_info.get('verified_at', ''),
                    'security_level': wallet_info.get('security_level', 'basic'),
                    'loan_amount': float(wallet_info.get('loan_amount', 0.0)),
                    'has_active_loan': wallet_info.get('has_active_loan', False),
                    'loan_granted_at': wallet_info.get('loan_granted_at', ''),
                    'debt_amount': float(wallet_info.get('debt_amount', 0.0)),
                    'has_debt': wallet_info.get('has_debt', False)
                }
                
                # التحقق من وجود المحفظة
                existing_wallet = self.db_manager.select_one('wallets', where_clause='wallet_number = ?', where_params=(wallet_number,))
                
                if existing_wallet:
                    # تحديث المحفظة الموجودة
                    self.db_manager.update('wallets', wallet_data, 'wallet_number = ?', (wallet_number,))
                else:
                    # إدراج محفظة جديدة
                    self.db_manager.insert('wallets', wallet_data)
                
                # ترحيل تاريخ القروض إذا كان موجوداً (بعد إدراج المحفظة)
                loan_history = wallet_info.get('loan_history', [])
                for loan_record in loan_history:
                    try:
                        loan_data = {
                            'wallet_number': wallet_number,
                            'loan_type': loan_record.get('type', ''),
                            'amount': float(loan_record.get('amount', 0.0)),
                            'granted_at': loan_record.get('granted_at', ''),
                            'old_balance': float(loan_record.get('old_balance', 0.0)),
                            'new_balance': float(loan_record.get('new_balance', 0.0)),
                            'status': 'completed'
                        }

                        # التحقق من عدم وجود السجل مسبقاً
                        existing_loan = self.db_manager.select_one(
                            'loan_history',
                            where_clause='wallet_number = ? AND granted_at = ? AND amount = ?',
                            where_params=(wallet_number, loan_data['granted_at'], loan_data['amount'])
                        )

                        if not existing_loan:
                            self.db_manager.insert('loan_history', loan_data)
                    except Exception as loan_error:
                        logger.warning(f"⚠️ تخطي سجل قرض للمحفظة {wallet_number}: {loan_error}")
                
                migrated_count += 1
                
            except Exception as e:
                error_msg = f"خطأ في ترحيل المحفظة {wallet_number}: {e}"
                errors.append(error_msg)
                logger.error(f"❌ {error_msg}")
        
        logger.info(f"✅ تم ترحيل {migrated_count} محفظة")
        if errors:
            logger.warning(f"⚠️ {len(errors)} أخطاء في الترحيل")
        
        self.migration_log.append({
            'table': 'wallets',
            'migrated': migrated_count,
            'errors': len(errors),
            'timestamp': datetime.now().isoformat()
        })
    
    def migrate_admin_data(self):
        """ترحيل بيانات الإدارة"""
        logger.info("🔄 بدء ترحيل بيانات الإدارة...")
        
        admin_data = self.load_json_file(self.json_files['admin_data'])
        if not admin_data:
            logger.info("ℹ️ لا توجد بيانات إدارة للترحيل")
            return
        
        migrated_count = 0
        
        try:
            for key, value in admin_data.items():
                data = {
                    'key_name': key,
                    'value_data': json.dumps(value, ensure_ascii=False),
                    'data_type': 'json'
                }
                
                # التحقق من وجود المفتاح
                existing_data = self.db_manager.select_one('admin_data', where_clause='key_name = ?', where_params=(key,))
                
                if existing_data:
                    self.db_manager.update('admin_data', data, 'key_name = ?', (key,))
                else:
                    self.db_manager.insert('admin_data', data)
                
                migrated_count += 1
            
            logger.info(f"✅ تم ترحيل {migrated_count} عنصر من بيانات الإدارة")
            
        except Exception as e:
            logger.error(f"❌ خطأ في ترحيل بيانات الإدارة: {e}")
        
        self.migration_log.append({
            'table': 'admin_data',
            'migrated': migrated_count,
            'errors': 0,
            'timestamp': datetime.now().isoformat()
        })
    
    def migrate_ai_models_config(self):
        """ترحيل إعدادات نماذج الذكاء الاصطناعي"""
        logger.info("🔄 بدء ترحيل إعدادات نماذج الذكاء الاصطناعي...")
        
        ai_models_data = self.load_json_file(self.json_files['ai_models'])
        if not ai_models_data:
            logger.info("ℹ️ لا توجد إعدادات نماذج ذكاء اصطناعي للترحيل")
            return
        
        migrated_count = 0
        
        try:
            for model_name, model_config in ai_models_data.items():
                data = {
                    'model_name': model_name,
                    'status': model_config.get('status', 'inactive'),
                    'priority': model_config.get('priority', 1),
                    'max_tokens': model_config.get('max_tokens', 1000),
                    'temperature': model_config.get('temperature', 0.7),
                    'timeout': model_config.get('timeout', 30),
                    'fallback_enabled': model_config.get('fallback_enabled', True),
                    'last_updated': model_config.get('last_updated', datetime.now().isoformat()),
                    'config_data': json.dumps(model_config, ensure_ascii=False)
                }
                
                # التحقق من وجود النموذج
                existing_model = self.db_manager.select_one('ai_models', where_clause='model_name = ?', where_params=(model_name,))
                
                if existing_model:
                    self.db_manager.update('ai_models', data, 'model_name = ?', (model_name,))
                else:
                    self.db_manager.insert('ai_models', data)
                
                migrated_count += 1
            
            logger.info(f"✅ تم ترحيل {migrated_count} نموذج ذكاء اصطناعي")
            
        except Exception as e:
            logger.error(f"❌ خطأ في ترحيل إعدادات نماذج الذكاء الاصطناعي: {e}")
        
        self.migration_log.append({
            'table': 'ai_models',
            'migrated': migrated_count,
            'errors': 0,
            'timestamp': datetime.now().isoformat()
        })
    
    def run_full_migration(self):
        """تشغيل عملية الترحيل الكاملة"""
        logger.info("🚀 بدء عملية الترحيل الكاملة من JSON إلى SQLite")

        start_time = datetime.now()

        # تعطيل المفاتيح الخارجية مؤقتاً أثناء الترحيل
        with self.db_manager.get_connection() as conn:
            conn.execute("PRAGMA foreign_keys = OFF")

        # إنشاء نسخة احتياطية
        self.backup_json_files()

        # تشغيل عمليات الترحيل
        self.migrate_users_data()
        self.migrate_wallets_data()
        self.migrate_admin_data()
        self.migrate_ai_models_config()

        # إعادة تفعيل المفاتيح الخارجية
        with self.db_manager.get_connection() as conn:
            conn.execute("PRAGMA foreign_keys = ON")
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # إنشاء تقرير الترحيل
        migration_report = {
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'duration_seconds': duration,
            'tables_migrated': self.migration_log,
            'total_records': sum(log['migrated'] for log in self.migration_log),
            'total_errors': sum(log['errors'] for log in self.migration_log)
        }
        
        # حفظ تقرير الترحيل
        report_path = os.path.join(self.database_dir, '..', '..', 'backups', 'migration_report.json')
        os.makedirs(os.path.dirname(report_path), exist_ok=True)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(migration_report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"🎉 اكتملت عملية الترحيل في {duration:.2f} ثانية")
        logger.info(f"📊 تم ترحيل {migration_report['total_records']} سجل")
        logger.info(f"📄 تقرير الترحيل محفوظ في: {report_path}")
        
        return migration_report

def main():
    """تشغيل سكريبت الترحيل"""
    try:
        migration = DataMigration()
        report = migration.run_full_migration()
        
        print("\n" + "="*50)
        print("📋 تقرير الترحيل النهائي")
        print("="*50)
        print(f"⏱️ المدة: {report['duration_seconds']:.2f} ثانية")
        print(f"📊 إجمالي السجلات: {report['total_records']}")
        print(f"❌ إجمالي الأخطاء: {report['total_errors']}")
        print("="*50)
        
        for table_log in report['tables_migrated']:
            print(f"📋 {table_log['table']}: {table_log['migrated']} سجل ({table_log['errors']} أخطاء)")
        
        print("="*50)
        print("✅ تم الانتهاء من عملية الترحيل بنجاح!")
        
    except Exception as e:
        logger.error(f"❌ فشلت عملية الترحيل: {e}")
        raise

if __name__ == "__main__":
    main()
