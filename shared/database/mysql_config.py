#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعدادات قاعدة البيانات MySQL
تحتوي على جميع الإعدادات المطلوبة للاتصال بقاعدة البيانات
"""

import os
from typing import Dict, Any

# إعدادات الاتصال بقاعدة البيانات
DATABASE_CONFIG = {
    # إعدادات الاتصال الأساسية
    "host": os.getenv("MYSQL_HOST", "localhost"),
    "port": int(os.getenv("MYSQL_PORT", 3306)),
    "user": os.getenv("MYSQL_USER", "bot_user"),
    "password": os.getenv("MYSQL_PASSWORD", "secure_password_123"),
    "database": os.getenv("MYSQL_DATABASE", "salah_bot_system"),
    "charset": "utf8mb4",
    "collation": "utf8mb4_unicode_ci",
    
    # إعدادات الأمان
    "ssl_disabled": False,
    "ssl_verify_cert": True,
    "ssl_verify_identity": True,
    
    # إعدادات الاتصال المتقدمة
    "autocommit": False,
    "pool_size": 10,
    "max_overflow": 20,
    "pool_timeout": 30,
    "pool_recycle": 3600,
    "pool_pre_ping": True,
    
    # إعدادات المهلة الزمنية
    "connect_timeout": 10,
    "read_timeout": 30,
    "write_timeout": 30,
}

# إعدادات SQLAlchemy
SQLALCHEMY_CONFIG = {
    "echo": False,  # تفعيل لعرض استعلامات SQL في وضع التطوير
    "echo_pool": False,
    "pool_pre_ping": True,
    "pool_recycle": 3600,
    "max_overflow": 20,
    "pool_size": 10,
}

# إعدادات النسخ الاحتياطي
BACKUP_CONFIG = {
    "auto_backup": True,
    "backup_interval": 3600,  # كل ساعة
    "backup_path": "backups/mysql_backups/",
    "max_backup_files": 24,  # الاحتفاظ بـ 24 نسخة احتياطية
    "compress_backups": True,
    "backup_format": "sql",  # sql أو csv
}

# إعدادات الأمان والتشفير
SECURITY_CONFIG = {
    "encrypt_sensitive_data": True,
    "encryption_key": os.getenv("DB_ENCRYPTION_KEY", "your_encryption_key_here"),
    "hash_passwords": True,
    "salt_rounds": 12,
    "enable_audit_log": True,
    "audit_table": "system_audit_log",
}

# إعدادات الأداء
PERFORMANCE_CONFIG = {
    "enable_query_cache": True,
    "cache_timeout": 300,  # 5 دقائق
    "batch_size": 1000,
    "max_connections": 100,
    "connection_timeout": 30,
    "enable_connection_pooling": True,
}

# إعدادات المراقبة
MONITORING_CONFIG = {
    "enable_slow_query_log": True,
    "slow_query_threshold": 2.0,  # ثانيتان
    "enable_performance_monitoring": True,
    "log_all_queries": False,  # تفعيل فقط في وضع التطوير
    "monitor_connection_pool": True,
}

# جداول قاعدة البيانات
DATABASE_TABLES = {
    "users": "users",
    "wallets": "wallets", 
    "transactions": "transactions",
    "admin_data": "admin_data",
    "banned_users": "banned_users",
    "restricted_users": "restricted_users",
    "monitoring_data": "monitoring_data",
    "bot_stats": "bot_stats",
    "ai_models_config": "ai_models_config",
    "user_sessions": "user_sessions",
    "system_logs": "system_logs",
    "backup_history": "backup_history",
    "audit_log": "audit_log",
}

# أنواع البيانات المخصصة
CUSTOM_DATA_TYPES = {
    "json_data": "JSON",
    "encrypted_text": "TEXT",
    "currency_amount": "DECIMAL(15,8)",
    "user_id": "BIGINT UNSIGNED",
    "wallet_number": "VARCHAR(20)",
    "timestamp": "TIMESTAMP DEFAULT CURRENT_TIMESTAMP",
    "status": "ENUM('active', 'inactive', 'suspended', 'deleted')",
}

def get_database_url() -> str:
    """إنشاء رابط قاعدة البيانات"""
    config = DATABASE_CONFIG
    return f"mysql+pymysql://{config['user']}:{config['password']}@{config['host']}:{config['port']}/{config['database']}?charset={config['charset']}"

def get_connection_params() -> Dict[str, Any]:
    """الحصول على معاملات الاتصال"""
    return {
        "host": DATABASE_CONFIG["host"],
        "port": DATABASE_CONFIG["port"],
        "user": DATABASE_CONFIG["user"],
        "password": DATABASE_CONFIG["password"],
        "database": DATABASE_CONFIG["database"],
        "charset": DATABASE_CONFIG["charset"],
        "autocommit": DATABASE_CONFIG["autocommit"],
        "connect_timeout": DATABASE_CONFIG["connect_timeout"],
        "read_timeout": DATABASE_CONFIG["read_timeout"],
        "write_timeout": DATABASE_CONFIG["write_timeout"],
    }

# إعدادات البيئة المختلفة
ENVIRONMENT_CONFIGS = {
    "development": {
        "debug": True,
        "echo": True,
        "pool_size": 5,
        "max_overflow": 10,
    },
    "production": {
        "debug": False,
        "echo": False,
        "pool_size": 20,
        "max_overflow": 50,
    },
    "testing": {
        "debug": True,
        "echo": False,
        "pool_size": 2,
        "max_overflow": 5,
    }
}

# الحصول على إعدادات البيئة الحالية
CURRENT_ENV = os.getenv("ENVIRONMENT", "development")
ENV_CONFIG = ENVIRONMENT_CONFIGS.get(CURRENT_ENV, ENVIRONMENT_CONFIGS["development"])
