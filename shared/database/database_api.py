#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة برمجية موحدة للتعامل مع قاعدة البيانات
توفر طرق سهلة ومبسطة للعمليات الشائعة
"""

import logging
from datetime import datetime
from typing import Dict, Any, List, Optional, Union
from .sqlite_manager import SQLiteManager
from .backup_system import BackupSystem

# إعداد نظام السجلات
logger = logging.getLogger(__name__)

class DatabaseAPI:
    """واجهة برمجية موحدة لقاعدة البيانات"""
    
    def __init__(self, db_path: str = None):
        """تهيئة واجهة قاعدة البيانات"""
        self.db_manager = SQLiteManager(db_path)
        self.backup_system = BackupSystem(self.db_manager)
        
        logger.info("🔌 تم تهيئة واجهة قاعدة البيانات الموحدة")
    
    # ==================== إدارة المستخدمين ====================
    
    def get_user(self, user_id: int) -> Optional[Dict[str, Any]]:
        """الحصول على بيانات مستخدم"""
        try:
            user = self.db_manager.select_one('users', where_clause='user_id = ?', where_params=(user_id,))
            return dict(user) if user else None
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على المستخدم {user_id}: {e}")
            return None
    
    def create_user(self, user_data: Dict[str, Any]) -> bool:
        """إنشاء مستخدم جديد"""
        try:
            # التحقق من عدم وجود المستخدم
            if self.get_user(user_data['user_id']):
                logger.warning(f"⚠️ المستخدم {user_data['user_id']} موجود بالفعل")
                return False
            
            self.db_manager.insert('users', user_data)
            logger.info(f"✅ تم إنشاء المستخدم {user_data['user_id']}")
            return True
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء المستخدم: {e}")
            return False
    
    def update_user(self, user_id: int, user_data: Dict[str, Any]) -> bool:
        """تحديث بيانات مستخدم"""
        try:
            rows_affected = self.db_manager.update('users', user_data, 'user_id = ?', (user_id,))
            if rows_affected > 0:
                logger.info(f"✅ تم تحديث المستخدم {user_id}")
                return True
            else:
                logger.warning(f"⚠️ لم يتم العثور على المستخدم {user_id}")
                return False
        except Exception as e:
            logger.error(f"❌ خطأ في تحديث المستخدم {user_id}: {e}")
            return False
    
    def delete_user(self, user_id: int) -> bool:
        """حذف مستخدم"""
        try:
            rows_affected = self.db_manager.delete('users', 'user_id = ?', (user_id,))
            if rows_affected > 0:
                logger.info(f"✅ تم حذف المستخدم {user_id}")
                return True
            else:
                logger.warning(f"⚠️ لم يتم العثور على المستخدم {user_id}")
                return False
        except Exception as e:
            logger.error(f"❌ خطأ في حذف المستخدم {user_id}: {e}")
            return False
    
    def get_all_users(self, active_only: bool = True) -> List[Dict[str, Any]]:
        """الحصول على جميع المستخدمين"""
        try:
            where_clause = 'is_active = 1' if active_only else ''
            users = self.db_manager.select('users', where_clause=where_clause, order_by='last_activity DESC')
            return [dict(user) for user in users]
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على المستخدمين: {e}")
            return []
    
    def get_active_users_today(self) -> List[Dict[str, Any]]:
        """الحصول على المستخدمين النشطين اليوم"""
        try:
            today = datetime.now().strftime('%Y-%m-%d')
            users = self.db_manager.select(
                'users', 
                where_clause='last_activity LIKE ? AND is_active = 1',
                where_params=(f'{today}%',),
                order_by='last_activity DESC'
            )
            return [dict(user) for user in users]
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على المستخدمين النشطين: {e}")
            return []
    
    def update_user_activity(self, user_id: int) -> bool:
        """تحديث آخر نشاط للمستخدم"""
        try:
            update_data = {
                'last_activity': datetime.now().isoformat(),
                'visit_count': self.db_manager.execute_query(
                    'SELECT visit_count FROM users WHERE user_id = ?',
                    (user_id,),
                    fetch='one'
                )[0] + 1 if self.get_user(user_id) else 1
            }
            return self.update_user(user_id, update_data)
        except Exception as e:
            logger.error(f"❌ خطأ في تحديث نشاط المستخدم {user_id}: {e}")
            return False
    
    # ==================== إدارة المحافظ ====================
    
    def get_wallet(self, wallet_number: str) -> Optional[Dict[str, Any]]:
        """الحصول على بيانات محفظة"""
        try:
            wallet = self.db_manager.select_one('wallets', where_clause='wallet_number = ?', where_params=(wallet_number,))
            return dict(wallet) if wallet else None
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على المحفظة {wallet_number}: {e}")
            return None
    
    def get_user_wallet(self, user_id: int) -> Optional[Dict[str, Any]]:
        """الحصول على محفظة المستخدم"""
        try:
            wallet = self.db_manager.select_one('wallets', where_clause='user_id = ?', where_params=(user_id,))
            return dict(wallet) if wallet else None
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على محفظة المستخدم {user_id}: {e}")
            return None
    
    def create_wallet(self, wallet_data: Dict[str, Any]) -> bool:
        """إنشاء محفظة جديدة"""
        try:
            # التحقق من عدم وجود المحفظة
            if self.get_wallet(wallet_data['wallet_number']):
                logger.warning(f"⚠️ المحفظة {wallet_data['wallet_number']} موجودة بالفعل")
                return False
            
            self.db_manager.insert('wallets', wallet_data)
            logger.info(f"✅ تم إنشاء المحفظة {wallet_data['wallet_number']}")
            return True
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء المحفظة: {e}")
            return False
    
    def update_wallet_balance(self, wallet_number: str, new_balance: float, transaction_type: str = "update") -> bool:
        """تحديث رصيد المحفظة"""
        try:
            with self.db_manager.transaction() as conn:
                # تحديث رصيد المحفظة
                update_data = {
                    'balance': new_balance,
                    'last_transaction': datetime.now().isoformat(),
                    'transaction_count': self.db_manager.execute_query(
                        'SELECT transaction_count FROM wallets WHERE wallet_number = ?',
                        (wallet_number,),
                        fetch='one'
                    )[0] + 1
                }
                
                self.db_manager.update('wallets', update_data, 'wallet_number = ?', (wallet_number,))
                
                # إضافة سجل المعاملة
                transaction_data = {
                    'wallet_number': wallet_number,
                    'transaction_type': transaction_type,
                    'amount': new_balance,
                    'description': f'تحديث الرصيد إلى {new_balance}',
                    'status': 'completed'
                }
                
                self.db_manager.insert('transactions', transaction_data)
                
            logger.info(f"✅ تم تحديث رصيد المحفظة {wallet_number} إلى {new_balance}")
            return True
        except Exception as e:
            logger.error(f"❌ خطأ في تحديث رصيد المحفظة {wallet_number}: {e}")
            return False
    
    def add_transaction(self, transaction_data: Dict[str, Any]) -> bool:
        """إضافة معاملة جديدة"""
        try:
            self.db_manager.insert('transactions', transaction_data)
            logger.info(f"✅ تم إضافة معاملة للمحفظة {transaction_data['wallet_number']}")
            return True
        except Exception as e:
            logger.error(f"❌ خطأ في إضافة المعاملة: {e}")
            return False
    
    def get_wallet_transactions(self, wallet_number: str, limit: int = 50) -> List[Dict[str, Any]]:
        """الحصول على معاملات المحفظة"""
        try:
            transactions = self.db_manager.select(
                'transactions',
                where_clause='wallet_number = ?',
                where_params=(wallet_number,),
                order_by='created_at DESC',
                limit=limit
            )
            return [dict(transaction) for transaction in transactions]
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على معاملات المحفظة {wallet_number}: {e}")
            return []
    
    # ==================== إدارة استخدام التوكن ====================
    
    def log_token_usage(self, usage_data: Dict[str, Any]) -> bool:
        """تسجيل استخدام التوكن"""
        try:
            self.db_manager.insert('token_usage', usage_data)
            logger.info(f"✅ تم تسجيل استخدام التوكن للمستخدم {usage_data['user_id']}")
            return True
        except Exception as e:
            logger.error(f"❌ خطأ في تسجيل استخدام التوكن: {e}")
            return False
    
    def get_user_token_usage(self, user_id: int, limit: int = 100) -> List[Dict[str, Any]]:
        """الحصول على سجل استخدام التوكن للمستخدم"""
        try:
            usage = self.db_manager.select(
                'token_usage',
                where_clause='user_id = ?',
                where_params=(user_id,),
                order_by='created_at DESC',
                limit=limit
            )
            return [dict(record) for record in usage]
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على سجل التوكن للمستخدم {user_id}: {e}")
            return []
    
    def get_token_usage_statistics(self, days: int = 30) -> Dict[str, Any]:
        """الحصول على إحصائيات استخدام التوكن"""
        try:
            from_date = (datetime.now() - timedelta(days=days)).isoformat()
            
            # إجمالي الاستخدام
            total_usage = self.db_manager.execute_query(
                'SELECT COUNT(*), SUM(total_tokens), SUM(cost_in_exa) FROM token_usage WHERE created_at >= ?',
                (from_date,),
                fetch='one'
            )
            
            # الاستخدام حسب المستخدم
            user_usage = self.db_manager.execute_query(
                'SELECT user_id, COUNT(*), SUM(total_tokens), SUM(cost_in_exa) FROM token_usage WHERE created_at >= ? GROUP BY user_id ORDER BY SUM(total_tokens) DESC',
                (from_date,),
                fetch='all'
            )
            
            return {
                'period_days': days,
                'total_requests': total_usage[0] or 0,
                'total_tokens': total_usage[1] or 0,
                'total_cost_exa': total_usage[2] or 0.0,
                'top_users': [
                    {
                        'user_id': row[0],
                        'requests': row[1],
                        'tokens': row[2],
                        'cost_exa': row[3]
                    } for row in user_usage[:10]
                ]
            }
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على إحصائيات التوكن: {e}")
            return {}
    
    # ==================== إدارة البيانات الإدارية ====================
    
    def set_admin_setting(self, key: str, value: Any) -> bool:
        """تعيين إعداد إداري"""
        try:
            import json
            data = {
                'key_name': key,
                'value_data': json.dumps(value, ensure_ascii=False),
                'data_type': 'json'
            }
            
            # التحقق من وجود الإعداد
            existing = self.db_manager.select_one('admin_data', where_clause='key_name = ?', where_params=(key,))
            
            if existing:
                self.db_manager.update('admin_data', data, 'key_name = ?', (key,))
            else:
                self.db_manager.insert('admin_data', data)
            
            logger.info(f"✅ تم تعيين الإعداد الإداري: {key}")
            return True
        except Exception as e:
            logger.error(f"❌ خطأ في تعيين الإعداد الإداري {key}: {e}")
            return False
    
    def get_admin_setting(self, key: str, default: Any = None) -> Any:
        """الحصول على إعداد إداري"""
        try:
            import json
            setting = self.db_manager.select_one('admin_data', where_clause='key_name = ?', where_params=(key,))
            
            if setting:
                return json.loads(setting['value_data'])
            else:
                return default
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على الإعداد الإداري {key}: {e}")
            return default
    
    def get_all_admin_settings(self) -> Dict[str, Any]:
        """الحصول على جميع الإعدادات الإدارية"""
        try:
            import json
            settings = self.db_manager.select('admin_data')
            result = {}
            
            for setting in settings:
                try:
                    result[setting['key_name']] = json.loads(setting['value_data'])
                except:
                    result[setting['key_name']] = setting['value_data']
            
            return result
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على الإعدادات الإدارية: {e}")
            return {}
    
    # ==================== إحصائيات عامة ====================
    
    def get_system_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات النظام"""
        try:
            stats = {
                'database': self.db_manager.get_statistics(),
                'users': {
                    'total': self.db_manager.count('users'),
                    'active': self.db_manager.count('users', 'is_active = 1'),
                    'active_today': len(self.get_active_users_today())
                },
                'wallets': {
                    'total': self.db_manager.count('wallets'),
                    'active': self.db_manager.count('wallets', 'status = "active"'),
                    'total_balance': self.db_manager.execute_query(
                        'SELECT SUM(balance) FROM wallets WHERE status = "active"',
                        fetch='one'
                    )[0] or 0.0
                },
                'transactions': {
                    'total': self.db_manager.count('transactions'),
                    'today': self.db_manager.count('transactions', 'DATE(created_at) = DATE("now")')
                },
                'token_usage': self.get_token_usage_statistics(30),
                'backup': self.backup_system.get_backup_statistics()
            }
            
            return stats
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على إحصائيات النظام: {e}")
            return {}
    
    # ==================== إدارة النسخ الاحتياطية ====================
    
    def create_backup(self, backup_type: str = "manual") -> str:
        """إنشاء نسخة احتياطية"""
        return self.backup_system.create_backup(backup_type)
    
    def restore_backup(self, backup_path: str, confirm: bool = False) -> bool:
        """استعادة نسخة احتياطية"""
        return self.backup_system.restore_backup(backup_path, confirm)
    
    def list_backups(self) -> List[Dict[str, Any]]:
        """عرض النسخ الاحتياطية"""
        return self.backup_system.list_backups()
    
    def start_auto_backup(self):
        """بدء النسخ الاحتياطي التلقائي"""
        self.backup_system.start_scheduler()
    
    def stop_auto_backup(self):
        """إيقاف النسخ الاحتياطي التلقائي"""
        self.backup_system.stop_scheduler()
    
    # ==================== تنظيف وصيانة ====================
    
    def cleanup_database(self):
        """تنظيف وصيانة قاعدة البيانات"""
        try:
            # تنظيف النسخ الاحتياطية القديمة
            self.backup_system.cleanup_old_backups()
            
            # تحسين قاعدة البيانات
            self.db_manager.vacuum()
            
            logger.info("✅ تم تنظيف وصيانة قاعدة البيانات")
            return True
        except Exception as e:
            logger.error(f"❌ خطأ في تنظيف قاعدة البيانات: {e}")
            return False
    
    def close(self):
        """إغلاق الاتصالات"""
        try:
            self.backup_system.stop_scheduler()
            self.db_manager.close_all_connections()
            logger.info("✅ تم إغلاق جميع اتصالات قاعدة البيانات")
        except Exception as e:
            logger.error(f"❌ خطأ في إغلاق الاتصالات: {e}")

# إنشاء مثيل عام للاستخدام
db_api = DatabaseAPI()
