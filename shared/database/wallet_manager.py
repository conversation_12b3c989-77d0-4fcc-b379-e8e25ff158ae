#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير المحافظ المشترك
يدير إنشاء وإدارة محافظ المستخدمين بين البوت الرئيسي والإداري
"""

import os
import json
import random
import logging
from datetime import datetime
from typing import Dict, Any, Optional, Tuple

# إعداد نظام السجلات
logger = logging.getLogger(__name__)

class WalletManager:
    """مدير المحافظ المشترك"""
    
    def __init__(self):
        """تهيئة مدير المحافظ"""
        # مسارات قواعد البيانات
        self.wallets_file = os.path.join(os.path.dirname(__file__), "wallets_database.json")
        self.users_file = os.path.join(os.path.dirname(__file__), "users_data.json")
        
        # إنشاء الملفات إذا لم تكن موجودة
        self._ensure_files_exist()
        
        logger.info("🏦 تم تهيئة مدير المحافظ")
    
    def _ensure_files_exist(self):
        """التأكد من وجود ملفات قواعد البيانات"""
        # إنشاء ملف المحافظ
        if not os.path.exists(self.wallets_file):
            with open(self.wallets_file, 'w', encoding='utf-8') as f:
                json.dump({}, f, ensure_ascii=False, indent=2)
            logger.info("📄 تم إنشاء ملف قاعدة بيانات المحافظ")
        
        # التأكد من وجود ملف المستخدمين
        if not os.path.exists(self.users_file):
            with open(self.users_file, 'w', encoding='utf-8') as f:
                json.dump({}, f, ensure_ascii=False, indent=2)
            logger.info("📄 تم إنشاء ملف قاعدة بيانات المستخدمين")
    
    def load_wallets_data(self) -> Dict[str, Any]:
        """تحميل بيانات المحافظ"""
        try:
            with open(self.wallets_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"خطأ في تحميل بيانات المحافظ: {e}")
            return {}
    
    def save_wallets_data(self, data: Dict[str, Any]):
        """حفظ بيانات المحافظ"""
        try:
            with open(self.wallets_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"خطأ في حفظ بيانات المحافظ: {e}")
    
    def load_users_data(self) -> Dict[str, Any]:
        """تحميل بيانات المستخدمين"""
        try:
            with open(self.users_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"خطأ في تحميل بيانات المستخدمين: {e}")
            return {}
    
    def save_users_data(self, data: Dict[str, Any]):
        """حفظ بيانات المستخدمين"""
        try:
            with open(self.users_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"خطأ في حفظ بيانات المستخدمين: {e}")
    
    def generate_wallet_number(self) -> str:
        """توليد رقم محفظة جديد"""
        # البادئة الثابتة: 909
        prefix = "909"
        
        # توليد 7 أرقام عشوائية
        random_digits = ''.join([str(random.randint(0, 9)) for _ in range(7)])
        
        # دمج البادئة مع الأرقام العشوائية
        wallet_number = prefix + random_digits
        
        # التأكد من عدم تكرار الرقم
        wallets_data = self.load_wallets_data()
        while wallet_number in wallets_data:
            random_digits = ''.join([str(random.randint(0, 9)) for _ in range(7)])
            wallet_number = prefix + random_digits
        
        return wallet_number
    
    def create_wallet(self, user_id: int, user_info: Dict[str, Any]) -> Tuple[bool, str, str]:
        """
        إنشاء محفظة جديدة للمستخدم
        
        Args:
            user_id: معرف المستخدم
            user_info: معلومات المستخدم
            
        Returns:
            Tuple[bool, str, str]: (نجح الإنشاء, رقم المحفظة, رسالة)
        """
        try:
            user_id_str = str(user_id)
            
            # فحص ما إذا كان المستخدم لديه محفظة بالفعل
            wallets_data = self.load_wallets_data()
            existing_wallet = self.get_user_wallet(user_id)
            
            if existing_wallet:
                logger.info(f"🏦 المستخدم {user_id} لديه محفظة بالفعل: {existing_wallet['wallet_number']}")
                return True, existing_wallet["wallet_number"], "المحفظة موجودة بالفعل"
            
            # توليد رقم محفظة جديد
            wallet_number = self.generate_wallet_number()
            
            # إنشاء بيانات المحفظة
            wallet_data = {
                "wallet_number": wallet_number,
                "user_id": user_id,
                "user_name": user_info.get("اسم المستخدم", "غير محدد"),
                "username": user_info.get("معرف المستخدم", "غير محدد"),
                "balance": 0.0,  # الرصيد الابتدائي
                "currency": "إكسا",  # العملة الافتراضية
                "status": "active",  # حالة المحفظة
                "created_at": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "last_transaction": None,
                "transaction_count": 0,
                "is_verified": False,
                "security_level": "basic",
                # نظام السلف
                "loan_amount": 0.0,  # مبلغ السلف الحالي
                "has_active_loan": False,  # هل يوجد سلف نشط
                "loan_granted_at": None,  # تاريخ منح السلف
                "loan_history": [],  # تاريخ السلف
                # نظام الدين للذكاء الاصطناعي
                "debt_amount": 0.0,  # مبلغ الدين الحالي
                "has_debt": False,  # هل يوجد دين نشط
                "debt_limit": 1.0,  # الحد الأقصى للدين (1 إكسا)
                "debt_created_at": None,  # تاريخ إنشاء الدين
                "debt_history": []  # تاريخ الدين
            }
            
            # حفظ بيانات المحفظة
            wallets_data[wallet_number] = wallet_data
            self.save_wallets_data(wallets_data)
            
            # تحديث بيانات المستخدم لإضافة رقم المحفظة
            users_data = self.load_users_data()
            if user_id_str in users_data:
                users_data[user_id_str]["رقم المحفظة"] = wallet_number
                users_data[user_id_str]["تاريخ إنشاء المحفظة"] = wallet_data["created_at"]
                self.save_users_data(users_data)
            
            logger.info(f"🏦 تم إنشاء محفظة جديدة: {wallet_number} للمستخدم {user_id}")
            return True, wallet_number, "تم إنشاء المحفظة بنجاح"
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء المحفظة للمستخدم {user_id}: {e}")
            return False, "", f"خطأ في إنشاء المحفظة: {e}"
    
    def get_user_wallet(self, user_id: int) -> Optional[Dict[str, Any]]:
        """الحصول على محفظة المستخدم"""
        try:
            wallets_data = self.load_wallets_data()
            
            # البحث عن المحفظة بمعرف المستخدم
            for wallet_number, wallet_data in wallets_data.items():
                if wallet_data.get("user_id") == user_id:
                    return wallet_data
            
            return None
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على محفظة المستخدم {user_id}: {e}")
            return None
    
    def get_wallet_by_number(self, wallet_number: str) -> Optional[Dict[str, Any]]:
        """الحصول على المحفظة برقمها"""
        try:
            wallets_data = self.load_wallets_data()
            return wallets_data.get(wallet_number)
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على المحفظة {wallet_number}: {e}")
            return None
    
    def update_wallet_balance(self, wallet_number: str, new_balance: float, transaction_type: str = "manual") -> Tuple[bool, Dict]:
        """
        تحديث رصيد المحفظة مع معالجة السلف تلقائياً

        Returns:
            Tuple[bool, Dict]: (نجح التحديث, بيانات معالجة السلف)
        """
        try:
            wallets_data = self.load_wallets_data()

            if wallet_number not in wallets_data:
                logger.error(f"المحفظة {wallet_number} غير موجودة")
                return False, {}

            old_balance = wallets_data[wallet_number]["balance"]

            # إذا كان هذا إضافة رصيد (الرصيد الجديد أكبر من القديم)
            loan_deduction_data = {}
            debt_payment_data = {}
            if new_balance > old_balance and transaction_type in ["admin_add", "manual", "add_balance", "test_partial_payment", "test_full_payment"]:
                added_amount = new_balance - old_balance
                remaining_amount = added_amount

                # أولاً: معالجة سداد الدين إذا وجد
                has_debt = wallets_data[wallet_number].get("has_debt", False)
                debt_amount = wallets_data[wallet_number].get("debt_amount", 0.0)

                if has_debt and debt_amount > 0:
                    # حساب المبلغ المسدد للدين
                    debt_payment = min(debt_amount, remaining_amount)
                    remaining_debt = debt_amount - debt_payment
                    remaining_amount -= debt_payment

                    # تحديث بيانات الدين
                    wallets_data[wallet_number]["debt_amount"] = remaining_debt

                    if remaining_debt <= 0:
                        wallets_data[wallet_number]["has_debt"] = False
                        wallets_data[wallet_number]["debt_amount"] = 0.0

                    # إضافة سجل سداد الدين
                    debt_payment_record = {
                        "type": "debt_payment",
                        "payment_amount": debt_payment,
                        "old_debt": debt_amount,
                        "remaining_debt": remaining_debt,
                        "is_fully_paid": remaining_debt <= 0,
                        "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }

                    if "debt_history" not in wallets_data[wallet_number]:
                        wallets_data[wallet_number]["debt_history"] = []
                    wallets_data[wallet_number]["debt_history"].append(debt_payment_record)

                    debt_payment_data = {
                        "payment_amount": debt_payment,
                        "old_debt": debt_amount,
                        "remaining_debt": remaining_debt,
                        "is_fully_paid": remaining_debt <= 0
                    }

                    logger.info(f"💳 تم سداد {debt_payment} إكسا من الدين للمحفظة {wallet_number}")

                # ثانياً: فحص وجود سلف نشط
                has_active_loan = wallets_data[wallet_number].get("has_active_loan", False)
                loan_amount = wallets_data[wallet_number].get("loan_amount", 0.0)

                if has_active_loan and loan_amount > 0 and remaining_amount > 0:
                    # حساب المبلغ المخصوم (أقل من السلف أو المبلغ المتبقي)
                    deducted_amount = min(loan_amount, remaining_amount)
                    remaining_loan = loan_amount - deducted_amount
                    remaining_amount -= deducted_amount

                    # تحديث بيانات السلف
                    wallets_data[wallet_number]["loan_amount"] = remaining_loan

                    # إذا تم سداد السلف بالكامل
                    if remaining_loan <= 0:
                        wallets_data[wallet_number]["has_active_loan"] = False
                        wallets_data[wallet_number]["loan_amount"] = 0.0
                        wallets_data[wallet_number]["loan_paid_at"] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                    # إضافة السجل إلى تاريخ السلف
                    deduction_record = {
                        "type": "loan_deduction",
                        "deducted_amount": deducted_amount,
                        "remaining_loan": remaining_loan,
                        "deducted_at": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                        "added_amount": added_amount,
                        "is_fully_paid": remaining_loan <= 0
                    }

                    if "loan_history" not in wallets_data[wallet_number]:
                        wallets_data[wallet_number]["loan_history"] = []
                    wallets_data[wallet_number]["loan_history"].append(deduction_record)

                    # إعداد بيانات خصم السلف
                    loan_deduction_data = {
                        "deducted_amount": deducted_amount,
                        "remaining_loan": remaining_loan,
                        "is_fully_paid": remaining_loan <= 0,
                        "original_loan": loan_amount,
                        "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }

                    logger.info(f"💳 تم خصم سلفة {deducted_amount} إكسا تلقائياً من المحفظة {wallet_number}")

                # ثالثاً: إضافة المبلغ المتبقي للرصيد
                final_balance = old_balance + remaining_amount
                wallets_data[wallet_number]["balance"] = final_balance
            else:
                # تحديث عادي للرصيد
                wallets_data[wallet_number]["balance"] = new_balance

            # تحديث معلومات المعاملة
            wallets_data[wallet_number]["last_transaction"] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            wallets_data[wallet_number]["transaction_count"] += 1

            # تحقق تلقائي للمحفظة عند أول إيداع (رصيد أكبر من 0)
            if new_balance > 0 and old_balance == 0:
                wallets_data[wallet_number]["is_verified"] = True
                wallets_data[wallet_number]["verified_at"] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                logger.info(f"🏦 تم تحقق المحفظة {wallet_number} تلقائياً بعد أول إيداع")

            # حفظ التغييرات
            self.save_wallets_data(wallets_data)

            final_balance = wallets_data[wallet_number]["balance"]
            logger.info(f"🏦 تم تحديث رصيد المحفظة {wallet_number}: {old_balance} → {final_balance}")

            # دمج بيانات السداد والخصم
            combined_data = {}
            if loan_deduction_data:
                combined_data["loan_deduction"] = loan_deduction_data
            if debt_payment_data:
                combined_data["debt_payment"] = debt_payment_data

            return True, combined_data

        except Exception as e:
            logger.error(f"خطأ في تحديث رصيد المحفظة {wallet_number}: {e}")
            return False, {}
    
    def get_all_wallets(self) -> Dict[str, Any]:
        """الحصول على جميع المحافظ"""
        return self.load_wallets_data()
    
    def get_wallet_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات المحافظ"""
        try:
            wallets_data = self.load_wallets_data()
            
            total_wallets = len(wallets_data)
            active_wallets = sum(1 for wallet in wallets_data.values() if wallet.get("status") == "active")
            total_balance = sum(wallet.get("balance", 0) for wallet in wallets_data.values())
            verified_wallets = sum(1 for wallet in wallets_data.values() if wallet.get("is_verified", False))

            # إحصائيات الديون
            total_debt = sum(wallet.get("debt_amount", 0) for wallet in wallets_data.values())
            users_with_debt = sum(1 for wallet in wallets_data.values() if wallet.get("has_debt", False))

            return {
                "total_wallets": total_wallets,
                "active_wallets": active_wallets,
                "inactive_wallets": total_wallets - active_wallets,
                "total_balance": total_balance,
                "verified_wallets": verified_wallets,
                "unverified_wallets": total_wallets - verified_wallets,
                "total_debt": total_debt,
                "users_with_debt": users_with_debt,
                "last_updated": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات المحافظ: {e}")
            return {}
    
    def deactivate_wallet(self, wallet_number: str, reason: str = "admin_action") -> bool:
        """إلغاء تفعيل المحفظة"""
        try:
            wallets_data = self.load_wallets_data()
            
            if wallet_number not in wallets_data:
                return False
            
            wallets_data[wallet_number]["status"] = "inactive"
            wallets_data[wallet_number]["deactivated_at"] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            wallets_data[wallet_number]["deactivation_reason"] = reason
            
            self.save_wallets_data(wallets_data)
            
            logger.info(f"🏦 تم إلغاء تفعيل المحفظة {wallet_number}: {reason}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إلغاء تفعيل المحفظة {wallet_number}: {e}")
            return False
    
    def activate_wallet(self, wallet_number: str) -> bool:
        """تفعيل المحفظة"""
        try:
            wallets_data = self.load_wallets_data()
            
            if wallet_number not in wallets_data:
                return False
            
            wallets_data[wallet_number]["status"] = "active"
            wallets_data[wallet_number]["reactivated_at"] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # إزالة معلومات الإلغاء
            if "deactivated_at" in wallets_data[wallet_number]:
                del wallets_data[wallet_number]["deactivated_at"]
            if "deactivation_reason" in wallets_data[wallet_number]:
                del wallets_data[wallet_number]["deactivation_reason"]
            
            self.save_wallets_data(wallets_data)
            
            logger.info(f"🏦 تم تفعيل المحفظة {wallet_number}")
            return True

        except Exception as e:
            logger.error(f"خطأ في تفعيل المحفظة {wallet_number}: {e}")
            return False

    def is_eligible_for_loan(self, user_id: int) -> Tuple[bool, str]:
        """
        فحص أهلية المستخدم للحصول على سلفة

        الشروط:
        1. المحفظة متحققة (لديها تاريخ معاملات سابقة)
        2. الرصيد الحالي = 0 إكسا
        3. لا يوجد سلف نشط

        Returns:
            Tuple[bool, str]: (مؤهل, رسالة التوضيح)
        """
        try:
            wallet_data = self.get_user_wallet(user_id)

            if not wallet_data:
                return False, "لا توجد محفظة للمستخدم"

            # فحص التحقق من المحفظة (وجود معاملات سابقة)
            if not wallet_data.get("is_verified", False):
                return False, "المحفظة غير متحققة - يجب أن تكون لديك معاملات سابقة في المحفظة"

            # فحص الرصيد الحالي (يجب أن يكون 0)
            current_balance = wallet_data.get("balance", 0.0)
            if current_balance != 0.0:
                return False, f"رصيدك الحالي {current_balance} إكسا - يجب أن يكون الرصيد 0 إكسا للحصول على سلفة"

            # فحص وجود سلف نشط
            if wallet_data.get("has_active_loan", False):
                loan_amount = wallet_data.get("loan_amount", 0.0)
                return False, f"لديك سلفة نشطة بقيمة {loan_amount} إكسا - يجب سداد السلفة الحالية أولاً"

            return True, "مؤهل للحصول على سلفة"

        except Exception as e:
            logger.error(f"خطأ في فحص أهلية السلف للمستخدم {user_id}: {e}")
            return False, f"خطأ في فحص الأهلية: {e}"

    def grant_loan(self, user_id: int, loan_amount: float = 3.0) -> Tuple[bool, str, Dict]:
        """
        منح سلفة للمستخدم

        Args:
            user_id: معرف المستخدم
            loan_amount: مبلغ السلفة (افتراضي 3 إكسا)

        Returns:
            Tuple[bool, str, Dict]: (نجح المنح, رسالة, بيانات المعاملة)
        """
        try:
            # فحص الأهلية
            eligible, message = self.is_eligible_for_loan(user_id)
            if not eligible:
                return False, message, {}

            wallet_data = self.get_user_wallet(user_id)
            if not wallet_data:
                return False, "لا توجد محفظة للمستخدم", {}

            wallet_number = wallet_data["wallet_number"]
            old_balance = wallet_data["balance"]
            new_balance = old_balance + loan_amount

            # تحديث بيانات المحفظة
            wallets_data = self.load_wallets_data()
            wallets_data[wallet_number]["balance"] = new_balance
            wallets_data[wallet_number]["loan_amount"] = loan_amount
            wallets_data[wallet_number]["has_active_loan"] = True
            wallets_data[wallet_number]["loan_granted_at"] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            wallets_data[wallet_number]["last_transaction"] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            wallets_data[wallet_number]["transaction_count"] += 1

            # إضافة السجل إلى تاريخ السلف
            loan_record = {
                "type": "loan_granted",
                "amount": loan_amount,
                "granted_at": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "old_balance": old_balance,
                "new_balance": new_balance
            }

            if "loan_history" not in wallets_data[wallet_number]:
                wallets_data[wallet_number]["loan_history"] = []
            wallets_data[wallet_number]["loan_history"].append(loan_record)

            # حفظ التغييرات
            self.save_wallets_data(wallets_data)

            logger.info(f"💳 تم منح سلفة {loan_amount} إكسا للمستخدم {user_id} - المحفظة {wallet_number}")

            # إعداد بيانات المعاملة للفاتورة
            transaction_data = {
                "wallet_number": wallet_number,
                "user_name": wallet_data.get("user_name", "غير محدد"),
                "amount": loan_amount,
                "old_balance": old_balance,
                "new_balance": new_balance,
                "transaction_type": "loan_granted",
                "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            return True, f"تم منح سلفة {loan_amount} إكسا بنجاح", transaction_data

        except Exception as e:
            logger.error(f"خطأ في منح السلفة للمستخدم {user_id}: {e}")
            return False, f"خطأ في منح السلفة: {e}", {}

    def process_loan_deduction_on_balance_add(self, wallet_number: str, added_amount: float) -> Tuple[bool, float, Dict]:
        """
        معالجة خصم السلفة تلقائياً عند إضافة رصيد

        Args:
            wallet_number: رقم المحفظة
            added_amount: المبلغ المضاف

        Returns:
            Tuple[bool, float, Dict]: (تم خصم سلفة, المبلغ المخصوم, بيانات الخصم)
        """
        try:
            wallets_data = self.load_wallets_data()

            if wallet_number not in wallets_data:
                return False, 0.0, {}

            wallet_data = wallets_data[wallet_number]

            # فحص وجود سلف نشط
            if not wallet_data.get("has_active_loan", False):
                return False, 0.0, {}

            loan_amount = wallet_data.get("loan_amount", 0.0)
            if loan_amount <= 0:
                return False, 0.0, {}

            # حساب المبلغ المخصوم (أقل من السلف أو المبلغ المضاف)
            deducted_amount = min(loan_amount, added_amount)
            remaining_loan = loan_amount - deducted_amount

            # تحديث بيانات السلف
            wallets_data[wallet_number]["loan_amount"] = remaining_loan

            # إذا تم سداد السلف بالكامل
            if remaining_loan <= 0:
                wallets_data[wallet_number]["has_active_loan"] = False
                wallets_data[wallet_number]["loan_amount"] = 0.0
                wallets_data[wallet_number]["loan_paid_at"] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # إضافة السجل إلى تاريخ السلف
            deduction_record = {
                "type": "loan_deduction",
                "deducted_amount": deducted_amount,
                "remaining_loan": remaining_loan,
                "deducted_at": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "added_amount": added_amount,
                "is_fully_paid": remaining_loan <= 0
            }

            if "loan_history" not in wallets_data[wallet_number]:
                wallets_data[wallet_number]["loan_history"] = []
            wallets_data[wallet_number]["loan_history"].append(deduction_record)

            # حفظ التغييرات
            self.save_wallets_data(wallets_data)

            logger.info(f"💳 تم خصم {deducted_amount} إكسا من السلف للمحفظة {wallet_number} - المتبقي: {remaining_loan}")

            # إعداد بيانات الخصم
            deduction_data = {
                "wallet_number": wallet_number,
                "user_name": wallet_data.get("user_name", "غير محدد"),
                "deducted_amount": deducted_amount,
                "remaining_loan": remaining_loan,
                "original_loan": loan_amount,
                "is_fully_paid": remaining_loan <= 0,
                "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            return True, deducted_amount, deduction_data

        except Exception as e:
            logger.error(f"خطأ في معالجة خصم السلف للمحفظة {wallet_number}: {e}")
            return False, 0.0, {}

    def get_loan_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات السلف"""
        try:
            wallets_data = self.load_wallets_data()

            total_active_loans = 0
            total_loan_amount = 0.0
            total_loans_granted = 0
            total_loans_paid = 0
            users_with_active_loans = []

            for wallet_number, wallet_data in wallets_data.items():
                # السلف النشطة
                if wallet_data.get("has_active_loan", False):
                    total_active_loans += 1
                    loan_amount = wallet_data.get("loan_amount", 0.0)
                    total_loan_amount += loan_amount

                    users_with_active_loans.append({
                        "wallet_number": wallet_number,
                        "user_name": wallet_data.get("user_name", "غير محدد"),
                        "loan_amount": loan_amount,
                        "granted_at": wallet_data.get("loan_granted_at", "غير محدد")
                    })

                # إحصائيات من تاريخ السلف
                loan_history = wallet_data.get("loan_history", [])
                for record in loan_history:
                    if record.get("type") == "loan_granted":
                        total_loans_granted += 1
                    elif record.get("type") == "loan_deduction" and record.get("is_fully_paid", False):
                        total_loans_paid += 1

            return {
                "total_active_loans": total_active_loans,
                "total_loan_amount": total_loan_amount,
                "total_loans_granted": total_loans_granted,
                "total_loans_paid": total_loans_paid,
                "users_with_active_loans": users_with_active_loans,
                "average_loan_amount": total_loan_amount / total_active_loans if total_active_loans > 0 else 0,
                "last_updated": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات السلف: {e}")
            return {}

    def add_loan_balance(self, wallet_number: str, loan_amount: float) -> Tuple[bool, str, Dict]:
        """
        إضافة رصيد سلفة للمحفظة

        Args:
            wallet_number: رقم المحفظة
            loan_amount: مبلغ السلفة المضاف

        Returns:
            Tuple[bool, str, Dict]: (نجح الإضافة, رسالة, بيانات المعاملة)
        """
        try:
            wallets_data = self.load_wallets_data()

            if wallet_number not in wallets_data:
                return False, "المحفظة غير موجودة", {}

            wallet_data = wallets_data[wallet_number]
            old_balance = wallet_data["balance"]
            old_loan_amount = wallet_data.get("loan_amount", 0.0)

            # إضافة مبلغ السلفة للرصيد
            new_balance = old_balance + loan_amount
            new_loan_amount = old_loan_amount + loan_amount

            # تحديث بيانات المحفظة
            wallets_data[wallet_number]["balance"] = new_balance
            wallets_data[wallet_number]["loan_amount"] = new_loan_amount
            wallets_data[wallet_number]["has_active_loan"] = True
            wallets_data[wallet_number]["last_transaction"] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            wallets_data[wallet_number]["transaction_count"] += 1

            # إضافة السجل إلى تاريخ السلف
            loan_record = {
                "type": "loan_balance_added",
                "amount": loan_amount,
                "old_balance": old_balance,
                "new_balance": new_balance,
                "old_loan_amount": old_loan_amount,
                "new_loan_amount": new_loan_amount,
                "added_at": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "added_by": "admin"
            }

            if "loan_history" not in wallets_data[wallet_number]:
                wallets_data[wallet_number]["loan_history"] = []
            wallets_data[wallet_number]["loan_history"].append(loan_record)

            # حفظ التغييرات
            self.save_wallets_data(wallets_data)

            logger.info(f"💳 تم إضافة رصيد سلفة {loan_amount} إكسا للمحفظة {wallet_number}")

            # إعداد بيانات المعاملة للفاتورة
            transaction_data = {
                "wallet_number": wallet_number,
                "user_name": wallet_data.get("user_name", "غير محدد"),
                "amount": loan_amount,
                "old_balance": old_balance,
                "new_balance": new_balance,
                "old_loan_amount": old_loan_amount,
                "new_loan_amount": new_loan_amount,
                "transaction_type": "loan_balance_added",
                "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            return True, f"تم إضافة رصيد سلفة {loan_amount} إكسا بنجاح", transaction_data

        except Exception as e:
            logger.error(f"خطأ في إضافة رصيد السلفة للمحفظة {wallet_number}: {e}")
            return False, f"خطأ في إضافة رصيد السلفة: {e}", {}

    def deduct_loan_balance(self, wallet_number: str, deduct_amount: float) -> Tuple[bool, str, Dict]:
        """
        خصم رصيد سلفة من المحفظة

        Args:
            wallet_number: رقم المحفظة
            deduct_amount: مبلغ السلفة المخصوم

        Returns:
            Tuple[bool, str, Dict]: (نجح الخصم, رسالة, بيانات المعاملة)
        """
        try:
            wallets_data = self.load_wallets_data()

            if wallet_number not in wallets_data:
                return False, "المحفظة غير موجودة", {}

            wallet_data = wallets_data[wallet_number]
            old_balance = wallet_data["balance"]
            old_loan_amount = wallet_data.get("loan_amount", 0.0)

            # التحقق من وجود رصيد كافي
            if old_balance < deduct_amount:
                return False, f"الرصيد غير كافي. الرصيد الحالي: {old_balance} إكسا", {}

            # خصم مبلغ السلفة من الرصيد
            new_balance = old_balance - deduct_amount
            new_loan_amount = max(0, old_loan_amount - deduct_amount)

            # تحديث بيانات المحفظة
            wallets_data[wallet_number]["balance"] = new_balance
            wallets_data[wallet_number]["loan_amount"] = new_loan_amount

            # إذا لم يعد هناك سلف نشط
            if new_loan_amount <= 0:
                wallets_data[wallet_number]["has_active_loan"] = False
                wallets_data[wallet_number]["loan_amount"] = 0.0
                wallets_data[wallet_number]["loan_paid_at"] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            wallets_data[wallet_number]["last_transaction"] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            wallets_data[wallet_number]["transaction_count"] += 1

            # إضافة السجل إلى تاريخ السلف
            loan_record = {
                "type": "loan_balance_deducted",
                "amount": deduct_amount,
                "old_balance": old_balance,
                "new_balance": new_balance,
                "old_loan_amount": old_loan_amount,
                "new_loan_amount": new_loan_amount,
                "deducted_at": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "deducted_by": "admin",
                "is_fully_paid": new_loan_amount <= 0
            }

            if "loan_history" not in wallets_data[wallet_number]:
                wallets_data[wallet_number]["loan_history"] = []
            wallets_data[wallet_number]["loan_history"].append(loan_record)

            # حفظ التغييرات
            self.save_wallets_data(wallets_data)

            logger.info(f"💳 تم خصم رصيد سلفة {deduct_amount} إكسا من المحفظة {wallet_number}")

            # إعداد بيانات المعاملة للفاتورة
            transaction_data = {
                "wallet_number": wallet_number,
                "user_name": wallet_data.get("user_name", "غير محدد"),
                "amount": deduct_amount,
                "old_balance": old_balance,
                "new_balance": new_balance,
                "old_loan_amount": old_loan_amount,
                "new_loan_amount": new_loan_amount,
                "transaction_type": "loan_balance_deducted",
                "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "is_fully_paid": new_loan_amount <= 0
            }

            return True, f"تم خصم رصيد سلفة {deduct_amount} إكسا بنجاح", transaction_data

        except Exception as e:
            logger.error(f"خطأ في خصم رصيد السلفة من المحفظة {wallet_number}: {e}")
            return False, f"خطأ في خصم رصيد السلفة: {e}", {}

    def get_user_loan_info(self, user_id: int) -> Dict[str, Any]:
        """الحصول على معلومات السلف للمستخدم"""
        try:
            # إعادة تحميل البيانات للحصول على أحدث المعلومات
            wallet_data = self.get_user_wallet(user_id)

            if not wallet_data:
                return {"error": "لا توجد محفظة للمستخدم"}

            loan_info = {
                "has_active_loan": wallet_data.get("has_active_loan", False),
                "loan_amount": wallet_data.get("loan_amount", 0.0),
                "loan_granted_at": wallet_data.get("loan_granted_at"),
                "loan_paid_at": wallet_data.get("loan_paid_at"),
                "loan_history": wallet_data.get("loan_history", []),
                "is_eligible": False,
                "eligibility_message": ""
            }

            # فحص الأهلية للسلف الجديد
            if not loan_info["has_active_loan"]:
                eligible, message = self.is_eligible_for_loan(user_id)
                loan_info["is_eligible"] = eligible
                loan_info["eligibility_message"] = message

            return loan_info

        except Exception as e:
            logger.error(f"خطأ في الحصول على معلومات السلف للمستخدم {user_id}: {e}")
            return {"error": f"خطأ: {e}"}

    def create_wallets_for_existing_users(self) -> Dict[str, Any]:
        """إنشاء محافظ للمستخدمين الموجودين الذين لا يملكون محافظ"""
        try:
            users_data = self.load_users_data()
            wallets_data = self.load_wallets_data()

            results = {
                "total_users": len(users_data),
                "users_with_wallets": 0,
                "users_without_wallets": 0,
                "wallets_created": 0,
                "failed_creations": 0,
                "created_wallets": []
            }

            # فحص كل مستخدم
            for user_id_str, user_info in users_data.items():
                user_id = int(user_id_str)

                # فحص ما إذا كان المستخدم لديه محفظة
                has_wallet = False
                for wallet_data in wallets_data.values():
                    if wallet_data.get("user_id") == user_id:
                        has_wallet = True
                        break

                if has_wallet:
                    results["users_with_wallets"] += 1
                else:
                    results["users_without_wallets"] += 1

                    # إنشاء محفظة للمستخدم
                    success, wallet_number, message = self.create_wallet(user_id, user_info)

                    if success:
                        results["wallets_created"] += 1
                        results["created_wallets"].append({
                            "user_id": user_id,
                            "user_name": user_info.get("اسم المستخدم", "غير محدد"),
                            "wallet_number": wallet_number
                        })
                        logger.info(f"🏦 تم إنشاء محفظة للمستخدم الموجود: {wallet_number} للمستخدم {user_id}")
                    else:
                        results["failed_creations"] += 1
                        logger.error(f"❌ فشل في إنشاء محفظة للمستخدم {user_id}: {message}")

            logger.info(f"🏦 تم إنشاء {results['wallets_created']} محفظة جديدة من أصل {results['users_without_wallets']} مستخدم بدون محفظة")
            return results

        except Exception as e:
            logger.error(f"خطأ في إنشاء محافظ للمستخدمين الموجودين: {e}")
            return {
                "error": str(e),
                "total_users": 0,
                "wallets_created": 0,
                "failed_creations": 0
            }

    def delete_user_wallet(self, user_id: int) -> Tuple[bool, str]:
        """حذف محفظة المستخدم نهائياً"""
        try:
            wallets_data = self.load_wallets_data()
            users_data = self.load_users_data()
            user_id_str = str(user_id)

            # البحث عن المحفظة
            wallet_to_delete = None
            wallet_number = None

            for wallet_num, wallet_info in wallets_data.items():
                if wallet_info.get("user_id") == user_id:
                    wallet_to_delete = wallet_num
                    wallet_number = wallet_num
                    break

            if wallet_to_delete:
                # حذف المحفظة من قاعدة بيانات المحافظ
                del wallets_data[wallet_to_delete]
                self.save_wallets_data(wallets_data)

                # إزالة معلومات المحفظة من بيانات المستخدم
                if user_id_str in users_data:
                    if "رقم المحفظة" in users_data[user_id_str]:
                        del users_data[user_id_str]["رقم المحفظة"]
                    if "حالة المحفظة" in users_data[user_id_str]:
                        del users_data[user_id_str]["حالة المحفظة"]
                    if "تاريخ إنشاء المحفظة" in users_data[user_id_str]:
                        del users_data[user_id_str]["تاريخ إنشاء المحفظة"]

                    self.save_users_data(users_data)

                logger.info(f"🗑️ تم حذف المحفظة {wallet_number} للمستخدم {user_id}")
                return True, f"تم حذف المحفظة {wallet_number} بنجاح"
            else:
                logger.info(f"🔍 لم يتم العثور على محفظة للمستخدم {user_id}")
                return True, "لم يتم العثور على محفظة للمستخدم"

        except Exception as e:
            logger.error(f"خطأ في حذف محفظة المستخدم {user_id}: {e}")
            return False, f"خطأ في حذف المحفظة: {e}"

    # ==================== نظام الدين للذكاء الاصطناعي ====================

    def can_use_ai_pro(self, user_id: int) -> Tuple[bool, str]:
        """
        فحص إمكانية استخدام الذكاء الاصطناعي المتقدم

        Args:
            user_id: معرف المستخدم

        Returns:
            Tuple[bool, str]: (يمكن الاستخدام, رسالة)
        """
        try:
            wallet_data = self.get_user_wallet(user_id)
            if not wallet_data:
                return False, "لا توجد محفظة للمستخدم"

            # فحص وجود دين يمنع الاستخدام
            if wallet_data.get("has_debt", False):
                debt_amount = wallet_data.get("debt_amount", 0.0)
                return False, f"تنبيه: لديك دين قدره {debt_amount:.3f} إكسا. يرجى سداد الدين أولاً لاستخدام إكسا الذكي برو."

            return True, "يمكن الاستخدام"

        except Exception as e:
            logger.error(f"خطأ في فحص إمكانية استخدام الذكاء الاصطناعي للمستخدم {user_id}: {e}")
            return False, f"خطأ في الفحص: {e}"

    def add_debt(self, user_id: int, debt_amount: float, description: str = "استخدام إكسا الذكي برو") -> Tuple[bool, str, Dict]:
        """
        إضافة دين للمستخدم (عند استخدام الذكاء الاصطناعي بدون رصيد كافي)

        Args:
            user_id: معرف المستخدم
            debt_amount: مبلغ الدين
            description: وصف سبب الدين

        Returns:
            Tuple[bool, str, Dict]: (نجح الإضافة, رسالة, بيانات المعاملة)
        """
        try:
            wallet_data = self.get_user_wallet(user_id)
            if not wallet_data:
                return False, "لا توجد محفظة للمستخدم", {}

            wallet_number = wallet_data["wallet_number"]
            wallets_data = self.load_wallets_data()

            current_debt = wallet_data.get("debt_amount", 0.0)
            debt_limit = wallet_data.get("debt_limit", 1.0)

            # فحص الحد الأقصى للدين
            if current_debt + debt_amount > debt_limit:
                return False, f"تجاوز الحد الأقصى للدين ({debt_limit} إكسا)", {}

            # تحديث بيانات الدين
            new_debt_amount = current_debt + debt_amount
            wallets_data[wallet_number]["debt_amount"] = new_debt_amount
            wallets_data[wallet_number]["has_debt"] = True

            if not wallet_data.get("debt_created_at"):
                wallets_data[wallet_number]["debt_created_at"] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # إضافة سجل الدين
            debt_record = {
                "type": "debt_added",
                "amount": debt_amount,
                "description": description,
                "old_debt": current_debt,
                "new_debt": new_debt_amount,
                "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            if "debt_history" not in wallets_data[wallet_number]:
                wallets_data[wallet_number]["debt_history"] = []
            wallets_data[wallet_number]["debt_history"].append(debt_record)

            # تحديث عداد المعاملات
            wallets_data[wallet_number]["transaction_count"] += 1
            wallets_data[wallet_number]["last_transaction"] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # حفظ التغييرات
            self.save_wallets_data(wallets_data)

            logger.info(f"💳 تم إضافة دين {debt_amount} إكسا للمستخدم {user_id} - المحفظة {wallet_number}")

            # إعداد بيانات المعاملة
            transaction_data = {
                "wallet_number": wallet_number,
                "user_name": wallet_data.get("user_name", "غير محدد"),
                "debt_amount": debt_amount,
                "old_debt": current_debt,
                "new_debt": new_debt_amount,
                "description": description,
                "transaction_type": "debt_added",
                "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            return True, f"تم إضافة دين {debt_amount:.3f} إكسا", transaction_data

        except Exception as e:
            logger.error(f"خطأ في إضافة الدين للمستخدم {user_id}: {e}")
            return False, f"خطأ في إضافة الدين: {e}", {}

    def process_debt_payment_on_balance_add(self, wallet_number: str, added_amount: float) -> Tuple[bool, float, Dict]:
        """
        معالجة سداد الدين تلقائياً عند إضافة رصيد

        Args:
            wallet_number: رقم المحفظة
            added_amount: المبلغ المضاف

        Returns:
            Tuple[bool, float, Dict]: (تم سداد دين, المبلغ المسدد, بيانات السداد)
        """
        try:
            wallets_data = self.load_wallets_data()

            if wallet_number not in wallets_data:
                return False, 0.0, {}

            wallet_data = wallets_data[wallet_number]

            # فحص وجود دين نشط
            if not wallet_data.get("has_debt", False):
                return False, 0.0, {}

            debt_amount = wallet_data.get("debt_amount", 0.0)
            if debt_amount <= 0:
                return False, 0.0, {}

            # حساب المبلغ المسدد (كامل الرصيد المضاف يذهب لسداد الدين)
            payment_amount = min(added_amount, debt_amount)
            remaining_debt = debt_amount - payment_amount

            # تحديث بيانات الدين
            wallets_data[wallet_number]["debt_amount"] = remaining_debt

            if remaining_debt <= 0:
                wallets_data[wallet_number]["has_debt"] = False
                wallets_data[wallet_number]["debt_amount"] = 0.0

            # إضافة سجل السداد
            payment_record = {
                "type": "debt_payment",
                "payment_amount": payment_amount,
                "old_debt": debt_amount,
                "remaining_debt": remaining_debt,
                "is_fully_paid": remaining_debt <= 0,
                "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            if "debt_history" not in wallets_data[wallet_number]:
                wallets_data[wallet_number]["debt_history"] = []
            wallets_data[wallet_number]["debt_history"].append(payment_record)

            # حفظ التغييرات
            self.save_wallets_data(wallets_data)

            logger.info(f"💳 تم سداد {payment_amount} إكسا من الدين للمحفظة {wallet_number} - المتبقي: {remaining_debt}")

            # إعداد بيانات السداد
            payment_data = {
                "wallet_number": wallet_number,
                "user_name": wallet_data.get("user_name", "غير محدد"),
                "payment_amount": payment_amount,
                "old_debt": debt_amount,
                "remaining_debt": remaining_debt,
                "is_fully_paid": remaining_debt <= 0,
                "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            return True, payment_amount, payment_data

        except Exception as e:
            logger.error(f"خطأ في معالجة سداد الدين للمحفظة {wallet_number}: {e}")
            return False, 0.0, {}

    def get_user_debt_info(self, user_id: int) -> Dict[str, Any]:
        """
        الحصول على معلومات دين المستخدم

        Args:
            user_id: معرف المستخدم

        Returns:
            dict: معلومات الدين
        """
        try:
            wallet_data = self.get_user_wallet(user_id)
            if not wallet_data:
                return {"error": "لا توجد محفظة للمستخدم"}

            debt_info = {
                "has_debt": wallet_data.get("has_debt", False),
                "debt_amount": wallet_data.get("debt_amount", 0.0),
                "debt_limit": wallet_data.get("debt_limit", 1.0),
                "debt_created_at": wallet_data.get("debt_created_at"),
                "debt_history": wallet_data.get("debt_history", []),
                "available_debt_limit": wallet_data.get("debt_limit", 1.0) - wallet_data.get("debt_amount", 0.0)
            }

            return debt_info

        except Exception as e:
            logger.error(f"خطأ في الحصول على معلومات الدين للمستخدم {user_id}: {e}")
            return {"error": f"خطأ: {e}"}

    def get_debt_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الديون"""
        try:
            wallets_data = self.load_wallets_data()

            total_users_with_debt = 0
            total_debt_amount = 0.0
            users_with_debt = []

            for wallet_number, wallet_data in wallets_data.items():
                if wallet_data.get("has_debt", False):
                    debt_amount = wallet_data.get("debt_amount", 0.0)
                    if debt_amount > 0:
                        total_users_with_debt += 1
                        total_debt_amount += debt_amount

                        users_with_debt.append({
                            "wallet_number": wallet_number,
                            "user_name": wallet_data.get("user_name", "غير محدد"),
                            "debt_amount": debt_amount,
                            "debt_created_at": wallet_data.get("debt_created_at", "غير محدد")
                        })

            return {
                "total_users_with_debt": total_users_with_debt,
                "total_debt_amount": total_debt_amount,
                "users_with_debt": users_with_debt,
                "average_debt_amount": total_debt_amount / total_users_with_debt if total_users_with_debt > 0 else 0,
                "last_updated": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات الديون: {e}")
            return {}
