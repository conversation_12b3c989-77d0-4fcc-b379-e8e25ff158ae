#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مخطط قاعدة البيانات MySQL
يحتوي على جميع استعلامات إنشاء الجداول والفهارس
"""

from typing import Dict, List

# استعلامات إنشاء الجداول
CREATE_TABLES_SQL = {
    
    # جدول المستخدمين
    "users": """
    CREATE TABLE IF NOT EXISTS users (
        id BIGINT UNSIGNED PRIMARY KEY,
        username VA<PERSON>HA<PERSON>(255),
        first_name <PERSON><PERSON><PERSON><PERSON>(255),
        last_name <PERSON><PERSON><PERSON><PERSON>(255),
        language_code VARCHAR(10) DEFAULT 'ar',
        registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        first_login TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        visit_count INT UNSIGNED DEFAULT 0,
        wallet_number VARCHAR(20) UNIQUE,
        wallet_status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
        is_banned BOOLEAN DEFAULT FALSE,
        is_restricted BOOLEAN DEFAULT FALSE,
        user_data JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_username (username),
        INDEX idx_wallet_number (wallet_number),
        INDEX idx_last_activity (last_activity),
        INDEX idx_registration_date (registration_date)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    """,
    
    # جدول المحافظ
    "wallets": """
    CREATE TABLE IF NOT EXISTS wallets (
        wallet_number VARCHAR(20) PRIMARY KEY,
        user_id BIGINT UNSIGNED NOT NULL,
        user_name VARCHAR(255),
        username VARCHAR(255),
        balance DECIMAL(15,8) DEFAULT 0.00000000,
        currency VARCHAR(10) DEFAULT 'إكسا',
        status ENUM('active', 'inactive', 'suspended', 'deleted') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_transaction TIMESTAMP NULL,
        transaction_count INT UNSIGNED DEFAULT 0,
        is_verified BOOLEAN DEFAULT FALSE,
        security_level ENUM('basic', 'medium', 'high') DEFAULT 'basic',
        verified_at TIMESTAMP NULL,
        loan_amount DECIMAL(15,8) DEFAULT 0.00000000,
        has_active_loan BOOLEAN DEFAULT FALSE,
        loan_granted_at TIMESTAMP NULL,
        debt_amount DECIMAL(15,8) DEFAULT 0.00000000,
        has_debt BOOLEAN DEFAULT FALSE,
        debt_limit DECIMAL(15,8) DEFAULT 1.00000000,
        debt_created_at TIMESTAMP NULL,
        wallet_data JSON,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_user_id (user_id),
        INDEX idx_balance (balance),
        INDEX idx_status (status),
        INDEX idx_created_at (created_at),
        INDEX idx_last_transaction (last_transaction)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    """,
    
    # جدول المعاملات
    "transactions": """
    CREATE TABLE IF NOT EXISTS transactions (
        id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        wallet_number VARCHAR(20) NOT NULL,
        user_id BIGINT UNSIGNED NOT NULL,
        transaction_type ENUM('deposit', 'withdrawal', 'transfer', 'loan', 'payment', 'fee') NOT NULL,
        amount DECIMAL(15,8) NOT NULL,
        balance_before DECIMAL(15,8) NOT NULL,
        balance_after DECIMAL(15,8) NOT NULL,
        description TEXT,
        reference_id VARCHAR(100),
        status ENUM('pending', 'completed', 'failed', 'cancelled') DEFAULT 'completed',
        transaction_data JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        processed_at TIMESTAMP NULL,
        
        FOREIGN KEY (wallet_number) REFERENCES wallets(wallet_number) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_wallet_number (wallet_number),
        INDEX idx_user_id (user_id),
        INDEX idx_transaction_type (transaction_type),
        INDEX idx_created_at (created_at),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    """,
    
    # جدول بيانات الإدارة
    "admin_data": """
    CREATE TABLE IF NOT EXISTS admin_data (
        id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        data_key VARCHAR(100) UNIQUE NOT NULL,
        data_value JSON,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_data_key (data_key)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    """,
    
    # جدول المستخدمين المحظورين
    "banned_users": """
    CREATE TABLE IF NOT EXISTS banned_users (
        user_id BIGINT UNSIGNED PRIMARY KEY,
        username VARCHAR(255),
        ban_reason TEXT,
        banned_by BIGINT UNSIGNED,
        ban_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        ban_expires TIMESTAMP NULL,
        is_permanent BOOLEAN DEFAULT FALSE,
        ban_data JSON,
        
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_ban_date (ban_date),
        INDEX idx_ban_expires (ban_expires)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    """,
    
    # جدول المستخدمين المقيدين
    "restricted_users": """
    CREATE TABLE IF NOT EXISTS restricted_users (
        user_id BIGINT UNSIGNED PRIMARY KEY,
        username VARCHAR(255),
        restriction_type ENUM('partial', 'limited', 'temporary') DEFAULT 'partial',
        restriction_reason TEXT,
        restricted_by BIGINT UNSIGNED,
        restriction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        restriction_expires TIMESTAMP NULL,
        restrictions JSON,
        
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_restriction_date (restriction_date),
        INDEX idx_restriction_expires (restriction_expires)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    """,
    
    # جدول بيانات المراقبة
    "monitoring_data": """
    CREATE TABLE IF NOT EXISTS monitoring_data (
        id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        metric_name VARCHAR(100) NOT NULL,
        metric_value JSON,
        recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        INDEX idx_metric_name (metric_name),
        INDEX idx_recorded_at (recorded_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    """,
    
    # جدول إحصائيات البوت
    "bot_stats": """
    CREATE TABLE IF NOT EXISTS bot_stats (
        id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        stat_name VARCHAR(100) NOT NULL,
        stat_value JSON,
        date_recorded DATE NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        UNIQUE KEY unique_stat_date (stat_name, date_recorded),
        INDEX idx_stat_name (stat_name),
        INDEX idx_date_recorded (date_recorded)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    """,
    
    # جدول إعدادات نماذج الذكاء الاصطناعي
    "ai_models_config": """
    CREATE TABLE IF NOT EXISTS ai_models_config (
        model_name VARCHAR(100) PRIMARY KEY,
        status ENUM('active', 'inactive', 'maintenance') DEFAULT 'inactive',
        priority INT UNSIGNED DEFAULT 1,
        max_tokens INT UNSIGNED DEFAULT 1000,
        temperature DECIMAL(3,2) DEFAULT 0.70,
        timeout INT UNSIGNED DEFAULT 30,
        fallback_enabled BOOLEAN DEFAULT TRUE,
        model_config JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_status (status),
        INDEX idx_priority (priority)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    """,
    
    # جدول جلسات المستخدمين
    "user_sessions": """
    CREATE TABLE IF NOT EXISTS user_sessions (
        session_id VARCHAR(100) PRIMARY KEY,
        user_id BIGINT UNSIGNED NOT NULL,
        session_data JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        expires_at TIMESTAMP NOT NULL,
        last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        is_active BOOLEAN DEFAULT TRUE,
        
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_user_id (user_id),
        INDEX idx_expires_at (expires_at),
        INDEX idx_last_activity (last_activity)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    """,
    
    # جدول سجلات النظام
    "system_logs": """
    CREATE TABLE IF NOT EXISTS system_logs (
        id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        log_level ENUM('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL') NOT NULL,
        logger_name VARCHAR(100),
        message TEXT NOT NULL,
        user_id BIGINT UNSIGNED NULL,
        module_name VARCHAR(100),
        function_name VARCHAR(100),
        line_number INT UNSIGNED,
        log_data JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        INDEX idx_log_level (log_level),
        INDEX idx_created_at (created_at),
        INDEX idx_user_id (user_id),
        INDEX idx_module_name (module_name)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    """,
    
    # جدول تاريخ النسخ الاحتياطية
    "backup_history": """
    CREATE TABLE IF NOT EXISTS backup_history (
        id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        backup_name VARCHAR(255) NOT NULL,
        backup_type ENUM('full', 'incremental', 'differential') DEFAULT 'full',
        backup_size BIGINT UNSIGNED,
        backup_path VARCHAR(500),
        status ENUM('started', 'completed', 'failed', 'cancelled') DEFAULT 'started',
        error_message TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        completed_at TIMESTAMP NULL,
        
        INDEX idx_backup_type (backup_type),
        INDEX idx_status (status),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    """,
    
    # جدول سجل المراجعة
    "audit_log": """
    CREATE TABLE IF NOT EXISTS audit_log (
        id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        user_id BIGINT UNSIGNED NULL,
        action VARCHAR(100) NOT NULL,
        table_name VARCHAR(100),
        record_id VARCHAR(100),
        old_values JSON,
        new_values JSON,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        INDEX idx_user_id (user_id),
        INDEX idx_action (action),
        INDEX idx_table_name (table_name),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    """
}

# استعلامات إنشاء الفهارس الإضافية
CREATE_INDEXES_SQL = [
    "CREATE INDEX idx_users_wallet_status ON users(wallet_status);",
    "CREATE INDEX idx_wallets_balance_status ON wallets(balance, status);",
    "CREATE INDEX idx_transactions_amount ON transactions(amount);",
    "CREATE INDEX idx_transactions_date_type ON transactions(created_at, transaction_type);",
    "CREATE INDEX idx_system_logs_level_date ON system_logs(log_level, created_at);",
]

# استعلامات إنشاء المشاهدات (Views)
CREATE_VIEWS_SQL = {
    "active_users_view": """
    CREATE OR REPLACE VIEW active_users_view AS
    SELECT 
        u.id,
        u.username,
        u.first_name,
        u.last_name,
        u.wallet_number,
        w.balance,
        u.last_activity,
        u.visit_count
    FROM users u
    LEFT JOIN wallets w ON u.wallet_number = w.wallet_number
    WHERE u.wallet_status = 'active' AND u.is_banned = FALSE;
    """,
    
    "wallet_summary_view": """
    CREATE OR REPLACE VIEW wallet_summary_view AS
    SELECT 
        w.wallet_number,
        w.user_id,
        u.username,
        w.balance,
        w.transaction_count,
        w.loan_amount,
        w.debt_amount,
        w.status,
        w.last_transaction
    FROM wallets w
    JOIN users u ON w.user_id = u.id
    WHERE w.status = 'active';
    """
}

def get_table_creation_order() -> List[str]:
    """ترتيب إنشاء الجداول حسب التبعيات"""
    return [
        "users",
        "wallets", 
        "transactions",
        "admin_data",
        "banned_users",
        "restricted_users",
        "monitoring_data",
        "bot_stats",
        "ai_models_config",
        "user_sessions",
        "system_logs",
        "backup_history",
        "audit_log"
    ]
