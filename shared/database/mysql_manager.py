#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات MySQL
يوفر طبقة وصول موحدة لقاعدة البيانات مع دعم العمليات المتزامنة
"""

import asyncio
import json
import logging
import threading
from contextlib import contextmanager
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple, Union
from concurrent.futures import ThreadPoolExecutor

import mysql.connector
from mysql.connector import pooling, Error
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool

from .mysql_config import (
    DATABASE_CONFIG, 
    SQLALCHEMY_CONFIG, 
    get_database_url, 
    get_connection_params,
    PERFORMANCE_CONFIG
)
from .mysql_schema import CREATE_TABLES_SQL, get_table_creation_order

# إعداد نظام السجلات
logger = logging.getLogger(__name__)

class MySQLManager:
    """مدير قاعدة البيانات MySQL مع دعم العمليات المتزامنة"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """تطبيق نمط Singleton"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(MySQLManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        """تهيئة مدير قاعدة البيانات"""
        if hasattr(self, '_initialized'):
            return
            
        self._initialized = True
        self.connection_pool = None
        self.engine = None
        self.session_factory = None
        self.executor = ThreadPoolExecutor(max_workers=10)
        self._setup_database()
        
        logger.info("🗄️ تم تهيئة مدير قاعدة البيانات MySQL")
    
    def _setup_database(self):
        """إعداد قاعدة البيانات والاتصالات"""
        try:
            # إنشاء مجموعة الاتصالات
            self._create_connection_pool()
            
            # إنشاء محرك SQLAlchemy
            self._create_sqlalchemy_engine()
            
            # إنشاء الجداول
            self._create_tables()
            
            logger.info("✅ تم إعداد قاعدة البيانات بنجاح")
            
        except Exception as e:
            logger.error(f"❌ خطأ في إعداد قاعدة البيانات: {e}")
            raise
    
    def _create_connection_pool(self):
        """إنشاء مجموعة اتصالات قاعدة البيانات"""
        try:
            pool_config = get_connection_params()
            pool_config.update({
                'pool_name': 'bot_pool',
                'pool_size': PERFORMANCE_CONFIG['max_connections'] // 2,
                'pool_reset_session': True,
                'autocommit': False,
            })
            
            self.connection_pool = pooling.MySQLConnectionPool(**pool_config)
            logger.info("🔗 تم إنشاء مجموعة اتصالات قاعدة البيانات")
            
        except Error as e:
            logger.error(f"❌ خطأ في إنشاء مجموعة الاتصالات: {e}")
            raise
    
    def _create_sqlalchemy_engine(self):
        """إنشاء محرك SQLAlchemy"""
        try:
            database_url = get_database_url()
            
            self.engine = create_engine(
                database_url,
                poolclass=QueuePool,
                pool_size=SQLALCHEMY_CONFIG['pool_size'],
                max_overflow=SQLALCHEMY_CONFIG['max_overflow'],
                pool_pre_ping=SQLALCHEMY_CONFIG['pool_pre_ping'],
                pool_recycle=SQLALCHEMY_CONFIG['pool_recycle'],
                echo=SQLALCHEMY_CONFIG['echo']
            )
            
            self.session_factory = sessionmaker(bind=self.engine)
            logger.info("⚙️ تم إنشاء محرك SQLAlchemy")
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء محرك SQLAlchemy: {e}")
            raise
    
    def _create_tables(self):
        """إنشاء جداول قاعدة البيانات"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # إنشاء الجداول بالترتيب الصحيح
                for table_name in get_table_creation_order():
                    if table_name in CREATE_TABLES_SQL:
                        cursor.execute(CREATE_TABLES_SQL[table_name])
                        logger.debug(f"✅ تم إنشاء جدول: {table_name}")
                
                conn.commit()
                logger.info("📊 تم إنشاء جميع الجداول بنجاح")
                
        except Error as e:
            logger.error(f"❌ خطأ في إنشاء الجداول: {e}")
            raise
    
    @contextmanager
    def get_connection(self):
        """الحصول على اتصال من مجموعة الاتصالات"""
        connection = None
        try:
            connection = self.connection_pool.get_connection()
            yield connection
        except Error as e:
            if connection:
                connection.rollback()
            logger.error(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
            raise
        finally:
            if connection and connection.is_connected():
                connection.close()
    
    @contextmanager
    def get_session(self) -> Session:
        """الحصول على جلسة SQLAlchemy"""
        session = self.session_factory()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"❌ خطأ في جلسة قاعدة البيانات: {e}")
            raise
        finally:
            session.close()
    
    def execute_query(self, query: str, params: Optional[Dict] = None) -> List[Dict]:
        """تنفيذ استعلام SELECT وإرجاع النتائج"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor(dictionary=True)
                cursor.execute(query, params or {})
                results = cursor.fetchall()
                return results
                
        except Error as e:
            logger.error(f"❌ خطأ في تنفيذ الاستعلام: {e}")
            raise
    
    def execute_update(self, query: str, params: Optional[Dict] = None) -> int:
        """تنفيذ استعلام UPDATE/INSERT/DELETE وإرجاع عدد الصفوف المتأثرة"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params or {})
                conn.commit()
                return cursor.rowcount
                
        except Error as e:
            logger.error(f"❌ خطأ في تنفيذ التحديث: {e}")
            raise
    
    def execute_batch(self, query: str, params_list: List[Dict]) -> int:
        """تنفيذ استعلامات متعددة في دفعة واحدة"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.executemany(query, params_list)
                conn.commit()
                return cursor.rowcount
                
        except Error as e:
            logger.error(f"❌ خطأ في تنفيذ الدفعة: {e}")
            raise
    
    async def execute_query_async(self, query: str, params: Optional[Dict] = None) -> List[Dict]:
        """تنفيذ استعلام بشكل غير متزامن"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self.executor, 
            self.execute_query, 
            query, 
            params
        )
    
    async def execute_update_async(self, query: str, params: Optional[Dict] = None) -> int:
        """تنفيذ تحديث بشكل غير متزامن"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self.executor, 
            self.execute_update, 
            query, 
            params
        )
    
    def transaction(self, operations: List[Tuple[str, Dict]]) -> bool:
        """تنفيذ عدة عمليات في معاملة واحدة"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                for query, params in operations:
                    cursor.execute(query, params)
                
                conn.commit()
                return True
                
        except Error as e:
            logger.error(f"❌ خطأ في المعاملة: {e}")
            return False
    
    def backup_table(self, table_name: str, backup_path: str) -> bool:
        """إنشاء نسخة احتياطية من جدول"""
        try:
            query = f"SELECT * FROM {table_name}"
            results = self.execute_query(query)
            
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2, default=str)
            
            logger.info(f"💾 تم إنشاء نسخة احتياطية من جدول {table_name}")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في النسخ الاحتياطي للجدول {table_name}: {e}")
            return False
    
    def get_table_info(self, table_name: str) -> Dict:
        """الحصول على معلومات الجدول"""
        try:
            # الحصول على عدد الصفوف
            count_query = f"SELECT COUNT(*) as count FROM {table_name}"
            count_result = self.execute_query(count_query)
            row_count = count_result[0]['count'] if count_result else 0
            
            # الحصول على معلومات الأعمدة
            columns_query = f"DESCRIBE {table_name}"
            columns = self.execute_query(columns_query)
            
            return {
                'table_name': table_name,
                'row_count': row_count,
                'columns': columns,
                'last_updated': datetime.now().isoformat()
            }
            
        except Error as e:
            logger.error(f"❌ خطأ في الحصول على معلومات الجدول {table_name}: {e}")
            return {}
    
    def health_check(self) -> Dict[str, Any]:
        """فحص صحة قاعدة البيانات"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                
                return {
                    'status': 'healthy' if result else 'unhealthy',
                    'connection_pool_size': self.connection_pool.pool_size,
                    'timestamp': datetime.now().isoformat()
                }
                
        except Error as e:
            logger.error(f"❌ خطأ في فحص صحة قاعدة البيانات: {e}")
            return {
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def close(self):
        """إغلاق جميع الاتصالات"""
        try:
            if self.executor:
                self.executor.shutdown(wait=True)
            
            if self.engine:
                self.engine.dispose()
            
            logger.info("🔒 تم إغلاق جميع اتصالات قاعدة البيانات")
            
        except Exception as e:
            logger.error(f"❌ خطأ في إغلاق الاتصالات: {e}")

# إنشاء مثيل مشترك
mysql_manager = MySQLManager()
