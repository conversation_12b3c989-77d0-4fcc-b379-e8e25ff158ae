#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام النسخ الاحتياطي والاستعادة لقاعدة البيانات SQLite
يدعم النسخ التلقائي والمجدول مع التشفير والضغط
"""

import os
import sqlite3
import gzip
import shutil
import threading
import schedule
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from cryptography.fernet import Fernet
import json
from .sqlite_manager import SQLiteManager

# إعداد نظام السجلات
logger = logging.getLogger(__name__)

class BackupSystem:
    """نظام النسخ الاحتياطي والاستعادة"""
    
    def __init__(self, db_manager: SQLiteManager = None, backup_dir: str = None):
        """تهيئة نظام النسخ الاحتياطي"""
        self.db_manager = db_manager or SQLiteManager()
        self.backup_dir = backup_dir or os.path.join(os.path.dirname(__file__), '..', '..', 'backups', 'database')
        self.config_file = os.path.join(self.backup_dir, 'backup_config.json')
        
        # إنشاء مجلد النسخ الاحتياطية
        os.makedirs(self.backup_dir, exist_ok=True)
        
        # إعدادات النسخ الاحتياطي
        self.config = self.load_backup_config()
        
        # مفتاح التشفير
        self.encryption_key = self._get_or_create_encryption_key()
        
        # خيط المجدولة
        self.scheduler_thread = None
        self.scheduler_running = False
        
        logger.info(f"🗄️ تم تهيئة نظام النسخ الاحتياطي: {self.backup_dir}")
    
    def load_backup_config(self) -> Dict[str, Any]:
        """تحميل إعدادات النسخ الاحتياطي"""
        default_config = {
            "auto_backup": {
                "enabled": True,
                "daily": {"enabled": True, "time": "02:00"},
                "weekly": {"enabled": True, "day": "sunday", "time": "03:00"},
                "monthly": {"enabled": True, "day": 1, "time": "04:00"}
            },
            "retention": {
                "daily_backups": 7,
                "weekly_backups": 4,
                "monthly_backups": 12
            },
            "compression": True,
            "encryption": True,
            "max_backup_size_mb": 100,
            "last_backup": {
                "daily": None,
                "weekly": None,
                "monthly": None
            }
        }
        
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # دمج الإعدادات الافتراضية مع الموجودة
                for key, value in default_config.items():
                    if key not in config:
                        config[key] = value
                return config
            else:
                self.save_backup_config(default_config)
                return default_config
        except Exception as e:
            logger.error(f"❌ خطأ في تحميل إعدادات النسخ الاحتياطي: {e}")
            return default_config
    
    def save_backup_config(self, config: Dict[str, Any] = None):
        """حفظ إعدادات النسخ الاحتياطي"""
        try:
            config_to_save = config or self.config
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_to_save, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"❌ خطأ في حفظ إعدادات النسخ الاحتياطي: {e}")
    
    def _get_or_create_encryption_key(self) -> bytes:
        """الحصول على مفتاح التشفير أو إنشاؤه"""
        key_file = os.path.join(self.backup_dir, '.backup_key')
        
        try:
            if os.path.exists(key_file):
                with open(key_file, 'rb') as f:
                    return f.read()
            else:
                key = Fernet.generate_key()
                with open(key_file, 'wb') as f:
                    f.write(key)
                # إخفاء الملف في Windows
                if os.name == 'nt':
                    os.system(f'attrib +h "{key_file}"')
                return key
        except Exception as e:
            logger.error(f"❌ خطأ في إدارة مفتاح التشفير: {e}")
            return Fernet.generate_key()
    
    def create_backup(self, backup_type: str = "manual", compress: bool = None, encrypt: bool = None) -> str:
        """إنشاء نسخة احتياطية"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = f"backup_{backup_type}_{timestamp}.db"
            backup_path = os.path.join(self.backup_dir, backup_name)
            
            # إنشاء النسخة الاحتياطية
            self.db_manager.backup_database(backup_path)
            
            # الضغط إذا كان مفعلاً
            if compress or (compress is None and self.config.get('compression', True)):
                compressed_path = backup_path + '.gz'
                with open(backup_path, 'rb') as f_in:
                    with gzip.open(compressed_path, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
                os.remove(backup_path)
                backup_path = compressed_path
                backup_name += '.gz'
            
            # التشفير إذا كان مفعلاً
            if encrypt or (encrypt is None and self.config.get('encryption', True)):
                encrypted_path = backup_path + '.enc'
                fernet = Fernet(self.encryption_key)
                
                with open(backup_path, 'rb') as f_in:
                    encrypted_data = fernet.encrypt(f_in.read())
                
                with open(encrypted_path, 'wb') as f_out:
                    f_out.write(encrypted_data)
                
                os.remove(backup_path)
                backup_path = encrypted_path
                backup_name += '.enc'
            
            # تحديث إعدادات آخر نسخة احتياطية
            self.config['last_backup'][backup_type] = datetime.now().isoformat()
            self.save_backup_config()
            
            # تسجيل معلومات النسخة الاحتياطية
            backup_info = {
                'name': backup_name,
                'path': backup_path,
                'type': backup_type,
                'size': os.path.getsize(backup_path),
                'compressed': compress or (compress is None and self.config.get('compression', True)),
                'encrypted': encrypt or (encrypt is None and self.config.get('encryption', True)),
                'created_at': datetime.now().isoformat()
            }
            
            self._log_backup_info(backup_info)
            
            logger.info(f"✅ تم إنشاء نسخة احتياطية: {backup_name}")
            logger.info(f"📁 المسار: {backup_path}")
            logger.info(f"📊 الحجم: {backup_info['size'] / 1024 / 1024:.2f} MB")
            
            return backup_path
            
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
            raise
    
    def restore_backup(self, backup_path: str, confirm: bool = False) -> bool:
        """استعادة نسخة احتياطية"""
        if not confirm:
            logger.warning("⚠️ استعادة النسخة الاحتياطية تتطلب تأكيد صريح")
            return False
        
        try:
            if not os.path.exists(backup_path):
                raise FileNotFoundError(f"النسخة الاحتياطية غير موجودة: {backup_path}")
            
            # إنشاء نسخة احتياطية من قاعدة البيانات الحالية قبل الاستعادة
            current_backup = self.create_backup("pre_restore")
            logger.info(f"📄 تم إنشاء نسخة احتياطية من الحالة الحالية: {current_backup}")
            
            temp_path = backup_path
            
            # فك التشفير إذا كان مشفراً
            if backup_path.endswith('.enc'):
                fernet = Fernet(self.encryption_key)
                decrypted_path = backup_path.replace('.enc', '')
                
                with open(backup_path, 'rb') as f_in:
                    encrypted_data = f_in.read()
                    decrypted_data = fernet.decrypt(encrypted_data)
                
                with open(decrypted_path, 'wb') as f_out:
                    f_out.write(decrypted_data)
                
                temp_path = decrypted_path
            
            # فك الضغط إذا كان مضغوطاً
            if temp_path.endswith('.gz'):
                decompressed_path = temp_path.replace('.gz', '')
                
                with gzip.open(temp_path, 'rb') as f_in:
                    with open(decompressed_path, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
                
                if temp_path != backup_path:  # إذا كان ملف مؤقت
                    os.remove(temp_path)
                temp_path = decompressed_path
            
            # إغلاق جميع الاتصالات
            self.db_manager.close_all_connections()
            
            # استبدال قاعدة البيانات
            shutil.copy2(temp_path, self.db_manager.db_path)
            
            # تنظيف الملفات المؤقتة
            if temp_path != backup_path:
                os.remove(temp_path)
            
            # إعادة تهيئة قاعدة البيانات
            self.db_manager._initialize_database()
            
            logger.info(f"✅ تم استعادة النسخة الاحتياطية بنجاح: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في استعادة النسخة الاحتياطية: {e}")
            return False
    
    def list_backups(self) -> List[Dict[str, Any]]:
        """عرض قائمة النسخ الاحتياطية"""
        backups = []
        
        try:
            for filename in os.listdir(self.backup_dir):
                if filename.startswith('backup_') and (filename.endswith('.db') or 
                    filename.endswith('.gz') or filename.endswith('.enc')):
                    
                    file_path = os.path.join(self.backup_dir, filename)
                    stat = os.stat(file_path)
                    
                    backup_info = {
                        'name': filename,
                        'path': file_path,
                        'size': stat.st_size,
                        'size_mb': stat.st_size / 1024 / 1024,
                        'created_at': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                        'modified_at': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                        'compressed': filename.endswith('.gz') or '.gz.' in filename,
                        'encrypted': filename.endswith('.enc'),
                        'type': self._extract_backup_type(filename)
                    }
                    
                    backups.append(backup_info)
            
            # ترتيب حسب تاريخ الإنشاء (الأحدث أولاً)
            backups.sort(key=lambda x: x['created_at'], reverse=True)
            
        except Exception as e:
            logger.error(f"❌ خطأ في عرض النسخ الاحتياطية: {e}")
        
        return backups
    
    def _extract_backup_type(self, filename: str) -> str:
        """استخراج نوع النسخة الاحتياطية من اسم الملف"""
        if 'daily' in filename:
            return 'daily'
        elif 'weekly' in filename:
            return 'weekly'
        elif 'monthly' in filename:
            return 'monthly'
        elif 'manual' in filename:
            return 'manual'
        else:
            return 'unknown'
    
    def cleanup_old_backups(self):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            backups = self.list_backups()
            retention = self.config.get('retention', {})
            
            # تجميع النسخ حسب النوع
            backups_by_type = {}
            for backup in backups:
                backup_type = backup['type']
                if backup_type not in backups_by_type:
                    backups_by_type[backup_type] = []
                backups_by_type[backup_type].append(backup)
            
            deleted_count = 0
            
            # تنظيف كل نوع حسب سياسة الاحتفاظ
            for backup_type, type_backups in backups_by_type.items():
                max_backups = retention.get(f'{backup_type}_backups', 10)
                
                if len(type_backups) > max_backups:
                    # ترتيب حسب التاريخ (الأقدم أولاً)
                    type_backups.sort(key=lambda x: x['created_at'])
                    
                    # حذف النسخ الزائدة
                    for backup in type_backups[:-max_backups]:
                        try:
                            os.remove(backup['path'])
                            deleted_count += 1
                            logger.info(f"🗑️ تم حذف النسخة الاحتياطية القديمة: {backup['name']}")
                        except Exception as e:
                            logger.error(f"❌ خطأ في حذف النسخة الاحتياطية {backup['name']}: {e}")
            
            if deleted_count > 0:
                logger.info(f"✅ تم تنظيف {deleted_count} نسخة احتياطية قديمة")
            
        except Exception as e:
            logger.error(f"❌ خطأ في تنظيف النسخ الاحتياطية القديمة: {e}")
    
    def _log_backup_info(self, backup_info: Dict[str, Any]):
        """تسجيل معلومات النسخة الاحتياطية"""
        log_file = os.path.join(self.backup_dir, 'backup_log.json')
        
        try:
            # تحميل السجل الحالي
            if os.path.exists(log_file):
                with open(log_file, 'r', encoding='utf-8') as f:
                    log_data = json.load(f)
            else:
                log_data = []
            
            # إضافة السجل الجديد
            log_data.append(backup_info)
            
            # الاحتفاظ بآخر 100 سجل فقط
            if len(log_data) > 100:
                log_data = log_data[-100:]
            
            # حفظ السجل
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(log_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"❌ خطأ في تسجيل معلومات النسخة الاحتياطية: {e}")
    
    def start_scheduler(self):
        """بدء مجدولة النسخ الاحتياطي التلقائي"""
        if self.scheduler_running:
            logger.warning("⚠️ مجدولة النسخ الاحتياطي تعمل بالفعل")
            return
        
        auto_backup = self.config.get('auto_backup', {})
        if not auto_backup.get('enabled', True):
            logger.info("ℹ️ النسخ الاحتياطي التلقائي معطل")
            return
        
        # جدولة النسخ اليومية
        if auto_backup.get('daily', {}).get('enabled', True):
            daily_time = auto_backup.get('daily', {}).get('time', '02:00')
            schedule.every().day.at(daily_time).do(self._scheduled_backup, 'daily')
            logger.info(f"📅 تم جدولة النسخ الاحتياطي اليومي في {daily_time}")
        
        # جدولة النسخ الأسبوعية
        if auto_backup.get('weekly', {}).get('enabled', True):
            weekly_day = auto_backup.get('weekly', {}).get('day', 'sunday')
            weekly_time = auto_backup.get('weekly', {}).get('time', '03:00')
            getattr(schedule.every(), weekly_day).at(weekly_time).do(self._scheduled_backup, 'weekly')
            logger.info(f"📅 تم جدولة النسخ الاحتياطي الأسبوعي يوم {weekly_day} في {weekly_time}")
        
        # جدولة النسخ الشهرية
        if auto_backup.get('monthly', {}).get('enabled', True):
            monthly_time = auto_backup.get('monthly', {}).get('time', '04:00')
            schedule.every().month.do(self._scheduled_backup, 'monthly')
            logger.info(f"📅 تم جدولة النسخ الاحتياطي الشهري في {monthly_time}")
        
        # بدء خيط المجدولة
        self.scheduler_running = True
        self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()
        
        logger.info("🚀 تم بدء مجدولة النسخ الاحتياطي التلقائي")
    
    def stop_scheduler(self):
        """إيقاف مجدولة النسخ الاحتياطي"""
        self.scheduler_running = False
        schedule.clear()
        logger.info("⏹️ تم إيقاف مجدولة النسخ الاحتياطي")
    
    def _run_scheduler(self):
        """تشغيل مجدولة النسخ الاحتياطي"""
        while self.scheduler_running:
            schedule.run_pending()
            time.sleep(60)  # فحص كل دقيقة
    
    def _scheduled_backup(self, backup_type: str):
        """تنفيذ نسخة احتياطية مجدولة"""
        try:
            logger.info(f"🔄 بدء النسخة الاحتياطية المجدولة: {backup_type}")
            backup_path = self.create_backup(backup_type)
            self.cleanup_old_backups()
            logger.info(f"✅ اكتملت النسخة الاحتياطية المجدولة: {backup_type}")
        except Exception as e:
            logger.error(f"❌ خطأ في النسخة الاحتياطية المجدولة {backup_type}: {e}")
    
    def get_backup_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات النسخ الاحتياطية"""
        backups = self.list_backups()
        
        stats = {
            'total_backups': len(backups),
            'total_size_mb': sum(b['size_mb'] for b in backups),
            'by_type': {},
            'oldest_backup': None,
            'newest_backup': None,
            'last_backup_dates': self.config.get('last_backup', {})
        }
        
        if backups:
            stats['oldest_backup'] = min(backups, key=lambda x: x['created_at'])
            stats['newest_backup'] = max(backups, key=lambda x: x['created_at'])
            
            # إحصائيات حسب النوع
            for backup in backups:
                backup_type = backup['type']
                if backup_type not in stats['by_type']:
                    stats['by_type'][backup_type] = {'count': 0, 'size_mb': 0}
                
                stats['by_type'][backup_type]['count'] += 1
                stats['by_type'][backup_type]['size_mb'] += backup['size_mb']
        
        return stats
