#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير المحافظ MySQL
يوفر واجهة موحدة لإدارة المحافظ في قاعدة البيانات MySQL مع دعم العمليات المتزامنة
"""

import asyncio
import json
import logging
import random
from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Any, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor

from .mysql_manager import mysql_manager

# إعداد نظام السجلات
logger = logging.getLogger(__name__)

class MySQLWalletManager:
    """مدير المحافظ MySQL مع دعم العمليات المتزامنة"""
    
    def __init__(self):
        """تهيئة مدير المحافظ"""
        self.executor = ThreadPoolExecutor(max_workers=5)
        logger.info("💰 تم تهيئة مدير المحافظ MySQL")
    
    def generate_wallet_number(self) -> str:
        """إنشاء رقم محفظة جديد"""
        while True:
            # إنشاء رقم محفظة بصيغة 909XXXXXXX
            wallet_number = f"909{random.randint(1000000, 9999999)}"
            
            # التحقق من عدم وجود الرقم مسبقاً
            if not self.wallet_exists(wallet_number):
                return wallet_number
    
    def wallet_exists(self, wallet_number: str) -> bool:
        """التحقق من وجود المحفظة"""
        try:
            query = "SELECT COUNT(*) as count FROM wallets WHERE wallet_number = %(wallet_number)s"
            result = mysql_manager.execute_query(query, {'wallet_number': wallet_number})
            return result[0]['count'] > 0 if result else False
            
        except Exception as e:
            logger.error(f"❌ خطأ في التحقق من وجود المحفظة {wallet_number}: {e}")
            return True  # إرجاع True للأمان
    
    def create_wallet(self, user_id: int, user_name: str, username: str = "") -> Optional[str]:
        """إنشاء محفظة جديدة للمستخدم"""
        try:
            # التحقق من عدم وجود محفظة للمستخدم
            existing_wallet = self.get_wallet_by_user_id(user_id)
            if existing_wallet:
                logger.warning(f"⚠️ المستخدم {user_id} لديه محفظة بالفعل: {existing_wallet['wallet_number']}")
                return existing_wallet['wallet_number']
            
            # إنشاء رقم محفظة جديد
            wallet_number = self.generate_wallet_number()
            
            # بيانات المحفظة
            wallet_data = {
                'wallet_number': wallet_number,
                'user_id': user_id,
                'user_name': user_name,
                'username': username.replace('@', ''),
                'balance': Decimal('0.00000000'),
                'currency': 'إكسا',
                'status': 'active',
                'created_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'transaction_count': 0,
                'is_verified': False,
                'security_level': 'basic',
                'loan_amount': Decimal('0.00000000'),
                'has_active_loan': False,
                'debt_amount': Decimal('0.00000000'),
                'has_debt': False,
                'debt_limit': Decimal('1.00000000'),
                'wallet_data': json.dumps({
                    'created_by': 'system',
                    'creation_timestamp': datetime.now().isoformat()
                }, ensure_ascii=False)
            }
            
            # إدراج المحفظة في قاعدة البيانات
            query = """
            INSERT INTO wallets (
                wallet_number, user_id, user_name, username, balance, currency,
                status, created_at, transaction_count, is_verified, security_level,
                loan_amount, has_active_loan, debt_amount, has_debt, debt_limit, wallet_data
            ) VALUES (
                %(wallet_number)s, %(user_id)s, %(user_name)s, %(username)s, %(balance)s, %(currency)s,
                %(status)s, %(created_at)s, %(transaction_count)s, %(is_verified)s, %(security_level)s,
                %(loan_amount)s, %(has_active_loan)s, %(debt_amount)s, %(has_debt)s, %(debt_limit)s, %(wallet_data)s
            )
            """
            
            rows_affected = mysql_manager.execute_update(query, wallet_data)
            
            if rows_affected > 0:
                logger.info(f"✅ تم إنشاء محفظة جديدة: {wallet_number} للمستخدم: {user_id}")
                return wallet_number
            else:
                logger.error(f"❌ فشل في إنشاء المحفظة للمستخدم: {user_id}")
                return None
                
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء المحفظة للمستخدم {user_id}: {e}")
            return None
    
    async def create_wallet_async(self, user_id: int, user_name: str, username: str = "") -> Optional[str]:
        """إنشاء محفظة جديدة بشكل غير متزامن"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.executor, self.create_wallet, user_id, user_name, username)
    
    def get_wallet_by_number(self, wallet_number: str) -> Optional[Dict[str, Any]]:
        """الحصول على بيانات المحفظة بواسطة الرقم"""
        try:
            query = "SELECT * FROM wallets WHERE wallet_number = %(wallet_number)s"
            results = mysql_manager.execute_query(query, {'wallet_number': wallet_number})
            
            if results:
                wallet = results[0]
                # تحويل JSON data إلى dict
                if wallet.get('wallet_data'):
                    try:
                        wallet['wallet_data'] = json.loads(wallet['wallet_data'])
                    except json.JSONDecodeError:
                        wallet['wallet_data'] = {}
                
                # تحويل Decimal إلى float للتوافق
                for field in ['balance', 'loan_amount', 'debt_amount', 'debt_limit']:
                    if wallet.get(field) is not None:
                        wallet[field] = float(wallet[field])
                
                return wallet
            
            return None
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على بيانات المحفظة {wallet_number}: {e}")
            return None
    
    def get_wallet_by_user_id(self, user_id: int) -> Optional[Dict[str, Any]]:
        """الحصول على محفظة المستخدم"""
        try:
            query = "SELECT * FROM wallets WHERE user_id = %(user_id)s"
            results = mysql_manager.execute_query(query, {'user_id': user_id})
            
            if results:
                wallet = results[0]
                # تحويل JSON data إلى dict
                if wallet.get('wallet_data'):
                    try:
                        wallet['wallet_data'] = json.loads(wallet['wallet_data'])
                    except json.JSONDecodeError:
                        wallet['wallet_data'] = {}
                
                # تحويل Decimal إلى float للتوافق
                for field in ['balance', 'loan_amount', 'debt_amount', 'debt_limit']:
                    if wallet.get(field) is not None:
                        wallet[field] = float(wallet[field])
                
                return wallet
            
            return None
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على محفظة المستخدم {user_id}: {e}")
            return None
    
    async def get_wallet_by_user_id_async(self, user_id: int) -> Optional[Dict[str, Any]]:
        """الحصول على محفظة المستخدم بشكل غير متزامن"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.executor, self.get_wallet_by_user_id, user_id)
    
    def update_balance(self, wallet_number: str, new_balance: float, transaction_type: str = "update") -> bool:
        """تحديث رصيد المحفظة"""
        try:
            # الحصول على الرصيد الحالي
            current_wallet = self.get_wallet_by_number(wallet_number)
            if not current_wallet:
                logger.error(f"❌ المحفظة غير موجودة: {wallet_number}")
                return False
            
            old_balance = current_wallet['balance']
            
            # تحديث الرصيد
            update_query = """
            UPDATE wallets 
            SET balance = %(new_balance)s, 
                last_transaction = %(timestamp)s,
                transaction_count = transaction_count + 1
            WHERE wallet_number = %(wallet_number)s
            """
            
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            rows_affected = mysql_manager.execute_update(update_query, {
                'new_balance': Decimal(str(new_balance)),
                'timestamp': timestamp,
                'wallet_number': wallet_number
            })
            
            if rows_affected > 0:
                # تسجيل المعاملة
                self.record_transaction(
                    wallet_number=wallet_number,
                    user_id=current_wallet['user_id'],
                    transaction_type=transaction_type,
                    amount=new_balance - old_balance,
                    balance_before=old_balance,
                    balance_after=new_balance,
                    description=f"تحديث الرصيد: {transaction_type}"
                )
                
                logger.info(f"✅ تم تحديث رصيد المحفظة {wallet_number}: {old_balance} -> {new_balance}")
                return True
            else:
                logger.error(f"❌ فشل في تحديث رصيد المحفظة: {wallet_number}")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في تحديث رصيد المحفظة {wallet_number}: {e}")
            return False
    
    async def update_balance_async(self, wallet_number: str, new_balance: float, transaction_type: str = "update") -> bool:
        """تحديث رصيد المحفظة بشكل غير متزامن"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.executor, self.update_balance, wallet_number, new_balance, transaction_type)
    
    def record_transaction(self, wallet_number: str, user_id: int, transaction_type: str, 
                          amount: float, balance_before: float, balance_after: float,
                          description: str = "", reference_id: str = "") -> bool:
        """تسجيل معاملة في سجل المعاملات"""
        try:
            transaction_data = {
                'wallet_number': wallet_number,
                'user_id': user_id,
                'transaction_type': transaction_type,
                'amount': Decimal(str(amount)),
                'balance_before': Decimal(str(balance_before)),
                'balance_after': Decimal(str(balance_after)),
                'description': description,
                'reference_id': reference_id,
                'status': 'completed',
                'transaction_data': json.dumps({
                    'timestamp': datetime.now().isoformat(),
                    'type': transaction_type,
                    'amount': amount
                }, ensure_ascii=False),
                'processed_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            query = """
            INSERT INTO transactions (
                wallet_number, user_id, transaction_type, amount, balance_before,
                balance_after, description, reference_id, status, transaction_data, processed_at
            ) VALUES (
                %(wallet_number)s, %(user_id)s, %(transaction_type)s, %(amount)s, %(balance_before)s,
                %(balance_after)s, %(description)s, %(reference_id)s, %(status)s, %(transaction_data)s, %(processed_at)s
            )
            """
            
            rows_affected = mysql_manager.execute_update(query, transaction_data)
            
            if rows_affected > 0:
                logger.debug(f"📝 تم تسجيل المعاملة: {transaction_type} للمحفظة {wallet_number}")
                return True
            else:
                logger.error(f"❌ فشل في تسجيل المعاملة للمحفظة: {wallet_number}")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في تسجيل المعاملة: {e}")
            return False
    
    def get_wallet_transactions(self, wallet_number: str, limit: int = 50) -> List[Dict[str, Any]]:
        """الحصول على معاملات المحفظة"""
        try:
            query = """
            SELECT * FROM transactions 
            WHERE wallet_number = %(wallet_number)s 
            ORDER BY created_at DESC 
            LIMIT %(limit)s
            """
            
            results = mysql_manager.execute_query(query, {
                'wallet_number': wallet_number,
                'limit': limit
            })
            
            # تحويل البيانات
            for transaction in results:
                if transaction.get('transaction_data'):
                    try:
                        transaction['transaction_data'] = json.loads(transaction['transaction_data'])
                    except json.JSONDecodeError:
                        transaction['transaction_data'] = {}
                
                # تحويل Decimal إلى float
                for field in ['amount', 'balance_before', 'balance_after']:
                    if transaction.get(field) is not None:
                        transaction[field] = float(transaction[field])
            
            return results
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على معاملات المحفظة {wallet_number}: {e}")
            return []
    
    def get_wallet_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات المحافظ"""
        try:
            stats = {}
            
            # إجمالي المحافظ
            total_query = "SELECT COUNT(*) as total FROM wallets"
            total_result = mysql_manager.execute_query(total_query)
            stats['total_wallets'] = total_result[0]['total'] if total_result else 0
            
            # المحافظ النشطة
            active_query = "SELECT COUNT(*) as active FROM wallets WHERE status = 'active'"
            active_result = mysql_manager.execute_query(active_query)
            stats['active_wallets'] = active_result[0]['active'] if active_result else 0
            
            # إجمالي الأرصدة
            balance_query = "SELECT SUM(balance) as total_balance FROM wallets WHERE status = 'active'"
            balance_result = mysql_manager.execute_query(balance_query)
            stats['total_balance'] = float(balance_result[0]['total_balance']) if balance_result and balance_result[0]['total_balance'] else 0.0
            
            # إجمالي القروض
            loans_query = "SELECT SUM(loan_amount) as total_loans FROM wallets WHERE has_active_loan = TRUE"
            loans_result = mysql_manager.execute_query(loans_query)
            stats['total_loans'] = float(loans_result[0]['total_loans']) if loans_result and loans_result[0]['total_loans'] else 0.0
            
            # إجمالي الديون
            debts_query = "SELECT SUM(debt_amount) as total_debts FROM wallets WHERE has_debt = TRUE"
            debts_result = mysql_manager.execute_query(debts_query)
            stats['total_debts'] = float(debts_result[0]['total_debts']) if debts_result and debts_result[0]['total_debts'] else 0.0
            
            # المحافظ المتحققة
            verified_query = "SELECT COUNT(*) as verified FROM wallets WHERE is_verified = TRUE"
            verified_result = mysql_manager.execute_query(verified_query)
            stats['verified_wallets'] = verified_result[0]['verified'] if verified_result else 0
            
            stats['last_updated'] = datetime.now().isoformat()
            
            return stats
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على إحصائيات المحافظ: {e}")
            return {}
    
    def transfer_funds(self, from_wallet: str, to_wallet: str, amount: float, description: str = "") -> bool:
        """تحويل الأموال بين المحافظ"""
        try:
            # التحقق من وجود المحافظ
            sender_wallet = self.get_wallet_by_number(from_wallet)
            receiver_wallet = self.get_wallet_by_number(to_wallet)
            
            if not sender_wallet or not receiver_wallet:
                logger.error("❌ إحدى المحافظ غير موجودة")
                return False
            
            # التحقق من الرصيد الكافي
            if sender_wallet['balance'] < amount:
                logger.error(f"❌ رصيد غير كافي في المحفظة {from_wallet}")
                return False
            
            # تنفيذ التحويل في معاملة واحدة
            operations = [
                # خصم من المحفظة المرسلة
                ("""UPDATE wallets SET balance = balance - %(amount)s, 
                    last_transaction = %(timestamp)s, transaction_count = transaction_count + 1 
                    WHERE wallet_number = %(from_wallet)s""",
                 {'amount': Decimal(str(amount)), 'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 'from_wallet': from_wallet}),
                
                # إضافة للمحفظة المستقبلة
                ("""UPDATE wallets SET balance = balance + %(amount)s, 
                    last_transaction = %(timestamp)s, transaction_count = transaction_count + 1 
                    WHERE wallet_number = %(to_wallet)s""",
                 {'amount': Decimal(str(amount)), 'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 'to_wallet': to_wallet})
            ]
            
            success = mysql_manager.transaction(operations)
            
            if success:
                # تسجيل المعاملات
                self.record_transaction(
                    wallet_number=from_wallet,
                    user_id=sender_wallet['user_id'],
                    transaction_type='transfer',
                    amount=-amount,
                    balance_before=sender_wallet['balance'],
                    balance_after=sender_wallet['balance'] - amount,
                    description=f"تحويل إلى {to_wallet}: {description}",
                    reference_id=f"transfer_{datetime.now().strftime('%Y%m%d%H%M%S')}"
                )
                
                self.record_transaction(
                    wallet_number=to_wallet,
                    user_id=receiver_wallet['user_id'],
                    transaction_type='transfer',
                    amount=amount,
                    balance_before=receiver_wallet['balance'],
                    balance_after=receiver_wallet['balance'] + amount,
                    description=f"تحويل من {from_wallet}: {description}",
                    reference_id=f"transfer_{datetime.now().strftime('%Y%m%d%H%M%S')}"
                )
                
                logger.info(f"✅ تم تحويل {amount} من {from_wallet} إلى {to_wallet}")
                return True
            else:
                logger.error("❌ فشل في تنفيذ التحويل")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في تحويل الأموال: {e}")
            return False

# إنشاء مثيل مشترك
mysql_wallet_manager = MySQLWalletManager()
