#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير المستخدمين MySQL
يوفر واجهة موحدة لإدارة المستخدمين في قاعدة البيانات MySQL مع دعم العمليات المتزامنة
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor

from .mysql_manager import mysql_manager

# إعداد نظام السجلات
logger = logging.getLogger(__name__)

class MySQLUserManager:
    """مدير المستخدمين MySQL مع دعم العمليات المتزامنة"""
    
    def __init__(self):
        """تهيئة مدير المستخدمين"""
        self.executor = ThreadPoolExecutor(max_workers=5)
        logger.info("👥 تم تهيئة مدير المستخدمين MySQL")
    
    def register_user(self, user_data: Dict[str, Any]) -> bool:
        """تسجيل مستخدم جديد"""
        try:
            # التحقق من وجود المستخدم
            existing_user = self.get_user_by_id(user_data['id'])
            
            if existing_user:
                # تحديث بيانات المستخدم الموجود
                return self.update_user(user_data['id'], user_data)
            
            # إدراج مستخدم جديد
            query = """
            INSERT INTO users (
                id, username, first_name, last_name, language_code,
                registration_date, first_login, last_activity, visit_count,
                wallet_number, wallet_status, user_data
            ) VALUES (
                %(id)s, %(username)s, %(first_name)s, %(last_name)s, %(language_code)s,
                %(registration_date)s, %(first_login)s, %(last_activity)s, %(visit_count)s,
                %(wallet_number)s, %(wallet_status)s, %(user_data)s
            )
            """
            
            # تحضير البيانات
            prepared_data = {
                'id': user_data['id'],
                'username': user_data.get('username', '').replace('@', ''),
                'first_name': user_data.get('first_name', ''),
                'last_name': user_data.get('last_name', ''),
                'language_code': user_data.get('language_code', 'ar'),
                'registration_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'first_login': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'last_activity': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'visit_count': 1,
                'wallet_number': user_data.get('wallet_number'),
                'wallet_status': 'active',
                'user_data': json.dumps(user_data, ensure_ascii=False)
            }
            
            rows_affected = mysql_manager.execute_update(query, prepared_data)
            
            if rows_affected > 0:
                logger.info(f"✅ تم تسجيل المستخدم: {user_data['id']}")
                return True
            else:
                logger.error(f"❌ فشل في تسجيل المستخدم: {user_data['id']}")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في تسجيل المستخدم: {e}")
            return False
    
    async def register_user_async(self, user_data: Dict[str, Any]) -> bool:
        """تسجيل مستخدم جديد بشكل غير متزامن"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.executor, self.register_user, user_data)
    
    def get_user_by_id(self, user_id: int) -> Optional[Dict[str, Any]]:
        """الحصول على بيانات المستخدم بواسطة المعرف"""
        try:
            query = "SELECT * FROM users WHERE id = %(user_id)s"
            results = mysql_manager.execute_query(query, {'user_id': user_id})
            
            if results:
                user = results[0]
                # تحويل JSON data إلى dict
                if user.get('user_data'):
                    try:
                        user['user_data'] = json.loads(user['user_data'])
                    except json.JSONDecodeError:
                        user['user_data'] = {}
                return user
            
            return None
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على بيانات المستخدم {user_id}: {e}")
            return None
    
    async def get_user_by_id_async(self, user_id: int) -> Optional[Dict[str, Any]]:
        """الحصول على بيانات المستخدم بشكل غير متزامن"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.executor, self.get_user_by_id, user_id)
    
    def update_user(self, user_id: int, update_data: Dict[str, Any]) -> bool:
        """تحديث بيانات المستخدم"""
        try:
            # بناء استعلام التحديث ديناميكياً
            set_clauses = []
            params = {'user_id': user_id}
            
            # الحقول القابلة للتحديث
            updatable_fields = {
                'username': 'username',
                'first_name': 'first_name', 
                'last_name': 'last_name',
                'language_code': 'language_code',
                'visit_count': 'visit_count',
                'wallet_number': 'wallet_number',
                'wallet_status': 'wallet_status'
            }
            
            for field, column in updatable_fields.items():
                if field in update_data:
                    set_clauses.append(f"{column} = %({field})s")
                    params[field] = update_data[field]
            
            # تحديث آخر نشاط دائماً
            set_clauses.append("last_activity = %(last_activity)s")
            params['last_activity'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # تحديث بيانات JSON
            if 'user_data' in update_data:
                set_clauses.append("user_data = %(user_data)s")
                params['user_data'] = json.dumps(update_data['user_data'], ensure_ascii=False)
            
            if not set_clauses:
                logger.warning(f"⚠️ لا توجد بيانات للتحديث للمستخدم: {user_id}")
                return False
            
            query = f"""
            UPDATE users 
            SET {', '.join(set_clauses)}
            WHERE id = %(user_id)s
            """
            
            rows_affected = mysql_manager.execute_update(query, params)
            
            if rows_affected > 0:
                logger.info(f"✅ تم تحديث بيانات المستخدم: {user_id}")
                return True
            else:
                logger.warning(f"⚠️ لم يتم العثور على المستخدم للتحديث: {user_id}")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في تحديث بيانات المستخدم {user_id}: {e}")
            return False
    
    async def update_user_async(self, user_id: int, update_data: Dict[str, Any]) -> bool:
        """تحديث بيانات المستخدم بشكل غير متزامن"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.executor, self.update_user, user_id, update_data)
    
    def get_all_users(self, limit: Optional[int] = None, offset: int = 0) -> List[Dict[str, Any]]:
        """الحصول على جميع المستخدمين"""
        try:
            query = "SELECT * FROM users ORDER BY registration_date DESC"
            params = {}
            
            if limit:
                query += " LIMIT %(limit)s OFFSET %(offset)s"
                params.update({'limit': limit, 'offset': offset})
            
            results = mysql_manager.execute_query(query, params)
            
            # تحويل JSON data
            for user in results:
                if user.get('user_data'):
                    try:
                        user['user_data'] = json.loads(user['user_data'])
                    except json.JSONDecodeError:
                        user['user_data'] = {}
            
            return results
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على جميع المستخدمين: {e}")
            return []
    
    def get_active_users(self, days: int = 7) -> List[Dict[str, Any]]:
        """الحصول على المستخدمين النشطين خلال فترة معينة"""
        try:
            query = """
            SELECT * FROM users 
            WHERE last_activity >= DATE_SUB(NOW(), INTERVAL %(days)s DAY)
            AND wallet_status = 'active'
            AND is_banned = FALSE
            ORDER BY last_activity DESC
            """
            
            results = mysql_manager.execute_query(query, {'days': days})
            
            # تحويل JSON data
            for user in results:
                if user.get('user_data'):
                    try:
                        user['user_data'] = json.loads(user['user_data'])
                    except json.JSONDecodeError:
                        user['user_data'] = {}
            
            return results
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على المستخدمين النشطين: {e}")
            return []
    
    def search_users(self, search_term: str, search_type: str = 'all') -> List[Dict[str, Any]]:
        """البحث عن المستخدمين"""
        try:
            if search_type == 'username':
                query = "SELECT * FROM users WHERE username LIKE %(search_term)s"
            elif search_type == 'name':
                query = "SELECT * FROM users WHERE first_name LIKE %(search_term)s OR last_name LIKE %(search_term)s"
            elif search_type == 'id':
                query = "SELECT * FROM users WHERE id = %(user_id)s"
                try:
                    user_id = int(search_term)
                    return [self.get_user_by_id(user_id)] if self.get_user_by_id(user_id) else []
                except ValueError:
                    return []
            else:  # search all fields
                query = """
                SELECT * FROM users 
                WHERE username LIKE %(search_term)s 
                OR first_name LIKE %(search_term)s 
                OR last_name LIKE %(search_term)s
                OR id = %(user_id)s
                """
            
            params = {'search_term': f'%{search_term}%'}
            
            # إضافة البحث بالمعرف إذا كان رقماً
            try:
                params['user_id'] = int(search_term)
            except ValueError:
                params['user_id'] = 0
            
            results = mysql_manager.execute_query(query, params)
            
            # تحويل JSON data
            for user in results:
                if user.get('user_data'):
                    try:
                        user['user_data'] = json.loads(user['user_data'])
                    except json.JSONDecodeError:
                        user['user_data'] = {}
            
            return results
            
        except Exception as e:
            logger.error(f"❌ خطأ في البحث عن المستخدمين: {e}")
            return []
    
    def ban_user(self, user_id: int, reason: str, banned_by: int, permanent: bool = False) -> bool:
        """حظر مستخدم"""
        try:
            # تحديث حالة المستخدم
            update_query = "UPDATE users SET is_banned = TRUE WHERE id = %(user_id)s"
            mysql_manager.execute_update(update_query, {'user_id': user_id})
            
            # إضافة سجل الحظر
            ban_query = """
            INSERT INTO banned_users (user_id, ban_reason, banned_by, is_permanent, ban_data)
            VALUES (%(user_id)s, %(reason)s, %(banned_by)s, %(permanent)s, %(ban_data)s)
            ON DUPLICATE KEY UPDATE
                ban_reason = VALUES(ban_reason),
                banned_by = VALUES(banned_by),
                is_permanent = VALUES(is_permanent),
                ban_date = CURRENT_TIMESTAMP,
                ban_data = VALUES(ban_data)
            """
            
            ban_data = {
                'user_id': user_id,
                'reason': reason,
                'banned_by': banned_by,
                'permanent': permanent,
                'ban_data': json.dumps({
                    'ban_timestamp': datetime.now().isoformat(),
                    'ban_reason': reason
                }, ensure_ascii=False)
            }
            
            rows_affected = mysql_manager.execute_update(ban_query, ban_data)
            
            if rows_affected > 0:
                logger.info(f"🚫 تم حظر المستخدم: {user_id}")
                return True
            else:
                logger.error(f"❌ فشل في حظر المستخدم: {user_id}")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في حظر المستخدم {user_id}: {e}")
            return False
    
    def unban_user(self, user_id: int) -> bool:
        """إلغاء حظر مستخدم"""
        try:
            # تحديث حالة المستخدم
            update_query = "UPDATE users SET is_banned = FALSE WHERE id = %(user_id)s"
            mysql_manager.execute_update(update_query, {'user_id': user_id})
            
            # حذف سجل الحظر
            delete_query = "DELETE FROM banned_users WHERE user_id = %(user_id)s"
            rows_affected = mysql_manager.execute_update(delete_query, {'user_id': user_id})
            
            if rows_affected > 0:
                logger.info(f"✅ تم إلغاء حظر المستخدم: {user_id}")
                return True
            else:
                logger.warning(f"⚠️ المستخدم غير محظور: {user_id}")
                return True  # ليس خطأ إذا لم يكن محظوراً
                
        except Exception as e:
            logger.error(f"❌ خطأ في إلغاء حظر المستخدم {user_id}: {e}")
            return False
    
    def get_user_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات المستخدمين"""
        try:
            stats = {}
            
            # إجمالي المستخدمين
            total_query = "SELECT COUNT(*) as total FROM users"
            total_result = mysql_manager.execute_query(total_query)
            stats['total_users'] = total_result[0]['total'] if total_result else 0
            
            # المستخدمون النشطون
            active_query = "SELECT COUNT(*) as active FROM users WHERE wallet_status = 'active' AND is_banned = FALSE"
            active_result = mysql_manager.execute_query(active_query)
            stats['active_users'] = active_result[0]['active'] if active_result else 0
            
            # المستخدمون المحظورون
            banned_query = "SELECT COUNT(*) as banned FROM users WHERE is_banned = TRUE"
            banned_result = mysql_manager.execute_query(banned_query)
            stats['banned_users'] = banned_result[0]['banned'] if banned_result else 0
            
            # المستخدمون الجدد اليوم
            new_today_query = "SELECT COUNT(*) as new_today FROM users WHERE DATE(registration_date) = CURDATE()"
            new_today_result = mysql_manager.execute_query(new_today_query)
            stats['new_users_today'] = new_today_result[0]['new_today'] if new_today_result else 0
            
            # المستخدمون النشطون اليوم
            active_today_query = "SELECT COUNT(*) as active_today FROM users WHERE DATE(last_activity) = CURDATE()"
            active_today_result = mysql_manager.execute_query(active_today_query)
            stats['active_users_today'] = active_today_result[0]['active_today'] if active_today_result else 0
            
            stats['last_updated'] = datetime.now().isoformat()
            
            return stats
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على إحصائيات المستخدمين: {e}")
            return {}

# إنشاء مثيل مشترك
mysql_user_manager = MySQLUserManager()
