#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات SQLite مع دعم العمليات المتزامنة
يوفر واجهة موحدة وآمنة للتعامل مع قاعدة البيانات
"""

import sqlite3
import threading
import os
import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List, Tuple, Union
from contextlib import contextmanager
from .models import DatabaseModels

# إعداد نظام السجلات
logger = logging.getLogger(__name__)

class SQLiteManager:
    """مدير قاعدة البيانات SQLite مع دعم العمليات المتزامنة"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls, db_path: str = None):
        """تطبيق نمط Singleton لضمان وجود مثيل واحد فقط"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(SQLiteManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self, db_path: str = None):
        """تهيئة مدير قاعدة البيانات"""
        if hasattr(self, '_initialized'):
            return
            
        self.db_path = db_path or os.path.join(os.path.dirname(__file__), "bot_database.db")
        self.connection_pool = {}
        self.pool_lock = threading.Lock()
        self.max_connections = 10
        self.current_connections = 0
        
        # إنشاء مجلد قاعدة البيانات إذا لم يكن موجوداً
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        # تهيئة قاعدة البيانات
        self._initialize_database()
        self._initialized = True
        
        logger.info(f"🗄️ تم تهيئة مدير قاعدة البيانات SQLite: {self.db_path}")
    
    def _initialize_database(self):
        """تهيئة قاعدة البيانات وإنشاء الجداول"""
        try:
            with self.get_connection() as conn:
                # تفعيل دعم المفاتيح الخارجية
                conn.execute("PRAGMA foreign_keys = ON")
                
                # تحسين الأداء
                conn.execute("PRAGMA journal_mode = WAL")
                conn.execute("PRAGMA synchronous = NORMAL")
                conn.execute("PRAGMA cache_size = 10000")
                conn.execute("PRAGMA temp_store = MEMORY")
                
                # إنشاء الجداول
                DatabaseModels.create_tables(conn)
                
                logger.info("✅ تم تهيئة قاعدة البيانات بنجاح")
                
        except Exception as e:
            logger.error(f"❌ خطأ في تهيئة قاعدة البيانات: {e}")
            raise
    
    @contextmanager
    def get_connection(self):
        """الحصول على اتصال آمن بقاعدة البيانات"""
        thread_id = threading.get_ident()
        
        with self.pool_lock:
            if thread_id in self.connection_pool:
                conn = self.connection_pool[thread_id]
            else:
                if self.current_connections >= self.max_connections:
                    raise Exception("تم الوصول للحد الأقصى من الاتصالات")
                
                conn = sqlite3.connect(
                    self.db_path,
                    check_same_thread=False,
                    timeout=30.0
                )
                conn.row_factory = sqlite3.Row
                self.connection_pool[thread_id] = conn
                self.current_connections += 1
        
        try:
            yield conn
        except Exception as e:
            conn.rollback()
            logger.error(f"❌ خطأ في العملية: {e}")
            raise
        finally:
            # لا نغلق الاتصال هنا، سيتم إغلاقه عند إنهاء الخيط
            pass
    
    def close_connection(self, thread_id: int = None):
        """إغلاق اتصال محدد"""
        if thread_id is None:
            thread_id = threading.get_ident()
        
        with self.pool_lock:
            if thread_id in self.connection_pool:
                self.connection_pool[thread_id].close()
                del self.connection_pool[thread_id]
                self.current_connections -= 1
    
    def close_all_connections(self):
        """إغلاق جميع الاتصالات"""
        with self.pool_lock:
            for conn in self.connection_pool.values():
                conn.close()
            self.connection_pool.clear()
            self.current_connections = 0
    
    def execute_query(self, query: str, params: Tuple = (), fetch: str = None) -> Union[List[sqlite3.Row], sqlite3.Row, int]:
        """تنفيذ استعلام مع دعم المعاملات"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            try:
                cursor.execute(query, params)
                
                if fetch == 'all':
                    result = cursor.fetchall()
                elif fetch == 'one':
                    result = cursor.fetchone()
                else:
                    result = cursor.rowcount
                
                conn.commit()
                return result
                
            except Exception as e:
                conn.rollback()
                logger.error(f"❌ خطأ في تنفيذ الاستعلام: {e}")
                logger.error(f"الاستعلام: {query}")
                logger.error(f"المعاملات: {params}")
                raise
    
    def execute_many(self, query: str, params_list: List[Tuple]) -> int:
        """تنفيذ عدة استعلامات في معاملة واحدة"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            try:
                cursor.executemany(query, params_list)
                conn.commit()
                return cursor.rowcount
                
            except Exception as e:
                conn.rollback()
                logger.error(f"❌ خطأ في تنفيذ الاستعلامات المتعددة: {e}")
                raise
    
    @contextmanager
    def transaction(self):
        """إدارة المعاملات يدوياً"""
        with self.get_connection() as conn:
            try:
                conn.execute("BEGIN")
                yield conn
                conn.commit()
            except Exception as e:
                conn.rollback()
                logger.error(f"❌ خطأ في المعاملة: {e}")
                raise
    
    def insert(self, table: str, data: Dict[str, Any]) -> int:
        """إدراج سجل جديد"""
        if not DatabaseModels.validate_data(table, data):
            raise ValueError(f"بيانات غير صحيحة للجدول {table}")
        
        # إضافة timestamp إذا لم يكن موجوداً
        if 'created_at' not in data:
            data['created_at'] = datetime.now().isoformat()
        if 'updated_at' not in data:
            data['updated_at'] = datetime.now().isoformat()
        
        columns = ', '.join(data.keys())
        placeholders = ', '.join(['?' for _ in data])
        query = f"INSERT INTO {table} ({columns}) VALUES ({placeholders})"
        
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(query, list(data.values()))
            conn.commit()
            return cursor.lastrowid
    
    def update(self, table: str, data: Dict[str, Any], where_clause: str, where_params: Tuple = ()) -> int:
        """تحديث سجل موجود"""
        # إضافة timestamp للتحديث
        data['updated_at'] = datetime.now().isoformat()
        
        set_clause = ', '.join([f"{key} = ?" for key in data.keys()])
        query = f"UPDATE {table} SET {set_clause} WHERE {where_clause}"
        
        params = list(data.values()) + list(where_params)
        return self.execute_query(query, params)
    
    def delete(self, table: str, where_clause: str, where_params: Tuple = ()) -> int:
        """حذف سجل"""
        query = f"DELETE FROM {table} WHERE {where_clause}"
        return self.execute_query(query, where_params)
    
    def select(self, table: str, columns: str = "*", where_clause: str = "", 
              where_params: Tuple = (), order_by: str = "", limit: int = None) -> List[sqlite3.Row]:
        """استعلام البيانات"""
        query = f"SELECT {columns} FROM {table}"
        
        if where_clause:
            query += f" WHERE {where_clause}"
        
        if order_by:
            query += f" ORDER BY {order_by}"
        
        if limit:
            query += f" LIMIT {limit}"
        
        return self.execute_query(query, where_params, fetch='all')
    
    def select_one(self, table: str, columns: str = "*", where_clause: str = "", 
                   where_params: Tuple = ()) -> Optional[sqlite3.Row]:
        """استعلام سجل واحد"""
        query = f"SELECT {columns} FROM {table}"
        
        if where_clause:
            query += f" WHERE {where_clause}"
        
        query += " LIMIT 1"
        
        return self.execute_query(query, where_params, fetch='one')
    
    def count(self, table: str, where_clause: str = "", where_params: Tuple = ()) -> int:
        """عد السجلات"""
        query = f"SELECT COUNT(*) FROM {table}"
        
        if where_clause:
            query += f" WHERE {where_clause}"
        
        result = self.execute_query(query, where_params, fetch='one')
        return result[0] if result else 0
    
    def table_exists(self, table_name: str) -> bool:
        """التحقق من وجود جدول"""
        query = "SELECT name FROM sqlite_master WHERE type='table' AND name=?"
        result = self.execute_query(query, (table_name,), fetch='one')
        return result is not None
    
    def get_table_info(self, table_name: str) -> List[sqlite3.Row]:
        """الحصول على معلومات الجدول"""
        query = f"PRAGMA table_info({table_name})"
        return self.execute_query(query, fetch='all')
    
    def vacuum(self):
        """تحسين قاعدة البيانات"""
        with self.get_connection() as conn:
            conn.execute("VACUUM")
            conn.commit()
        logger.info("✅ تم تحسين قاعدة البيانات")
    
    def backup_database(self, backup_path: str):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        with self.get_connection() as conn:
            backup_conn = sqlite3.connect(backup_path)
            conn.backup(backup_conn)
            backup_conn.close()
        logger.info(f"✅ تم إنشاء نسخة احتياطية: {backup_path}")
    
    def get_database_size(self) -> int:
        """الحصول على حجم قاعدة البيانات بالبايت"""
        return os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0
    
    def get_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات قاعدة البيانات"""
        stats = {
            'database_size': self.get_database_size(),
            'active_connections': self.current_connections,
            'tables': {}
        }
        
        # إحصائيات الجداول
        tables = ['users', 'wallets', 'transactions', 'admin_data', 'monitoring_data', 'ai_models', 'token_usage']
        for table in tables:
            if self.table_exists(table):
                stats['tables'][table] = self.count(table)
        
        return stats
