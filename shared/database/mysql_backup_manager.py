#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير النسخ الاحتياطي والاستعادة MySQL
يوفر نظام شامل للنسخ الاحتياطي التلقائي والاستعادة لقاعدة البيانات MySQL
"""

import os
import gzip
import json
import logging
import subprocess
import threading
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor
import schedule
import time

from .mysql_manager import mysql_manager
from .mysql_config import DATABASE_CONFIG, BACKUP_CONFIG

# إعداد نظام السجلات
logger = logging.getLogger(__name__)

class MySQLBackupManager:
    """مدير النسخ الاحتياطي والاستعادة MySQL"""
    
    def __init__(self):
        """تهيئة مدير النسخ الاحتياطي"""
        self.backup_path = Path(BACKUP_CONFIG['backup_path'])
        self.backup_path.mkdir(parents=True, exist_ok=True)
        
        self.executor = ThreadPoolExecutor(max_workers=3)
        self.scheduler_thread = None
        self.scheduler_running = False
        
        # إعداد مجلدات النسخ الاحتياطي
        self.daily_path = self.backup_path / "daily"
        self.weekly_path = self.backup_path / "weekly"
        self.monthly_path = self.backup_path / "monthly"
        self.manual_path = self.backup_path / "manual"
        
        for path in [self.daily_path, self.weekly_path, self.monthly_path, self.manual_path]:
            path.mkdir(exist_ok=True)
        
        logger.info("💾 تم تهيئة مدير النسخ الاحتياطي MySQL")
    
    def create_backup(self, backup_type: str = "manual", compress: bool = True) -> Tuple[bool, str, str]:
        """إنشاء نسخة احتياطية"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"backup_{backup_type}_{timestamp}"
            
            # تحديد مسار النسخة الاحتياطية
            if backup_type == "daily":
                backup_dir = self.daily_path
            elif backup_type == "weekly":
                backup_dir = self.weekly_path
            elif backup_type == "monthly":
                backup_dir = self.monthly_path
            else:
                backup_dir = self.manual_path
            
            backup_file = backup_dir / f"{backup_name}.sql"
            
            # تسجيل بداية النسخ الاحتياطي
            backup_id = self._log_backup_start(backup_name, backup_type)
            
            # إنشاء النسخة الاحتياطية باستخدام mysqldump
            success = self._create_mysql_dump(backup_file)
            
            if success:
                # ضغط الملف إذا كان مطلوباً
                if compress:
                    compressed_file = self._compress_backup(backup_file)
                    if compressed_file:
                        backup_file.unlink()  # حذف الملف غير المضغوط
                        backup_file = compressed_file
                
                # حساب حجم الملف
                file_size = backup_file.stat().st_size
                
                # تسجيل اكتمال النسخ الاحتياطي
                self._log_backup_complete(backup_id, str(backup_file), file_size)
                
                # تنظيف النسخ القديمة
                self._cleanup_old_backups(backup_type)
                
                logger.info(f"✅ تم إنشاء النسخة الاحتياطية: {backup_file}")
                return True, backup_name, str(backup_file)
            else:
                self._log_backup_failed(backup_id, "فشل في إنشاء mysqldump")
                return False, backup_name, "فشل في إنشاء النسخة الاحتياطية"
                
        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
            if 'backup_id' in locals():
                self._log_backup_failed(backup_id, str(e))
            return False, "", str(e)
    
    def _create_mysql_dump(self, backup_file: Path) -> bool:
        """إنشاء mysqldump"""
        try:
            # بناء أمر mysqldump
            cmd = [
                "mysqldump",
                f"--host={DATABASE_CONFIG['host']}",
                f"--port={DATABASE_CONFIG['port']}",
                f"--user={DATABASE_CONFIG['user']}",
                f"--password={DATABASE_CONFIG['password']}",
                "--single-transaction",
                "--routines",
                "--triggers",
                "--events",
                "--add-drop-table",
                "--add-locks",
                "--create-options",
                "--disable-keys",
                "--extended-insert",
                "--quick",
                "--lock-tables=false",
                DATABASE_CONFIG['database']
            ]
            
            # تنفيذ الأمر وحفظ النتيجة في الملف
            with open(backup_file, 'w', encoding='utf-8') as f:
                result = subprocess.run(
                    cmd,
                    stdout=f,
                    stderr=subprocess.PIPE,
                    text=True,
                    timeout=3600  # مهلة زمنية ساعة واحدة
                )
            
            if result.returncode == 0:
                return True
            else:
                logger.error(f"❌ خطأ في mysqldump: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("❌ انتهت المهلة الزمنية لـ mysqldump")
            return False
        except Exception as e:
            logger.error(f"❌ خطأ في تنفيذ mysqldump: {e}")
            return False
    
    def _compress_backup(self, backup_file: Path) -> Optional[Path]:
        """ضغط ملف النسخة الاحتياطية"""
        try:
            compressed_file = backup_file.with_suffix('.sql.gz')
            
            with open(backup_file, 'rb') as f_in:
                with gzip.open(compressed_file, 'wb') as f_out:
                    f_out.writelines(f_in)
            
            logger.debug(f"🗜️ تم ضغط النسخة الاحتياطية: {compressed_file}")
            return compressed_file
            
        except Exception as e:
            logger.error(f"❌ خطأ في ضغط النسخة الاحتياطية: {e}")
            return None
    
    def restore_backup(self, backup_file: str) -> Tuple[bool, str]:
        """استعادة نسخة احتياطية"""
        try:
            backup_path = Path(backup_file)
            
            if not backup_path.exists():
                return False, "ملف النسخة الاحتياطية غير موجود"
            
            # التحقق من نوع الملف
            if backup_path.suffix == '.gz':
                # ملف مضغوط
                return self._restore_compressed_backup(backup_path)
            elif backup_path.suffix == '.sql':
                # ملف SQL عادي
                return self._restore_sql_backup(backup_path)
            else:
                return False, "نوع ملف غير مدعوم"
                
        except Exception as e:
            logger.error(f"❌ خطأ في استعادة النسخة الاحتياطية: {e}")
            return False, str(e)
    
    def _restore_sql_backup(self, backup_file: Path) -> Tuple[bool, str]:
        """استعادة نسخة احتياطية SQL"""
        try:
            # بناء أمر mysql
            cmd = [
                "mysql",
                f"--host={DATABASE_CONFIG['host']}",
                f"--port={DATABASE_CONFIG['port']}",
                f"--user={DATABASE_CONFIG['user']}",
                f"--password={DATABASE_CONFIG['password']}",
                DATABASE_CONFIG['database']
            ]
            
            # تنفيذ الأمر
            with open(backup_file, 'r', encoding='utf-8') as f:
                result = subprocess.run(
                    cmd,
                    stdin=f,
                    stderr=subprocess.PIPE,
                    text=True,
                    timeout=3600
                )
            
            if result.returncode == 0:
                logger.info(f"✅ تم استعادة النسخة الاحتياطية: {backup_file}")
                return True, "تم استعادة النسخة الاحتياطية بنجاح"
            else:
                logger.error(f"❌ خطأ في استعادة النسخة الاحتياطية: {result.stderr}")
                return False, result.stderr
                
        except subprocess.TimeoutExpired:
            return False, "انتهت المهلة الزمنية لاستعادة النسخة الاحتياطية"
        except Exception as e:
            logger.error(f"❌ خطأ في استعادة النسخة الاحتياطية: {e}")
            return False, str(e)
    
    def _restore_compressed_backup(self, backup_file: Path) -> Tuple[bool, str]:
        """استعادة نسخة احتياطية مضغوطة"""
        try:
            # إلغاء ضغط الملف مؤقتاً
            temp_file = backup_file.with_suffix('')
            
            with gzip.open(backup_file, 'rb') as f_in:
                with open(temp_file, 'wb') as f_out:
                    f_out.write(f_in.read())
            
            # استعادة النسخة الاحتياطية
            success, message = self._restore_sql_backup(temp_file)
            
            # حذف الملف المؤقت
            temp_file.unlink()
            
            return success, message
            
        except Exception as e:
            logger.error(f"❌ خطأ في استعادة النسخة الاحتياطية المضغوطة: {e}")
            return False, str(e)
    
    def list_backups(self, backup_type: str = "all") -> List[Dict[str, Any]]:
        """عرض قائمة النسخ الاحتياطية"""
        try:
            backups = []
            
            if backup_type == "all":
                search_paths = [self.daily_path, self.weekly_path, self.monthly_path, self.manual_path]
                types = ["daily", "weekly", "monthly", "manual"]
            else:
                if backup_type == "daily":
                    search_paths = [self.daily_path]
                elif backup_type == "weekly":
                    search_paths = [self.weekly_path]
                elif backup_type == "monthly":
                    search_paths = [self.monthly_path]
                else:
                    search_paths = [self.manual_path]
                types = [backup_type]
            
            for path, backup_type_name in zip(search_paths, types):
                for backup_file in path.glob("backup_*.sql*"):
                    stat = backup_file.stat()
                    backups.append({
                        'name': backup_file.name,
                        'path': str(backup_file),
                        'type': backup_type_name,
                        'size': stat.st_size,
                        'size_mb': round(stat.st_size / (1024 * 1024), 2),
                        'created_at': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                        'compressed': backup_file.suffix == '.gz'
                    })
            
            # ترتيب حسب تاريخ الإنشاء
            backups.sort(key=lambda x: x['created_at'], reverse=True)
            
            return backups
            
        except Exception as e:
            logger.error(f"❌ خطأ في عرض قائمة النسخ الاحتياطية: {e}")
            return []
    
    def delete_backup(self, backup_path: str) -> bool:
        """حذف نسخة احتياطية"""
        try:
            backup_file = Path(backup_path)
            
            if backup_file.exists():
                backup_file.unlink()
                logger.info(f"🗑️ تم حذف النسخة الاحتياطية: {backup_path}")
                return True
            else:
                logger.warning(f"⚠️ النسخة الاحتياطية غير موجودة: {backup_path}")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطأ في حذف النسخة الاحتياطية: {e}")
            return False
    
    def _cleanup_old_backups(self, backup_type: str):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            if backup_type == "daily":
                max_backups = 7  # الاحتفاظ بـ 7 نسخ يومية
                search_path = self.daily_path
            elif backup_type == "weekly":
                max_backups = 4  # الاحتفاظ بـ 4 نسخ أسبوعية
                search_path = self.weekly_path
            elif backup_type == "monthly":
                max_backups = 12  # الاحتفاظ بـ 12 نسخة شهرية
                search_path = self.monthly_path
            else:
                max_backups = BACKUP_CONFIG.get('max_backup_files', 10)
                search_path = self.manual_path
            
            # الحصول على قائمة النسخ الاحتياطية مرتبة حسب التاريخ
            backups = list(search_path.glob("backup_*.sql*"))
            backups.sort(key=lambda x: x.stat().st_ctime, reverse=True)
            
            # حذف النسخ الزائدة
            for backup in backups[max_backups:]:
                backup.unlink()
                logger.info(f"🧹 تم حذف النسخة الاحتياطية القديمة: {backup.name}")
                
        except Exception as e:
            logger.error(f"❌ خطأ في تنظيف النسخ الاحتياطية القديمة: {e}")
    
    def _log_backup_start(self, backup_name: str, backup_type: str) -> int:
        """تسجيل بداية النسخ الاحتياطي"""
        try:
            query = """
            INSERT INTO backup_history (backup_name, backup_type, status)
            VALUES (%(backup_name)s, %(backup_type)s, 'started')
            """
            
            mysql_manager.execute_update(query, {
                'backup_name': backup_name,
                'backup_type': backup_type
            })
            
            # الحصول على معرف السجل
            result = mysql_manager.execute_query(
                "SELECT LAST_INSERT_ID() as id"
            )
            
            return result[0]['id'] if result else 0
            
        except Exception as e:
            logger.error(f"❌ خطأ في تسجيل بداية النسخ الاحتياطي: {e}")
            return 0
    
    def _log_backup_complete(self, backup_id: int, backup_path: str, file_size: int):
        """تسجيل اكتمال النسخ الاحتياطي"""
        try:
            query = """
            UPDATE backup_history 
            SET status = 'completed', backup_path = %(backup_path)s, 
                backup_size = %(file_size)s, completed_at = NOW()
            WHERE id = %(backup_id)s
            """
            
            mysql_manager.execute_update(query, {
                'backup_id': backup_id,
                'backup_path': backup_path,
                'file_size': file_size
            })
            
        except Exception as e:
            logger.error(f"❌ خطأ في تسجيل اكتمال النسخ الاحتياطي: {e}")
    
    def _log_backup_failed(self, backup_id: int, error_message: str):
        """تسجيل فشل النسخ الاحتياطي"""
        try:
            query = """
            UPDATE backup_history 
            SET status = 'failed', error_message = %(error_message)s, completed_at = NOW()
            WHERE id = %(backup_id)s
            """
            
            mysql_manager.execute_update(query, {
                'backup_id': backup_id,
                'error_message': error_message
            })
            
        except Exception as e:
            logger.error(f"❌ خطأ في تسجيل فشل النسخ الاحتياطي: {e}")
    
    def start_scheduler(self):
        """بدء جدولة النسخ الاحتياطي التلقائي"""
        if self.scheduler_running:
            return
        
        # جدولة النسخ الاحتياطية
        schedule.every().day.at("02:00").do(self._scheduled_daily_backup)
        schedule.every().sunday.at("03:00").do(self._scheduled_weekly_backup)
        schedule.every().month.do(self._scheduled_monthly_backup)
        
        self.scheduler_running = True
        self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()
        
        logger.info("⏰ تم بدء جدولة النسخ الاحتياطي التلقائي")
    
    def stop_scheduler(self):
        """إيقاف جدولة النسخ الاحتياطي التلقائي"""
        self.scheduler_running = False
        schedule.clear()
        logger.info("⏹️ تم إيقاف جدولة النسخ الاحتياطي التلقائي")
    
    def _run_scheduler(self):
        """تشغيل المجدول"""
        while self.scheduler_running:
            schedule.run_pending()
            time.sleep(60)  # فحص كل دقيقة
    
    def _scheduled_daily_backup(self):
        """نسخة احتياطية يومية مجدولة"""
        logger.info("📅 بدء النسخة الاحتياطية اليومية المجدولة")
        self.create_backup("daily", compress=True)
    
    def _scheduled_weekly_backup(self):
        """نسخة احتياطية أسبوعية مجدولة"""
        logger.info("📅 بدء النسخة الاحتياطية الأسبوعية المجدولة")
        self.create_backup("weekly", compress=True)
    
    def _scheduled_monthly_backup(self):
        """نسخة احتياطية شهرية مجدولة"""
        logger.info("📅 بدء النسخة الاحتياطية الشهرية المجدولة")
        self.create_backup("monthly", compress=True)

# إنشاء مثيل مشترك
mysql_backup_manager = MySQLBackupManager()
