#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
حماية البيانات
نظام متخصص لحماية وتشفير بيانات البوت
"""

import os
import json
import hashlib
import base64
from datetime import datetime
from telegram import Update
from telegram.ext import ContextTypes
from telegram.constants import ParseMode
import logging

logger = logging.getLogger(__name__)

class DataProtection:
    """فئة حماية البيانات"""
    
    def __init__(self, bot_instance):
        self.bot = bot_instance
        self.protection_config_file = "security/data_protection_config.json"
        self.sensitive_files = [
            "data/users_data.json",
            "data/bot_stats.json",
            "security/admin_sessions.json",
            "backups/backup_config.json"
        ]
        
        # إعدادات الحماية
        self.protection_settings = {
            "encrypt_sensitive_data": True,
            "backup_before_changes": True,
            "log_data_access": True,
            "validate_data_integrity": True,
            "auto_cleanup_old_logs": True
        }
        
        self.load_protection_config()
    
    def load_protection_config(self):
        """تحميل إعدادات الحماية"""
        try:
            if os.path.exists(self.protection_config_file):
                with open(self.protection_config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.protection_settings.update(config)
            else:
                self.save_protection_config()
        except Exception as e:
            logger.error(f"خطأ في تحميل إعدادات الحماية: {e}")
    
    def save_protection_config(self):
        """حفظ إعدادات الحماية"""
        try:
            os.makedirs(os.path.dirname(self.protection_config_file), exist_ok=True)
            with open(self.protection_config_file, 'w', encoding='utf-8') as f:
                json.dump(self.protection_settings, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"خطأ في حفظ إعدادات الحماية: {e}")
    
    def generate_file_hash(self, file_path: str) -> str:
        """إنشاء hash للملف للتحقق من سلامته"""
        try:
            if not os.path.exists(file_path):
                return ""
            
            with open(file_path, 'rb') as f:
                file_content = f.read()
                return hashlib.sha256(file_content).hexdigest()
        except Exception as e:
            logger.error(f"خطأ في إنشاء hash للملف {file_path}: {e}")
            return ""
    
    def verify_file_integrity(self, file_path: str, expected_hash: str) -> bool:
        """التحقق من سلامة الملف"""
        try:
            current_hash = self.generate_file_hash(file_path)
            return current_hash == expected_hash
        except Exception as e:
            logger.error(f"خطأ في التحقق من سلامة الملف {file_path}: {e}")
            return False
    
    async def create_secure_backup(self, file_path: str) -> str:
        """إنشاء نسخة احتياطية آمنة"""
        try:
            if not os.path.exists(file_path):
                return ""
            
            # إنشاء اسم النسخة الاحتياطية
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"{os.path.basename(file_path)}.backup_{timestamp}"
            backup_path = os.path.join("security/backups", backup_name)
            
            # إنشاء مجلد النسخ الاحتياطية
            os.makedirs(os.path.dirname(backup_path), exist_ok=True)
            
            # نسخ الملف
            import shutil
            shutil.copy2(file_path, backup_path)
            
            # إنشاء hash للنسخة الاحتياطية
            backup_hash = self.generate_file_hash(backup_path)
            
            # حفظ معلومات النسخة الاحتياطية
            backup_info = {
                "original_file": file_path,
                "backup_file": backup_path,
                "created_at": datetime.now().isoformat(),
                "file_hash": backup_hash,
                "file_size": os.path.getsize(backup_path)
            }
            
            # حفظ معلومات النسخة الاحتياطية
            backup_info_file = f"{backup_path}.info"
            with open(backup_info_file, 'w', encoding='utf-8') as f:
                json.dump(backup_info, f, ensure_ascii=False, indent=2)
            
            logger.info(f"تم إنشاء نسخة احتياطية آمنة: {backup_path}")
            return backup_path
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء نسخة احتياطية آمنة: {e}")
            return ""
    
    def encrypt_sensitive_data(self, data: str) -> str:
        """تشفير البيانات الحساسة"""
        try:
            # تشفير بسيط باستخدام base64 (يمكن تحسينه لاحقاً)
            encoded_data = base64.b64encode(data.encode('utf-8')).decode('utf-8')
            return encoded_data
        except Exception as e:
            logger.error(f"خطأ في تشفير البيانات: {e}")
            return data
    
    def decrypt_sensitive_data(self, encrypted_data: str) -> str:
        """فك تشفير البيانات الحساسة"""
        try:
            decoded_data = base64.b64decode(encrypted_data.encode('utf-8')).decode('utf-8')
            return decoded_data
        except Exception as e:
            logger.error(f"خطأ في فك تشفير البيانات: {e}")
            return encrypted_data
    
    async def log_data_access(self, update: Update, context: ContextTypes.DEFAULT_TYPE, 
                             action: str, file_path: str, details: str = ""):
        """تسجيل الوصول للبيانات"""
        try:
            if not self.protection_settings.get("log_data_access", True):
                return
            
            user_id = update.effective_user.id
            username = update.effective_user.username or "غير محدد"
            
            log_entry = {
                "timestamp": datetime.now().isoformat(),
                "user_id": user_id,
                "username": username,
                "action": action,
                "file_path": file_path,
                "details": details,
                "ip_info": "غير متاح"  # يمكن إضافة معلومات IP لاحقاً
            }
            
            # حفظ السجل
            log_file = "security/data_access_log.json"
            os.makedirs(os.path.dirname(log_file), exist_ok=True)
            
            logs = []
            if os.path.exists(log_file):
                with open(log_file, 'r', encoding='utf-8') as f:
                    logs = json.load(f)
            
            logs.append(log_entry)
            
            # الاحتفاظ بآخر 1000 سجل فقط
            if len(logs) > 1000:
                logs = logs[-1000:]
            
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(logs, f, ensure_ascii=False, indent=2)
            
            logger.info(f"تم تسجيل وصول البيانات: {action} - {file_path}")
            
        except Exception as e:
            logger.error(f"خطأ في تسجيل الوصول للبيانات: {e}")
    
    async def validate_data_operation(self, update: Update, context: ContextTypes.DEFAULT_TYPE, 
                                    operation: str, file_path: str) -> bool:
        """التحقق من صحة عملية البيانات"""
        try:
            user_id = update.effective_user.id
            
            # التحقق من صلاحية المدير
            if not self.bot.is_admin(user_id):
                await self.log_data_access(update, context, f"UNAUTHORIZED_{operation}", 
                                         file_path, "محاولة وصول غير مصرح")
                return False
            
            # التحقق من وضع المدير للعمليات الحساسة
            sensitive_operations = ["DELETE", "MODIFY", "EXPORT"]
            if operation in sensitive_operations:
                if not self.bot.is_in_admin_mode(context):
                    await self.log_data_access(update, context, f"NO_ADMIN_MODE_{operation}", 
                                             file_path, "محاولة عملية حساسة بدون وضع المدير")
                    return False
            
            # إنشاء نسخة احتياطية قبل العمليات المدمرة
            if operation in ["DELETE", "MODIFY"] and self.protection_settings.get("backup_before_changes", True):
                backup_path = await self.create_secure_backup(file_path)
                if backup_path:
                    await self.log_data_access(update, context, f"BACKUP_CREATED", 
                                             backup_path, f"نسخة احتياطية قبل {operation}")
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من عملية البيانات: {e}")
            return False
    
    def scan_for_sensitive_data(self, text: str) -> list:
        """فحص النص للبحث عن بيانات حساسة"""
        try:
            sensitive_patterns = [
                "password", "token", "key", "secret", "api",
                "كلمة مرور", "رمز", "مفتاح", "سري"
            ]
            
            found_patterns = []
            text_lower = text.lower()
            
            for pattern in sensitive_patterns:
                if pattern in text_lower:
                    found_patterns.append(pattern)
            
            return found_patterns
            
        except Exception as e:
            logger.error(f"خطأ في فحص البيانات الحساسة: {e}")
            return []
    
    async def secure_file_operation(self, update: Update, context: ContextTypes.DEFAULT_TYPE,
                                  operation: str, file_path: str, data: str = None) -> bool:
        """تنفيذ عملية ملف آمنة"""
        try:
            # التحقق من صحة العملية
            if not await self.validate_data_operation(update, context, operation, file_path):
                return False
            
            # تسجيل العملية
            await self.log_data_access(update, context, operation, file_path)
            
            # تنفيذ العملية حسب النوع
            if operation == "READ":
                return os.path.exists(file_path)
            
            elif operation == "WRITE":
                if data is not None:
                    # فحص البيانات الحساسة
                    sensitive_data = self.scan_for_sensitive_data(data)
                    if sensitive_data and self.protection_settings.get("encrypt_sensitive_data", True):
                        data = self.encrypt_sensitive_data(data)
                    
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(data)
                    return True
            
            elif operation == "DELETE":
                if os.path.exists(file_path):
                    os.remove(file_path)
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"خطأ في تنفيذ عملية الملف الآمنة: {e}")
            return False
    
    def get_protection_status(self) -> dict:
        """الحصول على حالة الحماية"""
        try:
            status = {
                "protection_enabled": True,
                "settings": self.protection_settings.copy(),
                "protected_files": len(self.sensitive_files),
                "backup_count": 0,
                "log_entries": 0
            }
            
            # حساب عدد النسخ الاحتياطية
            backup_dir = "security/backups"
            if os.path.exists(backup_dir):
                backup_files = [f for f in os.listdir(backup_dir) if f.endswith('.backup')]
                status["backup_count"] = len(backup_files)
            
            # حساب عدد سجلات الوصول
            log_file = "security/data_access_log.json"
            if os.path.exists(log_file):
                with open(log_file, 'r', encoding='utf-8') as f:
                    logs = json.load(f)
                    status["log_entries"] = len(logs)
            
            return status
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على حالة الحماية: {e}")
            return {"protection_enabled": False}
    
    async def generate_protection_report(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """إنشاء تقرير حماية البيانات"""
        try:
            status = self.get_protection_status()
            
            report = f"""
<b>🛡️ تقرير حماية البيانات</b>

📊 <b>الحالة العامة:</b>
• نظام الحماية: {'🟢 مفعل' if status['protection_enabled'] else '🔴 معطل'}
• الملفات المحمية: {status['protected_files']}
• النسخ الاحتياطية: {status['backup_count']}
• سجلات الوصول: {status['log_entries']}

⚙️ <b>إعدادات الحماية:</b>
• تشفير البيانات الحساسة: {'🟢 مفعل' if status['settings']['encrypt_sensitive_data'] else '🔴 معطل'}
• النسخ الاحتياطي التلقائي: {'🟢 مفعل' if status['settings']['backup_before_changes'] else '🔴 معطل'}
• تسجيل الوصول: {'🟢 مفعل' if status['settings']['log_data_access'] else '🔴 معطل'}
• التحقق من سلامة البيانات: {'🟢 مفعل' if status['settings']['validate_data_integrity'] else '🔴 معطل'}

🔒 <b>مستوى الأمان: عالي</b>
            """
            
            await update.message.reply_text(
                report,
                parse_mode=ParseMode.HTML,
                reply_markup=self.bot.get_admin_keyboard()
            )
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء تقرير حماية البيانات: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في إنشاء تقرير الحماية",
                reply_markup=self.bot.get_admin_keyboard()
            )
    
    async def cleanup_old_backups(self, days_to_keep: int = 30):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            backup_dir = "security/backups"
            if not os.path.exists(backup_dir):
                return
            
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            deleted_count = 0
            
            for filename in os.listdir(backup_dir):
                file_path = os.path.join(backup_dir, filename)
                
                if os.path.isfile(file_path):
                    file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                    
                    if file_time < cutoff_date:
                        os.remove(file_path)
                        deleted_count += 1
            
            logger.info(f"تم حذف {deleted_count} نسخة احتياطية قديمة")
            
        except Exception as e:
            logger.error(f"خطأ في تنظيف النسخ الاحتياطية القديمة: {e}")
