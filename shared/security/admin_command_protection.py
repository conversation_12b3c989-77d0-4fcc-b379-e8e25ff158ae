#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
حماية أوامر المدير
نظام متخصص لحماية جميع أوامر وأزرار المدير
"""

import logging
from telegram import Update
from telegram.ext import ContextTypes
from telegram.constants import ParseMode

logger = logging.getLogger(__name__)

class AdminCommandProtection:
    """فئة حماية أوامر المدير"""
    
    def __init__(self, bot_instance):
        self.bot = bot_instance
        
        # قائمة شاملة بجميع أوامر وأزرار المدير المحمية
        self.protected_commands = {
            # أوامر المدير الأساسية
            "admin_commands": [
                "/admin", "/مدير", "مدير", "/Amin"
            ],
            
            # أزرار المدير الرئيسية
            "main_admin_buttons": [
                # مع أيقونات
                "👥 إدارة المستخدمين", "📰 إدارة النشر", "⚙️ خيارات متقدمة", "🚪 خروج من وضع المدير",
                # بدون أيقونات
                "إدارة المستخدمين", "إدارة النشر", "خيارات متقدمة", "خروج من وضع المدير"
            ],
            
            # أزرار إدارة المستخدمين
            "user_management_buttons": [
                # مع أيقونات
                "👥 المستخدمين", "➖ إزالة مستخدم", "➕ إضافة مستخدم", "🚫 حظر مستخدم",
                "⚠️ تقييد مستخدم", "🔍 بحث مستخدم",
                # بدون أيقونات
                "المستخدمين", "إزالة مستخدم", "إضافة مستخدم", "حظر مستخدم",
                "تقييد مستخدم", "بحث مستخدم"
            ],
            
            # أزرار إدارة النشرات
            "newsletter_buttons": [
                # مع أيقونات
                "📝 نشرة جديدة", "📋 نشرة سابقة",
                # بدون أيقونات
                "نشرة جديدة", "نشرة سابقة"
            ],
            
            # أزرار الخيارات المتقدمة الجديدة
            "advanced_options_buttons": [
                # مع أيقونات
                "📈 إدارة الإحصائيات", "📤 إدارة التصدير", "🗄️ إدارة النسخ الاحتياطية",
                # بدون أيقونات
                "إدارة الإحصائيات", "إدارة التصدير", "إدارة النسخ الاحتياطية"
            ],
            
            # أزرار إدارة الإحصائيات
            "statistics_buttons": [
                # مع أيقونات
                "📅 إحصائية يوم", "📆 إحصائية أسبوع", "🗓️ إحصائية شهر",
                "📊 إحصائية شاملة", "📈 رسم بياني", "📋 تقرير مفصل",
                # بدون أيقونات
                "إحصائية يوم", "إحصائية أسبوع", "إحصائية شهر",
                "إحصائية شاملة", "رسم بياني", "تقرير مفصل"
            ],
            
            # أزرار إدارة التصدير
            "export_buttons": [
                # مع أيقونات
                "📄 تصدير PDF", "📊 تصدير Excel", "📝 تصدير Word",
                "📋 عرض الملفات", "🗑️ حذف الملفات القديمة",
                # بدون أيقونات
                "تصدير PDF", "تصدير Excel", "تصدير Word",
                "عرض الملفات", "حذف الملفات القديمة"
            ],
            
            # أزرار إدارة النسخ الاحتياطية
            "backup_buttons": [
                # مع أيقونات
                "💾 نسخة فورية", "📅 نسخة يومية", "📆 نسخة أسبوعية", "🗓️ نسخة شهرية",
                "📋 عرض النسخ", "🗑️ حذف النسخ القديمة",
                # بدون أيقونات
                "نسخة فورية", "نسخة يومية", "نسخة أسبوعية", "نسخة شهرية",
                "عرض النسخ", "حذف النسخ القديمة"
            ],
            
            # أزرار العودة
            "return_buttons": [
                "🔙 العودة للخيارات المتقدمة", "🔙 العودة لقائمة المدير",
                "🔙 العودة للقائمة السابقة", "العودة للخيارات المتقدمة",
                "العودة لقائمة المدير", "العودة للقائمة السابقة"
            ]
        }
        
        # دمج جميع الأزرار في قائمة واحدة للفحص السريع
        self.all_protected_items = []
        for category, items in self.protected_commands.items():
            self.all_protected_items.extend(items)
    
    def is_protected_command(self, text: str) -> bool:
        """فحص ما إذا كان النص أمر أو زر محمي"""
        return text in self.all_protected_items
    
    def get_protection_category(self, text: str) -> str:
        """الحصول على فئة الحماية للنص"""
        for category, items in self.protected_commands.items():
            if text in items:
                return category
        return "unknown"
    
    async def check_command_permission(self, update: Update, context: ContextTypes.DEFAULT_TYPE, command: str) -> bool:
        """فحص صلاحية تنفيذ أمر معين"""
        try:
            user_id = update.effective_user.id
            
            # فحص ما إذا كان الأمر محمي
            if not self.is_protected_command(command):
                return True  # الأمر غير محمي، يمكن تنفيذه
            
            # فحص صلاحية المدير
            if not self.bot.is_admin(user_id):
                await self.send_no_permission_message(update, context, "not_admin")
                return False
            
            # فحص وضع المدير للأوامر التي تتطلب وضع المدير
            category = self.get_protection_category(command)
            if self.requires_admin_mode(category):
                if not self.bot.is_in_admin_mode(context):
                    await self.send_no_permission_message(update, context, "not_in_admin_mode")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في فحص صلاحية الأمر {command}: {e}")
            return False
    
    def requires_admin_mode(self, category: str) -> bool:
        """فحص ما إذا كانت الفئة تتطلب وضع المدير"""
        # الأوامر الأساسية لا تتطلب وضع المدير (مثل /admin)
        non_admin_mode_categories = ["admin_commands"]
        return category not in non_admin_mode_categories
    
    async def send_no_permission_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE, reason: str):
        """إرسال رسالة عدم الصلاحية المناسبة"""
        try:
            if reason == "not_admin":
                message = "❌ غير متاح لك استخدام هذا الأمر - ليس لديك صلاحية مدير"
                keyboard = self.bot.get_reply_keyboard()
            elif reason == "not_in_admin_mode":
                message = "❌ هذا الأمر متاح في وضع المدير فقط"
                keyboard = self.bot.get_reply_keyboard()
            else:
                message = "❌ غير مسموح لك بتنفيذ هذا الأمر"
                keyboard = self.bot.get_reply_keyboard()
            
            # استخدام الإرسال الآمن
            from security.admin_security import AdminSecurity
            admin_security = AdminSecurity(self.bot)
            await admin_security.safe_send_message(update, message, reply_markup=keyboard)
            
        except Exception as e:
            logger.error(f"خطأ في إرسال رسالة عدم الصلاحية: {e}")
    
    async def validate_admin_button_access(self, update: Update, context: ContextTypes.DEFAULT_TYPE, button_text: str) -> bool:
        """التحقق من صلاحية الوصول لزر المدير"""
        try:
            user_id = update.effective_user.id
            
            # فحص ما إذا كان الزر محمي
            if not self.is_protected_command(button_text):
                return True
            
            # فحص صلاحية المدير
            if not self.bot.is_admin(user_id):
                await self.send_no_permission_message(update, context, "not_admin")
                return False
            
            # فحص وضع المدير
            category = self.get_protection_category(button_text)
            if self.requires_admin_mode(category):
                if not self.bot.is_in_admin_mode(context):
                    await self.send_no_permission_message(update, context, "not_in_admin_mode")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من صلاحية الزر {button_text}: {e}")
            return False
    
    def get_protection_stats(self) -> dict:
        """الحصول على إحصائيات الحماية"""
        stats = {
            "total_protected_items": len(self.all_protected_items),
            "categories": {}
        }
        
        for category, items in self.protected_commands.items():
            stats["categories"][category] = {
                "count": len(items),
                "items": items
            }
        
        return stats
    
    def add_protected_item(self, item: str, category: str = "custom"):
        """إضافة عنصر جديد للحماية"""
        if category not in self.protected_commands:
            self.protected_commands[category] = []
        
        if item not in self.protected_commands[category]:
            self.protected_commands[category].append(item)
            self.all_protected_items.append(item)
            logger.info(f"تم إضافة عنصر محمي جديد: {item} في فئة {category}")
    
    def remove_protected_item(self, item: str):
        """إزالة عنصر من الحماية"""
        for category, items in self.protected_commands.items():
            if item in items:
                items.remove(item)
                if item in self.all_protected_items:
                    self.all_protected_items.remove(item)
                logger.info(f"تم إزالة العنصر المحمي: {item} من فئة {category}")
                break
    
    async def log_protection_event(self, update: Update, context: ContextTypes.DEFAULT_TYPE, event_type: str, details: str):
        """تسجيل أحداث الحماية"""
        try:
            user_id = update.effective_user.id
            username = update.effective_user.username or "غير محدد"
            
            log_message = f"""
🛡️ حدث حماية: {event_type}
👤 المستخدم: @{username} ({user_id})
📝 التفاصيل: {details}
⏰ الوقت: {update.message.date}
            """
            
            logger.warning(log_message)
            
            # إرسال تنبيه للمدير إذا لزم الأمر
            if event_type == "unauthorized_access":
                from monitoring.bot_logger import bot_logger
                await bot_logger.log_security_event(update, f"محاولة وصول غير مصرح: {details}")
                
        except Exception as e:
            logger.error(f"خطأ في تسجيل حدث الحماية: {e}")
    
    def get_category_description(self, category: str) -> str:
        """الحصول على وصف الفئة"""
        descriptions = {
            "admin_commands": "أوامر المدير الأساسية",
            "main_admin_buttons": "أزرار المدير الرئيسية",
            "user_management_buttons": "أزرار إدارة المستخدمين",
            "newsletter_buttons": "أزرار إدارة النشرات",
            "advanced_options_buttons": "أزرار الخيارات المتقدمة",
            "statistics_buttons": "أزرار إدارة الإحصائيات",
            "export_buttons": "أزرار إدارة التصدير",
            "backup_buttons": "أزرار إدارة النسخ الاحتياطية",
            "return_buttons": "أزرار العودة"
        }
        return descriptions.get(category, "فئة غير معروفة")
    
    async def generate_protection_report(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """إنشاء تقرير شامل عن نظام الحماية"""
        try:
            stats = self.get_protection_stats()
            
            report = f"""
<b>🛡️ تقرير نظام الحماية</b>

📊 <b>الإحصائيات العامة:</b>
• إجمالي العناصر المحمية: {stats['total_protected_items']}
• عدد الفئات: {len(stats['categories'])}

📋 <b>تفصيل الفئات:</b>
            """
            
            for category, data in stats['categories'].items():
                description = self.get_category_description(category)
                report += f"\n• {description}: {data['count']} عنصر"
            
            report += f"""

🔒 <b>مستويات الحماية:</b>
• التحقق من صلاحية المدير ✅
• التحقق من وضع المدير ✅
• حماية الأزرار مع وبدون أيقونات ✅
• رسائل موحدة ومناسبة ✅
• تسجيل أحداث الحماية ✅

💡 <b>النظام يعمل بكفاءة عالية</b>
            """
            
            await update.message.reply_text(
                report,
                parse_mode=ParseMode.HTML,
                reply_markup=self.bot.get_admin_keyboard()
            )
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء تقرير الحماية: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في إنشاء تقرير الحماية",
                reply_markup=self.bot.get_admin_keyboard()
            )
