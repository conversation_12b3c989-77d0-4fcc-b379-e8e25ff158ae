#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام حماية وضع المدير
يحتوي على جميع دوال التحقق من الصلاحيات والحماية الأمنية
"""

import logging
import asyncio
from telegram import Update
from telegram.ext import ContextTypes
from telegram.error import TimedOut, NetworkError, RetryAfter

logger = logging.getLogger(__name__)

class AdminSecurity:
    """فئة حماية وضع المدير"""
    
    def __init__(self, bot_instance):
        self.bot = bot_instance
    
    def is_admin(self, user_id):
        """التحقق من صلاحيات المدير"""
        return self.bot.is_admin(user_id)
    
    def is_in_admin_mode(self, context):
        """التحقق من وجود المستخدم في وضع المدير"""
        return self.bot.is_in_admin_mode(context)
    
    def is_in_any_admin_mode(self, context):
        """التحقق من وجود المستخدم في أي وضع من أوضاع المدير"""
        admin_modes = [
            "admin_mode",
            "user_management_mode", 
            "newsletter_management_mode",
            "advanced_options_mode",
            "newsletter_confirmation_mode",
            "add_user_mode",
            "remove_user_mode", 
            "ban_user_mode",
            "restrict_user_mode",
            "admin_search_mode",
            "admin_newsletter_mode"
        ]
        
        return any(context.user_data.get(mode, False) for mode in admin_modes)
    
    async def check_admin_permission(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """
        التحقق من صلاحية المدير
        إرجاع True إذا كان مدير، False مع إرسال رسالة رفض إذا لم يكن مدير
        """
        if self.is_admin(update.effective_user.id):
            return True

        await self.safe_send_message(
            update,
            "❌ غير متاح لك استخدام هذا الأمر - ليس لديك صلاحية مدير",
            reply_markup=self.bot.get_reply_keyboard()
        )
        return False
    
    async def check_admin_mode_permission(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """
        التحقق من صلاحية المدير ووضع المدير
        إرجاع True إذا كان مدير في وضع المدير، False مع رسالة مناسبة
        """
        if not self.is_admin(update.effective_user.id):
            await self.safe_send_message(
                update,
                "❌ غير متاح لك استخدام هذا الأمر - ليس لديك صلاحية مدير",
                reply_markup=self.bot.get_reply_keyboard()
            )
            return False

        if not self.is_in_admin_mode(context):
            await self.safe_send_message(
                update,
                "❌ هذا الأمر متاح في وضع المدير فقط",
                reply_markup=self.bot.get_reply_keyboard()
            )
            return False

        return True
    
    def clear_all_admin_modes(self, context):
        """إلغاء جميع أوضاع المدير"""
        admin_modes = [
            "admin_mode",
            "user_management_mode",
            "newsletter_management_mode", 
            "advanced_options_mode",
            "newsletter_confirmation_mode",
            "add_user_mode",
            "remove_user_mode",
            "ban_user_mode", 
            "restrict_user_mode",
            "admin_search_mode",
            "admin_newsletter_mode"
        ]
        
        for mode in admin_modes:
            context.user_data.pop(mode, None)
        
        # حذف أي نشرة مؤقتة
        if "pending_newsletter" in context.user_data:
            del context.user_data["pending_newsletter"]
    
    async def secure_admin_button(self, update: Update, context: ContextTypes.DEFAULT_TYPE, button_text: str, action_func):
        """
        تأمين زر المدير - التحقق من الصلاحية قبل تنفيذ الإجراء
        
        Args:
            update: تحديث التليجرام
            context: سياق المحادثة
            button_text: نص الزر للسجلات
            action_func: الدالة المراد تنفيذها إذا كانت الصلاحية صحيحة
        """
        if await self.check_admin_permission(update, context):
            logger.info(f"Admin {update.effective_user.id} used button: {button_text}")
            await action_func(update, context)
        else:
            logger.warning(f"Non-admin user {update.effective_user.id} tried to use button: {button_text}")
    
    async def secure_admin_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE, command_text: str, action_func):
        """
        تأمين أمر المدير - التحقق من الصلاحية ووضع المدير قبل تنفيذ الإجراء
        
        Args:
            update: تحديث التليجرام
            context: سياق المحادثة  
            command_text: نص الأمر للسجلات
            action_func: الدالة المراد تنفيذها إذا كانت الصلاحية صحيحة
        """
        if await self.check_admin_mode_permission(update, context):
            logger.info(f"Admin {update.effective_user.id} used command: {command_text}")
            await action_func(update, context)
        else:
            logger.warning(f"User {update.effective_user.id} tried to use admin command: {command_text}")
    
    async def handle_exit_commands(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """
        معالجة أوامر الخروج (ابدا / start) من أي وضع مدير
        إرجاع True إذا تم التعامل مع الأمر، False إذا لم يكن في وضع مدير
        """
        if self.is_in_any_admin_mode(context):
            # إلغاء جميع أوضاع المدير
            self.clear_all_admin_modes(context)
            
            # إرسال رسالة الخروج من المدير
            await self.safe_send_message(
                update,
                "🔙 تم الخروج من وضع المدير والعودة لوضع المستخدم",
                reply_markup=self.bot.get_reply_keyboard()
            )

            # إرسال رسالة الترحيب بعد ثانية واحدة
            await asyncio.sleep(1)

            welcome_message = """
<b>أهلاً بعودتك إلى البوت الشخصي لصلاح الدين الدروبي</b>

مرحباً بك مرة أخرى! 🙏

يسعدني رؤيتك مجدداً، ويمكنك الاستمرار في استكشاف جميع الأقسام المتاحة.

💡 <b>جديد</b>: تم تحسين البوت وإضافة ميزات جديدة لتجربة أفضل

أهلاً بك مرة أخرى وأتمنى لك تجربة رائعة! 🌟
            """

            await self.safe_send_message(
                update,
                welcome_message,
                reply_markup=self.bot.get_reply_keyboard(),
                parse_mode='HTML'
            )
            
            logger.info(f"User {update.effective_user.id} exited admin mode")
            return True
        
        return False
    
    async def handle_admin_entry_commands(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """
        معالجة أوامر الدخول لوضع المدير (مدير / admin)
        إرجاع True إذا تم التعامل مع الأمر، False إذا لم يكن مدير
        """
        if not self.is_admin(update.effective_user.id):
            await self.safe_send_message(
                update,
                "❌ غير متاح لك استخدام هذا الأمر - ليس لديك صلاحية مدير",
                reply_markup=self.bot.get_reply_keyboard()
            )
            return True
        
        # إذا كان في وضع المستخدم أو أي وضع غير المدير الرئيسي، أرسل رسالة ترحيب أولاً
        if (not self.is_in_admin_mode(context) or 
            context.user_data.get("user_management_mode", False) or
            context.user_data.get("newsletter_management_mode", False) or
            context.user_data.get("advanced_options_mode", False)):
            
            # إلغاء جميع الأوضاع الفرعية
            self.clear_all_admin_modes(context)
            
            # إذا لم يكن في وضع المدير أصلاً، أرسل رسالة ترحيب
            if not self.is_in_admin_mode(context):
                welcome_message = """
<b>أهلاً بعودتك إلى البوت الشخصي لصلاح الدين الدروبي</b>

مرحباً بك مرة أخرى! 🙏

يسعدني رؤيتك مجدداً، ويمكنك الاستمرار في استكشاف جميع الأقسام المتاحة.

💡 <b>جديد</b>: تم تحسين البوت وإضافة ميزات جديدة لتجربة أفضل

أهلاً بك مرة أخرى وأتمنى لك تجربة رائعة! 🌟
                """
                
                await self.safe_send_message(
                    update,
                    welcome_message,
                    reply_markup=self.bot.get_reply_keyboard(),
                    parse_mode='HTML'
                )

                # انتظار ثانية واحدة ثم إرسال رسالة المدير
                await asyncio.sleep(1)
        
        await self.bot.admin_help_command(update, context)
        logger.info(f"Admin {update.effective_user.id} entered admin mode")
        return True
    
    def get_security_status(self, context):
        """الحصول على حالة الأمان الحالية"""
        return {
            "is_admin_mode": self.is_in_admin_mode(context),
            "is_any_admin_mode": self.is_in_any_admin_mode(context),
            "active_modes": [
                mode for mode in [
                    "admin_mode", "user_management_mode", "newsletter_management_mode",
                    "advanced_options_mode", "newsletter_confirmation_mode",
                    "add_user_mode", "remove_user_mode", "ban_user_mode",
                    "restrict_user_mode", "admin_search_mode", "admin_newsletter_mode"
                ] if context.user_data.get(mode, False)
            ]
        }
    
    async def log_security_event(self, user_id, event_type, details=""):
        """تسجيل أحداث الأمان"""
        logger.info(f"Security Event - User: {user_id}, Type: {event_type}, Details: {details}")
    
    def validate_admin_session(self, context):
        """التحقق من صحة جلسة المدير"""
        # يمكن إضافة فحوصات إضافية هنا مثل انتهاء الجلسة
        return True

    async def safe_send_message(self, update: Update, message: str, reply_markup=None, parse_mode=None, max_retries=3):
        """إرسال رسالة آمن مع معالجة الأخطاء"""
        for attempt in range(max_retries):
            try:
                await update.message.reply_text(
                    message,
                    reply_markup=reply_markup,
                    parse_mode=parse_mode
                )
                return True
            except TimedOut:
                logger.warning(f"Timeout on attempt {attempt + 1}/{max_retries}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(2 ** attempt)  # Exponential backoff
                continue
            except NetworkError as e:
                logger.warning(f"Network error on attempt {attempt + 1}/{max_retries}: {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(2 ** attempt)
                continue
            except RetryAfter as e:
                logger.warning(f"Rate limited, waiting {e.retry_after} seconds")
                await asyncio.sleep(e.retry_after)
                continue
            except Exception as e:
                logger.error(f"Unexpected error sending message: {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(1)
                continue

        logger.error(f"Failed to send message after {max_retries} attempts")
        return False
