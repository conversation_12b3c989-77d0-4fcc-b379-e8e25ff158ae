#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إدارة جلسات المدير
نظام متخصص لإدارة وحماية جلسات المدير
"""

import json
import os
from datetime import datetime, timedelta
from telegram import Update
from telegram.ext import ContextTypes
import logging

logger = logging.getLogger(__name__)

class AdminSessionManager:
    """فئة إدارة جلسات المدير"""
    
    def __init__(self, bot_instance):
        self.bot = bot_instance
        self.sessions_file = "security/admin_sessions.json"
        self.session_timeout = 30  # مهلة الجلسة بالدقائق
        
        # إنشاء ملف الجلسات إذا لم يكن موجود
        os.makedirs(os.path.dirname(self.sessions_file), exist_ok=True)
        self.load_sessions()
    
    def load_sessions(self):
        """تحميل بيانات الجلسات"""
        try:
            if os.path.exists(self.sessions_file):
                with open(self.sessions_file, 'r', encoding='utf-8') as f:
                    self.sessions = json.load(f)
            else:
                self.sessions = {}
                self.save_sessions()
        except Exception as e:
            logger.error(f"خطأ في تحميل بيانات الجلسات: {e}")
            self.sessions = {}
    
    def save_sessions(self):
        """حفظ بيانات الجلسات"""
        try:
            with open(self.sessions_file, 'w', encoding='utf-8') as f:
                json.dump(self.sessions, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"خطأ في حفظ بيانات الجلسات: {e}")
    
    async def start_admin_session(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """بدء جلسة مدير جديدة"""
        try:
            user_id = str(update.effective_user.id)
            
            if not self.bot.is_admin(int(user_id)):
                return False
            
            # إنشاء بيانات الجلسة
            session_data = {
                "user_id": user_id,
                "username": update.effective_user.username or "غير محدد",
                "first_name": update.effective_user.first_name or "غير محدد",
                "start_time": datetime.now().isoformat(),
                "last_activity": datetime.now().isoformat(),
                "session_id": f"admin_{user_id}_{int(datetime.now().timestamp())}",
                "is_active": True,
                "commands_used": [],
                "login_method": "command"  # command, button, etc.
            }
            
            # حفظ الجلسة
            self.sessions[user_id] = session_data
            self.save_sessions()
            
            # تعيين وضع المدير في السياق
            context.user_data["admin_mode"] = True
            context.user_data["admin_session_id"] = session_data["session_id"]
            
            logger.info(f"تم بدء جلسة مدير جديدة للمستخدم {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في بدء جلسة المدير: {e}")
            return False
    
    async def end_admin_session(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """إنهاء جلسة المدير"""
        try:
            user_id = str(update.effective_user.id)
            
            if user_id in self.sessions:
                # تحديث بيانات الجلسة
                self.sessions[user_id]["end_time"] = datetime.now().isoformat()
                self.sessions[user_id]["is_active"] = False
                
                # حساب مدة الجلسة
                start_time = datetime.fromisoformat(self.sessions[user_id]["start_time"])
                duration = datetime.now() - start_time
                self.sessions[user_id]["duration_minutes"] = duration.total_seconds() / 60
                
                self.save_sessions()
            
            # إزالة وضع المدير من السياق
            context.user_data["admin_mode"] = False
            if "admin_session_id" in context.user_data:
                del context.user_data["admin_session_id"]
            
            # إزالة أي أوضاع فرعية
            modes_to_clear = [
                "user_management_mode", "newsletter_management_mode", 
                "advanced_options_mode", "newsletter_confirmation_mode"
            ]
            for mode in modes_to_clear:
                if mode in context.user_data:
                    context.user_data[mode] = False
            
            logger.info(f"تم إنهاء جلسة المدير للمستخدم {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إنهاء جلسة المدير: {e}")
            return False
    
    def is_session_active(self, user_id: int) -> bool:
        """فحص ما إذا كانت جلسة المدير نشطة"""
        try:
            user_id_str = str(user_id)
            
            if user_id_str not in self.sessions:
                return False
            
            session = self.sessions[user_id_str]
            
            if not session.get("is_active", False):
                return False
            
            # فحص انتهاء صلاحية الجلسة
            last_activity = datetime.fromisoformat(session["last_activity"])
            if datetime.now() - last_activity > timedelta(minutes=self.session_timeout):
                # انتهت صلاحية الجلسة
                session["is_active"] = False
                session["end_time"] = datetime.now().isoformat()
                session["end_reason"] = "timeout"
                self.save_sessions()
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في فحص حالة الجلسة: {e}")
            return False
    
    async def update_session_activity(self, update: Update, context: ContextTypes.DEFAULT_TYPE, command: str = None):
        """تحديث نشاط الجلسة"""
        try:
            user_id = str(update.effective_user.id)
            
            if user_id in self.sessions and self.sessions[user_id].get("is_active", False):
                # تحديث وقت آخر نشاط
                self.sessions[user_id]["last_activity"] = datetime.now().isoformat()
                
                # إضافة الأمر المستخدم
                if command:
                    if "commands_used" not in self.sessions[user_id]:
                        self.sessions[user_id]["commands_used"] = []
                    
                    command_entry = {
                        "command": command,
                        "timestamp": datetime.now().isoformat()
                    }
                    self.sessions[user_id]["commands_used"].append(command_entry)
                
                self.save_sessions()
                
        except Exception as e:
            logger.error(f"خطأ في تحديث نشاط الجلسة: {e}")
    
    def get_session_info(self, user_id: int) -> dict:
        """الحصول على معلومات الجلسة"""
        try:
            user_id_str = str(user_id)
            
            if user_id_str not in self.sessions:
                return {}
            
            session = self.sessions[user_id_str].copy()
            
            # حساب مدة الجلسة الحالية
            if session.get("is_active", False):
                start_time = datetime.fromisoformat(session["start_time"])
                session["current_duration_minutes"] = (datetime.now() - start_time).total_seconds() / 60
            
            return session
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على معلومات الجلسة: {e}")
            return {}
    
    def get_all_active_sessions(self) -> list:
        """الحصول على جميع الجلسات النشطة"""
        try:
            active_sessions = []
            
            for user_id, session in self.sessions.items():
                if self.is_session_active(int(user_id)):
                    session_info = self.get_session_info(int(user_id))
                    active_sessions.append(session_info)
            
            return active_sessions
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على الجلسات النشطة: {e}")
            return []
    
    async def cleanup_expired_sessions(self):
        """تنظيف الجلسات المنتهية الصلاحية"""
        try:
            expired_count = 0
            
            for user_id, session in list(self.sessions.items()):
                if session.get("is_active", False):
                    last_activity = datetime.fromisoformat(session["last_activity"])
                    if datetime.now() - last_activity > timedelta(minutes=self.session_timeout):
                        session["is_active"] = False
                        session["end_time"] = datetime.now().isoformat()
                        session["end_reason"] = "timeout"
                        expired_count += 1
            
            if expired_count > 0:
                self.save_sessions()
                logger.info(f"تم تنظيف {expired_count} جلسة منتهية الصلاحية")
            
        except Exception as e:
            logger.error(f"خطأ في تنظيف الجلسات المنتهية: {e}")
    
    def get_session_statistics(self) -> dict:
        """الحصول على إحصائيات الجلسات"""
        try:
            stats = {
                "total_sessions": len(self.sessions),
                "active_sessions": len(self.get_all_active_sessions()),
                "total_commands_used": 0,
                "average_session_duration": 0,
                "most_used_commands": {},
                "session_by_day": {}
            }
            
            total_duration = 0
            completed_sessions = 0
            
            for session in self.sessions.values():
                # حساب الأوامر المستخدمة
                commands_used = session.get("commands_used", [])
                stats["total_commands_used"] += len(commands_used)
                
                # إحصائيات الأوامر الأكثر استخداماً
                for cmd_entry in commands_used:
                    cmd = cmd_entry.get("command", "غير محدد")
                    stats["most_used_commands"][cmd] = stats["most_used_commands"].get(cmd, 0) + 1
                
                # حساب متوسط مدة الجلسة
                if "duration_minutes" in session:
                    total_duration += session["duration_minutes"]
                    completed_sessions += 1
                
                # إحصائيات الجلسات حسب اليوم
                start_time = datetime.fromisoformat(session["start_time"])
                day = start_time.strftime("%Y-%m-%d")
                stats["session_by_day"][day] = stats["session_by_day"].get(day, 0) + 1
            
            if completed_sessions > 0:
                stats["average_session_duration"] = total_duration / completed_sessions
            
            return stats
            
        except Exception as e:
            logger.error(f"خطأ في حساب إحصائيات الجلسات: {e}")
            return {}
    
    async def force_end_session(self, user_id: int, reason: str = "admin_action"):
        """إنهاء جلسة بالقوة"""
        try:
            user_id_str = str(user_id)
            
            if user_id_str in self.sessions and self.sessions[user_id_str].get("is_active", False):
                self.sessions[user_id_str]["is_active"] = False
                self.sessions[user_id_str]["end_time"] = datetime.now().isoformat()
                self.sessions[user_id_str]["end_reason"] = reason
                
                # حساب مدة الجلسة
                start_time = datetime.fromisoformat(self.sessions[user_id_str]["start_time"])
                duration = datetime.now() - start_time
                self.sessions[user_id_str]["duration_minutes"] = duration.total_seconds() / 60
                
                self.save_sessions()
                logger.info(f"تم إنهاء جلسة المستخدم {user_id} بالقوة - السبب: {reason}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"خطأ في إنهاء الجلسة بالقوة: {e}")
            return False
    
    def set_session_timeout(self, timeout_minutes: int):
        """تعيين مهلة انتهاء الجلسة"""
        self.session_timeout = timeout_minutes
        logger.info(f"تم تعيين مهلة الجلسة إلى {timeout_minutes} دقيقة")
    
    async def send_session_warning(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """إرسال تحذير انتهاء الجلسة"""
        try:
            await update.message.reply_text(
                "⚠️ تحذير: ستنتهي جلسة المدير خلال 5 دقائق بسبب عدم النشاط\n"
                "💡 استخدم أي أمر للحفاظ على الجلسة نشطة",
                reply_markup=self.bot.get_admin_keyboard()
            )
        except Exception as e:
            logger.error(f"خطأ في إرسال تحذير الجلسة: {e}")
