#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام تتبع الحد الأقصى للاستخدام
يدير حد 25 طلب/ساعة للنماذج العادية المجانية
"""

import os
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Tuple
import sys

# إضافة مسار المكتبات المشتركة
current_dir = os.path.dirname(os.path.abspath(__file__))
shared_dir = os.path.dirname(current_dir)
sys.path.insert(0, shared_dir)

# إعداد نظام السجلات
logger = logging.getLogger(__name__)

class UsageLimiter:
    """نظام تتبع الحد الأقصى للاستخدام"""
    
    def __init__(self):
        """تهيئة نظام تتبع الاستخدام"""
        
        # الحد الأقصى للاستخدام المجاني
        self.FREE_LIMIT_PER_HOUR = 25
        
        # ملف تتبع الاستخدام
        self.usage_file = os.path.join(shared_dir, "database", "user_usage_limits.json")
        self._ensure_usage_file_exists()
        
        logger.info("⏱️ تم تهيئة نظام تتبع الحد الأقصى للاستخدام")
    
    def _ensure_usage_file_exists(self):
        """التأكد من وجود ملف تتبع الاستخدام"""
        if not os.path.exists(self.usage_file):
            with open(self.usage_file, 'w', encoding='utf-8') as f:
                json.dump({}, f, ensure_ascii=False, indent=2)
            logger.info("📄 تم إنشاء ملف تتبع حدود الاستخدام")
    
    def _load_usage_data(self) -> Dict[str, Any]:
        """تحميل بيانات الاستخدام"""
        try:
            with open(self.usage_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"خطأ في تحميل بيانات الاستخدام: {e}")
            return {}
    
    def _save_usage_data(self, data: Dict[str, Any]):
        """حفظ بيانات الاستخدام"""
        try:
            with open(self.usage_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"خطأ في حفظ بيانات الاستخدام: {e}")
    
    def _get_current_hour_key(self) -> str:
        """الحصول على مفتاح الساعة الحالية"""
        return datetime.now().strftime('%Y-%m-%d-%H')
    
    def _cleanup_old_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """تنظيف البيانات القديمة (أكثر من 24 ساعة)"""
        try:
            current_time = datetime.now()
            cutoff_time = current_time - timedelta(hours=24)
            
            cleaned_data = {}
            
            for user_id, user_data in data.items():
                cleaned_user_data = {}
                
                for hour_key, usage_info in user_data.items():
                    try:
                        # تحويل مفتاح الساعة إلى تاريخ
                        hour_time = datetime.strptime(hour_key, '%Y-%m-%d-%H')
                        
                        # الاحتفاظ بالبيانات الحديثة فقط
                        if hour_time >= cutoff_time:
                            cleaned_user_data[hour_key] = usage_info
                    except:
                        # تخطي البيانات التالفة
                        continue
                
                # الاحتفاظ بالمستخدم إذا كان لديه بيانات حديثة
                if cleaned_user_data:
                    cleaned_data[user_id] = cleaned_user_data
            
            return cleaned_data
            
        except Exception as e:
            logger.error(f"خطأ في تنظيف البيانات القديمة: {e}")
            return data
    
    def check_usage_limit(self, user_id: int) -> Tuple[bool, int, str]:
        """
        فحص الحد الأقصى للاستخدام
        
        Args:
            user_id: معرف المستخدم
            
        Returns:
            Tuple[bool, int, str]: (يمكن الاستخدام, الاستخدام الحالي, رسالة)
        """
        try:
            # تحميل البيانات وتنظيفها
            data = self._load_usage_data()
            data = self._cleanup_old_data(data)
            
            user_key = str(user_id)
            current_hour = self._get_current_hour_key()
            
            # الحصول على استخدام المستخدم في الساعة الحالية
            current_usage = 0
            if user_key in data and current_hour in data[user_key]:
                current_usage = data[user_key][current_hour].get('count', 0)
            
            # فحص الحد الأقصى
            can_use = current_usage < self.FREE_LIMIT_PER_HOUR
            remaining = max(0, self.FREE_LIMIT_PER_HOUR - current_usage)
            
            if can_use:
                message = f"ℹ️︙المحاولات المسموح بها︙{self.FREE_LIMIT_PER_HOUR}︙طلب في الساعة\nℹ️︙المحاولات المتبقية لك︙{remaining}︙طلب في الساعة"
            else:
                # حساب وقت إعادة التعيين
                next_hour = datetime.now().replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
                reset_time = next_hour.strftime('%H:%M')
                message = f"تم تجاوز الحد الأقصى ({self.FREE_LIMIT_PER_HOUR} طلب/ساعة). إعادة التعيين في {reset_time}"
            
            # حفظ البيانات المنظفة
            self._save_usage_data(data)
            
            return can_use, current_usage, message
            
        except Exception as e:
            logger.error(f"خطأ في فحص حد الاستخدام للمستخدم {user_id}: {e}")
            # في حالة الخطأ، نسمح بالاستخدام
            return True, 0, "خطأ في فحص الحد - مسموح مؤقتاً"
    
    def record_usage(self, user_id: int, model_type: str = "normal") -> bool:
        """
        تسجيل استخدام جديد
        
        Args:
            user_id: معرف المستخدم
            model_type: نوع النموذج (normal/pro)
            
        Returns:
            bool: نجح التسجيل
        """
        try:
            # تحميل البيانات
            data = self._load_usage_data()
            data = self._cleanup_old_data(data)
            
            user_key = str(user_id)
            current_hour = self._get_current_hour_key()
            
            # تهيئة بيانات المستخدم إذا لم تكن موجودة
            if user_key not in data:
                data[user_key] = {}
            
            if current_hour not in data[user_key]:
                data[user_key][current_hour] = {
                    'count': 0,
                    'first_request': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'last_request': None,
                    'model_type': model_type
                }
            
            # تسجيل الاستخدام
            data[user_key][current_hour]['count'] += 1
            data[user_key][current_hour]['last_request'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # حفظ البيانات
            self._save_usage_data(data)
            
            logger.info(f"📊 تم تسجيل استخدام للمستخدم {user_id} - العدد: {data[user_key][current_hour]['count']}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تسجيل الاستخدام للمستخدم {user_id}: {e}")
            return False
    
    def get_user_usage_stats(self, user_id: int) -> Dict[str, Any]:
        """
        الحصول على إحصائيات استخدام المستخدم
        
        Args:
            user_id: معرف المستخدم
            
        Returns:
            dict: إحصائيات الاستخدام
        """
        try:
            data = self._load_usage_data()
            data = self._cleanup_old_data(data)
            
            user_key = str(user_id)
            current_hour = self._get_current_hour_key()
            
            # الاستخدام الحالي
            current_usage = 0
            if user_key in data and current_hour in data[user_key]:
                current_usage = data[user_key][current_hour].get('count', 0)
            
            # إجمالي الاستخدام في آخر 24 ساعة
            total_24h = 0
            hours_active = 0
            
            if user_key in data:
                for hour_data in data[user_key].values():
                    total_24h += hour_data.get('count', 0)
                    hours_active += 1
            
            # حساب المتوسط
            avg_per_hour = total_24h / max(1, hours_active)
            
            return {
                "user_id": user_id,
                "current_hour_usage": current_usage,
                "limit_per_hour": self.FREE_LIMIT_PER_HOUR,
                "remaining_this_hour": max(0, self.FREE_LIMIT_PER_HOUR - current_usage),
                "total_24h_usage": total_24h,
                "hours_active_24h": hours_active,
                "avg_per_hour": round(avg_per_hour, 2),
                "can_use_free": current_usage < self.FREE_LIMIT_PER_HOUR,
                "last_updated": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات المستخدم {user_id}: {e}")
            return {
                "user_id": user_id,
                "error": str(e),
                "can_use_free": True  # السماح في حالة الخطأ
            }
    
    def get_overall_usage_stats(self) -> Dict[str, Any]:
        """الحصول على الإحصائيات العامة للاستخدام"""
        try:
            data = self._load_usage_data()
            data = self._cleanup_old_data(data)
            
            current_hour = self._get_current_hour_key()
            
            stats = {
                "total_users": len(data),
                "active_users_current_hour": 0,
                "total_requests_current_hour": 0,
                "users_at_limit": 0,
                "total_requests_24h": 0,
                "avg_requests_per_user": 0,
                "peak_hour_usage": 0,
                "current_hour": current_hour
            }
            
            total_requests_24h = 0
            current_hour_requests = 0
            users_at_limit = 0
            active_current_hour = 0
            
            for user_data in data.values():
                user_24h_total = 0
                user_current_hour = 0
                
                for hour_key, hour_data in user_data.items():
                    requests = hour_data.get('count', 0)
                    user_24h_total += requests
                    
                    if hour_key == current_hour:
                        user_current_hour = requests
                        if requests > 0:
                            active_current_hour += 1
                        if requests >= self.FREE_LIMIT_PER_HOUR:
                            users_at_limit += 1
                
                total_requests_24h += user_24h_total
                current_hour_requests += user_current_hour
            
            stats["active_users_current_hour"] = active_current_hour
            stats["total_requests_current_hour"] = current_hour_requests
            stats["users_at_limit"] = users_at_limit
            stats["total_requests_24h"] = total_requests_24h
            stats["avg_requests_per_user"] = round(total_requests_24h / max(1, len(data)), 2)
            
            return stats
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على الإحصائيات العامة: {e}")
            return {"error": str(e)}
    
    def reset_user_usage(self, user_id: int) -> bool:
        """
        إعادة تعيين استخدام المستخدم (للطوارئ)
        
        Args:
            user_id: معرف المستخدم
            
        Returns:
            bool: نجحت العملية
        """
        try:
            data = self._load_usage_data()
            user_key = str(user_id)
            
            if user_key in data:
                del data[user_key]
                self._save_usage_data(data)
                logger.info(f"🔄 تم إعادة تعيين استخدام المستخدم {user_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"خطأ في إعادة تعيين استخدام المستخدم {user_id}: {e}")
            return False
