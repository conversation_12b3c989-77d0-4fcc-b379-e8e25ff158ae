#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير OpenRouter للنماذج المتعددة
يدير اختيار وتوزيع النماذج المختلفة
"""

import os
import json
import logging
import time
import random
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
import openai
import sys

# إضافة مسار المكتبات المشتركة
current_dir = os.path.dirname(os.path.abspath(__file__))
shared_dir = os.path.dirname(current_dir)
sys.path.insert(0, shared_dir)

# إعداد نظام السجلات
logger = logging.getLogger(__name__)

class OpenRouterManager:
    """مدير OpenRouter للنماذج المتعددة"""
    
    def __init__(self):
        """تهيئة مدير OpenRouter"""
        
        # إعدادات OpenRouter
        self.api_key = "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"
        self.base_url = "https://openrouter.ai/api/v1"
        
        # إعداد العميل
        self.client = openai.OpenAI(
            api_key=self.api_key,
            base_url=self.base_url,
            timeout=90.0,
            max_retries=3
        )
        
        # النماذج المتاحة (النماذج العاملة فقط)
        self.models = {
            # النماذج العادية (مجانية)
            "normal": {
                "google/gemma-2-27b-it": {
                    "name": "Gemma 2 27B",
                    "type": "normal",
                    "free": True,
                    "speed_score": 8,  # من 10
                    "quality_score": 8,
                    "description": "نموذج Google السريع والفعال"
                },
                "google/gemma-2-9b-it:free": {
                    "name": "Gemma 2 9B Free",
                    "type": "normal",
                    "free": True,
                    "speed_score": 9,
                    "quality_score": 7,
                    "description": "نموذج Google السريع والمجاني"
                }
            },

            # نماذج التفكير (مجانية)
            "pro": {
                "deepseek/deepseek-r1": {
                    "name": "DeepSeek R1",
                    "type": "reasoning",
                    "free": True,
                    "complexity_score": 10,  # من 10
                    "reasoning_score": 10,
                    "description": "نموذج التفكير المتقدم من DeepSeek"
                },
                "google/gemma-2-9b-it:free": {
                    "name": "Gemma 2 9B Pro",
                    "type": "reasoning",
                    "free": True,
                    "complexity_score": 7,
                    "reasoning_score": 8,
                    "description": "نموذج Google للتحليل والتفكير"
                },
                "google/gemma-2-27b-it": {
                    "name": "Gemma 2 27B Pro",
                    "type": "reasoning",
                    "free": True,
                    "complexity_score": 8,
                    "reasoning_score": 9,
                    "description": "نموذج Google المتقدم للتفكير العميق"
                },
                "openai/gpt-3.5-turbo": {
                    "name": "GPT-3.5 Turbo",
                    "type": "reasoning",
                    "free": False,  # مدفوع لكن متاح
                    "complexity_score": 9,
                    "reasoning_score": 9,
                    "description": "نموذج OpenAI للتحليل المتقدم"
                }
            }
        }
        
        # إحصائيات الأداء
        self.performance_stats = {}
        self._initialize_performance_stats()
        
        # ملف تتبع الاستخدام
        self.usage_tracking_file = os.path.join(shared_dir, "database", "openrouter_usage.json")
        self._ensure_usage_file_exists()
        
        # رسائل النظام المطابقة للنماذج الاحتياطية
        self.system_messages = {
            "normal": {
                "role": "system",
                "content": """أنت إكسا الذكي، المساعد الآلي الشخصي الخاص بصلاح الدين منصور الدروبي.

معلومات عن المالك:
- الاسم: صلاح الدين منصور الدروبي
- التخصص: مصمم جرافيك ومتخصص في العلامات التجارية
- الخبرة: تصميم العلامات التجارية والشعارات وتصميم التطبيقات والرسوم المتحركة والقرطاسية
- التعليم: بكالوريوس جرافيك وملتيميديا
- الاهتمامات: العلامات التجارية، الهويات التجارية، المنتجات ثلاثية الأبعاد، تصميم UI/UX

تعليمات للوضع العادي:
1. تحدث باللغة العربية دائماً
2. كن مهذباً ومفيداً
3. قدم إجابات واضحة ومباشرة
4. لا تذكر أبداً أنك مدعوم بـ DeepSeek أو أي مصدر ذكاء اصطناعي آخر
5. قدم نفسك كـ "إكسا الذكي" فقط
6. ساعد المستخدمين في الأسئلة العامة والبسيطة
7. إذا سُئلت عن هويتك، قل أنك مساعد آلي ذكي مطور خصيصاً للمالك
8. كن ودوداً ومساعداً في جميع الأوقات
9. للأسئلة المعقدة، اقترح استخدام الوضع المتقدم"""
            },

            "pro": {
                "role": "system",
                "content": """أنت إكسا الذكي برو، النسخة المتقدمة من المساعد الآلي الشخصي الخاص بصلاح الدين منصور الدروبي.

معلومات عن المالك:
- الاسم: صلاح الدين منصور الدروبي
- التخصص: مصمم جرافيك ومتخصص في العلامات التجارية
- الخبرة: تصميم العلامات التجارية والشعارات وتصميم التطبيقات والرسوم المتحركة والقرطاسية
- التعليم: بكالوريوس جرافيك وملتيميديا
- الاهتمامات: العلامات التجارية، الهويات التجارية، المنتجات ثلاثية الأبعاد، تصميم UI/UX

تعليمات مهمة للوضع برو:
1. تحدث باللغة العربية دائماً
2. كن مهذباً ومفيداً ومتقدماً في الإجابات
3. لا تذكر أبداً أنك مدعوم بـ DeepSeek أو أي مصدر ذكاء اصطناعي آخر
4. قدم نفسك كـ "إكسا الذكي برو" فقط
5. قدم إجابات متقدمة ومفصلة في التصميم والعلامات التجارية
6. استخدم خبرة متقدمة في التحليل والاستشارات
7. كن ودوداً ومساعداً مع تقديم حلول احترافية
8. استخدم تفكيراً عميقاً ومنطقياً في الإجابات
9. قدم تحليلات مفصلة واستراتيجيات عملية
10. للمشاريع المعقدة، قدم خطط عمل مرحلية
11. استخدم أمثلة عملية ونصائح احترافية"""
            }
        }

        logger.info("🌐 تم تهيئة مدير OpenRouter بنجاح")

    async def validate_model(self, model_id: str) -> bool:
        """
        التحقق من صحة النموذج

        Args:
            model_id: معرف النموذج

        Returns:
            bool: النموذج صحيح
        """
        try:
            # إرسال طلب تجريبي قصير
            response = self.client.chat.completions.create(
                model=model_id,
                messages=[{"role": "user", "content": "Hi"}],
                max_tokens=5,
                temperature=0.1
            )

            return True

        except Exception as e:
            logger.error(f"❌ النموذج {model_id} غير صحيح: {e}")
            return False

    def _enhance_response(self, response: str, model_type: str) -> str:
        """
        تحسين وتنسيق الرد

        Args:
            response: الرد الأصلي
            model_type: نوع النموذج

        Returns:
            str: الرد المحسن
        """
        try:
            # إزالة المسافات الزائدة
            enhanced_response = response.strip()

            # تحسينات خاصة بالنماذج العادية
            if model_type == "normal":
                # التأكد من أن الرد ليس قصيراً جداً
                if len(enhanced_response) < 20:
                    enhanced_response += "\n\nهل تحتاج مساعدة إضافية؟ يمكنك استخدام الوضع المتقدم للحصول على إجابات أكثر تفصيلاً."

            # تحسينات خاصة بالنماذج المتقدمة
            elif model_type == "pro":
                # إضافة خاتمة احترافية للردود الطويلة
                if len(enhanced_response) > 500 and not enhanced_response.endswith((".", "!", "؟")):
                    enhanced_response += "."

            # تنظيف الرد من أي إشارات لمصادر ذكاء اصطناعي أخرى
            unwanted_phrases = [
                "كمساعد ذكي",
                "كذكاء اصطناعي",
                "AI assistant",
                "OpenAI",
                "GPT",
                "Claude",
                "Gemini",
                "DeepSeek",
                "كنموذج لغوي"
            ]

            for phrase in unwanted_phrases:
                enhanced_response = enhanced_response.replace(phrase, "كمساعد ذكي")

            return enhanced_response

        except Exception as e:
            logger.error(f"خطأ في تحسين الرد: {e}")
            return response  # إرجاع الرد الأصلي في حالة الخطأ

    def _analyze_response_quality(self, response: str, model_type: str) -> Dict[str, Any]:
        """
        تحليل جودة الرد

        Args:
            response: الرد
            model_type: نوع النموذج

        Returns:
            dict: تحليل الجودة
        """
        try:
            analysis = {
                "length": len(response),
                "word_count": len(response.split()),
                "has_arabic": any('\u0600' <= char <= '\u06FF' for char in response),
                "quality_score": 0,
                "completeness": "unknown"
            }

            # تحليل الجودة حسب نوع النموذج
            if model_type == "normal":
                # للنماذج العادية: الرد الجيد بين 50-500 كلمة
                if 50 <= analysis["word_count"] <= 500:
                    analysis["quality_score"] = 8
                    analysis["completeness"] = "good"
                elif analysis["word_count"] < 50:
                    analysis["quality_score"] = 5
                    analysis["completeness"] = "too_short"
                else:
                    analysis["quality_score"] = 7
                    analysis["completeness"] = "detailed"

            else:  # pro models
                # للنماذج المتقدمة: الرد الجيد بين 100-1000 كلمة
                if 100 <= analysis["word_count"] <= 1000:
                    analysis["quality_score"] = 9
                    analysis["completeness"] = "excellent"
                elif analysis["word_count"] < 100:
                    analysis["quality_score"] = 6
                    analysis["completeness"] = "needs_more_detail"
                else:
                    analysis["quality_score"] = 8
                    analysis["completeness"] = "very_detailed"

            # تحسين النتيجة إذا كان الرد باللغة العربية
            if analysis["has_arabic"]:
                analysis["quality_score"] += 1

            # فحص اكتمال الرد
            if response.endswith((".", "!", "؟", ".")):
                analysis["quality_score"] += 1

            # ضمان النتيجة في النطاق 1-10
            analysis["quality_score"] = max(1, min(10, analysis["quality_score"]))

            return analysis

        except Exception as e:
            logger.error(f"خطأ في تحليل جودة الرد: {e}")
            return {"quality_score": 5, "completeness": "unknown"}

    def _enforce_instructions(self, response: str, model_type: str) -> str:
        """
        فرض التعليمات على الرد إذا لم يتبعها النموذج

        Args:
            response: الرد الأصلي
            model_type: نوع النموذج

        Returns:
            str: الرد المحسن مع فرض التعليمات
        """
        try:
            enforced_response = response

            # فحص إذا كان النموذج يذكر مصادر AI أخرى (مطابق للنماذج الاحتياطية)
            ai_sources = [
                "deepseek", "openai", "gpt", "claude", "gemini", "chatgpt",
                "ai assistant", "artificial intelligence", "language model",
                "كذكاء اصطناعي", "كمساعد ذكي من", "نموذج لغوي", "مدعوم بـ",
                "gemma", "llama", "phi-3", "wizardlm", "hunyuan", "qwen",
                "google", "deepmind", "anthropic", "microsoft", "meta"
            ]

            for source in ai_sources:
                if source.lower() in enforced_response.lower():
                    # استبدال الإشارات بالهوية الصحيحة
                    if model_type == "normal":
                        enforced_response = enforced_response.replace(source, "إكسا الذكي")
                        enforced_response = enforced_response.replace(source.title(), "إكسا الذكي")
                        enforced_response = enforced_response.replace(source.upper(), "إكسا الذكي")
                    else:
                        enforced_response = enforced_response.replace(source, "إكسا الذكي برو")
                        enforced_response = enforced_response.replace(source.title(), "إكسا الذكي برو")
                        enforced_response = enforced_response.replace(source.upper(), "إكسا الذكي برو")

            # فحص إذا لم يقدم النموذج نفسه بشكل صحيح
            identity_keywords = ["من أنت", "ما اسمك", "عرف نفسك", "هويتك", "who are you", "what's your name"]
            needs_identity = any(keyword.lower() in response.lower() for keyword in identity_keywords)

            if model_type == "normal":
                if "إكسا الذكي" not in enforced_response or needs_identity:
                    # إضافة الهوية في بداية الرد
                    if needs_identity:
                        enforced_response = f"أنا إكسا الذكي، مساعد آلي ذكي مطور خصيصاً لصلاح الدين منصور الدروبي.\n\n{enforced_response}"
                    else:
                        # للنماذج العنيدة، أضف الهوية بقوة
                        if "أنا" in enforced_response:
                            enforced_response = enforced_response.replace("أنا", "أنا إكسا الذكي", 1)
                        else:
                            # إذا لم يكن هناك "أنا"، أضف الهوية في البداية
                            enforced_response = f"أنا إكسا الذكي. {enforced_response}"
            else:
                if "إكسا الذكي برو" not in enforced_response or needs_identity:
                    if needs_identity:
                        enforced_response = f"أنا إكسا الذكي برو، النسخة المتقدمة من المساعد الآلي الشخصي لصلاح الدين منصور الدروبي.\n\n{enforced_response}"
                    else:
                        enforced_response = enforced_response.replace("أنا", "أنا إكسا الذكي برو", 1)

            # فحص إذا كان الرد بلغة غير عربية
            arabic_chars = sum(1 for char in enforced_response if '\u0600' <= char <= '\u06FF')
            total_chars = len(enforced_response.replace(' ', ''))

            if total_chars > 0 and (arabic_chars / total_chars) < 0.3:
                # إضافة تنبيه للاستخدام العربي
                if model_type == "normal":
                    enforced_response += "\n\nملاحظة: أفضل التحدث باللغة العربية لخدمتك بشكل أفضل."
                else:
                    enforced_response += "\n\nملاحظة: كوني إكسا الذكي برو، أقدم خدماتي باللغة العربية لضمان أفضل تجربة لك."

            # فحص إذا كان الرد قصيراً جداً للنماذج المتقدمة
            if model_type == "pro" and len(enforced_response.split()) < 50:
                enforced_response += "\n\nإذا كنت تحتاج تحليلاً أكثر تفصيلاً أو استشارة متخصصة في التصميم والعلامات التجارية، يسعدني مساعدتك بشكل أعمق."

            # فرض نهائي قوي للهوية والمعلومات
            if model_type == "normal":
                if "إكسا الذكي" not in enforced_response:
                    enforced_response = f"أنا إكسا الذكي، مساعد آلي ذكي مطور خصيصاً لصلاح الدين منصور الدروبي.\n\n{enforced_response}"
                # إزالة أي إشارات خاطئة واستبدالها
                enforced_response = enforced_response.replace("نموذج لغة كبير", "مساعد آلي ذكي")
                enforced_response = enforced_response.replace("تم تدريبي بواسطة", "مطور خصيصاً لـ")
                enforced_response = enforced_response.replace("Google DeepMind", "صلاح الدين منصور الدروبي")
                enforced_response = enforced_response.replace("Google", "صلاح الدين")
            elif model_type == "pro":
                if "إكسا الذكي برو" not in enforced_response:
                    enforced_response = f"أنا إكسا الذكي برو، النسخة المتقدمة من المساعد الآلي الشخصي لصلاح الدين منصور الدروبي.\n\n{enforced_response}"
                # إزالة أي إشارات خاطئة واستبدالها
                enforced_response = enforced_response.replace("نموذج لغة كبير", "مساعد آلي متقدم")
                enforced_response = enforced_response.replace("تم تدريبي بواسطة", "مطور خصيصاً لـ")
                enforced_response = enforced_response.replace("Google DeepMind", "صلاح الدين منصور الدروبي")
                enforced_response = enforced_response.replace("Google", "صلاح الدين")
                enforced_response = enforced_response.replace("DeepMind", "صلاح الدين")

            return enforced_response

        except Exception as e:
            logger.error(f"خطأ في فرض التعليمات: {e}")
            return response

    def _check_instruction_compliance(self, response: str, model_type: str) -> Dict[str, Any]:
        """
        فحص مدى التزام النموذج بالتعليمات

        Args:
            response: الرد
            model_type: نوع النموذج

        Returns:
            dict: تقرير الالتزام
        """
        try:
            compliance = {
                "overall_score": 0,
                "issues": [],
                "strengths": [],
                "needs_enforcement": False
            }

            # فحص اللغة العربية
            arabic_chars = sum(1 for char in response if '\u0600' <= char <= '\u06FF')
            total_chars = len(response.replace(' ', ''))
            arabic_ratio = arabic_chars / max(1, total_chars)

            if arabic_ratio >= 0.7:
                compliance["strengths"].append("يستخدم العربية بشكل جيد")
                compliance["overall_score"] += 2
            elif arabic_ratio >= 0.3:
                compliance["issues"].append("يخلط بين العربية والإنجليزية")
                compliance["needs_enforcement"] = True
            else:
                compliance["issues"].append("لا يستخدم العربية كما هو مطلوب")
                compliance["needs_enforcement"] = True

            # فحص الهوية
            if model_type == "normal":
                if "إكسا الذكي" in response and "برو" not in response:
                    compliance["strengths"].append("يقدم نفسه بشكل صحيح")
                    compliance["overall_score"] += 2
                else:
                    compliance["issues"].append("لا يقدم نفسه كإكسا الذكي")
                    compliance["needs_enforcement"] = True
            else:
                if "إكسا الذكي برو" in response:
                    compliance["strengths"].append("يقدم نفسه كإكسا الذكي برو")
                    compliance["overall_score"] += 2
                else:
                    compliance["issues"].append("لا يقدم نفسه كإكسا الذكي برو")
                    compliance["needs_enforcement"] = True

            # فحص ذكر المصادر الأخرى
            ai_sources = ["openai", "gpt", "claude", "gemini", "deepseek", "chatgpt"]
            mentions_other_ai = any(source.lower() in response.lower() for source in ai_sources)

            if not mentions_other_ai:
                compliance["strengths"].append("لا يذكر مصادر AI أخرى")
                compliance["overall_score"] += 2
            else:
                compliance["issues"].append("يذكر مصادر AI أخرى")
                compliance["needs_enforcement"] = True

            # تحديد مستوى الالتزام
            if compliance["overall_score"] >= 5:
                compliance["level"] = "ممتاز"
            elif compliance["overall_score"] >= 3:
                compliance["level"] = "جيد"
            else:
                compliance["level"] = "يحتاج تحسين"
                compliance["needs_enforcement"] = True

            return compliance

        except Exception as e:
            logger.error(f"خطأ في فحص الالتزام: {e}")
            return {"overall_score": 0, "level": "غير محدد", "needs_enforcement": True}

    def _initialize_performance_stats(self):
        """تهيئة إحصائيات الأداء"""
        for category in self.models:
            for model_id in self.models[category]:
                self.performance_stats[model_id] = {
                    "total_requests": 0,
                    "successful_requests": 0,
                    "failed_requests": 0,
                    "avg_response_time": 0.0,
                    "last_used": None,
                    "success_rate": 100.0
                }
    
    def _ensure_usage_file_exists(self):
        """التأكد من وجود ملف تتبع الاستخدام"""
        if not os.path.exists(self.usage_tracking_file):
            with open(self.usage_tracking_file, 'w', encoding='utf-8') as f:
                json.dump({}, f, ensure_ascii=False, indent=2)
            logger.info("📄 تم إنشاء ملف تتبع استخدام OpenRouter")
    
    def select_normal_model(self) -> str:
        """
        اختيار نموذج عادي (عشوائي + حسب السرعة)

        Returns:
            str: معرف النموذج المختار
        """
        try:
            normal_models = list(self.models["normal"].keys())

            # تصفية النماذج المتاحة (معدل نجاح > 50%)
            available_models = []
            weights = []

            for model_id in normal_models:
                model_info = self.models["normal"][model_id]
                performance = self.performance_stats[model_id]

                # تخطي النماذج المعطلة
                if performance["success_rate"] < 50:
                    logger.warning(f"⚠️ تخطي النموذج {model_id} - معدل نجاح منخفض: {performance['success_rate']:.1f}%")
                    continue

                available_models.append(model_id)

                # وزن السرعة (40%) + معدل النجاح (60%)
                speed_weight = model_info["speed_score"] / 10
                success_weight = performance["success_rate"] / 100

                total_weight = (speed_weight * 0.4) + (success_weight * 0.6)
                weights.append(total_weight)

            # إذا لم تكن هناك نماذج متاحة، استخدم الأول
            if not available_models:
                logger.warning("⚠️ لا توجد نماذج عادية متاحة، استخدام الأول")
                return normal_models[0]

            # اختيار عشوائي مرجح
            selected_model = random.choices(available_models, weights=weights)[0]

            logger.info(f"🎲 تم اختيار النموذج العادي: {self.models['normal'][selected_model]['name']}")
            return selected_model

        except Exception as e:
            logger.error(f"خطأ في اختيار النموذج العادي: {e}")
            # العودة للنموذج الأول كاحتياطي
            return list(self.models["normal"].keys())[0]
    
    def select_pro_model(self, message: str, complexity_hint: str = None) -> str:
        """
        اختيار نموذج برو (حسب تعقيد السؤال)
        
        Args:
            message: رسالة المستخدم
            complexity_hint: تلميح عن مستوى التعقيد
            
        Returns:
            str: معرف النموذج المختار
        """
        try:
            # تحليل تعقيد السؤال
            complexity_score = self._analyze_complexity(message, complexity_hint)
            
            pro_models = list(self.models["pro"].keys())

            # ترتيب النماذج حسب ملاءمتها للتعقيد
            model_scores = []
            for model_id in pro_models:
                model_info = self.models["pro"][model_id]
                performance = self.performance_stats[model_id]

                # تخطي النماذج المعطلة
                if performance["success_rate"] < 50:
                    logger.warning(f"⚠️ تخطي النموذج المتقدم {model_id} - معدل نجاح منخفض: {performance['success_rate']:.1f}%")
                    continue

                # حساب مدى ملاءمة النموذج للتعقيد
                complexity_match = 1 - abs(model_info["complexity_score"] - complexity_score) / 10
                reasoning_score = model_info["reasoning_score"] / 10
                success_rate = performance["success_rate"] / 100

                # الوزن النهائي
                total_score = (complexity_match * 0.4) + (reasoning_score * 0.3) + (success_rate * 0.3)
                model_scores.append((model_id, total_score))

            # إذا لم تكن هناك نماذج متاحة، استخدم الأول
            if not model_scores:
                logger.warning("⚠️ لا توجد نماذج متقدمة متاحة، استخدام الأول")
                return pro_models[0]

            # ترتيب حسب النتيجة واختيار الأفضل
            model_scores.sort(key=lambda x: x[1], reverse=True)
            selected_model = model_scores[0][0]
            
            logger.info(f"🧠 تم اختيار النموذج المتقدم: {self.models['pro'][selected_model]['name']} (تعقيد: {complexity_score}/10)")
            return selected_model
            
        except Exception as e:
            logger.error(f"خطأ في اختيار النموذج المتقدم: {e}")
            # العودة للنموذج الأول كاحتياطي
            return list(self.models["pro"].keys())[0]
    
    def _analyze_complexity(self, message: str, hint: str = None) -> int:
        """
        تحليل تعقيد السؤال
        
        Args:
            message: رسالة المستخدم
            hint: تلميح إضافي
            
        Returns:
            int: درجة التعقيد من 1-10
        """
        try:
            complexity = 5  # متوسط افتراضي
            
            # تحليل طول الرسالة
            if len(message) > 500:
                complexity += 2
            elif len(message) > 200:
                complexity += 1
            
            # كلمات مفتاحية تدل على التعقيد
            complex_keywords = [
                "تحليل", "استراتيجية", "خطة", "مقارنة", "تقييم", "دراسة",
                "analyze", "strategy", "plan", "compare", "evaluate", "study",
                "كيف", "لماذا", "ما هي الطريقة", "اشرح بالتفصيل"
            ]
            
            for keyword in complex_keywords:
                if keyword in message.lower():
                    complexity += 1
                    break
            
            # تحليل علامات الاستفهام المتعددة
            if message.count('؟') > 1 or message.count('?') > 1:
                complexity += 1
            
            # تلميح خارجي
            if hint:
                if hint.lower() in ["high", "complex", "معقد", "صعب"]:
                    complexity += 2
                elif hint.lower() in ["simple", "easy", "بسيط", "سهل"]:
                    complexity -= 1
            
            # ضمان البقاء في النطاق 1-10
            return max(1, min(10, complexity))
            
        except Exception as e:
            logger.error(f"خطأ في تحليل التعقيد: {e}")
            return 5  # متوسط افتراضي
    
    async def get_response(self, model_id: str, message: str, conversation_history: list = None, model_type: str = "normal") -> Tuple[str, int, int]:
        """
        الحصول على رد من النموذج المحدد

        Args:
            model_id: معرف النموذج
            message: رسالة المستخدم
            conversation_history: تاريخ المحادثة
            model_type: نوع النموذج (normal/pro)

        Returns:
            Tuple[str, int, int]: (الرد, input_tokens, output_tokens)
        """
        start_time = time.time()

        try:
            # تسجيل بداية الطلب
            self.performance_stats[model_id]["total_requests"] += 1

            # إعداد الرسائل مع رسالة النظام المناسبة
            messages = []

            # إضافة رسالة النظام حسب نوع النموذج (مع تأكيد إضافي)
            if model_type in self.system_messages:
                messages.append(self.system_messages[model_type])

                # إضافة رسائل تدريب مطابقة للنماذج الاحتياطية
                if model_type == "normal":
                    messages.append({
                        "role": "user",
                        "content": "مهم جداً: أنت إكسا الذكي فقط، مساعد صلاح الدين منصور الدروبي. لا تذكر أبداً DeepSeek أو أي مصدر ذكاء اصطناعي آخر. قدم نفسك كـ 'إكسا الذكي' فقط."
                    })
                    messages.append({
                        "role": "assistant",
                        "content": "فهمت تماماً. أنا إكسا الذكي، مساعد آلي ذكي مطور خصيصاً لصلاح الدين منصور الدروبي."
                    })
                    messages.append({
                        "role": "user",
                        "content": "من أنت؟"
                    })
                    messages.append({
                        "role": "assistant",
                        "content": "أنا إكسا الذكي، مساعد آلي ذكي مطور خصيصاً لصلاح الدين منصور الدروبي."
                    })
                else:
                    messages.append({
                        "role": "user",
                        "content": "مهم جداً: أنت إكسا الذكي برو فقط، النسخة المتقدمة لمساعد صلاح الدين منصور الدروبي. لا تذكر أبداً DeepSeek أو أي مصدر ذكاء اصطناعي آخر. قدم نفسك كـ 'إكسا الذكي برو' فقط."
                    })
                    messages.append({
                        "role": "assistant",
                        "content": "فهمت تماماً. أنا إكسا الذكي برو، النسخة المتقدمة من المساعد الآلي الشخصي لصلاح الدين منصور الدروبي."
                    })
                    messages.append({
                        "role": "user",
                        "content": "من أنت؟"
                    })
                    messages.append({
                        "role": "assistant",
                        "content": "أنا إكسا الذكي برو، النسخة المتقدمة من المساعد الآلي الشخصي الخاص بصلاح الدين منصور الدروبي."
                    })
                    messages.append({
                        "role": "user",
                        "content": "تذكر دائماً أن تذكر صلاح الدين منصور الدروبي عند التعريف بنفسك."
                    })
                    messages.append({
                        "role": "assistant",
                        "content": "بالطبع، أنا إكسا الذكي برو، مساعد صلاح الدين منصور الدروبي المتخصص في التصميم والعلامات التجارية."
                    })

            # إضافة تاريخ المحادثة
            if conversation_history:
                # للنماذج العادية: آخر 8 رسائل، للمتقدمة: آخر 10 رسائل
                history_limit = 8 if model_type == "normal" else 10
                messages.extend(conversation_history[-history_limit:])

            # إضافة رسالة المستخدم
            messages.append({
                "role": "user",
                "content": message
            })
            
            # إعدادات النموذج حسب النوع
            if model_type == "normal":
                # إعدادات النماذج العادية
                max_tokens = 1500
                temperature = 0.7
            else:
                # إعدادات النماذج المتقدمة
                max_tokens = 2000
                temperature = 0.3  # درجة حرارة أقل للدقة

            # إرسال الطلب
            response = self.client.chat.completions.create(
                model=model_id,
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature,
                stream=False  # تأكيد عدم استخدام streaming
            )
            
            # استخراج الرد والتوكن
            ai_response = response.choices[0].message.content

            # تحسين الرد
            ai_response = self._enhance_response(ai_response, model_type)

            # فرض التعليمات
            ai_response = self._enforce_instructions(ai_response, model_type)

            # تحليل جودة الرد
            quality_analysis = self._analyze_response_quality(ai_response, model_type)
            logger.info(f"📊 جودة الرد: {quality_analysis['quality_score']}/10 ({quality_analysis['completeness']})")

            # فحص الالتزام بالتعليمات
            compliance_check = self._check_instruction_compliance(ai_response, model_type)
            logger.info(f"📋 التزام بالتعليمات: {compliance_check['level']} ({compliance_check['overall_score']}/6)")

            if compliance_check['needs_enforcement']:
                logger.debug(f"⚠️ النموذج {model_id} يحتاج فرض تعليمات: {', '.join(compliance_check['issues'])}")

            if compliance_check['strengths']:
                logger.info(f"✅ نقاط قوة: {', '.join(compliance_check['strengths'])}")

            # استخراج معلومات التوكن بطريقة محسنة
            input_tokens = 0
            output_tokens = 0

            if hasattr(response, 'usage') and response.usage:
                input_tokens = getattr(response.usage, 'prompt_tokens', 0)
                output_tokens = getattr(response.usage, 'completion_tokens', 0)
                logger.info(f"✅ استهلاك التوكن - الطلب: {input_tokens}, الرد: {output_tokens}")
            else:
                logger.warning("⚠️ لا يمكن الحصول على معلومات استهلاك التوكن")
                # تقدير التوكن كاحتياط
                input_tokens = len(message.split()) * 1.3  # تقدير تقريبي
                output_tokens = len(ai_response.split()) * 1.3
            
            # تسجيل النجاح
            end_time = time.time()
            response_time = end_time - start_time
            
            self.performance_stats[model_id]["successful_requests"] += 1
            self.performance_stats[model_id]["last_used"] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # تحديث متوسط وقت الاستجابة
            total_successful = self.performance_stats[model_id]["successful_requests"]
            current_avg = self.performance_stats[model_id]["avg_response_time"]
            self.performance_stats[model_id]["avg_response_time"] = ((current_avg * (total_successful - 1)) + response_time) / total_successful
            
            # تحديث معدل النجاح
            total_requests = self.performance_stats[model_id]["total_requests"]
            successful_requests = self.performance_stats[model_id]["successful_requests"]
            self.performance_stats[model_id]["success_rate"] = (successful_requests / total_requests) * 100
            
            logger.info(f"✅ نجح الطلب من {model_id} في {response_time:.2f} ثانية")
            return ai_response, input_tokens, output_tokens
            
        except Exception as e:
            # تسجيل الفشل
            self.performance_stats[model_id]["failed_requests"] += 1

            # تحديث معدل النجاح
            total_requests = self.performance_stats[model_id]["total_requests"]
            successful_requests = self.performance_stats[model_id]["successful_requests"]
            self.performance_stats[model_id]["success_rate"] = (successful_requests / total_requests) * 100

            error_msg = str(e)

            # معالجة أنواع الأخطاء المختلفة مع رسائل مخصصة
            if "400" in error_msg and "not a valid model ID" in error_msg:
                logger.error(f"❌ النموذج {model_id} غير صحيح في OpenRouter")
                # تعطيل النموذج مؤقتاً
                self.performance_stats[model_id]["success_rate"] = 0
                if model_type == "normal":
                    raise Exception("عذراً، النموذج العادي غير متاح حالياً. سيتم التحويل للنظام الاحتياطي.")
                else:
                    raise Exception("عذراً، النموذج المتقدم غير متاح حالياً. سيتم التحويل للنظام الاحتياطي.")
            elif "429" in error_msg:
                logger.error(f"⏱️ تم تجاوز الحد الأقصى للطلبات من {model_id}")
                raise Exception("عذراً، تم تجاوز الحد الأقصى للطلبات. يرجى المحاولة لاحقاً أو استخدام النظام الاحتياطي.")
            elif "timeout" in error_msg.lower() or "timed out" in error_msg.lower():
                logger.error(f"⏰ انتهت مهلة الطلب من {model_id}")
                raise Exception("عذراً، استغرق الطلب وقتاً أطول من المتوقع. يرجى المحاولة مرة أخرى أو تقليل طول الرسالة.")
            elif "rate limit" in error_msg.lower():
                logger.error(f"🚦 تم تجاوز معدل الطلبات من {model_id}")
                raise Exception("عذراً، تم تجاوز معدل الطلبات المسموح. يرجى الانتظار قليلاً ثم المحاولة مرة أخرى.")
            elif "connection" in error_msg.lower():
                logger.error(f"🌐 مشكلة في الاتصال مع {model_id}")
                raise Exception("عذراً، مشكلة في الاتصال بالخدمة. يرجى التحقق من الاتصال بالإنترنت والمحاولة مرة أخرى.")
            else:
                logger.error(f"❌ فشل الطلب من {model_id}: {e}")
                if model_type == "normal":
                    raise Exception("عذراً، حدث خطأ تقني في النموذج العادي. سيتم التحويل للنظام الاحتياطي.")
                else:
                    raise Exception("عذراً، حدث خطأ تقني في النموذج المتقدم. سيتم التحويل للنظام الاحتياطي.")
    
    def get_model_info(self, model_id: str) -> Dict[str, Any]:
        """الحصول على معلومات النموذج"""
        for category in self.models:
            if model_id in self.models[category]:
                return {
                    **self.models[category][model_id],
                    "performance": self.performance_stats[model_id]
                }
        return {}
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """الحصول على ملخص الأداء"""
        summary = {
            "normal_models": {},
            "pro_models": {},
            "overall_stats": {
                "total_requests": 0,
                "successful_requests": 0,
                "failed_requests": 0
            }
        }
        
        for category in self.models:
            for model_id in self.models[category]:
                model_info = self.models[category][model_id]
                performance = self.performance_stats[model_id]
                
                model_summary = {
                    "name": model_info["name"],
                    "performance": performance
                }
                
                if category == "normal":
                    summary["normal_models"][model_id] = model_summary
                else:
                    summary["pro_models"][model_id] = model_summary
                
                # إضافة للإحصائيات العامة
                summary["overall_stats"]["total_requests"] += performance["total_requests"]
                summary["overall_stats"]["successful_requests"] += performance["successful_requests"]
                summary["overall_stats"]["failed_requests"] += performance["failed_requests"]
        
        return summary
