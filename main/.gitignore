# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Bot Data
data/users_data.json
data/bot_stats.json

# Logs
*.log
logs/

# Config (if contains sensitive data)
# core/config.py

# Assets (optional - uncomment if you don't want to track images)
# assets/
