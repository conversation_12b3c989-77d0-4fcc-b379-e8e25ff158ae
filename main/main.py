#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
البوت الرئيسي - للمستخدمين العاديين
نقطة البداية لتشغيل البوت الرئيسي
"""

import sys
import os

# إضافة المجلد الحالي لمسار Python
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# إضافة مسار المكتبات المشتركة
shared_dir = os.path.join(os.path.dirname(current_dir), 'shared')
sys.path.insert(0, shared_dir)

# استيراد نظام السجلات
from utils.logging_config import get_main_logger, log_startup, log_shutdown, log_error

from core.telegram import main

if __name__ == "__main__":
    logger = get_main_logger()

    try:
        log_startup("البوت الرئيسي")
        logger.info("📋 البوت مخصص للمستخدمين العاديين فقط")
        logger.info("=" * 50)

        main()

    except Exception as e:
        log_error("البوت الرئيسي", e, "خطأ في تشغيل البوت الرئيسي")
        raise
    finally:
        log_shutdown("البوت الرئيسي")
