#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام النشرات والإشعارات
لإرسال التحديثات والأخبار لجميع مستخدمي البوت
"""

import json
import os
import asyncio
from datetime import datetime
from typing import List, Dict, Any
from telegram import Bot
from telegram.error import TelegramError

class NewsletterSystem:
    """نظام النشرات والإشعارات"""
    
    def __init__(self, bot_token: str, admin_chat_id: str):
        self.bot = Bot(token=bot_token)
        self.admin_chat_id = admin_chat_id
        self.users_file = "shared/database/users_data.json"
        self.newsletter_file = "shared/database/newsletters.json"
        self.stats_file = "shared/database/newsletter_stats.json"

        # إنشاء مجلد البيانات إذا لم يكن موجوداً
        os.makedirs("shared/database", exist_ok=True)
    
    def load_users_data(self) -> Dict[str, Any]:
        """تحميل بيانات المستخدمين"""
        try:
            if os.path.exists(self.users_file):
                with open(self.users_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            print(f"خطأ في تحميل بيانات المستخدمين: {e}")
            return {}
    
    def load_newsletter_history(self) -> List[Dict[str, Any]]:
        """تحميل تاريخ النشرات"""
        try:
            if os.path.exists(self.newsletter_file):
                with open(self.newsletter_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return []
        except Exception as e:
            print(f"خطأ في تحميل تاريخ النشرات: {e}")
            return []
    
    def save_newsletter_history(self, newsletters: List[Dict[str, Any]]):
        """حفظ تاريخ النشرات"""
        try:
            os.makedirs(os.path.dirname(self.newsletter_file), exist_ok=True)
            with open(self.newsletter_file, 'w', encoding='utf-8') as f:
                json.dump(newsletters, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"خطأ في حفظ تاريخ النشرات: {e}")
    
    def create_newsletter(self, title: str, content: str, newsletter_type: str = "update") -> Dict[str, Any]:
        """إنشاء نشرة جديدة"""
        newsletter = {
            "id": datetime.now().strftime('%Y%m%d_%H%M%S'),
            "title": title,
            "content": content,
            "type": newsletter_type,  # update, feature, announcement, maintenance
            "created_at": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "sent_at": None,
            "status": "draft",  # draft, sending, sent, failed
            "recipients_count": 0,
            "success_count": 0,
            "failed_count": 0,
            "failed_users": []
        }
        
        return newsletter
    
    def format_newsletter_message(self, newsletter: Dict[str, Any]) -> str:
        """تنسيق رسالة النشرة"""
        type_icons = {
            "update": "🔄",
            "feature": "✨", 
            "announcement": "📢",
            "maintenance": "🔧"
        }
        
        type_names = {
            "update": "تحديث",
            "feature": "ميزة جديدة",
            "announcement": "إعلان",
            "maintenance": "صيانة"
        }
        
        icon = type_icons.get(newsletter["type"], "📢")
        type_name = type_names.get(newsletter["type"], "إشعار")
        
        message = f"{icon} **{type_name}: {newsletter['title']}**\n\n"
        message += f"{newsletter['content']}\n\n"
        message += f"📅 **تاريخ النشر**: {newsletter['created_at']}\n"
        message += f"🤖 **من**: البوت الشخصي لصلاح الدين الدروبي\n\n"
        message += "💡 **نصيحة**: استخدم الأزرار أدناه للتنقل في البوت"
        
        return message
    
    async def send_newsletter_to_user(self, user_id: str, newsletter: Dict[str, Any]) -> bool:
        """إرسال النشرة لمستخدم واحد"""
        try:
            message = self.format_newsletter_message(newsletter)
            
            await self.bot.send_message(
                chat_id=user_id,
                text=message,
                parse_mode='Markdown',
                protect_content=True  # حماية المحتوى
            )
            
            return True
            
        except TelegramError as e:
            print(f"فشل إرسال النشرة للمستخدم {user_id}: {e}")
            return False
        except Exception as e:
            print(f"خطأ غير متوقع في إرسال النشرة للمستخدم {user_id}: {e}")
            return False
    
    async def send_newsletter_to_all(self, newsletter: Dict[str, Any]) -> Dict[str, Any]:
        """إرسال النشرة لجميع المستخدمين"""
        users_data = self.load_users_data()
        
        if not users_data:
            return {
                "status": "failed",
                "message": "لا توجد مستخدمين لإرسال النشرة إليهم"
            }
        
        newsletter["status"] = "sending"
        newsletter["sent_at"] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        newsletter["recipients_count"] = len(users_data)
        
        success_count = 0
        failed_count = 0
        failed_users = []
        
        # إرسال تقرير بداية الإرسال للمدير
        await self.send_admin_notification(
            f"📤 **بدء إرسال النشرة**\n\n"
            f"📋 **العنوان**: {newsletter['title']}\n"
            f"👥 **عدد المستقبلين**: {len(users_data)}\n"
            f"🕐 **وقت البداية**: {newsletter['sent_at']}"
        )
        
        # إرسال النشرة لكل مستخدم
        for user_id, user_info in users_data.items():
            try:
                # تأخير أقصر لتحسين السرعة
                await asyncio.sleep(0.05)
                
                success = await self.send_newsletter_to_user(user_id, newsletter)
                
                if success:
                    success_count += 1
                else:
                    failed_count += 1
                    failed_users.append({
                        "user_id": user_id,
                        "username": user_info.get('اسم المستخدم', 'غير محدد')
                    })
                
                # تقرير تقدم كل 10 مستخدمين
                if (success_count + failed_count) % 10 == 0:
                    progress = (success_count + failed_count) / len(users_data) * 100
                    await self.send_admin_notification(
                        f"📊 **تقدم الإرسال**: {progress:.1f}%\n"
                        f"✅ نجح: {success_count} | ❌ فشل: {failed_count}"
                    )
                    
            except Exception as e:
                failed_count += 1
                failed_users.append({
                    "user_id": user_id,
                    "username": user_info.get('اسم المستخدم', 'غير محدد'),
                    "error": str(e)
                })
        
        # تحديث إحصائيات النشرة
        newsletter["success_count"] = success_count
        newsletter["failed_count"] = failed_count
        newsletter["failed_users"] = failed_users
        newsletter["status"] = "sent" if failed_count == 0 else "partial"
        
        # حفظ النشرة في التاريخ
        newsletters = self.load_newsletter_history()
        newsletters.append(newsletter)
        self.save_newsletter_history(newsletters)
        
        # إرسال تقرير نهائي للمدير
        await self.send_final_report(newsletter)
        
        return {
            "status": "completed",
            "success_count": success_count,
            "failed_count": failed_count,
            "newsletter": newsletter
        }
    
    async def send_admin_notification(self, message: str):
        """إرسال إشعار للمدير"""
        try:
            await self.bot.send_message(
                chat_id=self.admin_chat_id,
                text=message,
                parse_mode='Markdown'
            )
        except Exception as e:
            print(f"خطأ في إرسال إشعار للمدير: {e}")
    
    async def send_final_report(self, newsletter: Dict[str, Any]):
        """إرسال التقرير النهائي للمدير"""
        success_rate = (newsletter["success_count"] / newsletter["recipients_count"]) * 100
        
        report = f"""
📊 **تقرير إرسال النشرة النهائي**

📋 **العنوان**: {newsletter['title']}
🆔 **معرف النشرة**: {newsletter['id']}

📈 **الإحصائيات**:
👥 **إجمالي المستقبلين**: {newsletter['recipients_count']}
✅ **نجح الإرسال**: {newsletter['success_count']}
❌ **فشل الإرسال**: {newsletter['failed_count']}
📊 **معدل النجاح**: {success_rate:.1f}%

🕐 **الأوقات**:
📅 **تاريخ الإنشاء**: {newsletter['created_at']}
📤 **تاريخ الإرسال**: {newsletter['sent_at']}

📋 **الحالة النهائية**: {newsletter['status']}
        """
        
        if newsletter["failed_users"]:
            report += f"\n❌ **المستخدمون الذين فشل إرسالهم**: {len(newsletter['failed_users'])}"
        
        await self.send_admin_notification(report)
    
    def get_newsletter_templates(self) -> Dict[str, str]:
        """قوالب النشرات الجاهزة"""
        return {
            "new_feature": """
✨ **ميزة جديدة في البوت!**

تم إضافة ميزة جديدة رائعة:
• [وصف الميزة]
• [كيفية الاستخدام]
• [الفوائد]

جرب الميزة الجديدة الآن واستمتع بتجربة محسنة!
            """,
            
            "update": """
🔄 **تحديث البوت**

تم تحديث البوت بالتحسينات التالية:
• [التحسين الأول]
• [التحسين الثاني]
• [إصلاح الأخطاء]

البوت الآن أسرع وأكثر استقراراً!
            """,
            
            "maintenance": """
🔧 **إشعار صيانة**

سيتم إجراء صيانة دورية للبوت:
📅 **التاريخ**: [التاريخ]
🕐 **الوقت**: [الوقت]
⏱️ **المدة المتوقعة**: [المدة]

نعتذر عن أي إزعاج قد يحدث خلال فترة الصيانة.
            """,
            
            "announcement": """
📢 **إعلان مهم**

[محتوى الإعلان]

شكراً لاستخدامكم البوت!
            """
        }

# دالة مساعدة لإنشاء مثيل النظام
def create_newsletter_system(bot_token: str, admin_chat_id: str) -> NewsletterSystem:
    """إنشاء مثيل من نظام النشرات"""
    return NewsletterSystem(bot_token, admin_chat_id)
