#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام فحص المستخدمين المحظورين والمقيدين في البوت الرئيسي
"""

import json
import os
import time
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List

logger = logging.getLogger(__name__)

class BanChecker:
    """فاحص حالة المستخدمين (حظر/تقييد)"""
    
    def __init__(self):
        # استخدام مسار مطلق للملفات لضمان الوصول من أي مجلد
        base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        data_dir = os.path.join(base_dir, "shared", "database")
        os.makedirs(data_dir, exist_ok=True)

        self.banned_users_file = os.path.join(data_dir, "banned_users.json")
        self.restricted_users_file = os.path.join(data_dir, "restricted_users.json")
        self.reload_signal_file = os.path.join(data_dir, "reload_restrictions_signal.txt")

        # متغيرات التخزين المؤقت
        self._restricted_users_cache = None
        self._cache_timestamp = 0
    
    def load_banned_users(self) -> Dict[str, Any]:
        """تحميل قائمة المستخدمين المحظورين"""
        try:
            if os.path.exists(self.banned_users_file):
                with open(self.banned_users_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            logger.error(f"خطأ في تحميل قائمة المحظورين: {e}")
            return {}
    
    def load_restricted_users(self, force_reload: bool = False) -> Dict[str, Any]:
        """تحميل قائمة المستخدمين المقيدين مع تخزين مؤقت ذكي"""
        try:
            current_time = time.time()

            # فحص إشارة إعادة التحميل
            should_reload = force_reload
            if os.path.exists(self.reload_signal_file):
                try:
                    os.remove(self.reload_signal_file)
                    should_reload = True
                    logger.info("تم استلام إشارة إعادة تحميل بيانات التقييد")
                except:
                    pass

            # فحص ما إذا كان التخزين المؤقت قديم (أكثر من 5 ثواني)
            if current_time - self._cache_timestamp > 5:
                should_reload = True

            # إعادة تحميل البيانات إذا لزم الأمر
            if should_reload or self._restricted_users_cache is None:
                if os.path.exists(self.restricted_users_file):
                    with open(self.restricted_users_file, 'r', encoding='utf-8') as f:
                        raw_data = json.load(f)

                    # تنظيف البيانات: إزالة المستخدمين بقيود فارغة
                    self._restricted_users_cache = {}
                    for user_id, user_data in raw_data.items():
                        restrictions = user_data.get('restrictions', [])
                        if restrictions and len(restrictions) > 0:
                            self._restricted_users_cache[user_id] = user_data
                        else:
                            logger.info(f"تم تجاهل المستخدم {user_id} لأن قيوده فارغة")
                else:
                    self._restricted_users_cache = {}

                self._cache_timestamp = current_time
                logger.debug(f"تم إعادة تحميل بيانات التقييد: {len(self._restricted_users_cache)} مستخدم مقيد")

            return self._restricted_users_cache.copy()

        except Exception as e:
            logger.error(f"خطأ في تحميل قائمة المقيدين: {e}")
            return {}
    
    def is_user_banned(self, user_id: int) -> tuple[bool, Optional[Dict[str, Any]]]:
        """فحص ما إذا كان المستخدم محظور"""
        banned_users = self.load_banned_users()
        user_id_str = str(user_id)
        
        if user_id_str in banned_users:
            return True, banned_users[user_id_str]
        return False, None
    
    def is_user_restricted(self, user_id: int, force_reload: bool = False) -> tuple[bool, Optional[Dict[str, Any]]]:
        """فحص ما إذا كان المستخدم مقيد"""
        restricted_users = self.load_restricted_users(force_reload=force_reload)
        user_id_str = str(user_id)

        if user_id_str in restricted_users:
            return True, restricted_users[user_id_str]
        return False, None
    
    def check_restriction(self, user_id: int, service: str) -> bool:
        """فحص ما إذا كان المستخدم مقيد من خدمة معينة"""
        try:
            # مسح التخزين المؤقت أولاً لضمان البيانات الحديثة
            self.clear_cache()

            # قراءة مباشرة من الملف بدون تخزين مؤقت
            restricted_users = {}
            if os.path.exists(self.restricted_users_file):
                try:
                    with open(self.restricted_users_file, 'r', encoding='utf-8') as f:
                        restricted_users = json.load(f)
                except Exception as e:
                    logger.error(f"خطأ في قراءة ملف التقييد: {e}")
                    return False

            user_id_str = str(user_id)

            # فحص وجود المستخدم في قائمة المقيدين
            if user_id_str not in restricted_users:
                logger.debug(f"المستخدم {user_id} غير موجود في قائمة المقيدين")
                return False

            user_data = restricted_users[user_id_str]
            restrictions = user_data.get('restrictions', [])

            # فحص إضافي: إذا كانت قائمة القيود فارغة، اعتبر المستخدم غير مقيد
            if not restrictions or len(restrictions) == 0:
                logger.info(f"المستخدم {user_id} لديه قيود فارغة، سيتم اعتباره غير مقيد")
                # إزالة المستخدم من الملف إذا كانت قيوده فارغة
                self._remove_empty_restriction(user_id_str, restricted_users)
                return False

            is_restricted_from_service = service in restrictions

            # تسجيل مفصل للتتبع
            logger.info(f"فحص التقييد للمستخدم {user_id} من {service}:")
            logger.info(f"  - موجود في قائمة المقيدين: نعم")
            logger.info(f"  - القيود الحالية: {restrictions}")
            logger.info(f"  - مقيد من {service}: {'نعم' if is_restricted_from_service else 'لا'}")

            return is_restricted_from_service

        except Exception as e:
            logger.error(f"خطأ في فحص التقييد للمستخدم {user_id} من {service}: {e}")
            # في حالة الخطأ، اعتبر المستخدم غير مقيد لتجنب منع الوصول خطأً
            return False
    
    def get_ban_message(self, ban_info: Dict[str, Any]) -> str:
        """إنشاء رسالة الحظر"""
        reason = ban_info.get('reason', 'غير محدد')
        ban_date = ban_info.get('ban_date', 'غير محدد')
        
        message = f"""🚫 **تم حظرك من استخدام البوت**

📋 **تفاصيل الحظر:**
• السبب: {reason}
• تاريخ الحظر: {ban_date}

📞 **للاستفسار:**
يمكنك التواصل مع الإدارة لمراجعة حالة الحظر

⚠️ **ملاحظة:**
هذا الحظر يمنعك من استخدام جميع خدمات البوت"""
        
        return message
    
    def get_restriction_message(self, service: str, restriction_info: Dict[str, Any]) -> str:
        """إنشاء رسالة التقييد"""
        reason = restriction_info.get('reason', 'غير محدد')
        restriction_date = restriction_info.get('restriction_date', 'غير محدد')
        
        service_names = {
            'ai_assistant': 'إكسا الذكي',
            'exa_ai_pro': 'إكسا الذكي برو',
            'location_access': 'معلومات الموقع',
            'about_access': 'النبذة الشخصية',
            'works_access': 'قسم الأعمال',
            'experience_access': 'قسم الخبرة',
            'achievements_access': 'قسم الإنجازات',
            'file_sending': 'إرسال الملفات',
            'help_access': 'قسم المساعدة',
            'file_sharing': 'مشاركة الملفات',
            'media_access': 'الوصول للوسائط'
        }
        
        service_name = service_names.get(service, service)
        
        message = f"""⚠️ **تم تقييد وصولك لهذه الخدمة**

🚫 **الخدمة المقيدة:** {service_name}

📋 **تفاصيل التقييد:**
• السبب: {reason}
• تاريخ التقييد: {restriction_date}

📞 **للاستفسار:**
يمكنك التواصل مع الإدارة لمراجعة القيود

💡 **ملاحظة:**
يمكنك استخدام باقي خدمات البوت بشكل طبيعي"""
        
        return message
    
    def get_user_status_summary(self, user_id: int) -> Dict[str, Any]:
        """الحصول على ملخص حالة المستخدم"""
        is_banned, ban_info = self.is_user_banned(user_id)
        is_restricted, restriction_info = self.is_user_restricted(user_id)
        
        status = {
            'user_id': user_id,
            'is_banned': is_banned,
            'is_restricted': is_restricted,
            'ban_info': ban_info,
            'restriction_info': restriction_info,
            'status_text': 'نشط'
        }
        
        if is_banned:
            status['status_text'] = 'محظور'
        elif is_restricted:
            status['status_text'] = 'مقيد'
        
        return status

    def clear_cache(self):
        """مسح التخزين المؤقت لإجبار إعادة التحميل"""
        self._restricted_users_cache = None
        self._cache_timestamp = 0
        logger.info("تم مسح تخزين بيانات التقييد المؤقت")

    def get_cache_info(self) -> Dict[str, Any]:
        """الحصول على معلومات التخزين المؤقت للتشخيص"""
        return {
            "cache_exists": self._restricted_users_cache is not None,
            "cache_timestamp": self._cache_timestamp,
            "cache_age_seconds": time.time() - self._cache_timestamp if self._cache_timestamp > 0 else 0,
            "cached_users_count": len(self._restricted_users_cache) if self._restricted_users_cache else 0
        }

    def _remove_empty_restriction(self, user_id_str: str, restricted_users: Dict[str, Any]):
        """إزالة المستخدم من قائمة المقيدين إذا كانت قيوده فارغة"""
        try:
            if user_id_str in restricted_users:
                del restricted_users[user_id_str]
                # حفظ البيانات المحدثة
                with open(self.restricted_users_file, 'w', encoding='utf-8') as f:
                    json.dump(restricted_users, f, ensure_ascii=False, indent=2)
                logger.info(f"تم إزالة المستخدم {user_id_str} من قائمة المقيدين لأن قيوده فارغة")
        except Exception as e:
            logger.error(f"خطأ في إزالة القيود الفارغة للمستخدم {user_id_str}: {e}")

    def force_clean_restrictions_file(self):
        """تنظيف قوي لملف القيود من البيانات الفارغة"""
        try:
            if not os.path.exists(self.restricted_users_file):
                logger.info("ملف القيود غير موجود")
                return

            # قراءة البيانات
            with open(self.restricted_users_file, 'r', encoding='utf-8') as f:
                restricted_users = json.load(f)

            original_count = len(restricted_users)

            # تنظيف البيانات
            cleaned_data = {}
            for user_id, user_data in restricted_users.items():
                restrictions = user_data.get('restrictions', [])
                if restrictions and len(restrictions) > 0:
                    cleaned_data[user_id] = user_data
                else:
                    logger.info(f"سيتم إزالة المستخدم {user_id} لأن قيوده فارغة: {restrictions}")

            # حفظ البيانات المنظفة
            with open(self.restricted_users_file, 'w', encoding='utf-8') as f:
                json.dump(cleaned_data, f, ensure_ascii=False, indent=2)

            # مسح التخزين المؤقت
            self.clear_cache()

            removed_count = original_count - len(cleaned_data)
            logger.info(f"تم تنظيف ملف القيود: إزالة {removed_count} مستخدم من أصل {original_count}")

            return removed_count

        except Exception as e:
            logger.error(f"خطأ في تنظيف ملف القيود: {e}")
            return 0

# إنشاء مثيل عام للاستخدام
ban_checker = BanChecker()
