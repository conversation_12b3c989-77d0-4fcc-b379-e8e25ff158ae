#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة المستخدمين البسيط للبوت الرئيسي
يوفر وظائف تسجيل المستخدمين الجدد فقط
"""

import json
import os
import sys
import logging
from datetime import datetime

# إضافة مسار المكتبات المشتركة
current_dir = os.path.dirname(os.path.abspath(__file__))
shared_dir = os.path.join(os.path.dirname(os.path.dirname(current_dir)), 'shared')
sys.path.insert(0, shared_dir)

# استيراد مدير المحافظ المشترك
from database.wallet_manager import WalletManager

logger = logging.getLogger(__name__)

class UserManager:
    """مدير المستخدمين البسيط"""

    def __init__(self, monitoring_system=None):
        self.users_file = "shared/database/users_data.json"
        self.monitoring_system = monitoring_system

        # إنشاء مجلد البيانات إذا لم يكن موجوداً
        os.makedirs("shared/database", exist_ok=True)

        # إنشاء ملف البيانات إذا لم يكن موجوداً
        self._ensure_files_exist()

        # تهيئة مدير المحافظ المشترك
        self.wallet_manager = WalletManager()
    
    def _ensure_files_exist(self):
        """التأكد من وجود ملف البيانات"""
        if not os.path.exists(self.users_file):
            with open(self.users_file, 'w', encoding='utf-8') as f:
                json.dump({}, f, ensure_ascii=False, indent=2)

    def load_users_data(self):
        """تحميل بيانات المستخدمين"""
        try:
            if os.path.exists(self.users_file):
                with open(self.users_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            logger.error(f"خطأ في تحميل بيانات المستخدمين: {e}")
            return {}

    def save_users_data(self, data):
        """حفظ بيانات المستخدمين"""
        try:
            with open(self.users_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"خطأ في حفظ بيانات المستخدمين: {e}")

    def register_user(self, user):
        """تسجيل مستخدم جديد"""
        try:
            users_data = self.load_users_data()
            user_id = str(user.id)

            # فحص ما إذا كان المستخدم موجود بالفعل
            is_new_user = user_id not in users_data

            if is_new_user:
                # إضافة مستخدم جديد بتنسيق موحد
                user_data = {
                    "اسم المستخدم": user.full_name or "غير محدد",
                    "معرف المستخدم": f"@{user.username}" if user.username else "غير محدد",
                    "أيدي المستخدم": user.id,
                    "اللغة": user.language_code or "غير محدد",
                    "تاريخ التسجيل": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    "تاريخ أول دخول": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    "آخر نشاط": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    "عدد الزيارات": 1
                }

                # إنشاء محفظة تلقائية للمستخدم الجديد
                wallet_created, wallet_number, wallet_message = self.wallet_manager.create_wallet(user.id, user_data)

                # تحديث بيانات المحفظة (سواء تم إنشاؤها أو كانت موجودة)
                user_data["رقم المحفظة"] = wallet_number
                user_data["حالة المحفظة"] = "نشطة"

                if "المحفظة موجودة بالفعل" in wallet_message:
                    logger.info(f"🏦 المستخدم {user.full_name} ({user.id}) لديه محفظة: {wallet_number}")
                elif wallet_created:
                    logger.info(f"🏦 تم إنشاء محفظة تلقائية: {wallet_number} للمستخدم {user.full_name} ({user.id})")

                    # ملاحظة: إشعار إنشاء المحفظة سيتم إرساله من start_command بعد رسالة الترحيب
                    logger.info(f"تم إنشاء محفظة للمستخدم الجديد: {user.id} - سيتم إرسال الإشعار من start_command")
                else:
                    user_data["رقم المحفظة"] = "غير متوفر"
                    user_data["حالة المحفظة"] = "خطأ في الإنشاء"
                    logger.error(f"❌ فشل في إنشاء المحفظة للمستخدم {user.full_name} ({user.id}): {wallet_message}")

                users_data[user_id] = user_data
                self.save_users_data(users_data)
                logger.info(f"تم تسجيل مستخدم جديد: {user.full_name} ({user.id})")
                return True
            else:
                # تحديث بيانات المستخدم الموجود
                users_data[user_id]["آخر نشاط"] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                users_data[user_id]["عدد الزيارات"] = users_data[user_id].get("عدد الزيارات", 0) + 1

                # تحديث المعلومات إذا تغيرت
                users_data[user_id]["اسم المستخدم"] = user.full_name or "غير محدد"
                users_data[user_id]["معرف المستخدم"] = f"@{user.username}" if user.username else "غير محدد"
                users_data[user_id]["اللغة"] = user.language_code or "غير محدد"

                # التحقق من وجود محفظة للمستخدم الموجود وإنشاؤها إذا لم تكن موجودة
                if "رقم المحفظة" not in users_data[user_id] or users_data[user_id]["رقم المحفظة"] == "غير متوفر":
                    existing_wallet = self.wallet_manager.get_user_wallet(user.id)
                    if not existing_wallet:
                        # إنشاء محفظة للمستخدم الموجود الذي لا يملك محفظة
                        wallet_created, wallet_number, wallet_message = self.wallet_manager.create_wallet(user.id, users_data[user_id])
                        if wallet_created:
                            users_data[user_id]["رقم المحفظة"] = wallet_number
                            users_data[user_id]["حالة المحفظة"] = "نشطة"
                            logger.info(f"🏦 تم إنشاء محفظة للمستخدم الموجود: {wallet_number} للمستخدم {user.full_name} ({user.id})")

                            # إرسال إشعار إنشاء المحفظة للمستخدم الموجود (مع تأخير لضمان الترتيب)
                            if self.monitoring_system:
                                try:
                                    import asyncio
                                    import time

                                    # إعداد معلومات المستخدم للإشعار
                                    user_info = {
                                        'first_name': user.full_name or "غير محدد",
                                        'username': f"@{user.username}" if user.username else "غير محدد",
                                        'id': user.id
                                    }

                                    # دالة مساعدة لإرسال الإشعار مع تأخير
                                    async def send_delayed_notification():
                                        # تأخير بسيط لضمان الترتيب الصحيح (3 ثواني)
                                        await asyncio.sleep(3)
                                        await self.monitoring_system.send_wallet_creation_notification(user_info, wallet_number)

                                    # إرسال الإشعار مع تأخير
                                    try:
                                        loop = asyncio.get_event_loop()
                                        if loop.is_running():
                                            # إذا كان هناك حلقة تعمل، استخدم create_task
                                            asyncio.create_task(send_delayed_notification())
                                        else:
                                            # إذا لم تكن هناك حلقة، استخدم run_until_complete
                                            loop.run_until_complete(send_delayed_notification())
                                    except RuntimeError:
                                        # إذا لم تكن هناك حلقة، أنشئ واحدة جديدة
                                        asyncio.run(send_delayed_notification())
                                    logger.info(f"تم جدولة إرسال إشعار إنشاء المحفظة للمستخدم الموجود: {user.id}")
                                except Exception as e:
                                    logger.warning(f"خطأ في إرسال إشعار إنشاء المحفظة للمستخدم الموجود: {e}")
                                    # المتابعة بدون توقف
                    else:
                        # المستخدم لديه محفظة، تحديث البيانات فقط
                        users_data[user_id]["رقم المحفظة"] = existing_wallet["wallet_number"]
                        users_data[user_id]["حالة المحفظة"] = "نشطة"

                self.save_users_data(users_data)
                logger.info(f"تم تحديث بيانات المستخدم: {user.full_name} ({user.id})")
                return False

        except Exception as e:
            logger.error(f"خطأ في تسجيل المستخدم: {e}")
            return False

    def get_user_wallet_info(self, user_id: int):
        """الحصول على معلومات محفظة المستخدم"""
        try:
            wallet_info = self.wallet_manager.get_user_wallet(user_id)
            if wallet_info:
                return {
                    "wallet_number": wallet_info.get("wallet_number"),
                    "balance": wallet_info.get("balance", 0.0),
                    "currency": wallet_info.get("currency", "USD"),
                    "status": wallet_info.get("status", "unknown"),
                    "created_at": wallet_info.get("created_at"),
                    "is_verified": wallet_info.get("is_verified", False)
                }
            return None
        except Exception as e:
            logger.error(f"خطأ في الحصول على معلومات المحفظة للمستخدم {user_id}: {e}")
            return None

    def create_wallet_for_existing_user(self, user_id: int):
        """إنشاء محفظة لمستخدم موجود (في حالة عدم وجود محفظة)"""
        try:
            users_data = self.load_users_data()
            user_id_str = str(user_id)

            if user_id_str not in users_data:
                return False, "المستخدم غير موجود"

            # فحص ما إذا كان لديه محفظة بالفعل
            existing_wallet = self.wallet_manager.get_user_wallet(user_id)
            if existing_wallet:
                return False, "المستخدم لديه محفظة بالفعل"

            # إنشاء المحفظة
            user_info = users_data[user_id_str]
            wallet_created, wallet_number, wallet_message = self.wallet_manager.create_wallet(user_id, user_info)

            if wallet_created:
                # تحديث بيانات المستخدم
                users_data[user_id_str]["رقم المحفظة"] = wallet_number
                users_data[user_id_str]["حالة المحفظة"] = "نشطة"
                self.save_users_data(users_data)

                logger.info(f"🏦 تم إنشاء محفظة للمستخدم الموجود: {wallet_number} للمستخدم {user_id}")
                return True, wallet_number
            else:
                return False, wallet_message

        except Exception as e:
            logger.error(f"خطأ في إنشاء محفظة للمستخدم الموجود {user_id}: {e}")
            return False, f"خطأ: {e}"

# إنشاء مثيل مدير المستخدمين
def create_user_manager(bot_token: str):
    """إنشاء مدير المستخدمين"""
    return UserManager(bot_token)
