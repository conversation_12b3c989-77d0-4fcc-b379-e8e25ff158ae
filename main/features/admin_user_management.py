#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إدارة المستخدمين للمدير
يحتوي على جميع دوال إدارة المستخدمين (إضافة، إزالة، حظر، تقييد)
"""

import json
import os
import logging
from datetime import datetime
from telegram import Update, ReplyKeyboardMarkup, KeyboardButton
from telegram.ext import ContextTypes
from telegram.constants import ParseMode

logger = logging.getLogger(__name__)

class AdminUserManagement:
    """فئة إدارة المستخدمين للمدير"""
    
    def __init__(self, bot_instance):
        self.bot = bot_instance
        self.user_manager = bot_instance.user_manager
    
    async def handle_add_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج إضافة مستخدم"""
        username = update.message.text.strip().replace('@', '')
        
        # إلغاء وضع إضافة المستخدم
        context.user_data["add_user_mode"] = False
        
        try:
            # محاولة إرسال دعوة
            success, message = await self.user_manager.invite_user(username)
            
            if success:
                response = f"""
✅ **تم إرسال الدعوة بنجاح**

👤 **المستخدم**: @{username}
📤 **حالة الدعوة**: تم الإرسال
📅 **التاريخ**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

💡 **ملاحظة**: سيتم إشعار المستخدم بالدعوة للانضمام للبوت
                """
            else:
                response = f"""
❌ **فشل في إرسال الدعوة**

👤 **المستخدم**: @{username}
🚫 **السبب**: {message}

💡 **اقتراح**: تأكد من صحة المعرف وحاول مرة أخرى
                """
            
            await update.message.reply_text(
                response,
                parse_mode='Markdown',
                reply_markup=self.bot.get_user_management_keyboard()
            )
            
        except Exception as e:
            logger.error(f"خطأ في إضافة المستخدم: {e}")
            await update.message.reply_text(
                f"❌ خطأ في إضافة المستخدم: {e}",
                reply_markup=self.bot.get_user_management_keyboard()
            )
    
    async def handle_remove_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج إزالة مستخدم"""
        identifier = update.message.text.strip()
        
        # إلغاء وضع إزالة المستخدم
        context.user_data["remove_user_mode"] = False
        
        try:
            # البحث عن المستخدم
            user_id, user_info = self.user_manager.find_user_by_identifier(identifier)
            
            if not user_id:
                await update.message.reply_text(
                    f"""❌ **لم يتم العثور على المستخدم**

🔍 **البحث عن**: `{identifier}`

💡 **اقتراحات**:
• تأكد من صحة المعرف أو الأيدي
• جرب البحث بالاسم الكامل
• استخدم "👥 المستخدمين" لرؤية القائمة""",
                    parse_mode='Markdown',
                    reply_markup=self.bot.get_user_management_keyboard()
                )
                return
            
            # إزالة المستخدم
            success, message = self.user_manager.remove_user(user_id)
            
            if success:
                username = user_info.get('معرف المستخدم', 'غير محدد')
                username_display = f"@{username}" if username != 'غير محدد' else 'غير محدد'

                response = f"""
✅ تم حذف المستخدم بنجاح

👤 المستخدم: {user_info.get('اسم المستخدم', 'غير محدد')}
@ المعرف: {username_display}
🆔 الأيدي: {user_info.get('أيدي المستخدم', user_id)}
📅 تاريخ الحذف: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

⚠️ ملاحظة: إذا عاد المستخدم، سيعامل كمستخدم جديد
                """
            else:
                response = f"""
❌ فشل في حذف المستخدم

🚫 السبب: {message}
                """
            
            await update.message.reply_text(
                response,
                parse_mode='HTML',
                reply_markup=self.bot.get_user_management_keyboard()
            )
            
        except Exception as e:
            logger.error(f"خطأ في إزالة المستخدم: {e}")
            await update.message.reply_text(
                f"❌ خطأ في إزالة المستخدم: {e}",
                reply_markup=self.bot.get_user_management_keyboard()
            )
    
    async def handle_ban_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج حظر مستخدم"""
        identifier = update.message.text.strip()
        
        # إلغاء وضع حظر المستخدم
        context.user_data["ban_user_mode"] = False
        
        try:
            # البحث عن المستخدم
            user_id, user_info = self.user_manager.find_user_by_identifier(identifier)
            
            if not user_id:
                await update.message.reply_text(
                    f"""❌ **لم يتم العثور على المستخدم**

🔍 **البحث عن**: `{identifier}`

💡 **اقتراحات**:
• تأكد من صحة المعرف أو الأيدي
• جرب البحث بالاسم الكامل
• استخدم "👥 المستخدمين" لرؤية القائمة""",
                    parse_mode='Markdown',
                    reply_markup=self.bot.get_user_management_keyboard()
                )
                return
            
            # حظر المستخدم
            success, message = self.user_manager.ban_user(user_id, user_info, "حظر من المدير")
            
            if success:
                response = f"""
🚫 **تم حظر المستخدم بنجاح**

👤 **المستخدم**: {user_info.get('اسم المستخدم', 'غير محدد')}
@ **المعرف**: @{user_info.get('معرف المستخدم', 'غير محدد')}
🆔 **الأيدي**: `{user_info.get('أيدي المستخدم', user_id)}`
📅 **تاريخ الحظر**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

⚠️ **تأثير الحظر**: المستخدم لن يتمكن من استخدام البوت نهائياً
                """
            else:
                response = f"""
❌ **فشل في حظر المستخدم**

🚫 **السبب**: {message}
                """
            
            await update.message.reply_text(
                response,
                parse_mode='Markdown',
                reply_markup=self.bot.get_user_management_keyboard()
            )
            
        except Exception as e:
            logger.error(f"خطأ في حظر المستخدم: {e}")
            await update.message.reply_text(
                f"❌ خطأ في حظر المستخدم: {e}",
                reply_markup=self.bot.get_user_management_keyboard()
            )
    
    async def handle_restrict_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج تقييد مستخدم"""
        identifier = update.message.text.strip()
        
        # إلغاء وضع تقييد المستخدم
        context.user_data["restrict_user_mode"] = False
        
        try:
            # البحث عن المستخدم
            user_id, user_info = self.user_manager.find_user_by_identifier(identifier)
            
            if not user_id:
                await update.message.reply_text(
                    f"""❌ **لم يتم العثور على المستخدم**

🔍 **البحث عن**: `{identifier}`

💡 **اقتراحات**:
• تأكد من صحة المعرف أو الأيدي
• جرب البحث بالاسم الكامل
• استخدم "👥 المستخدمين" لرؤية القائمة""",
                    parse_mode='Markdown',
                    reply_markup=self.bot.get_user_management_keyboard()
                )
                return
            
            # تقييد المستخدم (منع إكسا الذكي افتراضياً)
            restrictions = ["ai_assistant"]
            success, message = self.user_manager.restrict_user(user_id, user_info, restrictions)
            
            if success:
                response = f"""
⚠️ **تم تقييد المستخدم بنجاح**

👤 **المستخدم**: {user_info.get('اسم المستخدم', 'غير محدد')}
@ **المعرف**: @{user_info.get('معرف المستخدم', 'غير محدد')}
🆔 **الأيدي**: `{user_info.get('أيدي المستخدم', user_id)}`
📅 **تاريخ التقييد**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🚫 **القيود المطبقة**:
• منع استخدام إكسا الذكي
• يمكن استخدام باقي الميزات

💡 **ملاحظة**: يمكن للمستخدم استخدام البوت لكن بقيود
                """
            else:
                response = f"""
❌ **فشل في تقييد المستخدم**

🚫 **السبب**: {message}
                """
            
            await update.message.reply_text(
                response,
                parse_mode='Markdown',
                reply_markup=self.bot.get_user_management_keyboard()
            )
            
        except Exception as e:
            logger.error(f"خطأ في تقييد المستخدم: {e}")
            await update.message.reply_text(
                f"❌ خطأ في تقييد المستخدم: {e}",
                reply_markup=self.bot.get_user_management_keyboard()
            )
