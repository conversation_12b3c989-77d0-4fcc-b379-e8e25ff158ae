#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إدارة التصدير للمدير
نظام متقدم لتصدير البيانات بصيغ مختلفة
"""

import os
import json
from datetime import datetime
from telegram import Update
from telegram.ext import ContextTypes
from telegram.constants import ParseMode
import logging

# استيراد المكتبات الاختيارية مع معالجة الأخطاء
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

try:
    from docx import Document
    from docx.shared import Inches
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False

logger = logging.getLogger(__name__)

class AdminExportManagement:
    """فئة إدارة التصدير للمدير"""
    
    def __init__(self, bot_instance):
        self.bot = bot_instance
        self.export_dir = "exports"
        
        # إنشاء مجلد التصدير
        os.makedirs(self.export_dir, exist_ok=True)
    
    async def export_management_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض قائمة إدارة التصدير"""
        try:
            # إحصائيات التصدير
            export_files = os.listdir(self.export_dir) if os.path.exists(self.export_dir) else []
            total_exports = len(export_files)
            
            # حساب حجم ملفات التصدير
            total_size = 0
            for export_file in export_files:
                file_path = os.path.join(self.export_dir, export_file)
                if os.path.exists(file_path):
                    total_size += os.path.getsize(file_path)
            
            size_mb = total_size / (1024 * 1024)
            
            # إحصائيات المستخدمين
            users_data = self.bot.user_manager.load_users_data()
            total_users = len(users_data)
            
            message = f"""
<b>📤 إدارة التصدير</b>

📊 <b>الإحصائيات:</b>
• عدد المستخدمين: {total_users}
• ملفات التصدير: {total_exports}
• الحجم الإجمالي: {size_mb:.2f} ميجابايت

📋 <b>صيغ التصدير المتاحة:</b>
• PDF - تقرير مفصل مع تنسيق احترافي
• Excel - جدول بيانات قابل للتحرير
• Word - مستند نصي منسق

👇 <b>اختر صيغة التصدير المطلوبة</b>
            """
            
            await update.message.reply_text(
                message,
                parse_mode=ParseMode.HTML,
                reply_markup=self.get_export_management_keyboard()
            )
            
        except Exception as e:
            logger.error(f"خطأ في عرض قائمة التصدير: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في عرض قائمة التصدير",
                reply_markup=self.bot.get_admin_keyboard()
            )
    
    def get_export_management_keyboard(self):
        """لوحة مفاتيح إدارة التصدير"""
        keyboard = [
            ["📄 تصدير PDF", "📊 تصدير Excel"],
            ["📝 تصدير Word", "📋 عرض الملفات"],
            ["🗑️ حذف الملفات القديمة", "🔙 العودة للخيارات المتقدمة"]
        ]
        
        from telegram import ReplyKeyboardMarkup
        return ReplyKeyboardMarkup(keyboard, resize_keyboard=True)
    
    async def export_to_pdf(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """تصدير البيانات إلى PDF"""
        try:
            if not REPORTLAB_AVAILABLE:
                await update.message.reply_text(
                    "❌ مكتبة ReportLab غير مثبتة. يرجى تثبيتها أولاً:\npip install reportlab",
                    reply_markup=self.get_export_management_keyboard()
                )
                return

            loading_msg = await update.message.reply_text("⏳ جاري إنشاء ملف PDF...")
            
            users_data = self.bot.user_manager.load_users_data()
            
            if not users_data:
                await loading_msg.edit_text("📭 لا توجد بيانات للتصدير")
                return
            
            # إنشاء اسم الملف
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"users_report_{timestamp}.pdf"
            filepath = os.path.join(self.export_dir, filename)
            
            # إنشاء مستند PDF
            doc = SimpleDocTemplate(filepath, pagesize=A4)
            story = []
            styles = getSampleStyleSheet()
            
            # عنوان التقرير
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                alignment=1  # وسط
            )
            
            title = Paragraph("تقرير المستخدمين", title_style)
            story.append(title)
            
            # معلومات التقرير
            info_style = ParagraphStyle(
                'InfoStyle',
                parent=styles['Normal'],
                fontSize=12,
                spaceAfter=20
            )
            
            report_info = f"""
            تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            عدد المستخدمين: {len(users_data)}
            """
            
            info_para = Paragraph(report_info, info_style)
            story.append(info_para)
            story.append(Spacer(1, 12))
            
            # إنشاء جدول البيانات
            data = [['#', 'اسم المستخدم', 'المعرف', 'الأيدي', 'تاريخ التسجيل']]
            
            for i, (user_id, user_info) in enumerate(users_data.items(), 1):
                row = [
                    str(i),
                    user_info.get('اسم المستخدم', 'غير محدد'),
                    user_info.get('معرف المستخدم', 'غير محدد'),
                    str(user_info.get('أيدي المستخدم', user_id)),
                    user_info.get('تاريخ التسجيل', 'غير محدد')
                ]
                data.append(row)
            
            # تنسيق الجدول
            table = Table(data)
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(table)
            
            # بناء المستند
            doc.build(story)
            
            # حساب حجم الملف
            file_size = os.path.getsize(filepath)
            size_mb = file_size / (1024 * 1024)
            
            await loading_msg.delete()
            
            # إرسال الملف
            with open(filepath, 'rb') as pdf_file:
                await update.message.reply_document(
                    document=pdf_file,
                    filename=filename,
                    caption=f"""
✅ <b>تم إنشاء تقرير PDF بنجاح</b>

📄 <b>اسم الملف:</b> {filename}
📊 <b>الحجم:</b> {size_mb:.2f} ميجابايت
👥 <b>عدد المستخدمين:</b> {len(users_data)}
📅 <b>تاريخ الإنشاء:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🔒 <b>محمي من التحميل والحفظ</b>
                    """,
                    parse_mode=ParseMode.HTML,
                    protect_content=True,
                    reply_markup=self.get_export_management_keyboard()
                )
            
        except Exception as e:
            logger.error(f"خطأ في تصدير PDF: {e}")
            await update.message.reply_text(
                f"❌ فشل في إنشاء ملف PDF: {str(e)}",
                reply_markup=self.get_export_management_keyboard()
            )
    
    async def export_to_excel(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """تصدير البيانات إلى Excel"""
        try:
            if not PANDAS_AVAILABLE:
                await update.message.reply_text(
                    "❌ مكتبة Pandas غير مثبتة. يرجى تثبيتها أولاً:\npip install pandas openpyxl",
                    reply_markup=self.get_export_management_keyboard()
                )
                return

            loading_msg = await update.message.reply_text("⏳ جاري إنشاء ملف Excel...")
            
            users_data = self.bot.user_manager.load_users_data()
            
            if not users_data:
                await loading_msg.edit_text("📭 لا توجد بيانات للتصدير")
                return
            
            # إنشاء اسم الملف
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"users_data_{timestamp}.xlsx"
            filepath = os.path.join(self.export_dir, filename)
            
            # تحضير البيانات للـ DataFrame
            data_list = []
            for user_id, user_info in users_data.items():
                row = {
                    'أيدي المستخدم': user_info.get('أيدي المستخدم', user_id),
                    'اسم المستخدم': user_info.get('اسم المستخدم', 'غير محدد'),
                    'معرف المستخدم': user_info.get('معرف المستخدم', 'غير محدد'),
                    'الاسم الأول': user_info.get('الاسم الأول', 'غير محدد'),
                    'الاسم الأخير': user_info.get('الاسم الأخير', 'غير محدد'),
                    'تاريخ التسجيل': user_info.get('تاريخ التسجيل', 'غير محدد'),
                    'آخر نشاط': user_info.get('آخر نشاط', 'غير محدد'),
                    'عدد الرسائل': user_info.get('عدد الرسائل', 0),
                    'عدد الأوامر': user_info.get('عدد الأوامر', 0)
                }
                data_list.append(row)
            
            # إنشاء DataFrame
            df = pd.DataFrame(data_list)
            
            # إنشاء ملف Excel مع تنسيق
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='بيانات المستخدمين', index=False)
                
                # تنسيق الورقة
                worksheet = writer.sheets['بيانات المستخدمين']
                
                # تعديل عرض الأعمدة
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            # حساب حجم الملف
            file_size = os.path.getsize(filepath)
            size_mb = file_size / (1024 * 1024)
            
            await loading_msg.delete()
            
            # إرسال الملف
            with open(filepath, 'rb') as excel_file:
                await update.message.reply_document(
                    document=excel_file,
                    filename=filename,
                    caption=f"""
✅ <b>تم إنشاء ملف Excel بنجاح</b>

📊 <b>اسم الملف:</b> {filename}
📈 <b>الحجم:</b> {size_mb:.2f} ميجابايت
👥 <b>عدد المستخدمين:</b> {len(users_data)}
📅 <b>تاريخ الإنشاء:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

💡 <b>يمكن فتحه في Excel أو Google Sheets</b>
🔒 <b>محمي من التحميل والحفظ</b>
                    """,
                    parse_mode=ParseMode.HTML,
                    protect_content=True,
                    reply_markup=self.get_export_management_keyboard()
                )
            
        except Exception as e:
            logger.error(f"خطأ في تصدير Excel: {e}")
            await update.message.reply_text(
                f"❌ فشل في إنشاء ملف Excel: {str(e)}",
                reply_markup=self.get_export_management_keyboard()
            )
    
    async def export_to_word(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """تصدير البيانات إلى Word"""
        try:
            if not DOCX_AVAILABLE:
                await update.message.reply_text(
                    "❌ مكتبة python-docx غير مثبتة. يرجى تثبيتها أولاً:\npip install python-docx",
                    reply_markup=self.get_export_management_keyboard()
                )
                return

            loading_msg = await update.message.reply_text("⏳ جاري إنشاء ملف Word...")
            
            users_data = self.bot.user_manager.load_users_data()
            
            if not users_data:
                await loading_msg.edit_text("📭 لا توجد بيانات للتصدير")
                return
            
            # إنشاء اسم الملف
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"users_report_{timestamp}.docx"
            filepath = os.path.join(self.export_dir, filename)
            
            # إنشاء مستند Word
            doc = Document()
            
            # عنوان المستند
            title = doc.add_heading('تقرير المستخدمين', 0)
            title.alignment = 1  # وسط
            
            # معلومات التقرير
            doc.add_paragraph(f'تاريخ التقرير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
            doc.add_paragraph(f'عدد المستخدمين: {len(users_data)}')
            doc.add_paragraph('')
            
            # إنشاء جدول
            table = doc.add_table(rows=1, cols=5)
            table.style = 'Table Grid'
            
            # رؤوس الجدول
            hdr_cells = table.rows[0].cells
            hdr_cells[0].text = '#'
            hdr_cells[1].text = 'اسم المستخدم'
            hdr_cells[2].text = 'المعرف'
            hdr_cells[3].text = 'الأيدي'
            hdr_cells[4].text = 'تاريخ التسجيل'
            
            # إضافة البيانات
            for i, (user_id, user_info) in enumerate(users_data.items(), 1):
                row_cells = table.add_row().cells
                row_cells[0].text = str(i)
                row_cells[1].text = user_info.get('اسم المستخدم', 'غير محدد')
                row_cells[2].text = user_info.get('معرف المستخدم', 'غير محدد')
                row_cells[3].text = str(user_info.get('أيدي المستخدم', user_id))
                row_cells[4].text = user_info.get('تاريخ التسجيل', 'غير محدد')
            
            # حفظ المستند
            doc.save(filepath)
            
            # حساب حجم الملف
            file_size = os.path.getsize(filepath)
            size_mb = file_size / (1024 * 1024)
            
            await loading_msg.delete()
            
            # إرسال الملف
            with open(filepath, 'rb') as word_file:
                await update.message.reply_document(
                    document=word_file,
                    filename=filename,
                    caption=f"""
✅ <b>تم إنشاء مستند Word بنجاح</b>

📝 <b>اسم الملف:</b> {filename}
📄 <b>الحجم:</b> {size_mb:.2f} ميجابايت
👥 <b>عدد المستخدمين:</b> {len(users_data)}
📅 <b>تاريخ الإنشاء:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

💡 <b>يمكن فتحه في Microsoft Word أو Google Docs</b>
🔒 <b>محمي من التحميل والحفظ</b>
                    """,
                    parse_mode=ParseMode.HTML,
                    protect_content=True,
                    reply_markup=self.get_export_management_keyboard()
                )
            
        except Exception as e:
            logger.error(f"خطأ في تصدير Word: {e}")
            await update.message.reply_text(
                f"❌ فشل في إنشاء ملف Word: {str(e)}",
                reply_markup=self.get_export_management_keyboard()
            )
