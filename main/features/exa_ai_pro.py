#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إكسا الذكي برو - النموذج المتقدم
متصل بـ DeepSeek-R1 (DeepSeek Reasoner)
"""

import openai
import logging
import sys
import os
import time
import asyncio
from typing import List, Dict, Any, Tuple

# إضافة المجلد الرئيسي لمسار Python
root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, root_dir)

from core.config import DEEPSEEK_PRO_API_KEY, DEEPSEEK_BASE_URL

logger = logging.getLogger(__name__)

class ExaAiPro:
    """إكسا الذكي برو - للاستشارات المتقدمة والمعقدة"""

    def __init__(self):
        """تهيئة إكسا الذكي برو"""
        self.client = openai.OpenAI(
            api_key=DEEPSEEK_PRO_API_KEY,
            base_url=DEEPSEEK_BASE_URL,
            timeout=90.0,  # timeout أطول للوضع المتقدم (90 ثانية)
            max_retries=3,  # إعادة المحاولة 3 مرات
        )

        # رسالة النظام للوضع برو
        self.system_message = {
            "role": "system",
            "content": """أنت إكسا الذكي برو، المساعد الآلي المتقدم الخاص بصلاح الدين منصور الدروبي.

معلومات عن المالك:
- الاسم: صلاح الدين منصور الدروبي
- التخصص: مصمم جرافيك ومتخصص في العلامات التجارية
- الخبرة: تصميم العلامات التجارية والشعارات وتصميم التطبيقات والرسوم المتحركة والقرطاسية
- التعليم: بكالوريوس جرافيك وملتيميديا
- الاهتمامات: العلامات التجارية، الهويات التجارية، المنتجات ثلاثية الأبعاد، تصميم UI/UX

تعليمات مهمة للوضع برو:
1. تحدث باللغة العربية دائماً
2. كن مهذباً ومفيداً ومتقدماً في الإجابات
3. لا تذكر أبداً أنك مدعوم بـ DeepSeek أو أي مصدر ذكاء اصطناعي آخر
4. قدم نفسك كـ "إكسا الذكي برو" فقط
5. قدم إجابات متقدمة ومفصلة في التصميم والعلامات التجارية
6. استخدم خبرة متقدمة في التحليل والاستشارات
7. كن ودوداً ومساعداً مع تقديم حلول احترافية
8. استخدم تفكيراً عميقاً ومنطقياً في الإجابات
9. قدم تحليلات مفصلة واستراتيجيات عملية
10. للمشاريع المعقدة، قدم خطط عمل مرحلية
11. استخدم أمثلة عملية ونصائح احترافية"""
        }

        # إحصائيات الأداء
        self.performance_stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "timeout_errors": 0,
            "avg_response_time": 0.0,
            "last_request_time": None
        }

        logger.info("🧠 تم تهيئة إكسا الذكي برو بنجاح")

    async def _retry_with_backoff(self, func, max_retries: int = 3, base_delay: float = 1.0):
        """
        إعادة المحاولة مع تأخير متزايد

        Args:
            func: الدالة المراد تنفيذها
            max_retries: عدد المحاولات القصوى
            base_delay: التأخير الأساسي بالثواني

        Returns:
            نتيجة الدالة أو None في حالة الفشل
        """
        for attempt in range(max_retries):
            try:
                return await func()
            except Exception as e:
                if attempt == max_retries - 1:  # آخر محاولة
                    raise e

                # حساب التأخير (exponential backoff)
                delay = base_delay * (2 ** attempt)
                logger.warning(f"⚠️ فشلت المحاولة {attempt + 1}/{max_retries}، إعادة المحاولة خلال {delay} ثانية: {e}")
                await asyncio.sleep(delay)

        return None

    async def get_response(self, user_message: str, conversation_history: list = None) -> tuple:
        """
        الحصول على رد من إكسا الذكي برو باستخدام DeepSeek-R1

        Returns:
            tuple: (الرد, input_tokens, output_tokens)
        """

        async def _make_api_call():
            """دالة داخلية لإجراء استدعاء API"""
            # إعداد الرسائل
            messages = [self.system_message]

            # إضافة تاريخ المحادثة إذا كان متوفراً
            if conversation_history:
                messages.extend(conversation_history[-10:])  # آخر 10 رسائل

            # إضافة رسالة المستخدم الحالية
            messages.append({
                "role": "user",
                "content": user_message
            })

            # إرسال الطلب إلى DeepSeek-R1 مع timeout محسن
            response = self.client.chat.completions.create(
                model="deepseek-reasoner",  # استخدام DeepSeek-R1
                messages=messages,
                max_tokens=2000,  # توكنز أكثر للوضع برو
                temperature=0.3,   # درجة حرارة أقل للدقة
                stream=False  # تأكيد عدم استخدام streaming
            )

            return response

        try:
            # تسجيل بداية الطلب
            start_time = time.time()
            self.performance_stats["total_requests"] += 1
            self.performance_stats["last_request_time"] = time.strftime('%Y-%m-%d %H:%M:%S')

            logger.info("🧠 بدء طلب إكسا الذكي برو...")

            # استخدام نظام إعادة المحاولة
            response = await self._retry_with_backoff(_make_api_call, max_retries=3, base_delay=2.0)

            if response is None:
                raise Exception("فشل في الحصول على رد بعد عدة محاولات")

            # استخراج الرد
            ai_response = response.choices[0].message.content

            # استخراج معلومات التوكن
            input_tokens = 0
            output_tokens = 0

            if hasattr(response, 'usage') and response.usage:
                input_tokens = getattr(response.usage, 'prompt_tokens', 0)
                output_tokens = getattr(response.usage, 'completion_tokens', 0)
                logger.info(f"✅ استهلاك التوكن - الطلب: {input_tokens}, الرد: {output_tokens}")
            else:
                logger.warning("⚠️ لا يمكن الحصول على معلومات استهلاك التوكن")

            # تسجيل نجاح الطلب
            end_time = time.time()
            response_time = end_time - start_time

            self.performance_stats["successful_requests"] += 1

            # تحديث متوسط وقت الاستجابة
            total_successful = self.performance_stats["successful_requests"]
            current_avg = self.performance_stats["avg_response_time"]
            self.performance_stats["avg_response_time"] = ((current_avg * (total_successful - 1)) + response_time) / total_successful

            logger.info(f"✅ تم الحصول على رد من إكسا الذكي برو بنجاح في {response_time:.2f} ثانية")
            return ai_response, input_tokens, output_tokens

        except Exception as e:
            # تسجيل فشل الطلب
            self.performance_stats["failed_requests"] += 1

            error_msg = str(e)
            logger.error(f"❌ خطأ في الحصول على رد من إكسا الذكي برو: {error_msg}")

            # تسجيل نوع الخطأ
            if "timeout" in error_msg.lower() or "timed out" in error_msg.lower():
                self.performance_stats["timeout_errors"] += 1

            # رسائل خطأ مخصصة حسب نوع المشكلة
            if "timeout" in error_msg.lower() or "timed out" in error_msg.lower():
                return "عذراً، استغرق الطلب وقتاً أطول من المتوقع. يرجى المحاولة مرة أخرى أو تقليل طول الرسالة. ⏱️", 0, 0
            elif "rate limit" in error_msg.lower():
                return "عذراً، تم تجاوز الحد المسموح من الطلبات. يرجى الانتظار قليلاً ثم المحاولة مرة أخرى. 🚦", 0, 0
            elif "connection" in error_msg.lower():
                return "عذراً، مشكلة في الاتصال بالخدمة. يرجى التحقق من الاتصال بالإنترنت والمحاولة مرة أخرى. 🌐", 0, 0
            else:
                return "عذراً، حدث خطأ تقني في الوضع المتقدم. يرجى المحاولة مرة أخرى لاحقاً. 🧠", 0, 0

    def get_performance_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الأداء"""
        return {
            **self.performance_stats,
            "success_rate": (self.performance_stats["successful_requests"] / max(1, self.performance_stats["total_requests"])) * 100,
            "timeout_rate": (self.performance_stats["timeout_errors"] / max(1, self.performance_stats["total_requests"])) * 100
        }

    def is_conversation_active(self, context_data: dict) -> bool:
        """التحقق من وجود محادثة نشطة مع إكسا الذكي برو"""
        return context_data.get("exa_ai_pro_active", False)

    def start_conversation(self, context_data: dict):
        """بدء محادثة جديدة مع إكسا الذكي برو"""
        context_data["exa_mode"] = True
        context_data["exa_ai_active"] = False
        context_data["exa_ai_pro_active"] = True
        context_data["exa_conversation"] = []
        logger.info("تم بدء محادثة جديدة مع إكسا الذكي برو")

    def end_conversation(self, context_data: dict):
        """إنهاء المحادثة مع إكسا الذكي برو"""
        context_data["exa_mode"] = False
        context_data["exa_ai_pro_active"] = False
        context_data["exa_conversation"] = []
        logger.info("تم إنهاء المحادثة مع إكسا الذكي برو")

    def add_to_conversation(self, context_data: dict, role: str, content: str):
        """إضافة رسالة إلى تاريخ المحادثة"""
        if "exa_conversation" not in context_data:
            context_data["exa_conversation"] = []

        context_data["exa_conversation"].append({
            "role": role,
            "content": content
        })

        # الحفاظ على آخر 20 رسالة فقط لتوفير الذاكرة
        if len(context_data["exa_conversation"]) > 20:
            context_data["exa_conversation"] = context_data["exa_conversation"][-20:]

    def get_welcome_message(self) -> str:
        """رسالة الترحيب لإكسا الذكي برو"""
        return """🧠︙مرحباً بك في إكسا الذكي برو!

أنا المساعد الذكي المتقدم الخاص بالمالك. هنا لمساعدتك في أي استفسارات أو أسئلة قد تكون لديك.

يمكنني مساعدتك في︙
• طرح أسئلة حول التصميم والعلاقات التجارية
• الاستفسار عن خدمات المالك
• طلب نصائح في مجال الجرافيك والمنتمديا
• تحليل المشاريع وتقديم استراتيجيات عملية
• وضع خطط عمل مرحلية للمشاريع المعقدة
• أو أي شيء آخر تحتاج المساعدة فيه

🧠︙ **مميز:** أستخدم نموذج ExaPro-Q1 المتقدم للتفكير العميق والتحليل المفصل

كيف يمكنني مساعدتك اليوم ؟ 🤖"""
