#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إدارة النشرات للمدير
يحتوي على جميع دوال إدارة النشرات (إنشاء، عرض، إرسال)
"""

import json
import os
import logging
from datetime import datetime
from telegram import Update, ReplyKeyboardMarkup, KeyboardButton
from telegram.ext import ContextTypes
from telegram.constants import ParseMode

logger = logging.getLogger(__name__)

class AdminNewsletterManagement:
    """فئة إدارة النشرات للمدير"""
    
    def __init__(self, bot_instance):
        self.bot = bot_instance
        self.newsletter_system = bot_instance.newsletter_system
    
    async def newsletter_management_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض قائمة إدارة النشر"""
        # استخدام نظام الحماية الجديد
        if not await self.bot.admin_command_protection.check_command_permission(update, context, "إدارة النشر"):
            return
        
        # تفعيل وضع إدارة النشر
        context.user_data["newsletter_management_mode"] = True
        
        management_message = """
📰 **إدارة النشر**

مرحباً بك في نظام إدارة النشرات والإشعارات. يمكنك من هنا:

📋 **نشرة سابقة**
• عرض جميع النشرات المرسلة سابقاً
• مراجعة تفاصيل كل نشرة
• إحصائيات الإرسال والوصول

📝 **نشرة جديدة**
• إنشاء نشرة جديدة وإرسالها لجميع المستخدمين
• اختيار نوع النشرة (تحديث، ميزة جديدة، إعلان، صيانة)
• معاينة النشرة قبل الإرسال

🔙 **العودة للقائمة السابقة**
• العودة لقائمة المدير الرئيسية

👇 **اختر الخيار المطلوب من الأزرار أدناه**
        """
        
        await update.message.reply_text(
            management_message,
            parse_mode='Markdown',
            reply_markup=self.bot.get_newsletter_management_keyboard()
        )
    
    async def admin_newsletter_prompt(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """طلب إنشاء نشرة جديدة"""
        newsletter_help = """
📝 **إنشاء نشرة جديدة**

أرسل النشرة بالتنسيق التالي:

```
العنوان: [عنوان النشرة]
النوع: [update/feature/announcement/maintenance]
المحتوى:
[محتوى النشرة هنا]
```

**مثال:**
```
العنوان: ميزة إدارة النشر الجديدة
النوع: feature
المحتوى:
تم إضافة نظام إدارة النشر المتطور!
يمكنك الآن إنشاء نشرات جديدة ومراجعة النشرات السابقة.
استمتع بالميزات الجديدة في إدارة النشر.
```

**أنواع النشرات:**
• `update` - تحديث عام 🔄
• `feature` - ميزة جديدة ✨
• `announcement` - إعلان مهم 📢
• `maintenance` - إشعار صيانة 🔧

👇 **اكتب النشرة الآن أو استخدم الأزرار للعودة**
        """
        
        # تفعيل وضع إنشاء النشرة
        context.user_data["admin_newsletter_mode"] = True
        
        await update.message.reply_text(
            newsletter_help,
            parse_mode='Markdown',
            reply_markup=self.bot.get_newsletter_management_keyboard()
        )
    
    async def handle_admin_newsletter(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج إنشاء النشرة"""
        try:
            # تحليل النشرة
            newsletter = self.newsletter_system.parse_newsletter(update.message.text)
            
            # حفظ النشرة المؤقتة
            context.user_data["pending_newsletter"] = newsletter
            context.user_data["admin_newsletter_mode"] = False
            context.user_data["newsletter_confirmation_mode"] = True
            
            # إنشاء معاينة النشرة
            preview = self.newsletter_system.format_newsletter_preview(newsletter)
            
            confirmation_message = f"""
📋 **معاينة النشرة**

{preview}

📊 **تفاصيل الإرسال:**
👥 **سيتم الإرسال إلى**: {len(self.bot.load_users_data())} مستخدم
🆔 **معرف النشرة**: {newsletter['id']}

👇 **اختر أحد الأزرار أدناه**
            """
            
            try:
                await update.message.reply_text(
                    confirmation_message,
                    parse_mode='Markdown',
                    reply_markup=self.bot.get_confirmation_keyboard()
                )
            except Exception as e:
                # إذا فشل Markdown، أرسل بدون تنسيق
                confirmation_message_plain = confirmation_message.replace('**', '').replace('`', '')
                await update.message.reply_text(
                    confirmation_message_plain,
                    reply_markup=self.bot.get_confirmation_keyboard()
                )
                
        except Exception as e:
            error_message = f"❌ خطأ في تحليل النشرة: {str(e)}"
            
            # إذا كان الخطأ متعلق بـ Markdown، أعد المحاولة بدون تنسيق
            if "parse entities" in str(e).lower() or "markdown" in str(e).lower():
                await update.message.reply_text(
                    "❌ خطأ في تنسيق النشرة. يرجى المحاولة مرة أخرى بتنسيق أبسط.",
                    reply_markup=self.bot.get_newsletter_management_keyboard()
                )
            else:
                await update.message.reply_text(
                    error_message,
                    reply_markup=self.bot.get_newsletter_management_keyboard()
                )
    
    async def show_previous_newsletters(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض النشرات السابقة"""
        # استخدام نظام الحماية الجديد
        if not await self.bot.admin_command_protection.check_command_permission(update, context, "نشرة سابقة"):
            return
        
        try:
            # تحميل تاريخ النشرات
            newsletters = self.newsletter_system.load_newsletter_history()
            
            if not newsletters:
                await update.message.reply_text(
                    """📋 **النشرات السابقة**
                    
❌ لا توجد نشرات سابقة

💡 **نصيحة**: يمكنك إنشاء نشرة جديدة باستخدام زر "📝 نشرة جديدة"
                    """,
                    parse_mode='Markdown',
                    reply_markup=self.bot.get_newsletter_management_keyboard()
                )
                return
            
            # ترتيب النشرات حسب التاريخ (الأحدث أولاً)
            newsletters.sort(key=lambda x: x.get('created_at', ''), reverse=True)
            
            # تحديد عدد النشرات المعروضة (آخر 10)
            newsletters_to_show = newsletters[:10]
            
            message = f"""📋 **النشرات السابقة**

📊 **الإحصائيات**:
• إجمالي النشرات: {len(newsletters)}
• المعروضة: {len(newsletters_to_show)}

---------------------------
"""
            
            for i, newsletter in enumerate(newsletters_to_show, 1):
                # تحديد أيقونة النوع
                type_icons = {
                    "update": "🔄",
                    "feature": "✨", 
                    "announcement": "📢",
                    "maintenance": "🔧"
                }
                
                type_names = {
                    "update": "تحديث",
                    "feature": "ميزة جديدة",
                    "announcement": "إعلان",
                    "maintenance": "صيانة"
                }
                
                icon = type_icons.get(newsletter.get("type", "announcement"), "📢")
                type_name = type_names.get(newsletter.get("type", "announcement"), "إشعار")
                
                # حالة الإرسال
                status_icons = {
                    "draft": "📝",
                    "sending": "📤",
                    "sent": "✅",
                    "partial": "⚠️",
                    "failed": "❌"
                }
                
                status_names = {
                    "draft": "مسودة",
                    "sending": "جاري الإرسال",
                    "sent": "تم الإرسال",
                    "partial": "إرسال جزئي",
                    "failed": "فشل الإرسال"
                }
                
                status = newsletter.get("status", "draft")
                status_icon = status_icons.get(status, "📝")
                status_name = status_names.get(status, "غير محدد")
                
                message += f"""
{i}. {icon} **{type_name}: {newsletter.get('title', 'بدون عنوان')}**

📝 **المحتوى**: {newsletter.get('content', 'لا يوجد محتوى')[:100]}{'...' if len(newsletter.get('content', '')) > 100 else ''}

🆔 **معرف النشرة**: `{newsletter.get('id', 'غير محدد')}`
📅 **تاريخ الإنشاء**: {newsletter.get('created_at', 'غير محدد')}
📤 **تاريخ الإرسال**: {newsletter.get('sent_at', 'لم يتم الإرسال')}
{status_icon} **الحالة**: {status_name}

📊 **إحصائيات الإرسال**:
• المستقبلين: {newsletter.get('recipients_count', 0)}
• نجح: {newsletter.get('success_count', 0)}
• فشل: {newsletter.get('failed_count', 0)}
• معدل النجاح: {(newsletter.get('success_count', 0) / max(newsletter.get('recipients_count', 1), 1) * 100):.1f}%

---------------------------
"""
            
            # تقسيم الرسالة إذا كانت طويلة
            if len(message) > 4000:
                parts = [message[i:i+4000] for i in range(0, len(message), 4000)]
                for i, part in enumerate(parts):
                    if i == len(parts) - 1:
                        await update.message.reply_text(
                            part,
                            parse_mode='Markdown',
                            reply_markup=self.bot.get_newsletter_management_keyboard()
                        )
                    else:
                        await update.message.reply_text(
                            part,
                            parse_mode='Markdown'
                        )
            else:
                await update.message.reply_text(
                    message,
                    parse_mode='Markdown',
                    reply_markup=self.bot.get_newsletter_management_keyboard()
                )
                
        except Exception as e:
            await update.message.reply_text(
                f"❌ خطأ في تحميل النشرات السابقة: {e}",
                reply_markup=self.bot.get_newsletter_management_keyboard()
            )
    
    async def confirm_newsletter_send(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """تأكيد إرسال النشرة"""
        # استخدام نظام الحماية الجديد
        if not await self.bot.admin_command_protection.check_command_permission(update, context, "تأكيد النشرة"):
            return
        
        newsletter = context.user_data.get("pending_newsletter")
        if newsletter:
            # إلغاء وضع التأكيد وإعادة الأزرار العادية
            context.user_data["newsletter_confirmation_mode"] = False
            
            await update.message.reply_text(
                "📤 **بدء إرسال النشرة...**\n\nسيتم إرسال تقارير التقدم أثناء العملية.",
                parse_mode='Markdown',
                reply_markup=self.bot.get_newsletter_management_keyboard()
            )
            
            # إرسال النشرة في الخلفية
            import asyncio
            asyncio.create_task(self.newsletter_system.send_newsletter_to_all(newsletter))
            
            # حذف النشرة المؤقتة
            del context.user_data["pending_newsletter"]
        else:
            await update.message.reply_text(
                "❌ لم يتم العثور على النشرة المؤقتة",
                reply_markup=self.bot.get_newsletter_management_keyboard()
            )
    
    async def cancel_newsletter_send(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """إلغاء إرسال النشرة"""
        # استخدام نظام الحماية الجديد
        if not await self.bot.admin_command_protection.check_command_permission(update, context, "إلغاء النشرة"):
            return
        
        # إلغاء وضع التأكيد وإعادة الأزرار العادية
        context.user_data["newsletter_confirmation_mode"] = False
        
        # حذف النشرة المؤقتة
        if "pending_newsletter" in context.user_data:
            del context.user_data["pending_newsletter"]
        
        await update.message.reply_text(
            "❌ تم إلغاء إرسال النشرة",
            reply_markup=self.bot.get_newsletter_management_keyboard()
        )
