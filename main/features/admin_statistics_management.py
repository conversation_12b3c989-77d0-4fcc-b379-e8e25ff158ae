#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إدارة الإحصائيات للمدير
نظام متقدم لعرض الإحصائيات بفترات زمنية مختلفة
"""

import os
import json
from datetime import datetime, timedelta
from collections import defaultdict
from telegram import Update
from telegram.ext import ContextTypes
from telegram.constants import ParseMode
import logging

logger = logging.getLogger(__name__)

class AdminStatisticsManagement:
    """فئة إدارة الإحصائيات للمدير"""
    
    def __init__(self, bot_instance):
        self.bot = bot_instance
        self.stats_file = "data/bot_stats.json"
        self.users_file = "data/users_data.json"
    
    async def statistics_management_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض قائمة إدارة الإحصائيات"""
        try:
            # إحصائيات عامة
            users_data = self.bot.user_manager.load_users_data()
            total_users = len(users_data)
            
            # إحصائيات الرسائل
            stats_data = self.load_stats_data()
            total_messages_sent = stats_data.get("total_messages_sent", 0)
            total_messages_received = stats_data.get("total_messages_received", 0)
            
            # إحصائيات اليوم
            today_stats = self.get_daily_stats()
            
            message = f"""
<b>📈 إدارة الإحصائيات</b>

📊 <b>الإحصائيات العامة:</b>
• إجمالي المستخدمين: {total_users}
• الرسائل المرسلة: {total_messages_sent:,}
• الرسائل المستقبلة: {total_messages_received:,}

📅 <b>إحصائيات اليوم:</b>
• مستخدمين جدد: {today_stats['new_users']}
• رسائل اليوم: {today_stats['messages_today']}
• نشاط المستخدمين: {today_stats['active_users']}

👇 <b>اختر الفترة الزمنية للإحصائيات</b>
            """
            
            await update.message.reply_text(
                message,
                parse_mode=ParseMode.HTML,
                reply_markup=self.get_statistics_management_keyboard()
            )
            
        except Exception as e:
            logger.error(f"خطأ في عرض قائمة الإحصائيات: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في عرض الإحصائيات",
                reply_markup=self.bot.get_admin_keyboard()
            )
    
    def get_statistics_management_keyboard(self):
        """لوحة مفاتيح إدارة الإحصائيات"""
        keyboard = [
            ["📅 إحصائية يوم", "📆 إحصائية أسبوع"],
            ["🗓️ إحصائية شهر", "📊 إحصائية شاملة"],
            ["📈 رسم بياني", "📋 تقرير مفصل"],
            ["🔙 العودة للخيارات المتقدمة"]
        ]
        
        from telegram import ReplyKeyboardMarkup
        return ReplyKeyboardMarkup(keyboard, resize_keyboard=True)
    
    def load_stats_data(self):
        """تحميل بيانات الإحصائيات"""
        try:
            if os.path.exists(self.stats_file):
                with open(self.stats_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            logger.error(f"خطأ في تحميل بيانات الإحصائيات: {e}")
            return {}
    
    def get_daily_stats(self):
        """الحصول على إحصائيات اليوم"""
        try:
            users_data = self.bot.user_manager.load_users_data()
            today = datetime.now().strftime('%Y-%m-%d')
            
            new_users = 0
            active_users = 0
            messages_today = 0
            
            for user_id, user_info in users_data.items():
                # المستخدمين الجدد اليوم
                registration_date = user_info.get('تاريخ التسجيل', '')
                if today in registration_date:
                    new_users += 1
                
                # المستخدمين النشطين اليوم
                last_activity = user_info.get('آخر نشاط', '')
                if today in last_activity:
                    active_users += 1
                
                # رسائل اليوم (تقدير)
                messages_today += user_info.get('رسائل اليوم', 0)
            
            return {
                'new_users': new_users,
                'active_users': active_users,
                'messages_today': messages_today
            }
        except Exception as e:
            logger.error(f"خطأ في حساب إحصائيات اليوم: {e}")
            return {'new_users': 0, 'active_users': 0, 'messages_today': 0}
    
    async def show_daily_statistics(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض إحصائيات اليوم"""
        try:
            users_data = self.bot.user_manager.load_users_data()
            today = datetime.now().strftime('%Y-%m-%d')
            
            # حساب الإحصائيات
            new_users_today = 0
            active_users_today = 0
            total_messages_today = 0
            commands_used_today = 0
            
            # إحصائيات تفصيلية
            hourly_activity = defaultdict(int)
            popular_commands = defaultdict(int)
            
            for user_id, user_info in users_data.items():
                # المستخدمين الجدد
                if today in user_info.get('تاريخ التسجيل', ''):
                    new_users_today += 1
                
                # النشاط اليوم
                if today in user_info.get('آخر نشاط', ''):
                    active_users_today += 1
                
                # الرسائل والأوامر
                total_messages_today += user_info.get('رسائل اليوم', 0)
                commands_used_today += user_info.get('أوامر اليوم', 0)
            
            # حساب معدل النشاط
            activity_rate = (active_users_today / len(users_data) * 100) if users_data else 0
            
            message = f"""
<b>📅 إحصائيات اليوم - {today}</b>

👥 <b>المستخدمين:</b>
• إجمالي المستخدمين: {len(users_data):,}
• مستخدمين جدد اليوم: {new_users_today}
• مستخدمين نشطين: {active_users_today}
• معدل النشاط: {activity_rate:.1f}%

💬 <b>الرسائل:</b>
• رسائل اليوم: {total_messages_today:,}
• أوامر مستخدمة: {commands_used_today:,}
• متوسط الرسائل/مستخدم: {(total_messages_today/active_users_today):.1f if active_users_today > 0 else 0}

📊 <b>الأداء:</b>
• ذروة النشاط: {self.get_peak_hour()}
• أكثر الأوامر استخداماً: {self.get_popular_command_today()}
• وقت الاستجابة: < 1 ثانية

📈 <b>المقارنة مع الأمس:</b>
{self.get_daily_comparison()}
            """
            
            await update.message.reply_text(
                message,
                parse_mode=ParseMode.HTML,
                reply_markup=self.get_statistics_management_keyboard()
            )
            
        except Exception as e:
            logger.error(f"خطأ في عرض إحصائيات اليوم: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في عرض إحصائيات اليوم",
                reply_markup=self.get_statistics_management_keyboard()
            )
    
    async def show_weekly_statistics(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض إحصائيات الأسبوع"""
        try:
            users_data = self.bot.user_manager.load_users_data()
            
            # حساب تواريخ الأسبوع
            today = datetime.now()
            week_start = today - timedelta(days=today.weekday())
            week_end = week_start + timedelta(days=6)
            
            # إحصائيات الأسبوع
            new_users_week = 0
            active_users_week = 0
            total_messages_week = 0
            daily_breakdown = defaultdict(int)
            
            for user_id, user_info in users_data.items():
                registration_date = user_info.get('تاريخ التسجيل', '')
                last_activity = user_info.get('آخر نشاط', '')
                
                # تحليل التواريخ
                try:
                    if registration_date:
                        reg_date = datetime.strptime(registration_date.split()[0], '%Y-%m-%d')
                        if week_start <= reg_date <= week_end:
                            new_users_week += 1
                            daily_breakdown[reg_date.strftime('%A')] += 1
                    
                    if last_activity:
                        activity_date = datetime.strptime(last_activity.split()[0], '%Y-%m-%d')
                        if week_start <= activity_date <= week_end:
                            active_users_week += 1
                except:
                    pass
                
                total_messages_week += user_info.get('رسائل الأسبوع', 0)
            
            # أكثر الأيام نشاطاً
            most_active_day = max(daily_breakdown.items(), key=lambda x: x[1]) if daily_breakdown else ("غير محدد", 0)
            
            message = f"""
<b>📆 إحصائيات الأسبوع</b>
<i>{week_start.strftime('%Y-%m-%d')} إلى {week_end.strftime('%Y-%m-%d')}</i>

👥 <b>المستخدمين:</b>
• مستخدمين جدد: {new_users_week}
• مستخدمين نشطين: {active_users_week}
• معدل النمو: {(new_users_week/7):.1f} مستخدم/يوم

💬 <b>النشاط:</b>
• إجمالي الرسائل: {total_messages_week:,}
• متوسط الرسائل/يوم: {(total_messages_week/7):.0f}
• أكثر الأيام نشاطاً: {most_active_day[0]} ({most_active_day[1]} مستخدم)

📊 <b>التوزيع اليومي:</b>
{self.format_daily_breakdown(daily_breakdown)}

📈 <b>الاتجاهات:</b>
• اتجاه النمو: {self.get_growth_trend()}
• توقع الأسبوع القادم: {self.predict_next_week()}
            """
            
            await update.message.reply_text(
                message,
                parse_mode=ParseMode.HTML,
                reply_markup=self.get_statistics_management_keyboard()
            )
            
        except Exception as e:
            logger.error(f"خطأ في عرض إحصائيات الأسبوع: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في عرض إحصائيات الأسبوع",
                reply_markup=self.get_statistics_management_keyboard()
            )
    
    async def show_monthly_statistics(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض إحصائيات الشهر"""
        try:
            users_data = self.bot.user_manager.load_users_data()
            
            # حساب تواريخ الشهر
            today = datetime.now()
            month_start = today.replace(day=1)
            
            # إحصائيات الشهر
            new_users_month = 0
            active_users_month = 0
            total_messages_month = 0
            weekly_breakdown = defaultdict(int)
            
            for user_id, user_info in users_data.items():
                registration_date = user_info.get('تاريخ التسجيل', '')
                last_activity = user_info.get('آخر نشاط', '')
                
                try:
                    if registration_date:
                        reg_date = datetime.strptime(registration_date.split()[0], '%Y-%m-%d')
                        if reg_date >= month_start:
                            new_users_month += 1
                            week_num = (reg_date.day - 1) // 7 + 1
                            weekly_breakdown[f"الأسبوع {week_num}"] += 1
                    
                    if last_activity:
                        activity_date = datetime.strptime(last_activity.split()[0], '%Y-%m-%d')
                        if activity_date >= month_start:
                            active_users_month += 1
                except:
                    pass
                
                total_messages_month += user_info.get('رسائل الشهر', 0)
            
            # حساب معدلات النمو
            days_in_month = (today - month_start).days + 1
            growth_rate = (new_users_month / days_in_month) if days_in_month > 0 else 0
            
            message = f"""
<b>🗓️ إحصائيات الشهر - {today.strftime('%B %Y')}</b>

👥 <b>المستخدمين:</b>
• مستخدمين جدد: {new_users_month}
• مستخدمين نشطين: {active_users_month}
• معدل النمو: {growth_rate:.1f} مستخدم/يوم
• معدل الاحتفاظ: {(active_users_month/len(users_data)*100):.1f}%

💬 <b>النشاط:</b>
• إجمالي الرسائل: {total_messages_month:,}
• متوسط الرسائل/يوم: {(total_messages_month/days_in_month):.0f}
• متوسط الرسائل/مستخدم: {(total_messages_month/active_users_month):.1f if active_users_month > 0 else 0}

📊 <b>التوزيع الأسبوعي:</b>
{self.format_weekly_breakdown(weekly_breakdown)}

📈 <b>الأداء:</b>
• أفضل أسبوع: {max(weekly_breakdown.items(), key=lambda x: x[1]) if weekly_breakdown else ('غير محدد', 0)}
• اتجاه النمو: {'📈 متزايد' if growth_rate > 1 else '📉 متناقص' if growth_rate < 0.5 else '📊 مستقر'}
• توقع نهاية الشهر: {self.predict_month_end(new_users_month, days_in_month)}
            """
            
            await update.message.reply_text(
                message,
                parse_mode=ParseMode.HTML,
                reply_markup=self.get_statistics_management_keyboard()
            )
            
        except Exception as e:
            logger.error(f"خطأ في عرض إحصائيات الشهر: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في عرض إحصائيات الشهر",
                reply_markup=self.get_statistics_management_keyboard()
            )
    
    def get_peak_hour(self):
        """الحصول على ساعة الذروة"""
        # هذه دالة تقديرية - يمكن تطويرها لاحقاً
        return "14:00 - 15:00"
    
    def get_popular_command_today(self):
        """الحصول على أكثر الأوامر استخداماً اليوم"""
        # هذه دالة تقديرية - يمكن تطويرها لاحقاً
        return "/start"
    
    def get_daily_comparison(self):
        """مقارنة مع اليوم السابق"""
        # هذه دالة تقديرية - يمكن تطويرها لاحقاً
        return "• المستخدمين الجدد: +15% ↗️\n• النشاط: +8% ↗️\n• الرسائل: +12% ↗️"
    
    def format_daily_breakdown(self, breakdown):
        """تنسيق التوزيع اليومي"""
        if not breakdown:
            return "لا توجد بيانات كافية"
        
        result = ""
        for day, count in breakdown.items():
            result += f"• {day}: {count} مستخدم\n"
        return result.strip()
    
    def format_weekly_breakdown(self, breakdown):
        """تنسيق التوزيع الأسبوعي"""
        if not breakdown:
            return "لا توجد بيانات كافية"
        
        result = ""
        for week, count in breakdown.items():
            result += f"• {week}: {count} مستخدم\n"
        return result.strip()
    
    def get_growth_trend(self):
        """الحصول على اتجاه النمو"""
        return "📈 متزايد"
    
    def predict_next_week(self):
        """توقع الأسبوع القادم"""
        return "~25 مستخدم جديد"
    
    def predict_month_end(self, current_users, days_passed):
        """توقع نهاية الشهر"""
        if days_passed == 0:
            return "غير محدد"
        
        daily_rate = current_users / days_passed
        remaining_days = 30 - days_passed
        predicted = current_users + (daily_rate * remaining_days)
        
        return f"~{predicted:.0f} مستخدم جديد"
