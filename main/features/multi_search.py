#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
البحث المتعدد للمستخدمين
يسمح بالبحث عن عدة مستخدمين في نفس الوقت
"""

import json
import os
from typing import List, Dict, Any

class MultiUserSearch:
    """نظام البحث المتعدد للمستخدمين"""
    
    def __init__(self, users_file: str = "data/users_data.json"):
        self.users_file = users_file
    
    def load_users_data(self) -> Dict[str, Any]:
        """تحميل بيانات المستخدمين"""
        try:
            if os.path.exists(self.users_file):
                with open(self.users_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            print(f"خطأ في تحميل بيانات المستخدمين: {e}")
            return {}
    
    def search_single_user(self, search_term: str, users_data: Dict[str, Any]) -> tuple:
        """البحث عن مستخدم واحد"""
        search_term = search_term.strip()
        
        for user_id, user_info in users_data.items():
            # البحث بالأيدي
            user_id_to_check = user_info.get('أيدي المستخدم', user_info.get('ID', user_id))
            if search_term == str(user_id_to_check):
                return user_info, user_id
            
            # البحث بالمعرف (مع أو بدون @)
            elif search_term.replace('@', '') == user_info.get('معرف المستخدم', '').replace('@', ''):
                return user_info, user_id
            
            # البحث بالاسم
            elif search_term.lower() in user_info.get('اسم المستخدم', '').lower():
                return user_info, user_id
        
        return None, None
    
    def search_multiple_users(self, search_terms: List[str]) -> List[Dict[str, Any]]:
        """البحث عن عدة مستخدمين"""
        users_data = self.load_users_data()
        results = []
        
        for search_term in search_terms:
            if not search_term.strip():
                continue
                
            found_user, found_user_id = self.search_single_user(search_term, users_data)
            
            if found_user:
                result = {
                    "search_term": search_term.strip(),
                    "found": True,
                    "user_data": found_user,
                    "user_id": found_user_id
                }
            else:
                result = {
                    "search_term": search_term.strip(),
                    "found": False,
                    "user_data": None,
                    "user_id": None
                }
            
            results.append(result)
        
        return results
    
    def parse_search_input(self, search_input: str) -> List[str]:
        """تحليل نص البحث لاستخراج عدة معرفات"""
        # فصل المعرفات بالفواصل أو المسافات أو أسطر جديدة
        separators = [',', '\n', ';', '|']
        
        terms = [search_input]
        
        for separator in separators:
            new_terms = []
            for term in terms:
                new_terms.extend(term.split(separator))
            terms = new_terms
        
        # تنظيف المعرفات
        cleaned_terms = []
        for term in terms:
            cleaned_term = term.strip()
            if cleaned_term:
                cleaned_terms.append(cleaned_term)
        
        return cleaned_terms
    
    def format_search_results(self, results: List[Dict[str, Any]]) -> str:
        """تنسيق نتائج البحث المتعدد"""
        if not results:
            return "❌ لم يتم العثور على أي نتائج"
        
        found_count = sum(1 for result in results if result["found"])
        not_found_count = len(results) - found_count
        
        message = f"🔍 **نتائج البحث المتعدد**\n\n"
        message += f"📊 **الإحصائيات**: {found_count} موجود | {not_found_count} غير موجود\n"
        message += "=" * 40 + "\n\n"
        
        # عرض النتائج الموجودة
        found_results = [r for r in results if r["found"]]
        if found_results:
            message += "✅ **المستخدمون الموجودون**:\n\n"
            
            for i, result in enumerate(found_results, 1):
                user_data = result["user_data"]
                user_id_display = user_data.get('أيدي المستخدم', user_data.get('ID', result["user_id"]))
                username_display = user_data.get('اسم المستخدم', 'غير محدد')
                user_handle = user_data.get('معرف المستخدم', 'غير محدد')
                
                message += f"{i}. 👤 **اسم المستخدم**: `{username_display}`\n"
                message += f"   @ **معرف المستخدم**: `@{user_handle}`\n"
                message += f"   🆔 **أيدي المستخدم**: `{user_id_display}`\n"
                message += f"   📈 **الزيارات**: {user_data.get('عدد الزيارات', 1)}\n"
                message += f"   🕐 **آخر نشاط**: {user_data.get('آخر نشاط', 'غير محدد')}\n"
                message += f"   🔍 **البحث عن**: `{result['search_term']}`\n"
                message += f"   📊 **الحالة**: {'🟢 نشط' if user_data.get('عدد الزيارات', 1) > 1 else '🟡 جديد'}\n"
                message += "-" * 40 + "\n\n"
        
        # عرض النتائج غير الموجودة
        not_found_results = [r for r in results if not r["found"]]
        if not_found_results:
            message += "❌ **المستخدمون غير الموجودون**:\n\n"
            
            for i, result in enumerate(not_found_results, 1):
                message += f"{i}. 🔍 **البحث عن**: `{result['search_term']}`\n"
                message += f"   ❌ **النتيجة**: لم يتم العثور على المستخدم\n"
                message += "-" * 20 + "\n\n"
        
        return message
    
    def get_search_help(self) -> str:
        """رسالة المساعدة للبحث المتعدد"""
        return """
🔍 **البحث المتعدد للمستخدمين**

يمكنك البحث عن عدة مستخدمين في نفس الوقت بطرق مختلفة:

**📝 طرق الفصل:**
• **بالفواصل**: `123456789, @ahmed, فاطمة`
• **بأسطر جديدة**: 
```
123456789
@ahmed
فاطمة
```
• **بالفاصلة المنقوطة**: `123456789; @ahmed; فاطمة`
• **بالخط العمودي**: `123456789 | @ahmed | فاطمة`

**🎯 طرق البحث:**
• **بالأيدي**: `123456789`
• **بالمعرف**: `@ahmed123` أو `ahmed123`
• **بالاسم**: `أحمد محمد`

**💡 مثال شامل:**
```
123456789
@ahmed123
فاطمة علي
987654321
@sara_ali
```

**📊 النتيجة:**
ستحصل على تفاصيل كل مستخدم في رسالة واحدة مع إحصائيات شاملة.
        """

# إنشاء مثيل عام للاستخدام
multi_search = MultiUserSearch()
