#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام المراقبة المنفصل للبوت الرئيسي
يرسل إشعارات لبوت الإدارة والمراقبة
"""

import httpx
import asyncio
import logging
import random
from datetime import datetime
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)

class MonitoringSystem:
    """نظام المراقبة المتقدم"""

    def __init__(self, admin_bot_token: str, admin_chat_id: str):
        """تهيئة نظام المراقبة"""
        self.admin_bot_token = admin_bot_token
        self.admin_chat_id = admin_chat_id
        self.base_url = f"https://api.telegram.org/bot{admin_bot_token}"
        
        # إحصائيات النشاط
        self.activity_stats = {
            "total_users": 0,
            "new_users_today": 0,
            "messages_today": 0,
            "button_clicks": 0,
            "ai_conversations": 0,
            "files_received": 0
        }

    def get_arabic_day(self, date_obj):
        """تحويل يوم الأسبوع للعربية"""
        days_arabic = {
            'Monday': 'الاثنين',
            'Tuesday': 'الثلاثاء',
            'Wednesday': 'الأربعاء',
            'Thursday': 'الخميس',
            'Friday': 'الجمعة',
            'Saturday': 'السبت',
            'Sunday': 'الأحد'
        }
        english_day = date_obj.strftime('%A')
        return days_arabic.get(english_day, english_day)

    async def send_notification(self, message: str, notification_type: str = "info"):
        """إرسال إشعار لبوت الإدارة"""
        try:
            formatted_message = f"🤖 **البوت الرئيسي**\n{message}"

            async with httpx.AsyncClient() as client:
                url = f"{self.base_url}/sendMessage"
                data = {
                    "chat_id": self.admin_chat_id,
                    "text": formatted_message,
                    "parse_mode": "Markdown",
                    "disable_notification": True
                }
                response = await client.post(url, data=data, timeout=2)

        except:
            # تجاهل جميع أخطاء المراقبة
            pass
    
    async def log_new_user(self, user_data: Dict[str, Any]):
        """تسجيل مستخدم جديد"""
        self.activity_stats["total_users"] += 1
        self.activity_stats["new_users_today"] += 1
        
        # إعداد بيانات المستخدم للنسخ
        first_name = user_data.get('first_name', 'غير محدد')
        username = user_data.get('username', 'غير محدد')
        user_id = user_data.get('id', 'غير محدد')

        message = (
            f"📊︙إشعار مراقبة\n"
            f"🔢︙رقم الإشعار : 101{random.randint(1000000, 9999999)}\n\n"
            f"🤖︙البوت الرئيسي\n"
            f"🤖︙الحالة : مستخدم جديد\n"
            f"💬︙رسالة نصية\n"
            f"👤︙الاسم : `{first_name}`\n"
            f"📝︙النص : انضم للبوت\n\n"
            f"📅︙اليوم : {self.get_arabic_day(datetime.now())}\n"
            f"📅︙التاريخ : {datetime.now().strftime('%Y-%m-%d')}\n"
            f"⏰︙الوقت : {datetime.now().strftime('%H:%M:%S')}"
        )
        
        try:
            await self.send_notification(message, "user")
        except:
            pass
    
    async def log_button_click(self, user_data: Dict[str, Any], button_name: str, content_type: str = "نص", is_new_user: bool = False):
        """تسجيل ضغطة زر"""
        self.activity_stats["button_clicks"] += 1

        # ترجمة أسماء الأزرار للعربية
        button_translations = {
            "location": "موقعي",
            "about": "نبذة عني",
            "works": "أعمالي",
            "experience": "خبرتي",
            "achievements": "إنجازاتي",
            "exa_ai": "إكسا الذكي",
            "help": "المساعدة"
        }

        arabic_button_name = button_translations.get(button_name, button_name)
        user_status = "جديد" if is_new_user else "قديم"

        now = datetime.now()
        arabic_day = self.get_arabic_day(now)

        # إعداد معرف المستخدم وإيدي المستخدم للنسخ
        username = user_data.get('username', 'غير محدد')
        user_id = user_data.get('id', 'غير محدد')
        first_name = user_data.get('first_name', 'غير محدد')

        message = (
            f"📊︙إشعار مراقبة\n"
            f"🔢︙رقم الإشعار︙101{random.randint(1000000, 9999999)}\n\n"
            f"🤖︙البوت الرئيسي\n"
            f"🤖︙الحالة︙{user_status}\n"
            f"💬︙رسالة نصية \n"
            f"👤︙الاسم︙`{first_name}`\n"
            f"📝︙النص︙{arabic_button_name}\n\n\n\n"
            f"📅︙اليوم︙{arabic_day}\n"
            f"📅︙التاريخ︙{now.strftime('%Y-%m-%d')}\n"
            f"⏰︙الوقت︙{now.strftime('%H:%M:%S')}"
        )

        try:
            await self.send_notification(message, "activity")
        except:
            pass
    
    async def log_bot_response(self, user_data: Dict[str, Any], response_content: str):
        """تسجيل رد البوت"""
        self.activity_stats["messages_today"] += 1

        # اختصار النص إذا كان طويلاً
        short_text = response_content[:50] + "..." if len(response_content) > 50 else response_content

        # رسالة الرد بالتنسيق المحدث
        now = datetime.now()
        arabic_day = self.get_arabic_day(now)

        # إعداد بيانات المستخدم للنسخ
        first_name = user_data.get('first_name', 'غير محدد')

        message = (
            f"📊︙إشعار مراقبة\n"
            f"🔢︙رقم الإشعار : 101{random.randint(1000000, 9999999)}\n\n"
            f"🤖︙البوت الرئيسي\n"
            f"🤖︙الحالة : رد\n"
            f"💬︙رسالة نصية\n"
            f"👤︙الاسم : `{first_name}`\n"
            f"📝︙النص : {short_text}\n\n"
            f"📅︙اليوم : {arabic_day}\n"
            f"📅︙التاريخ : {now.strftime('%Y-%m-%d')}\n"
            f"⏰︙الوقت : {now.strftime('%H:%M:%S')}"
        )

        try:
            await self.send_notification(message, "info")
        except:
            pass
    
    async def log_ai_conversation(self, user_data: Dict[str, Any], user_question: str, ai_response: str):
        """تسجيل محادثة مع إكسا الذكي"""
        self.activity_stats["ai_conversations"] += 1
        
        # اختصار النصوص
        short_question = user_question[:80] + "..." if len(user_question) > 80 else user_question
        short_response = ai_response[:80] + "..." if len(ai_response) > 80 else ai_response
        
        # إعداد بيانات المستخدم للنسخ
        first_name = user_data.get('first_name', 'غير محدد')
        username = user_data.get('username', 'غير محدد')
        user_id = user_data.get('id', 'غير محدد')

        message = (
            f"📊︙إشعار مراقبة\n"
            f"🔢︙رقم الإشعار︙101{random.randint(1000000, 9999999)}\n\n"
            f"🤖︙البوت الرئيسي\n"
            f"🤖︙الحالة︙محادثة إكسا\n"
            f"💬︙رسالة نصية \n"
            f"👤︙الاسم︙`{first_name}`\n"
            f"📝︙النص︙{short_question}\n\n\n\n"
            f"📅︙اليوم︙{self.get_arabic_day(datetime.now())}\n"
            f"📅︙التاريخ︙{datetime.now().strftime('%Y-%m-%d')}\n"
            f"⏰︙الوقت︙{datetime.now().strftime('%H:%M:%S')}"
        )
        
        try:
            await self.send_notification(message, "ai")
        except Exception as e:
            logger.debug(f"خطأ في تسجيل محادثة AI: {e}")
            pass
    
    async def log_file_received(self, user_data: Dict[str, Any], file_info: Dict[str, Any]):
        """تسجيل استلام ملف"""
        self.activity_stats["files_received"] += 1
        
        file_type = file_info.get("type", "غير محدد")
        file_name = file_info.get("name", "غير محدد")
        file_size = file_info.get("size", 0)
        caption = file_info.get("caption", "بدون وصف")
        
        # تحويل حجم الملف لوحدة مناسبة
        if file_size > 1024 * 1024:
            size_str = f"{file_size / (1024 * 1024):.1f} MB"
        elif file_size > 1024:
            size_str = f"{file_size / 1024:.1f} KB"
        else:
            size_str = f"{file_size} بايت"
        
        # أيقونات أنواع الملفات
        file_icons = {
            "photo": "📷",
            "document": "📄", 
            "audio": "🎵",
            "video": "🎬",
            "voice": "🎤"
        }
        
        icon = file_icons.get(file_type, "📎")
        
        # إعداد بيانات المستخدم للنسخ
        first_name = user_data.get('first_name', 'غير محدد')
        username = user_data.get('username', 'غير محدد')
        user_id = user_data.get('id', 'غير محدد')

        message = (
            f"📊︙إشعار مراقبة\n"
            f"🔢︙رقم الإشعار : 101{random.randint(1000000, 9999999)}\n\n"
            f"🤖︙البوت الرئيسي\n"
            f"🤖︙الحالة : ملف جديد\n"
            f"💬︙رسالة نصية\n"
            f"👤︙الاسم : `{first_name}`\n"
            f"📝︙النص : {file_type} - {file_name}\n\n"
            f"📅︙اليوم : {self.get_arabic_day(datetime.now())}\n"
            f"📅︙التاريخ : {datetime.now().strftime('%Y-%m-%d')}\n"
            f"⏰︙الوقت : {datetime.now().strftime('%H:%M:%S')}"
        )
        
        try:
            await self.send_notification(message, "file")
        except Exception as e:
            logger.debug(f"خطأ في تسجيل ملف: {e}")
            pass
    
    async def send_daily_summary(self):
        """إرسال ملخص يومي"""
        message = (
            f"📊︙إشعار مراقبة\n"
            f"🔢︙رقم الإشعار : 101{random.randint(1000000, 9999999)}\n\n"
            f"🤖︙البوت الرئيسي\n"
            f"🤖︙الحالة : ملخص يومي\n"
            f"💬︙رسالة نصية\n"
            f"👤︙الاسم : النظام\n"
            f"📝︙النص : إحصائيات اليوم\n\n"
            f"📅︙اليوم : {self.get_arabic_day(datetime.now())}\n"
            f"📅︙التاريخ : {datetime.now().strftime('%Y-%m-%d')}\n"
            f"⏰︙الوقت : {datetime.now().strftime('%H:%M:%S')}"
        )
        
        try:
            await self.send_notification(message, "success")
        except Exception as e:
            logger.debug(f"خطأ في إرسال الملخص اليومي: {e}")
            pass
        
        # إعادة تعيين الإحصائيات اليومية
        self.activity_stats["new_users_today"] = 0
        self.activity_stats["messages_today"] = 0
        self.activity_stats["button_clicks"] = 0
        self.activity_stats["ai_conversations"] = 0
        self.activity_stats["files_received"] = 0
    
    def get_stats(self) -> Dict[str, int]:
        """الحصول على الإحصائيات الحالية"""
        return self.activity_stats.copy()
