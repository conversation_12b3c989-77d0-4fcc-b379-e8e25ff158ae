#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مساعد الذكاء الاصطناعي - إكسا الذكي الموحد
يدير كلاً من إكسا الذكي العادي وإكسا الذكي برو
"""

import logging
import sys
import os
import time
import asyncio
from typing import List, Dict, Any, Tuple

# إضافة المجلد الرئيسي لمسار Python
root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, root_dir)

# إضافة مسار المكتبات المشتركة
shared_dir = os.path.join(os.path.dirname(root_dir), 'shared')
sys.path.insert(0, shared_dir)

from .exa_ai_normal import ExaAiNormal
from .exa_ai_pro import ExaAiPro
from ai_billing.token_manager import TokenManager
from ai_models.multi_model_manager import MultiModelManager

logger = logging.getLogger(__name__)

class ExaAlThakiAssistant:
    """مساعد إكسا الذكي الموحد - يدير إكسا الذكي العادي وبرو"""

    def __init__(self):
        """تهيئة مساعد الذكاء الاصطناعي الموحد"""
        self.exa_normal = ExaAiNormal()
        self.exa_pro = ExaAiPro()
        self.token_manager = TokenManager()

        # النظام الجديد للنماذج المتعددة
        try:
            self.multi_model_manager = MultiModelManager()
            self.use_multi_models = True
            logger.info("✅ تم تفعيل نظام النماذج المتعددة")
        except Exception as e:
            logger.error(f"خطأ في تهيئة النماذج المتعددة: {e}")
            self.multi_model_manager = None
            self.use_multi_models = False
            logger.warning("⚠️ سيتم استخدام النظام القديم فقط")

    async def get_ai_response(self, user_id: int, user_message: str, conversation_history: list = None) -> tuple:
        """
        الحصول على رد من إكسا الذكي العادي (محدث)

        Args:
            user_id: معرف المستخدم
            user_message: رسالة المستخدم
            conversation_history: تاريخ المحادثة

        Returns:
            tuple: (الرد, رسالة الحالة, نجح الطلب)
        """
        try:
            if self.use_multi_models and self.multi_model_manager:
                # استخدام النظام الجديد
                response, status_message, success = await self.multi_model_manager.get_normal_response(
                    user_id, user_message, conversation_history
                )
                return response, status_message, success
            else:
                # استخدام النظام القديم
                response = await self.exa_normal.get_response(user_message, conversation_history)
                return response, "نظام قديم", True

        except Exception as e:
            logger.error(f"خطأ في get_ai_response: {e}")
            # العودة للنظام القديم كاحتياط
            try:
                response = await self.exa_normal.get_response(user_message, conversation_history)
                return response, "نظام احتياطي", True
            except Exception as fallback_error:
                logger.error(f"خطأ في النظام الاحتياطي: {fallback_error}")
                return "عذراً، حدث خطأ تقني. يرجى المحاولة مرة أخرى لاحقاً.", "خطأ", False

    async def get_ai_response_pro(self, user_id: int, user_message: str, conversation_history: list = None) -> tuple:
        """
        الحصول على رد من إكسا الذكي برو مع فوترة التوكن

        Args:
            user_id: معرف المستخدم
            user_message: رسالة المستخدم
            conversation_history: تاريخ المحادثة

        Returns:
            tuple: (الرد, رسالة الفوترة, نجح الطلب)
        """
        try:
            # فحص الرصيد قبل الطلب
            can_use, balance_message, balance_info = self.token_manager.check_balance_before_request(user_id)

            if not can_use:
                logger.warning(f"المستخدم {user_id} لا يملك رصيد كافي للوضع المتقدم")
                return balance_message, "", False

            # إرسال الطلب للذكاء الاصطناعي مع معالجة timeout
            try:
                if self.use_multi_models and self.multi_model_manager:
                    # استخدام النظام الجديد
                    ai_response, input_tokens, output_tokens = await self.multi_model_manager.get_pro_response(
                        user_id, user_message, conversation_history
                    )
                else:
                    # استخدام النظام القديم
                    ai_response, input_tokens, output_tokens = await self.exa_pro.get_response(user_message, conversation_history)

                # فحص إذا كان الرد يحتوي على رسالة خطأ
                if "عذراً" in ai_response and ("timeout" in ai_response.lower() or "تقني" in ai_response):
                    # في حالة timeout، نستخدم تقدير التوكن ونعيد رسالة مناسبة
                    input_tokens = self.token_manager.estimate_tokens(user_message)
                    output_tokens = self.token_manager.estimate_tokens(ai_response)

                    # لا نقوم بالفوترة في حالة الخطأ
                    return ai_response, "", False

                # إذا لم نحصل على معلومات التوكن الدقيقة، نستخدم التقدير
                if input_tokens == 0 and output_tokens == 0:
                    input_tokens = self.token_manager.estimate_tokens(user_message)
                    output_tokens = self.token_manager.estimate_tokens(ai_response)

            except Exception as e:
                logger.error(f"خطأ في الاتصال بإكسا الذكي برو: {e}")
                return "عذراً، حدث خطأ في الاتصال بالخدمة. يرجى المحاولة مرة أخرى لاحقاً. 🔄", "", False

            # معالجة الفوترة
            billing_success, billing_message, billing_data = self.token_manager.process_ai_request_billing(
                user_id, input_tokens, output_tokens, user_message, ai_response
            )

            if billing_success:
                return ai_response, billing_message, True
            else:
                return f"خطأ في الفوترة: {billing_message}", "", False

        except Exception as e:
            logger.error(f"خطأ في الحصول على رد إكسا برو للمستخدم {user_id}: {e}")
            return "عذراً، حدث خطأ تقني. يرجى المحاولة مرة أخرى لاحقاً.", "", False

    async def check_balance_for_ai_pro(self, user_id: int) -> tuple:
        """
        فحص الرصيد قبل استخدام إكسا الذكي برو

        Args:
            user_id: معرف المستخدم

        Returns:
            tuple: (يمكن الاستخدام, رسالة, معلومات إضافية)
        """
        return self.token_manager.check_balance_before_request(user_id)

    def get_user_usage_info(self, user_id: int) -> Dict[str, Any]:
        """
        الحصول على معلومات استخدام المستخدم

        Args:
            user_id: معرف المستخدم

        Returns:
            dict: معلومات الاستخدام
        """
        try:
            if self.use_multi_models and self.multi_model_manager:
                return self.multi_model_manager.get_user_usage_info(user_id)
            else:
                return {
                    "user_id": user_id,
                    "system": "قديم",
                    "free_usage": {"can_use_free": True, "unlimited": True}
                }
        except Exception as e:
            logger.error(f"خطأ في الحصول على معلومات المستخدم {user_id}: {e}")
            return {"user_id": user_id, "error": str(e)}

    def get_system_status(self) -> Dict[str, Any]:
        """الحصول على حالة النظام"""
        try:
            status = {
                "multi_models_enabled": self.use_multi_models,
                "old_system_available": True
            }

            if self.use_multi_models and self.multi_model_manager:
                status.update(self.multi_model_manager.get_system_status())

            return status
        except Exception as e:
            logger.error(f"خطأ في الحصول على حالة النظام: {e}")
            return {"error": str(e)}

    def is_conversation_active(self, context_data: dict) -> bool:
        """التحقق من وجود محادثة نشطة مع إكسا الذكي"""
        return (context_data.get("exa_ai_active", False) or
                context_data.get("exa_ai_pro_active", False))

    def start_conversation(self, context_data: dict):
        """بدء محادثة جديدة مع إكسا الذكي (يحدد النوع حسب السياق)"""
        if context_data.get("exa_ai_pro_active", False):
            self.exa_pro.start_conversation(context_data)
        else:
            self.exa_normal.start_conversation(context_data)

    def end_conversation(self, context_data: dict):
        """إنهاء المحادثة مع إكسا الذكي"""
        if context_data.get("exa_ai_pro_active", False):
            self.exa_pro.end_conversation(context_data)
        else:
            self.exa_normal.end_conversation(context_data)

    def add_to_conversation(self, context_data: dict, role: str, content: str):
        """إضافة رسالة إلى تاريخ المحادثة"""
        if context_data.get("exa_ai_pro_active", False):
            self.exa_pro.add_to_conversation(context_data, role, content)
        else:
            self.exa_normal.add_to_conversation(context_data, role, content)

    def get_welcome_message(self) -> str:
        """الحصول على رسالة الترحيب لإكسا الذكي العادي"""
        return self.exa_normal.get_welcome_message()



