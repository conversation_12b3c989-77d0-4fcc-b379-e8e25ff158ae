#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إدارة النسخ الاحتياطية للمدير
نظام متقدم لإنشاء وإدارة النسخ الاحتياطية
"""

import os
import json
import shutil
import zipfile
from datetime import datetime, timedelta
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from telegram.constants import ParseMode
import logging

logger = logging.getLogger(__name__)

class AdminBackupManagement:
    """فئة إدارة النسخ الاحتياطية للمدير"""
    
    def __init__(self, bot_instance):
        self.bot = bot_instance
        self.backup_dir = "backups"
        self.data_dir = "data"
        self.config_file = "backups/backup_config.json"
        
        # إنشاء مجلد النسخ الاحتياطية
        os.makedirs(self.backup_dir, exist_ok=True)
        
        # تحميل إعدادات النسخ الاحتياطي
        self.load_backup_config()
    
    def load_backup_config(self):
        """تحميل إعدادات النسخ الاحتياطي"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            else:
                self.config = {
                    "auto_backup": {
                        "daily": False,
                        "weekly": False,
                        "monthly": False
                    },
                    "last_backup": {
                        "daily": None,
                        "weekly": None,
                        "monthly": None
                    },
                    "backup_count": 0
                }
                self.save_backup_config()
        except Exception as e:
            logger.error(f"خطأ في تحميل إعدادات النسخ الاحتياطي: {e}")
            self.config = {
                "auto_backup": {"daily": False, "weekly": False, "monthly": False},
                "last_backup": {"daily": None, "weekly": None, "monthly": None},
                "backup_count": 0
            }
    
    def save_backup_config(self):
        """حفظ إعدادات النسخ الاحتياطي"""
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"خطأ في حفظ إعدادات النسخ الاحتياطي: {e}")
    
    async def backup_management_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض قائمة إدارة النسخ الاحتياطية"""
        try:
            # إحصائيات النسخ الاحتياطية
            backup_files = [f for f in os.listdir(self.backup_dir) if f.endswith('.zip')]
            total_backups = len(backup_files)
            
            # حساب حجم النسخ الاحتياطية
            total_size = 0
            for backup_file in backup_files:
                file_path = os.path.join(self.backup_dir, backup_file)
                if os.path.exists(file_path):
                    total_size += os.path.getsize(file_path)
            
            size_mb = total_size / (1024 * 1024)
            
            message = f"""
<b>🗄️ إدارة النسخ الاحتياطية</b>

📊 <b>الإحصائيات:</b>
• عدد النسخ: {total_backups}
• الحجم الإجمالي: {size_mb:.2f} ميجابايت
• آخر نسخة: {self.get_last_backup_time()}

⚙️ <b>النسخ التلقائي:</b>
• يومي: {'🟢 مفعل' if self.config['auto_backup']['daily'] else '🔴 معطل'}
• أسبوعي: {'🟢 مفعل' if self.config['auto_backup']['weekly'] else '🔴 معطل'}
• شهري: {'🟢 مفعل' if self.config['auto_backup']['monthly'] else '🔴 معطل'}

👇 <b>استخدم الأزرار أدناه لإدارة النسخ الاحتياطية</b>
            """
            
            await update.message.reply_text(
                message,
                parse_mode=ParseMode.HTML,
                reply_markup=self.get_backup_management_keyboard()
            )
            
        except Exception as e:
            logger.error(f"خطأ في عرض قائمة النسخ الاحتياطية: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في عرض قائمة النسخ الاحتياطية",
                reply_markup=self.bot.get_admin_keyboard()
            )
    
    def get_backup_management_keyboard(self):
        """لوحة مفاتيح إدارة النسخ الاحتياطية"""
        keyboard = [
            ["💾 نسخة فورية", "📅 نسخة يومية"],
            ["📆 نسخة أسبوعية", "🗓️ نسخة شهرية"],
            ["📋 عرض النسخ", "🗑️ حذف النسخ القديمة"],
            ["🔙 العودة للخيارات المتقدمة"]
        ]
        
        from telegram import ReplyKeyboardMarkup
        return ReplyKeyboardMarkup(keyboard, resize_keyboard=True)
    
    def get_last_backup_time(self):
        """الحصول على وقت آخر نسخة احتياطية"""
        try:
            backup_files = [f for f in os.listdir(self.backup_dir) if f.endswith('.zip')]
            if not backup_files:
                return "لا توجد نسخ"
            
            # ترتيب الملفات حسب تاريخ التعديل
            backup_files.sort(key=lambda x: os.path.getmtime(os.path.join(self.backup_dir, x)), reverse=True)
            latest_file = backup_files[0]
            
            # استخراج التاريخ من اسم الملف
            if "backup_" in latest_file:
                date_part = latest_file.replace("backup_", "").replace(".zip", "")
                try:
                    backup_date = datetime.strptime(date_part, "%Y%m%d_%H%M%S")
                    return backup_date.strftime("%Y-%m-%d %H:%M")
                except:
                    pass
            
            return "غير محدد"
        except Exception as e:
            logger.error(f"خطأ في الحصول على وقت آخر نسخة: {e}")
            return "خطأ"
    
    async def create_backup(self, update: Update, context: ContextTypes.DEFAULT_TYPE, backup_type="manual"):
        """إنشاء نسخة احتياطية"""
        try:
            # إرسال رسالة التحميل
            loading_msg = await update.message.reply_text("⏳ جاري إنشاء النسخة الاحتياطية...")
            
            # إنشاء اسم الملف
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"backup_{timestamp}.zip"
            backup_path = os.path.join(self.backup_dir, backup_filename)
            
            # إنشاء النسخة الاحتياطية
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # نسخ ملفات البيانات
                if os.path.exists(self.data_dir):
                    for root, dirs, files in os.walk(self.data_dir):
                        for file in files:
                            file_path = os.path.join(root, file)
                            arcname = os.path.relpath(file_path, ".")
                            zipf.write(file_path, arcname)
                
                # نسخ ملفات الإعدادات
                config_files = ["core/config.py"]
                for config_file in config_files:
                    if os.path.exists(config_file):
                        zipf.write(config_file, config_file)
                
                # نسخ ملفات السجلات إذا كانت موجودة
                if os.path.exists("logs"):
                    for root, dirs, files in os.walk("logs"):
                        for file in files:
                            file_path = os.path.join(root, file)
                            arcname = os.path.relpath(file_path, ".")
                            zipf.write(file_path, arcname)
            
            # حساب حجم الملف
            file_size = os.path.getsize(backup_path)
            size_mb = file_size / (1024 * 1024)
            
            # تحديث الإحصائيات
            self.config["backup_count"] += 1
            self.config["last_backup"][backup_type] = timestamp
            self.save_backup_config()
            
            # حذف رسالة التحميل
            await loading_msg.delete()
            
            # إرسال النسخة الاحتياطية
            with open(backup_path, 'rb') as backup_file:
                await update.message.reply_document(
                    document=backup_file,
                    filename=backup_filename,
                    caption=f"""
✅ <b>تم إنشاء النسخة الاحتياطية بنجاح</b>

📁 <b>اسم الملف:</b> {backup_filename}
📊 <b>الحجم:</b> {size_mb:.2f} ميجابايت
📅 <b>التاريخ:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🔧 <b>النوع:</b> {self.get_backup_type_name(backup_type)}

🔒 <b>محمي من التحميل والحفظ</b>
                    """,
                    parse_mode=ParseMode.HTML,
                    protect_content=True,
                    reply_markup=self.get_backup_management_keyboard()
                )
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            await update.message.reply_text(
                f"❌ فشل في إنشاء النسخة الاحتياطية: {str(e)}",
                reply_markup=self.get_backup_management_keyboard()
            )
    
    def get_backup_type_name(self, backup_type):
        """الحصول على اسم نوع النسخة الاحتياطية"""
        types = {
            "manual": "نسخة فورية",
            "daily": "نسخة يومية",
            "weekly": "نسخة أسبوعية", 
            "monthly": "نسخة شهرية"
        }
        return types.get(backup_type, "نسخة يدوية")
    
    async def toggle_auto_backup(self, update: Update, context: ContextTypes.DEFAULT_TYPE, backup_type):
        """تفعيل/إلغاء النسخ التلقائي"""
        try:
            current_status = self.config["auto_backup"][backup_type]
            self.config["auto_backup"][backup_type] = not current_status
            self.save_backup_config()
            
            status_text = "تم تفعيل" if not current_status else "تم إلغاء"
            type_name = self.get_backup_type_name(backup_type)
            
            await update.message.reply_text(
                f"✅ {status_text} {type_name} التلقائية",
                reply_markup=self.get_backup_management_keyboard()
            )
            
        except Exception as e:
            logger.error(f"خطأ في تغيير إعدادات النسخ التلقائي: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في تغيير الإعدادات",
                reply_markup=self.get_backup_management_keyboard()
            )
    
    async def list_backups(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض قائمة النسخ الاحتياطية"""
        try:
            backup_files = [f for f in os.listdir(self.backup_dir) if f.endswith('.zip')]
            
            if not backup_files:
                await update.message.reply_text(
                    "📭 لا توجد نسخ احتياطية",
                    reply_markup=self.get_backup_management_keyboard()
                )
                return
            
            # ترتيب الملفات حسب التاريخ
            backup_files.sort(key=lambda x: os.path.getmtime(os.path.join(self.backup_dir, x)), reverse=True)
            
            message = "<b>📋 قائمة النسخ الاحتياطية</b>\n\n"
            
            for i, backup_file in enumerate(backup_files[:10], 1):  # عرض آخر 10 نسخ
                file_path = os.path.join(self.backup_dir, backup_file)
                file_size = os.path.getsize(file_path) / (1024 * 1024)
                mod_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                
                message += f"{i}. <code>{backup_file}</code>\n"
                message += f"   📊 الحجم: {file_size:.2f} ميجابايت\n"
                message += f"   📅 التاريخ: {mod_time.strftime('%Y-%m-%d %H:%M')}\n\n"
            
            if len(backup_files) > 10:
                message += f"... و {len(backup_files) - 10} نسخة أخرى"
            
            await update.message.reply_text(
                message,
                parse_mode=ParseMode.HTML,
                reply_markup=self.get_backup_management_keyboard()
            )
            
        except Exception as e:
            logger.error(f"خطأ في عرض قائمة النسخ الاحتياطية: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في عرض القائمة",
                reply_markup=self.get_backup_management_keyboard()
            )
    
    async def cleanup_old_backups(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """حذف النسخ الاحتياطية القديمة"""
        try:
            backup_files = [f for f in os.listdir(self.backup_dir) if f.endswith('.zip')]
            
            if len(backup_files) <= 5:
                await update.message.reply_text(
                    "ℹ️ عدد النسخ الاحتياطية قليل، لا حاجة للحذف",
                    reply_markup=self.get_backup_management_keyboard()
                )
                return
            
            # ترتيب الملفات حسب التاريخ
            backup_files.sort(key=lambda x: os.path.getmtime(os.path.join(self.backup_dir, x)), reverse=True)
            
            # الاحتفاظ بآخر 5 نسخ فقط
            files_to_delete = backup_files[5:]
            deleted_count = 0
            
            for backup_file in files_to_delete:
                file_path = os.path.join(self.backup_dir, backup_file)
                try:
                    os.remove(file_path)
                    deleted_count += 1
                except Exception as e:
                    logger.error(f"خطأ في حذف {backup_file}: {e}")
            
            await update.message.reply_text(
                f"✅ تم حذف {deleted_count} نسخة احتياطية قديمة\n"
                f"📁 تم الاحتفاظ بآخر 5 نسخ",
                reply_markup=self.get_backup_management_keyboard()
            )
            
        except Exception as e:
            logger.error(f"خطأ في حذف النسخ القديمة: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في حذف النسخ القديمة",
                reply_markup=self.get_backup_management_keyboard()
            )
