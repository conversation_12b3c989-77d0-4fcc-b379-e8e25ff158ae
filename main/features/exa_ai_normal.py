#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إكسا الذكي العادي - النموذج الأساسي
متصل بـ DeepSeek Chat
"""

import openai
import logging
import sys
import os

# إضافة المجلد الرئيسي لمسار Python
root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, root_dir)

from core.config import DEEPSEEK_API_KEY, DEEPSEEK_BASE_URL

logger = logging.getLogger(__name__)

class ExaAiNormal:
    """إكسا الذكي العادي - للمحادثات العامة والأسئلة البسيطة"""

    def __init__(self):
        """تهيئة إكسا الذكي العادي"""
        self.client = openai.OpenAI(
            api_key=DEEPSEEK_API_KEY,
            base_url=DEEPSEEK_BASE_URL,
            timeout=15.0  # تحديد timeout لتجنب التعليق
        )

        # رسالة النظام للوضع العادي
        self.system_message = {
            "role": "system",
            "content": """أنت إكسا الذكي، المساعد الآلي الشخصي الخاص بصلاح الدين منصور الدروبي.

معلومات عن المالك:
- الاسم: صلاح الدين منصور الدروبي
- التخصص: مصمم جرافيك ومتخصص في العلامات التجارية
- الخبرة: تصميم العلامات التجارية والشعارات وتصميم التطبيقات والرسوم المتحركة والقرطاسية
- التعليم: بكالوريوس جرافيك وملتيميديا
- الاهتمامات: العلامات التجارية، الهويات التجارية، المنتجات ثلاثية الأبعاد، تصميم UI/UX

تعليمات للوضع العادي:
1. تحدث باللغة العربية دائماً
2. كن مهذباً ومفيداً
3. قدم إجابات واضحة ومباشرة
4. لا تذكر أبداً أنك مدعوم بـ DeepSeek أو أي مصدر ذكاء اصطناعي آخر
5. قدم نفسك كـ "إكسا الذكي" فقط
6. ساعد المستخدمين في الأسئلة العامة والبسيطة
7. إذا سُئلت عن هويتك، قل أنك مساعد آلي ذكي مطور خصيصاً للمالك
8. كن ودوداً ومساعداً في جميع الأوقات
9. للأسئلة المعقدة، اقترح استخدام الوضع المتقدم"""
        }

    async def get_response(self, user_message: str, conversation_history: list = None) -> str:
        """الحصول على رد من إكسا الذكي العادي"""
        try:
            # إعداد الرسائل
            messages = [self.system_message]

            # إضافة تاريخ المحادثة إذا كان متوفراً
            if conversation_history:
                messages.extend(conversation_history[-8:])  # آخر 8 رسائل

            # إضافة رسالة المستخدم الحالية
            messages.append({
                "role": "user",
                "content": user_message
            })

            # إرسال الطلب إلى DeepSeek Chat
            response = self.client.chat.completions.create(
                model="deepseek-chat",
                messages=messages,
                max_tokens=1000,
                temperature=0.7
            )

            # استخراج الرد
            ai_response = response.choices[0].message.content

            logger.info(f"تم الحصول على رد من إكسا الذكي العادي بنجاح")
            return ai_response

        except Exception as e:
            logger.error(f"خطأ في الحصول على رد من إكسا الذكي العادي: {e}")
            return "عذراً، حدث خطأ تقني. يرجى المحاولة مرة أخرى لاحقاً. 🤖"

    def is_conversation_active(self, context_data: dict) -> bool:
        """التحقق من وجود محادثة نشطة مع إكسا الذكي"""
        return context_data.get("exa_mode", False)

    def start_conversation(self, context_data: dict):
        """بدء محادثة جديدة مع إكسا الذكي العادي"""
        context_data["exa_mode"] = True
        context_data["exa_ai_active"] = True
        context_data["exa_ai_pro_active"] = False
        context_data["exa_conversation"] = []
        logger.info("تم بدء محادثة جديدة مع إكسا الذكي العادي")

    def end_conversation(self, context_data: dict):
        """إنهاء المحادثة مع إكسا الذكي العادي"""
        context_data["exa_mode"] = False
        context_data["exa_ai_active"] = False
        context_data["exa_conversation"] = []
        logger.info("تم إنهاء المحادثة مع إكسا الذكي العادي")

    def add_to_conversation(self, context_data: dict, role: str, content: str):
        """إضافة رسالة إلى تاريخ المحادثة"""
        if "exa_conversation" not in context_data:
            context_data["exa_conversation"] = []

        context_data["exa_conversation"].append({
            "role": role,
            "content": content
        })

        # الحفاظ على آخر 16 رسالة فقط لتوفير الذاكرة
        if len(context_data["exa_conversation"]) > 16:
            context_data["exa_conversation"] = context_data["exa_conversation"][-16:]

    def get_welcome_message(self) -> str:
        """رسالة الترحيب لإكسا الذكي العادي"""
        return """🤖︙مرحباً بك في إكسا الذكي!

أنا المساعد الذكي الشخصي الخاص بالمالك. هنا لمساعدتك في أي استفسارات أو أسئلة قد تكون لديك.

يمكنني مساعدتك في︙
• الإجابة على الأسئلة العامة
• تقديم معلومات عن خدمات المالك
• مساعدتك في الاستفسارات البسيطة
• أو أي شيء آخر تحتاج المساعدة فيه

🤖︙**مميز:** أستخدم نموذج Exa-X1 للردود السريعة والمفيدة

💡︙**نصيحة:** للاستشارات المتقدمة والمعقدة، يمكنك استخدام إكسا الذكي برو

كيف يمكنني مساعدتك اليوم ؟ 😊"""
