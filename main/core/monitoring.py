#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام مراقبة محسن مع رقم إشعار موحد
"""

import asyncio
import httpx
import random
import json
import os
import html
from datetime import datetime
import logging
import threading

logger = logging.getLogger(__name__)

class EnhancedMonitoringSystem:
    """نظام مراقبة محسن مع رقم إشعار موحد"""
    
    def __init__(self, admin_bot_token, admin_chat_id):
        self.admin_bot_token = admin_bot_token
        self.admin_chat_id = admin_chat_id
        self.current_notification_id = None
        self.pending_responses = {}  # لحفظ الردود المنتظرة
        self._lock = threading.Lock()  # قفل لحماية العمليات على pending_responses
        
        # إعداد مجلد البيانات
        self.data_dir = os.path.join(os.path.dirname(__file__), 'data')
        os.makedirs(self.data_dir, exist_ok=True)
        self.notifications_file = os.path.join(self.data_dir, 'notifications.json')
        
        self.load_notifications()
    
    def safe_html_escape(self, text):
        """تنظيف النص بأمان للاستخدام في HTML مع الحفاظ على التنسيق"""
        if not text or text == "غير محدد":
            return text
        
        try:
            # تنظيف النص من الرموز الخطيرة فقط
            cleaned_text = str(text).strip()
            # إزالة الرموز التي قد تكسر HTML
            cleaned_text = html.escape(cleaned_text)
            return cleaned_text
        except Exception as e:
            logger.error(f"خطأ في تنظيف النص: {e}")
            return "نص غير صالح"
    
    def generate_notification_id(self):
        """توليد رقم إشعار عشوائي من 10 خانات يبدأ بـ 101"""
        try:
            # توليد 7 أرقام عشوائية لإكمال الـ 10 خانات مع بداية 101
            random_part = random.randint(1000000, 9999999)
            return f"101{random_part}"
        except Exception as e:
            logger.error(f"خطأ في توليد رقم الإشعار: {e}")
            return "1010000000"
    
    def get_arabic_day(self, date_time=None):
        """الحصول على اسم اليوم بالعربية"""
        day_names = {
            'Monday': 'الاثنين',
            'Tuesday': 'الثلاثاء',
            'Wednesday': 'الأربعاء',
            'Thursday': 'الخميس',
            'Friday': 'الجمعة',
            'Saturday': 'السبت',
            'Sunday': 'الأحد'
        }

        # استخدام التاريخ الممرر أو التاريخ الحالي
        if date_time is None:
            date_time = datetime.now()
        current_day = date_time.strftime('%A')
        return day_names.get(current_day, 'غير محدد')

    def process_username(self, user):
        """معالجة أسماء المستخدمين متعددة اللغات"""
        try:
            # الحصول على الاسم الأول والأخير
            first_name = user.first_name or ""
            last_name = user.last_name or ""

            # دمج الاسم الكامل
            full_name = f"{first_name} {last_name}".strip()

            # إذا لم يكن هناك اسم، استخدم اسم المستخدم
            if not full_name:
                full_name = user.username or "مستخدم غير محدد"

            # إرجاع الاسم بدون إضافة | إضافية
            return full_name

        except Exception as e:
            logger.error(f"خطأ في معالجة اسم المستخدم: {e}")
            return "مستخدم غير محدد"

    def get_user_info(self, user, is_new_user=None):
        """الحصول على معلومات المستخدم الكاملة"""
        # تحديد حالة المستخدم بناءً على المعطى أو فحص قاعدة البيانات
        if is_new_user is None:
            # فحص قاعدة البيانات لتحديد حالة المستخدم
            try:
                users_file = "shared/database/users_data.json"
                if os.path.exists(users_file):
                    with open(users_file, 'r', encoding='utf-8') as f:
                        users_data = json.load(f)
                    is_new_user = str(user.id) not in users_data
                else:
                    is_new_user = True
            except Exception:
                is_new_user = False

        user_status = 'جديد' if is_new_user else 'قديم'

        return {
            'user_id': str(user.id),
            'name': self.process_username(user),
            'username': f"@{user.username}" if user.username else "غير محدد",
            'status': user_status,
            'id': user.id,
            'first_name': user.first_name or "غير محدد",
            'last_name': user.last_name or "غير محدد",
            'language_code': user.language_code or "غير محدد"
        }
    
    def load_notifications(self):
        """تحميل بيانات الإشعارات"""
        try:
            if os.path.exists(self.notifications_file):
                with open(self.notifications_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.pending_responses = data.get('pending_responses', {})
        except Exception as e:
            logger.error(f"خطأ في تحميل بيانات الإشعارات: {e}")
            self.pending_responses = {}
    
    def save_notifications(self):
        """حفظ بيانات الإشعارات"""
        try:
            # استخدام القفل لحماية العملية
            with self._lock:
                # إنشاء نسخة من القاموس لتجنب خطأ "dictionary changed size during iteration"
                pending_responses_copy = dict(self.pending_responses)

            data = {
                'pending_responses': pending_responses_copy,
                'last_updated': datetime.now().isoformat()
            }
            with open(self.notifications_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"خطأ في حفظ بيانات الإشعارات: {e}")
    
    async def send_user_action_notification(self, user_info, action_type, action_details):
        """إرسال إشعار عمل المستخدم (ضغط زر، رسالة، إلخ)"""
        try:
            # توليد رقم إشعار جديد
            notification_id = self.generate_notification_id()
            self.current_notification_id = notification_id

            # حفظ معلومات الإشعار للرد المستقبلي باستخدام القفل
            with self._lock:
                self.pending_responses[notification_id] = {
                    'user_info': user_info,
                    'action_type': action_type,
                    'action_details': action_details,
                    'timestamp': datetime.now().isoformat(),
                    'status': 'waiting_response'
                }
            
            current_time = datetime.now()
            arabic_day = self.get_arabic_day()
            
            # تحديد نوع المحتوى
            content_type = "نص"
            if action_details.get('has_photo'):
                content_type = "مع صورة"
            elif action_details.get('has_file'):
                content_type = "مع ملف"
            elif action_details.get('has_audio'):
                content_type = "مع صوت"
            
            # تنسيق رسالة ضغط الزر أو العمل
            if action_type == "button_click":
                # تنظيف البيانات بأمان
                safe_notification_id = self.safe_html_escape(notification_id)
                safe_user_name = self.safe_html_escape(user_info.get('name', 'غير محدد'))
                safe_username = self.safe_html_escape(user_info.get('username', 'غير محدد'))
                safe_user_id = self.safe_html_escape(user_info.get('user_id', 'غير محدد'))
                safe_button_name = self.safe_html_escape(action_details.get('button_name', 'غير محدد'))
                
                message = f"""📊︙إشعار مراقبة
🔢︙رقم الإشعار : <code>{safe_notification_id}</code>

🤖︙البوت الرئيسي
👤︙حالة : {user_info.get('status', 'قديم')}
👤︙ الاسم : <code>{safe_user_name}</code>
📧︙معرف : <code>{safe_username}</code>
🆔︙ايدي : <code>{safe_user_id}</code>
💬︙أرسل رسالة : {safe_button_name}
📄︙نوع المحتوى : {content_type}
📅︙اليوم : {arabic_day}
📅︙التاريخ : {current_time.strftime('%Y-%m-%d')}
⏰︙الوقت : {current_time.strftime('%H:%M:%S')}"""
            
            elif action_type == "message":
                # تنظيف البيانات بأمان
                safe_notification_id = self.safe_html_escape(notification_id)
                safe_user_name = self.safe_html_escape(user_info.get('name', 'غير محدد'))
                safe_username = self.safe_html_escape(user_info.get('username', 'غير محدد'))
                safe_user_id = self.safe_html_escape(user_info.get('user_id', 'غير محدد'))
                safe_message_text = self.safe_html_escape(action_details.get('message_text', 'غير محدد')[:50])
                
                message = f"""📊︙إشعار مراقبة
🔢︙رقم الإشعار︙<code>{safe_notification_id}</code>

🤖︙البوت الرئيسي
🤖︙الحالة︙{user_info.get('status', 'قديم')}
💬︙رسالة نصية
👤︙الاسم︙<code>{safe_user_name}</code>
📝︙النص︙{safe_message_text}...



📅︙اليوم︙{arabic_day}
📅︙التاريخ︙{current_time.strftime('%Y-%m-%d')}
⏰︙الوقت︙{current_time.strftime('%H:%M:%S')}"""
            
            else:
                # تنظيف البيانات بأمان
                safe_notification_id = self.safe_html_escape(notification_id)
                safe_user_name = self.safe_html_escape(user_info.get('name', 'غير محدد'))
                safe_action_type = self.safe_html_escape(action_type)
                
                message = f"""📊︙إشعار مراقبة
🔢︙رقم الإشعار : <code>{safe_notification_id}</code>

🤖︙البوت الرئيسي
🤖︙الحالة : {safe_action_type}
💬︙رسالة نصية
👤︙الاسم : <code>{safe_user_name}</code>
📝︙النص : عملية نظام

📅︙اليوم : {arabic_day}
📅︙التاريخ : {current_time.strftime('%Y-%m-%d')}
⏰︙الوقت : {current_time.strftime('%H:%M:%S')}"""
            
            # إرسال الإشعار
            await self.send_to_admin(message)

            # حفظ البيانات بدون انتظار
            import asyncio
            asyncio.create_task(asyncio.to_thread(self.save_notifications))
            
            logger.info(f"تم إرسال إشعار المستخدم برقم: {notification_id}")
            
        except Exception as e:
            logger.error(f"خطأ في إرسال إشعار المستخدم: {e}")

    async def send_wallet_creation_notification(self, user_info, wallet_number):
        """إرسال إشعار إنشاء محفظة جديدة"""
        try:
            # توليد رقم إشعار يبدأ بـ 202
            notification_id = f"202{random.randint(1000000, 9999999)}"

            # الحصول على التاريخ والوقت الحالي
            current_time = datetime.now()
            arabic_day = self.get_arabic_day(current_time)

            # إعداد بيانات المستخدم
            user_name = user_info.get('first_name', 'غير محدد')
            username = user_info.get('username', 'غير محدد')
            user_id = user_info.get('id', 'غير محدد')

            # تنسيق الإشعار حسب المطلوب
            message = f"""📊︙إشعار محفظة
🔢︙رقم الإشعار : {notification_id}

🤖︙البوت الرئيسي
🤖︙الحالة : إنشاء محفظة
🔢︙رقم المحفظة : {wallet_number}
👤︙الأسم : {user_name}
📧︙المعرف : {username}
🆔︙الأيدي : {user_id}
📝︙النص : تم إنشاء محفظة جديدة بنجاح

📅︙اليوم : {arabic_day}
📅︙التاريخ : {current_time.strftime('%Y-%m-%d')}
⏰︙الوقت : {current_time.strftime('%H:%M:%S')}"""

            # إرسال الإشعار
            await self.send_to_admin(message)

            logger.info(f"تم إرسال إشعار إنشاء المحفظة برقم: {notification_id} للمحفظة: {wallet_number}")

        except Exception as e:
            logger.error(f"خطأ في إرسال إشعار إنشاء المحفظة: {e}")

    async def send_loan_operation_notification(self, user_info, wallet_number, operation_type, amount, transaction_data):
        """إرسال إشعار عمليات السلف التلقائية"""
        try:
            # توليد رقم إشعار يبدأ بـ 203 لعمليات السلف
            notification_id = f"203{random.randint(1000000, 9999999)}"

            # الحصول على التاريخ والوقت الحالي
            current_time = datetime.now()
            arabic_day = self.get_arabic_day(current_time)

            # إعداد بيانات المستخدم
            user_name = user_info.get('first_name', 'غير محدد')
            username = user_info.get('username', 'غير محدد')
            user_id = user_info.get('id', 'غير محدد')

            # تنسيق المبلغ
            amount_display = f"{amount:.0f}" if amount == int(amount) else f"{amount:.2f}"

            # تحديد نوع العملية والحالة
            if operation_type == "إضافة رصيد سلفة":
                status_text = "إضافة رصيد سلفة"
                operation_text = f"تم إضافة رصيد سلفة {amount_display} إكسا"
            elif operation_type == "خصم رصيد سلفة":
                status_text = "خصم رصيد سلفة"
                is_fully_paid = transaction_data.get('is_fully_paid', False)
                loan_status = "مسددة بالكامل" if is_fully_paid else "جزئياً مسددة"
                operation_text = f"تم خصم رصيد سلفة {amount_display} إكسا - الحالة: {loan_status}"
            else:
                status_text = operation_type
                operation_text = f"عملية سلف: {operation_type}"

            # تنسيق الإشعار حسب المطلوب
            message = f"""📊︙إشعار سلفة
🔢︙رقم الإشعار : {notification_id}

🤖︙البوت الإداري
🤖︙الحالة : {status_text}
🔢︙رقم المحفظة : {wallet_number}
👤︙الأسم : {user_name}
📧︙المعرف : {username}
🆔︙الأيدي : {user_id}
💰︙المبلغ : {amount_display} إكسا
📝︙النص : {operation_text}

📅︙اليوم : {arabic_day}
📅︙التاريخ : {current_time.strftime('%Y-%m-%d')}
⏰︙الوقت : {current_time.strftime('%H:%M:%S')}"""

            # إرسال الإشعار
            await self.send_to_admin(message)

            logger.info(f"تم إرسال إشعار عملية السلف برقم: {notification_id} للمحفظة: {wallet_number}")

        except Exception as e:
            logger.error(f"خطأ في إرسال إشعار عملية السلف: {e}")

    async def send_bot_response_notification(self, response_text, response_type="text"):
        """إرسال إشعار رد البوت باستخدام نفس رقم الإشعار"""
        try:
            if not self.current_notification_id:
                logger.warning("لا يوجد رقم إشعار حالي لرد البوت")
                return
            
            notification_id = self.current_notification_id
            
            # الحصول على معلومات الإشعار الأصلي
            original_notification = self.pending_responses.get(notification_id, {})
            user_info = original_notification.get('user_info', {})
            
            current_time = datetime.now()
            arabic_day = self.get_arabic_day()
            
            # تنظيف البيانات بأمان
            safe_notification_id = self.safe_html_escape(notification_id)
            safe_user_name = self.safe_html_escape(user_info.get('name', 'غير محدد'))
            safe_response_text = self.safe_html_escape(response_text[:50])
            
            # تنسيق رسالة رد البوت
            message = f"""📊︙إشعار مراقبة
🔢︙رقم الإشعار︙<code>{safe_notification_id}</code>

🤖︙البوت الرئيسي
🤖︙الحالة︙رد
💬︙رسالة نصية
👤︙الاسم︙<code>{safe_user_name}</code>
📝︙النص︙{safe_response_text}{'...' if len(response_text) > 50 else ''}



📅︙اليوم︙{arabic_day}
📅︙التاريخ︙{current_time.strftime('%Y-%m-%d')}
⏰︙الوقت︙{current_time.strftime('%H:%M:%S')}"""
            
            # إرسال الإشعار
            await self.send_to_admin(message)

            # تحديث حالة الإشعار باستخدام القفل
            with self._lock:
                if notification_id in self.pending_responses:
                    self.pending_responses[notification_id]['status'] = 'completed'
                    self.pending_responses[notification_id]['response_text'] = response_text
                    self.pending_responses[notification_id]['response_time'] = current_time.isoformat()
            
            # حفظ البيانات بدون انتظار
            import asyncio
            asyncio.create_task(asyncio.to_thread(self.save_notifications))

            logger.info(f"تم إرسال إشعار رد البوت برقم: {notification_id}")

            # إعادة تعيين رقم الإشعار الحالي بعد الطباعة
            self.current_notification_id = None
            
        except Exception as e:
            logger.error(f"خطأ في إرسال إشعار رد البوت: {e}")
    
    async def send_to_admin(self, message):
        """إرسال رسالة للمدير بسرعة محسنة"""
        try:
            # استخدام timeout أقل للسرعة
            async with httpx.AsyncClient(timeout=3.0) as client:
                url = f"https://api.telegram.org/bot{self.admin_bot_token}/sendMessage"
                data = {
                    "chat_id": self.admin_chat_id,
                    "text": message,
                    "parse_mode": "HTML",
                    "disable_notification": True  # تعطيل الإشعارات لتقليل الحمل
                }

                response = await client.post(url, data=data)

                if response.status_code != 200:
                    logger.debug(f"فشل في إرسال الإشعار: {response.status_code}")
                else:
                    logger.debug("تم إرسال الإشعار بنجاح")

        except Exception as e:
            logger.debug(f"خطأ في إرسال الإشعار للمدير: {e}")
            # تجاهل أخطاء المراقبة لعدم تأثيرها على سرعة البوت
            pass


    
    def get_notification_stats(self):
        """الحصول على إحصائيات الإشعارات"""
        try:
            # استخدام القفل وإنشاء نسخة من القاموس لتجنب خطأ "dictionary changed size during iteration"
            with self._lock:
                pending_responses_copy = dict(self.pending_responses)

            total_notifications = len(pending_responses_copy)
            completed = sum(1 for n in pending_responses_copy.values() if n.get('status') == 'completed')
            pending = total_notifications - completed

            return {
                'total': total_notifications,
                'completed': completed,
                'pending': pending
            }
        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات الإشعارات: {e}")
            return {
                'total': 0,
                'completed': 0,
                'pending': 0
            }

    def cleanup_old_notifications(self, hours_to_keep: int = 24):
        """تنظيف الإشعارات القديمة لتجنب تراكم البيانات"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours_to_keep)
            cutoff_timestamp = cutoff_time.isoformat()

            # استخدام القفل لحماية العملية
            with self._lock:
                # إنشاء نسخة من المفاتيح لتجنب خطأ "dictionary changed size during iteration"
                notification_ids = list(self.pending_responses.keys())

                deleted_count = 0
                for notification_id in notification_ids:
                    notification = self.pending_responses.get(notification_id, {})
                    notification_time = notification.get('timestamp', '')

                    # حذف الإشعارات القديمة أو المكتملة
                    if (notification_time < cutoff_timestamp or
                        notification.get('status') == 'completed'):
                        del self.pending_responses[notification_id]
                        deleted_count += 1

                if deleted_count > 0:
                    logger.info(f"🧹 تم تنظيف {deleted_count} إشعار قديم من نظام المراقبة")
                    # حفظ البيانات المحدثة
                    self.save_notifications()

                return deleted_count

        except Exception as e:
            logger.error(f"خطأ في تنظيف الإشعارات القديمة: {e}")
            return 0
