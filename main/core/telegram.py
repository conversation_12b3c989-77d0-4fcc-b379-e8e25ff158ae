#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
البوت الرئيسي - للمستخدمين العاديين فقط
نسخة نظيفة بدون أي وظائف إدارية
"""

import os
import sys
import asyncio
import time
import logging
from datetime import datetime
from typing import Dict, Any
from telegram import Update, ReplyKeyboardMarkup, KeyboardButton, InlineKeyboardButton, InlineKeyboardMarkup, InputMediaPhoto
from telegram.ext import Application, CommandHandler, MessageHandler, filters, ContextTypes, CallbackQueryHandler
from telegram.constants import ParseMode

# إضافة المجلد الرئيسي لمسار Python
root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, root_dir)

# إضافة مسار المجلد المشترك
shared_dir = os.path.join(os.path.dirname(root_dir), 'shared')
sys.path.insert(0, shared_dir)

# استيراد نظام السجلات
from utils.logging_config import (
    get_main_logger,
    log_error,
    log_user_activity,
    log_system_event
)

try:
    from .config import BOT_TOKEN, MESSAGES, IMAGES
except ImportError:
    from config import BOT_TOKEN, MESSAGES, IMAGES

try:
    from data_processing import SharedTextProcessor
except ImportError:
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'shared'))
    from data_processing import SharedTextProcessor

try:
    from features.user_management import UserManager
    from features.ai_assistant import ExaAlThakiAssistant
    from features.monitoring_system import MonitoringSystem
    from features.ban_checker import ban_checker
except ImportError:
    sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
    from features.user_management import UserManager
    from features.ai_assistant import ExaAlThakiAssistant
    from features.monitoring_system import MonitoringSystem
    from features.ban_checker import ban_checker

# استيراد نظام المراقبة من نفس المجلد
try:
    from .monitoring import EnhancedMonitoringSystem
except ImportError:
    from monitoring import EnhancedMonitoringSystem

# استيراد معالج الأزرار من data_processing
try:
    from data_processing.buttons import ButtonProcessor
except ImportError:
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'shared'))
    from data_processing.buttons import ButtonProcessor

# استيراد نظام الرسائل الموحد للفواتير
try:
    # إضافة مسار مجلد data_processing
    data_processing_path = os.path.join(os.path.dirname(__file__), '..', '..', 'shared', 'data_processing')
    if data_processing_path not in sys.path:
        sys.path.append(data_processing_path)
    from invoice_messages import invoice_message_manager
except ImportError as e:
    invoice_message_manager = None
    print(f"تحذير: لا يمكن تحميل نظام الرسائل الموحد: {e}")

# الحصول على سجل البوت الرئيسي
logger = get_main_logger()

class TelegramBot:
    """البوت الرئيسي - للمستخدمين العاديين فقط"""

    # إعدادات بوت المراقبة
    ADMIN_BOT_TOKEN = "7633053725:AAH6Gkm6MJ9HSUcaruoAwhhZEc_BjhoQ6JQ"
    ADMIN_CHAT_ID = "591967813"

    def __init__(self):
        """تهيئة البوت"""
        # إعداد البوت بالإعدادات المحسنة للسرعة والاستقرار
        self.application = (
            Application.builder()
            .token(BOT_TOKEN)
            .connection_pool_size(8)
            .pool_timeout(20)      # زيادة pool timeout
            .read_timeout(60)      # زيادة read timeout للملفات الكبيرة
            .write_timeout(60)     # زيادة write timeout للصور
            .connect_timeout(30)   # زيادة connect timeout
            .build()
        )
        # إعداد نظام المراقبة المحسن أولاً
        self.monitoring = EnhancedMonitoringSystem(self.ADMIN_BOT_TOKEN, self.ADMIN_CHAT_ID)

        # تمرير نظام المراقبة لمدير المستخدمين
        self.user_manager = UserManager(monitoring_system=self.monitoring)
        self.ai_assistant = ExaAlThakiAssistant()
        self.text_processor = SharedTextProcessor()

        # إعداد معالج الأزرار
        self.button_processor = ButtonProcessor(self.monitoring)

        # إعداد المعالجات
        self.setup_handlers()



    def setup_handlers(self):
        """إعداد معالجات الأوامر والرسائل"""
        # معالجات الأوامر (الإنجليزية فقط)
        self.application.add_handler(CommandHandler("start", self.start_command))
        self.application.add_handler(CommandHandler("help", self.help_command))
        self.application.add_handler(CommandHandler("update", self.update_keyboard))
        self.application.add_handler(CommandHandler("about", self.about_command))
        self.application.add_handler(CommandHandler("location", self.location_command))
        self.application.add_handler(CommandHandler("works", self.works_command))
        self.application.add_handler(CommandHandler("experience", self.experience_command))
        self.application.add_handler(CommandHandler("achievements", self.achievements_command))
        self.application.add_handler(CommandHandler("location", self.location_command))
        self.application.add_handler(CommandHandler("about", self.about_command))
        self.application.add_handler(CommandHandler("works", self.works_command))
        self.application.add_handler(CommandHandler("experience", self.experience_command))
        self.application.add_handler(CommandHandler("achievements", self.achievements_command))
        self.application.add_handler(CommandHandler("update_keyboard", self.update_keyboard_command))

        # معالج الأزرار المضمنة (معالج واحد فقط)
        self.application.add_handler(CallbackQueryHandler(self.handle_callback_query))

        # معالج الرسائل النصية
        self.application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_text_messages))

        # معالج الصور والملفات
        self.application.add_handler(MessageHandler(filters.PHOTO, self.handle_photo))
        self.application.add_handler(MessageHandler(filters.Document.ALL, self.handle_document))
        self.application.add_handler(MessageHandler(filters.AUDIO, self.handle_audio))
        self.application.add_handler(MessageHandler(filters.VIDEO, self.handle_video))
        self.application.add_handler(MessageHandler(filters.VOICE, self.handle_voice))

        # معالج الأخطاء
        self.application.add_error_handler(self.error_handler)
    
    def get_reply_keyboard(self):
        """إنشاء لوحة المفاتيح السفلية للمستخدمين العاديين"""
        keyboard = [
            [KeyboardButton("💰 محفظتي")],
            [KeyboardButton("👨‍💼 نبذة عني"), KeyboardButton("📍 موقعي")],
            [KeyboardButton("🎯 خبرتي"), KeyboardButton("💼 أعمالي")],
            [KeyboardButton("❓ المساعدة"), KeyboardButton("🏆 إنجازاتي")],
            [KeyboardButton("🤖 إكسا الذكي")]
        ]
        return ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=False)

    def get_wallet_keyboard(self):
        """إنشاء لوحة المفاتيح السفلية لخيارات المحفظة"""
        keyboard = [
            [KeyboardButton("💰 عرض الرصيد"), KeyboardButton("➕ إضافة رصيد")],
            [KeyboardButton("📊 عرض المعاملات"), KeyboardButton("💳 خدمة سلفني")],
            [KeyboardButton("📈 تقرير الاستخدام")],
            [KeyboardButton("🔙 العودة للقائمة الرئيسية")]
        ]
        return ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=False)
    
    def get_main_keyboard(self):
        """إنشاء لوحة المفاتيح المضمنة الرئيسية"""
        keyboard = [
            [InlineKeyboardButton("💰 محفظتي", callback_data="my_wallet")],
            [InlineKeyboardButton("👨‍💼 نبذة عني", callback_data="about"),
             InlineKeyboardButton("📍 موقعي", callback_data="location")],
            [InlineKeyboardButton("🎯 خبرتي", callback_data="experience"),
             InlineKeyboardButton("💼 أعمالي", callback_data="works")],
            [InlineKeyboardButton("❓ المساعدة", callback_data="help"),
             InlineKeyboardButton("🏆 إنجازاتي", callback_data="achievements")],
            [InlineKeyboardButton("🤖 إكسا الذكي", callback_data="exa_ai")]
        ]
        return InlineKeyboardMarkup(keyboard)
    
    def get_back_keyboard(self):
        """إنشاء لوحة مفاتيح العودة للقائمة الرئيسية"""
        return self.button_processor.get_back_keyboard()

    def get_location_keyboard(self):
        """إنشاء لوحة مفاتيح الموقع مع أزرار البريد الإلكتروني والهاتف"""
        return self.button_processor.get_location_keyboard()

    def get_exa_ai_keyboard(self):
        """إنشاء لوحة مفاتيح إكسا الذكي مع خيارات الوضع"""
        keyboard = [
            [InlineKeyboardButton("🧠 إكسا الذكي برو", callback_data="exa_ai_pro"),
             InlineKeyboardButton("🤖 إكسا الذكي العادي", callback_data="exa_ai_normal")],
            [InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="main_menu")]
        ]
        return InlineKeyboardMarkup(keyboard)

    def get_exa_ai_chat_keyboard(self, is_pro=False):
        """إنشاء لوحة مفاتيح المحادثة مع إكسا الذكي"""
        if is_pro:
            keyboard = [
                [InlineKeyboardButton("❌ إنهاء المحادثة مع إكسا الذكي برو", callback_data="end_exa_ai_pro")],
                [InlineKeyboardButton("🔙 العودة للقائمة السابقة", callback_data="exa_ai")]
            ]
        else:
            keyboard = [
                [InlineKeyboardButton("❌ إنهاء المحادثة مع إكسا الذكي العادي", callback_data="end_exa_ai")],
                [InlineKeyboardButton("🔙 العودة للقائمة السابقة", callback_data="exa_ai")]
            ]
        return InlineKeyboardMarkup(keyboard)

    async def check_user_ban_status(self, update: Update) -> bool:
        """فحص حالة حظر المستخدم وإرسال رسالة إذا كان محظور"""
        user = update.effective_user
        is_banned, ban_info = ban_checker.is_user_banned(user.id)

        if is_banned:
            ban_message = ban_checker.get_ban_message(ban_info)
            await update.message.reply_text(
                ban_message,
                parse_mode=ParseMode.MARKDOWN
            )
            return True
        return False

    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج أمر /start"""
        user = update.effective_user

        # فحص ما إذا كان المستخدم محظور
        if await self.check_user_ban_status(update):
            return

        # تسجيل المستخدم والتحقق من كونه جديد
        is_new_user = self.user_manager.register_user(user)

        # إعداد معلومات المستخدم للمراقبة مع تمرير حالة المستخدم
        user_info = self.monitoring.get_user_info(user, is_new_user)

        # إرسال إشعار طلب المستخدم (مع معالجة الأخطاء)
        try:
            action_details = {
                'message_text': '/start',
                'conversation_type': 'command',
                'has_photo': False,
                'has_file': False
            }

            await self.monitoring.send_user_action_notification(
                user_info,
                "message",
                action_details
            )

            if is_new_user:
                await self.monitoring.log_new_user(user_info)
        except Exception as e:
            logger.warning(f"خطأ في إرسال إشعار المراقبة: {e}")
            # المتابعة بدون توقف

        # حفظ حالة المستخدم للاستخدام في الأزرار
        context.user_data["is_new_user"] = is_new_user

        # إرسال رسالة الترحيب مع اسم المستخدم
        username = f"{user.first_name} {user.last_name}".strip() if user.last_name else user.first_name
        if not username:
            username = user.username or "مستخدم غير محدد"

        welcome_text = self.text_processor.get_welcome_message(username)

        await update.message.reply_text(
            welcome_text,
            parse_mode=ParseMode.HTML,
            reply_markup=self.get_reply_keyboard()
        )

        # إرسال الأزرار المضمنة في رسالة منفصلة
        await update.message.reply_text(
            "يمكنك أيضاً استخدام الأزرار المضمنة للتنقل السريع:",
            reply_markup=self.get_main_keyboard()
        )

        # إرسال رسالة ترحيب بالمحفظة للمستخدمين الجدد
        logger.info(f"فحص المستخدم: {user.id}, جديد: {is_new_user}")

        if is_new_user:
            try:
                # انتظار قصير للتأكد من إنشاء المحفظة
                import asyncio
                await asyncio.sleep(0.5)

                wallet_info = self.user_manager.get_user_wallet_info(user.id)
                logger.info(f"معلومات المحفظة للمستخدم الجديد {user.id}: {wallet_info}")

                if wallet_info:
                    # تحديد حالة المحفظة
                    status_emoji = "✅" if wallet_info['status'] == 'active' else "❌"
                    status_text = "نشط" if wallet_info['status'] == 'active' else "خامل"

                    # تحديد اليوم والتاريخ والوقت
                    from datetime import datetime
                    now = datetime.now()

                    # أسماء الأيام بالعربية
                    day_names = {
                        'Monday': 'الاثنين',
                        'Tuesday': 'الثلاثاء',
                        'Wednesday': 'الأربعاء',
                        'Thursday': 'الخميس',
                        'Friday': 'الجمعة',
                        'Saturday': 'السبت',
                        'Sunday': 'الأحد'
                    }

                    current_day = day_names.get(now.strftime('%A'), 'غير محدد')
                    current_date = now.strftime('%Y-%m-%d')
                    current_time = now.strftime('%H:%M:%S')

                    # تنسيق الرصيد بدون عشري إذا كان صحيحاً
                    balance = wallet_info['balance']
                    balance_display = f"{balance:.0f}" if balance == int(balance) else f"{balance:.2f}"

                    wallet_welcome_message = f"""🎉︙مرحباً بك في عائلة البوت!

💰︙تم إنشاء محفظتك الشخصية بنجاح︙

🔢︙رقم المحفظة : <code>{wallet_info['wallet_number']}</code>
💎︙الرصيد الابتدائي : {balance_display} إكسا
📅︙اليوم : {current_day}
📅︙تاريخ : {current_date}
📅︙الوقت : {current_time}
📊︙الحالة : {status_text} {status_emoji}

💡︙ملاحظات مهمة :
• تم إنشاء محفظتك تلقائياً عند دخولك للبوت
• يمكنك الوصول لمحفظتك في أي وقت من زر "💰 محفظتي"
• رقم المحفظة فريد وخاص بك فقط
• ستحتاج رقم المحفظة لاستخدام الخدمات المدفوعة

🌟︙استمتع بتجربة البوت!"""

                    # إرسال تأثير الاحتفال أولاً
                    await self.send_celebration_effect(update)

                    # ثم إرسال رسالة ترحيب المحفظة
                    await update.message.reply_text(
                        wallet_welcome_message,
                        parse_mode=ParseMode.HTML,
                        reply_markup=self.get_reply_keyboard()
                    )
                    logger.info(f"تم إرسال رسالة ترحيب المحفظة للمستخدم {user.id}")

                    # إرسال إشعار إنشاء المحفظة بعد انتهاء رسالة الترحيب
                    try:
                        import asyncio

                        # إعداد معلومات المستخدم للإشعار
                        user_info_for_notification = {
                            'first_name': user.full_name or "غير محدد",
                            'username': f"@{user.username}" if user.username else "غير محدد",
                            'id': user.id
                        }

                        # دالة مساعدة لإرسال الإشعار مع تأخير بسيط
                        async def send_wallet_notification():
                            # تأخير بسيط لضمان وصول الإشعار بعد انتهاء رسالة الترحيب
                            await asyncio.sleep(2)
                            await self.monitoring.send_wallet_creation_notification(
                                user_info_for_notification,
                                wallet_info['wallet_number']
                            )

                        # إرسال الإشعار
                        asyncio.create_task(send_wallet_notification())
                        logger.info(f"تم جدولة إرسال إشعار إنشاء المحفظة للمستخدم الجديد: {user.id}")

                    except Exception as e:
                        logger.warning(f"خطأ في إرسال إشعار إنشاء المحفظة من start_command: {e}")
                else:
                    logger.warning(f"لم يتم العثور على معلومات المحفظة للمستخدم الجديد {user.id}")
            except Exception as e:
                logger.error(f"خطأ في إرسال رسالة ترحيب المحفظة: {e}")
        else:
            logger.info(f"المستخدم {user.id} موجود بالفعل، لن يتم إرسال رسالة ترحيب المحفظة")

        # إرسال إشعار رد البوت (مع معالجة الأخطاء)
        try:
            await self.monitoring.send_bot_response_notification(
                welcome_text[:100] + "..." if len(welcome_text) > 100 else welcome_text,
                "text"
            )
        except Exception as e:
            logger.warning(f"خطأ في إرسال إشعار رد البوت: {e}")
            # المتابعة بدون توقف

        logger.info(f"مستخدم جديد: {user.first_name} ({user.id})")

    async def send_celebration_effect(self, update: Update):
        """إرسال تأثير الاحتفال بالإيموجي المتطايرة"""
        try:
            # مجموعة إيموجي الاحتفال
            celebration_emojis = [
                "🎉🎊✨🌟💫⭐🎈🎁🏆🥳",
                "🎉 🎊 ✨ 🌟 💫 ⭐ 🎈 🎁 🏆 🥳",
                "✨🎉✨🎊✨🌟✨💫✨⭐✨",
                "🎊🎉🎊🎉🎊🎉🎊🎉🎊🎉",
                "🌟💫🌟💫🌟💫🌟💫🌟💫",
                "🎈🎁🎈🎁🎈🎁🎈🎁🎈🎁"
            ]

            # إرسال موجات من الإيموجي
            import asyncio

            for i, emoji_wave in enumerate(celebration_emojis):
                celebration_msg = await update.message.reply_text(emoji_wave)

                # انتظار قصير بين كل موجة
                await asyncio.sleep(0.3)

                # حذف الرسالة بعد فترة قصيرة لإنشاء تأثير التطاير
                try:
                    await asyncio.sleep(0.5)
                    await celebration_msg.delete()
                except:
                    pass  # تجاهل أخطاء الحذف

            # رسالة احتفال نهائية تبقى لفترة أطول
            final_celebration = await update.message.reply_text(
                "🎉🎊 مبروك! تم إنشاء محفظتك بنجاح! 🎊🎉"
            )

            # حذف الرسالة النهائية بعد 3 ثواني
            await asyncio.sleep(3)
            try:
                await final_celebration.delete()
            except:
                pass

        except Exception as e:
            logger.warning(f"خطأ في تأثير الاحتفال: {e}")
            # المتابعة بدون توقف

    async def update_keyboard(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """تحديث البوت كاملاً"""
        user = update.effective_user

        # إعادة تسجيل المستخدم
        is_new_user = self.user_manager.register_user(user)
        context.user_data["is_new_user"] = is_new_user

        # معالجة اسم المستخدم
        username = f"{user.first_name} {user.last_name}".strip() if user.last_name else user.first_name
        if not username:
            username = user.username or "مستخدم غير محدد"

        # الرسالة الأولى - بدء التحديث
        loading_message = await update.message.reply_text(
            "🔄︙جاري عمل تحديث البوت..."
        )

        # تحديث رسالة التحميل بنجاح التحديث أولاً
        try:
            await loading_message.edit_text("✅ تم الانتهاء من التحديث بنجاح!")
            await asyncio.sleep(1)  # عرض رسالة النجاح لثانية واحدة
            await loading_message.delete()
        except Exception:
            # إذا فشل التحديث، احذف الرسالة مباشرة
            try:
                await loading_message.delete()
            except Exception:
                pass

        # رسالة تأكيد التحديث
        await update.message.reply_text(
            f"✅︙تم تحديث البوت بنجاح! شكراً {username} لانتظارك\n"
            "🔹︙ الأزرار محدثة\n"
            "🔹︙ القوائم محدثة\n"
            "🔹︙ الوظائف محدثة"
        )

        # رسالة الترحيب مع اسم المستخدم
        await update.message.reply_text(
            f"🌟 مرحباً بك! {username} 🌟\n\n"
            "أهلاً وسهلاً بك في بوتي الشخصي\n\n"
            "استخدم الأزرار أدناه للتعرف علي أكثر 👇",
            reply_markup=self.get_reply_keyboard()
        )

        # إرسال الأزرار المضمنة أيضاً
        await update.message.reply_text(
            "يمكنك أيضاً استخدام الأزرار المضمنة للتنقل السريع:",
            reply_markup=self.get_main_keyboard()
        )

    async def about_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج أمر حول/about"""
        user = update.effective_user
        user_data = {
            "id": user.id,
            "first_name": user.first_name,
            "username": user.username
        }
        is_new_user = context.user_data.get("is_new_user", False)

        await self.send_text_with_image(update, "about", context)

    async def location_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج أمر موقعي/location"""
        user = update.effective_user
        user_data = {
            "id": user.id,
            "first_name": user.first_name,
            "username": user.username
        }
        is_new_user = context.user_data.get("is_new_user", False)

        await self.send_text_with_image(update, "location", context)
    async def works_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج أمر أعمالي/works"""
        user = update.effective_user
        user_data = {
            "id": user.id,
            "first_name": user.first_name,
            "username": user.username
        }
        is_new_user = context.user_data.get("is_new_user", False)

        await self.send_text_with_image(update, "works", context)
    async def experience_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج أمر خبرتي/experience"""
        user = update.effective_user
        user_data = {
            "id": user.id,
            "first_name": user.first_name,
            "username": user.username
        }
        is_new_user = context.user_data.get("is_new_user", False)

        await self.send_text_with_image(update, "experience", context)
    async def achievements_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج أمر إنجازاتي/achievements"""
        user = update.effective_user
        user_data = {
            "id": user.id,
            "first_name": user.first_name,
            "username": user.username
        }
        is_new_user = context.user_data.get("is_new_user", False)

        await self.send_text_with_image(update, "achievements", context)
    async def handle_text_messages(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج الرسائل النصية"""
        text = update.message.text
        user = update.effective_user

        # فحص ما إذا كان المستخدم محظور
        if await self.check_user_ban_status(update):
            return

        # إعداد معلومات المستخدم للمراقبة
        user_info = self.monitoring.get_user_info(user)

        # التحقق من وضع إكسا الذكي
        if self.ai_assistant.is_conversation_active(context.user_data):
            # تحديد نوع المحادثة
            conversation_type = 'exa_ai_pro' if context.user_data.get("exa_ai_pro_active", False) else 'exa_ai'

            # إرسال إشعار رسالة المستخدم لإكسا
            action_details = {
                'message_text': text,
                'conversation_type': conversation_type,
                'has_photo': False,
                'has_file': False
            }

            await self.monitoring.send_user_action_notification(
                user_info,
                "message",
                action_details
            )

            await self.handle_exa_ai_message(update, context)
            return

        # إرسال إشعار مبسط للرسائل النصية (بدون انتظار)
        action_details = {
            'message_text': text[:30] + "..." if len(text) > 30 else text,
            'conversation_type': 'general'
        }

        # إرسال الإشعار بدون انتظار لتحسين السرعة
        import asyncio
        asyncio.create_task(self.monitoring.send_user_action_notification(
            user_info,
            "message",
            action_details
        ))

        # معالجة الأزرار النصية
        user_data = {
            "id": user.id,
            "first_name": user.first_name,
            "username": user.username
        }

        # الحصول على حالة المستخدم
        is_new_user = context.user_data.get("is_new_user", False)

        # معالجة النص باستخدام المعالج الموحد
        message_result = self.text_processor.process_main_bot_message(text, user)
        command_type = message_result.get("command_type")

        # تنفيذ الأمر المناسب
        if command_type == "start":
            await self.start_command(update, context)
        elif command_type == "update":
            await self.update_keyboard(update, context)
        elif command_type == "help":
            # فحص التقييد للمساعدة
            user = update.effective_user
            if ban_checker.check_restriction(user.id, 'help_access'):
                is_restricted, restriction_info = ban_checker.is_user_restricted(user.id)
                restriction_message = ban_checker.get_restriction_message('help_access', restriction_info)
                await update.message.reply_text(
                    restriction_message,
                    parse_mode=ParseMode.MARKDOWN,
                    reply_markup=self.get_reply_keyboard()
                )
                return
            await self.help_command(update, context)
        elif command_type in ['location', 'about', 'works', 'experience', 'achievements']:
            # فحص التقييد للأقسام
            user = update.effective_user
            restriction_key = f"{command_type}_access"
            if ban_checker.check_restriction(user.id, restriction_key):
                is_restricted, restriction_info = ban_checker.is_user_restricted(user.id)
                restriction_message = ban_checker.get_restriction_message(restriction_key, restriction_info)
                await update.message.reply_text(
                    restriction_message,
                    parse_mode=ParseMode.MARKDOWN,
                    reply_markup=self.get_reply_keyboard()
                )
                return
            await self.send_text_with_image(update, command_type, context)
        elif command_type == "ai_assistant":
            # فحص التقييد قبل عرض خيارات إكسا الذكي
            user = update.effective_user
            if ban_checker.check_restriction(user.id, 'ai_assistant'):
                is_restricted, restriction_info = ban_checker.is_user_restricted(user.id)
                restriction_message = ban_checker.get_restriction_message('ai_assistant', restriction_info)
                await update.message.reply_text(
                    restriction_message,
                    parse_mode=ParseMode.MARKDOWN,
                    reply_markup=self.get_reply_keyboard()
                )
                return
            # فحص حالة النماذج قبل عرض خيارات إكسا
            models_status = self.ai_assistant.multi_model_manager._check_models_status() if self.ai_assistant.multi_model_manager else {"any_active": True}

            if not models_status.get("any_active", True):
                # جميع النماذج معطلة - عرض رسالة التوقف
                disabled_message = """🚫 **إكسا الذكي متوقف مؤقتاً**

عذراً، جميع نماذج الذكاء الاصطناعي معطلة حالياً من قبل المدير.

⏰ يرجى المحاولة لاحقاً

💡 يمكنك استخدام باقي خدمات البوت بشكل طبيعي"""

                await update.message.reply_text(
                    disabled_message,
                    reply_markup=self.get_reply_keyboard(),
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            await self.show_exa_ai_options_from_text(update, context)
        elif command_type == "exa_ai_pro":
            await self.start_exa_ai_pro_from_text(update, context)
        elif command_type == "my_wallet":
            await self.show_my_wallet_from_text(update, context)
        elif command_type == "my_balance":
            await self.show_my_balance_from_text(update, context)
        elif command_type == "add_balance":
            await self.add_balance_from_text(update, context)
        elif command_type == "show_transactions":
            await self.show_transactions_from_text(update, context)
        elif command_type == "request_loan":
            await self.request_loan_from_text(update, context)
        elif command_type == "usage_report":
            await self.show_usage_report_from_text(update, context)
        elif command_type == "back_to_main":
            await self.back_to_main_menu(update, context)
        elif command_type == "general_message":
            # رسالة عامة - استخدام الرد الافتراضي من المعالج
            response_text = message_result.get("default_response", "مرحباً! استخدم الأزرار أدناه للتنقل")

            await update.message.reply_text(
                response_text,
                reply_markup=self.get_reply_keyboard()
            )

            # إرسال إشعار رد البوت بدون انتظار
            import asyncio
            asyncio.create_task(self.monitoring.send_bot_response_notification(response_text[:50], "text"))
        else:
            # رسالة افتراضية للأخطاء
            response_text = "مرحباً! استخدم الأزرار أدناه للتنقل"

            await update.message.reply_text(
                f"{response_text} 👇",
                reply_markup=self.get_reply_keyboard()
            )

            # إرسال إشعار رد البوت بدون انتظار
            import asyncio
            asyncio.create_task(self.monitoring.send_bot_response_notification(response_text[:50], "text"))
    
    async def location_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج أمر /location"""
        user = update.effective_user
        user_info = self.monitoring.get_user_info(user)

        action_details = {
            'message_text': '/location',
            'conversation_type': 'command',
            'has_photo': False,
            'has_file': False
        }

        await self.monitoring.send_user_action_notification(user_info, "message", action_details)
        await self.send_text_with_image(update, "location", context)

    async def about_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج أمر /about"""
        user = update.effective_user
        user_info = self.monitoring.get_user_info(user)

        action_details = {
            'message_text': '/about',
            'conversation_type': 'command',
            'has_photo': False,
            'has_file': False
        }

        await self.monitoring.send_user_action_notification(user_info, "message", action_details)
        await self.send_text_with_image(update, "about", context)

    async def works_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج أمر /works"""
        user = update.effective_user
        user_info = self.monitoring.get_user_info(user)

        action_details = {
            'message_text': '/works',
            'conversation_type': 'command',
            'has_photo': False,
            'has_file': False
        }

        await self.monitoring.send_user_action_notification(user_info, "message", action_details)
        await self.send_text_with_image(update, "works", context)

    async def experience_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج أمر /experience"""
        user = update.effective_user
        user_info = self.monitoring.get_user_info(user)

        action_details = {
            'message_text': '/experience',
            'conversation_type': 'command',
            'has_photo': False,
            'has_file': False
        }

        await self.monitoring.send_user_action_notification(user_info, "message", action_details)
        await self.send_text_with_image(update, "experience", context)

    async def achievements_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج أمر /achievements"""
        user = update.effective_user
        user_info = self.monitoring.get_user_info(user)

        action_details = {
            'message_text': '/achievements',
            'conversation_type': 'command',
            'has_photo': False,
            'has_file': False
        }

        await self.monitoring.send_user_action_notification(user_info, "message", action_details)
        await self.send_text_with_image(update, "achievements", context)
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج أمر /help"""
        user = update.effective_user

        # إعداد معلومات المستخدم للمراقبة
        user_info = self.monitoring.get_user_info(user)

        # إرسال إشعار طلب المستخدم
        action_details = {
            'message_text': '/help',
            'conversation_type': 'command',
            'has_photo': False,
            'has_file': False
        }

        await self.monitoring.send_user_action_notification(
            user_info,
            "message",
            action_details
        )

        help_text = """
🤖 **مرحباً بك في البوت!**

استخدم الأزرار التفاعلية للتنقل:

📍 **موقعي** - معلومات الموقع والتواصل
👨‍💼 **نبذة عني** - نبذة شخصية ومهنية
💼 **أعمالي** - المشاريع والأعمال
🎯 **خبرتي** - المهارات والخبرات
🏆 **إنجازاتي** - الجوائز والشهادات
🤖 **إكسا الذكي** - المساعد الذكي

💡 **نصيحة:** استخدم الأزرار للحصول على تجربة أفضل!
        """
        await update.message.reply_text(
            help_text,
            parse_mode=ParseMode.MARKDOWN,
            reply_markup=self.get_reply_keyboard()
        )

        # إرسال إشعار رد البوت
        await self.monitoring.send_bot_response_notification(
            "تم عرض صفحة المساعدة",
            "text"
        )

    async def update_keyboard_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """أمر تحديث الأزرار السفلية"""
        await update.message.reply_text(
            "✅ تم تحديث الأزرار السفلية!",
            reply_markup=self.get_reply_keyboard()
        )
    
    def get_works_inline_keyboard(self):
        """إنشاء لوحة مفاتيح مضمنة لرسالة الأعمال"""
        return self.button_processor.get_works_keyboard()

    def get_welcome_inline_keyboard(self):
        """إنشاء لوحة مفاتيح مضمنة لرسالة الترحيب"""
        from telegram import InlineKeyboardButton, InlineKeyboardMarkup

        keyboard = [
            [InlineKeyboardButton("💰 محفظتي", callback_data="my_wallet")],
            [
                InlineKeyboardButton("👨‍💼 نبذة عني", callback_data="about_me"),
                InlineKeyboardButton("📍 موقعي", callback_data="my_location")
            ],
            [
                InlineKeyboardButton("🎯 خبرتي", callback_data="my_experience"),
                InlineKeyboardButton("💼 أعمالي", callback_data="my_works")
            ],
            [
                InlineKeyboardButton("❓ المساعدة", callback_data="help_info"),
                InlineKeyboardButton("🏆 إنجازاتي", callback_data="my_achievements")
            ],
            [InlineKeyboardButton("🤖 إكسا الذكي", callback_data="exa_ai")]
        ]
        return InlineKeyboardMarkup(keyboard)

    async def handle_welcome_buttons(self, query, context: ContextTypes.DEFAULT_TYPE, callback_data: str):
        """معالجة أزرار الترحيب المضمنة"""
        try:
            # خريطة الأزرار إلى أنواع الرسائل
            button_mapping = {
                "about_me": "about",
                "my_location": "location",
                "my_works": "works",
                "my_experience": "experience",
                "my_achievements": "achievements",
                "help_info": "help"
            }

            message_type = button_mapping.get(callback_data)

            if message_type == "help":
                # معالجة خاصة للمساعدة
                help_text = """❓ المساعدة

🤖 مرحباً بك في بوتي الشخصي!

📋 الأوامر المتاحة:
• /start - بدء البوت
• /help - عرض المساعدة

🔘 الأزرار المتاحة:
• 👨‍💼 نبذة عني - معلومات شخصية
• 📍 موقعي - معلومات الموقع
• 💼 أعمالي - مشاريعي وأعمالي
• 🎯 خبرتي - مهاراتي وخبراتي
• 🏆 إنجازاتي - جوائزي وإنجازاتي
• 🤖 إكسا الذكي - مساعد ذكي

💡 يمكنك استخدام الأزرار السفلية أو المضمنة للتنقل"""

                try:
                    await query.edit_message_text(
                        help_text,
                        reply_markup=self.get_welcome_inline_keyboard()
                    )
                except Exception as edit_error:
                    await query.message.reply_text(
                        help_text,
                        reply_markup=self.get_reply_keyboard()
                    )
            else:
                # إرسال رسالة مع صورة للأنواع الأخرى
                await self.send_message_with_image_from_callback(query, context, message_type)

        except Exception as e:
            logger.error(f"خطأ في معالجة أزرار الترحيب: {e}")
            try:
                await query.edit_message_text("❌ حدث خطأ أثناء المعالجة")
            except:
                await query.message.reply_text(
                    "❌ حدث خطأ أثناء المعالجة",
                    reply_markup=self.get_reply_keyboard()
                )

    async def send_message_with_image_from_callback(self, query, context: ContextTypes.DEFAULT_TYPE, message_type: str):
        """إرسال رسالة مع صورة من الزر المضمن"""
        try:
            from main_bot.core.config import MESSAGES, IMAGES

            message_text = MESSAGES[message_type]
            image_path = IMAGES[message_type]

            # التحقق من وجود الصورة
            if os.path.exists(image_path):
                # تحديد نوع لوحة المفاتيح حسب نوع الرسالة
                if message_type == "works":
                    reply_markup = self.get_works_inline_keyboard()
                else:
                    reply_markup = self.get_reply_keyboard()

                # إرسال الصورة مع النص
                with open(image_path, 'rb') as photo:
                    await context.bot.send_photo(
                        chat_id=query.message.chat_id,
                        photo=photo,
                        caption=message_text,
                        parse_mode=ParseMode.HTML,
                        reply_markup=reply_markup
                    )

                # حذف رسالة الترحيب الأصلية
                try:
                    await query.delete_message()
                except:
                    pass

            else:
                # إرسال النص فقط إذا لم توجد الصورة
                await context.bot.send_message(
                    chat_id=query.message.chat_id,
                    text=message_text,
                    parse_mode=ParseMode.HTML,
                    reply_markup=self.get_reply_keyboard()
                )

                # حذف رسالة الترحيب الأصلية
                try:
                    await query.delete_message()
                except:
                    pass

            # إرسال إشعار مراقبة
            await self.monitoring.send_bot_response_notification(
                message_text[:100] + "..." if len(message_text) > 100 else message_text,
                "photo_with_text" if os.path.exists(image_path) else "text"
            )

        except Exception as e:
            logger.error(f"خطأ في إرسال الرسالة من الزر المضمن: {e}")
            try:
                await query.edit_message_text("❌ حدث خطأ أثناء إرسال الرسالة")
            except:
                await query.message.reply_text(
                    "❌ حدث خطأ أثناء إرسال الرسالة",
                    reply_markup=self.get_reply_keyboard()
                )

    async def handle_callback_query(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالجة الضغط على الأزرار المضمنة"""
        query = update.callback_query
        await query.answer()

        user = query.from_user
        callback_data = query.data

        logger.info(f"تم الضغط على زر مضمن: {callback_data} من المستخدم: {user.first_name}")

        # إعداد معلومات المستخدم للمراقبة
        user_info = self.monitoring.get_user_info(user)

        # تحديد اسم الزر
        button_names = {
            "main_menu": "القائمة الرئيسية",
            "location": "الموقع",
            "about": "نبذة عني",
            "works": "أعمالي",
            "experience": "خبراتي",
            "achievements": "إنجازاتي",
            "exa_ai": "إكسا الذكي",
            "end_exa_ai": "إنهاء إكسا الذكي",
            "my_wallet": "محفظتي",
            "my_balance": "عرض الرصيد",
            "add_balance": "إضافة رصيد",
            "show_transactions": "عرض المعاملات",
            "usage_report": "تقرير الاستخدام",
            "request_loan": "طلب سلفة",
            "loan_status": "حالة السلفة",
            "loan_history": "عمليات السلفة",
            "help": "المساعدة",
            "about_me": "نبذة عني",
            "my_location": "موقعي",
            "my_works": "أعمالي",
            "my_experience": "خبرتي",
            "my_achievements": "إنجازاتي",
            "help_info": "المساعدة"
        }

        button_name = button_names.get(callback_data, callback_data)

        # إرسال إشعار ضغط الزر
        action_details = {
            'button_name': button_name,
            'button_data': callback_data,
            'has_photo': False,  # سيتم تحديثه حسب نوع الرد
            'total_clicks': 1  # يمكن تحديثه من قاعدة البيانات
        }

        await self.monitoring.send_user_action_notification(
            user_info,
            "button_click",
            action_details
        )

        # معالجة الأزرار المختلفة
        if callback_data == "main_menu":
            try:
                await query.edit_message_text(
                    MESSAGES["welcome"],
                    reply_markup=self.get_main_keyboard(),
                    parse_mode=ParseMode.HTML
                )
            except Exception as e:
                await query.message.reply_text(
                    MESSAGES["welcome"],
                    reply_markup=self.get_main_keyboard(),
                    parse_mode=ParseMode.HTML
                )
        elif callback_data in ["about_me", "my_location", "my_works", "my_experience", "my_achievements", "help_info"]:
            await self.handle_welcome_buttons(query, context, callback_data)
        elif callback_data in ["location", "about", "works", "experience", "achievements"]:
            await self.send_media_message(update, context, callback_data, callback_data)
        elif callback_data == "my_wallet":
            await self.show_my_wallet(query, context)
        elif callback_data == "my_balance":
            await self.show_my_balance(query, context)
        elif callback_data == "add_balance":
            await self.add_balance(query, context)
        elif callback_data == "show_transactions":
            await self.show_transactions(query, context)
        elif callback_data == "usage_report":
            await self.show_usage_report(query, context)
        elif callback_data == "request_loan":
            await self.request_loan(query, context)
        elif callback_data == "loan_status":
            await self.loan_status(query, context)
        elif callback_data == "loan_history":
            await self.show_loan_history(query, context)
        elif callback_data == "loan_service":
            await self.show_loan_service(query, context)
        elif callback_data == "exa_ai":
            # فحص التقييد قبل عرض خيارات إكسا الذكي
            user = update.effective_user
            if ban_checker.check_restriction(user.id, 'ai_assistant'):
                is_restricted, restriction_info = ban_checker.is_user_restricted(user.id)
                restriction_message = ban_checker.get_restriction_message('ai_assistant', restriction_info)
                await query.edit_message_text(
                    restriction_message,
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            # فحص حالة النماذج قبل عرض خيارات إكسا
            # فحص حالة النماذج قبل عرض خيارات إكسا
            models_status = self.ai_assistant.multi_model_manager._check_models_status() if self.ai_assistant.multi_model_manager else {"any_active": True}

            if not models_status.get("any_active", True):
                # جميع النماذج معطلة - عرض رسالة التوقف
                disabled_message = """🚫 **إكسا الذكي متوقف مؤقتاً**

عذراً، جميع نماذج الذكاء الاصطناعي معطلة حالياً من قبل المدير.

⏰ يرجى المحاولة لاحقاً

💡 يمكنك استخدام باقي خدمات البوت بشكل طبيعي"""

                try:
                    await query.edit_message_text(
                        disabled_message,
                        reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="main_menu")]]),
                        parse_mode=ParseMode.MARKDOWN
                    )
                except Exception as e:
                    await query.message.reply_text(
                        disabled_message,
                        reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="main_menu")]]),
                        parse_mode=ParseMode.MARKDOWN
                    )
                return

            await self.check_models_and_show_exa_options(update, context)
        elif callback_data == "exa_ai_normal":
            # فحص التقييد لإكسا الذكي العادي
            user = update.effective_user
            if ban_checker.check_restriction(user.id, 'ai_assistant'):
                is_restricted, restriction_info = ban_checker.is_user_restricted(user.id)
                restriction_message = ban_checker.get_restriction_message('ai_assistant', restriction_info)
                await query.edit_message_text(
                    restriction_message,
                    parse_mode=ParseMode.MARKDOWN
                )
                return
            await self.start_exa_ai_conversation(update, context)
        elif callback_data == "exa_ai_pro":
            # فحص التقييد لإكسا الذكي برو
            user = update.effective_user
            if ban_checker.check_restriction(user.id, 'exa_ai_pro'):
                is_restricted, restriction_info = ban_checker.is_user_restricted(user.id)
                restriction_message = ban_checker.get_restriction_message('exa_ai_pro', restriction_info)
                await query.edit_message_text(
                    restriction_message,
                    parse_mode=ParseMode.MARKDOWN
                )
                return
            await self.start_exa_ai_pro_conversation(update, context)
        elif callback_data == "end_exa_ai":
            await self.end_exa_ai_conversation(update, context)
        elif callback_data == "end_exa_ai_pro":
            await self.end_exa_ai_pro_conversation(update, context)
        elif callback_data == "help":
            await self.show_help_message(update, context)
        elif callback_data == "copy_balance_form":
            # معالجة نسخ نموذج إضافة الرصيد
            await self.copy_balance_form(query, context)
        else:
            # محاولة معالجة الزر باستخدام معالج الأزرار أولاً
            handled = await self.button_processor.handle_callback_query(update, context, callback_data)

            if not handled:
                # معالجة الزر باستخدام المعالج المشترك
                button_result = self.text_processor.process_callback_query(callback_data)

                if button_result['success']:
                    if button_result['action'] == "send_pdf":
                        await self.send_pdf_file(query, context, button_result['target'])
                    elif button_result['action'] == "view_invoice":
                        await self.handle_view_invoice(query, context, button_result['target'])
                    elif button_result['action'] == "print_invoice":
                        await self.handle_print_invoice(query, context, button_result['target'])
                    else:
                        await query.edit_message_text("❌ إجراء غير مدعوم")
                else:
                    await query.edit_message_text("❌ زر غير معروف")

    async def send_pdf_file(self, query, context: ContextTypes.DEFAULT_TYPE, file_type: str):
        """إرسال ملف PDF باستخدام المعالج المشترك"""
        try:
            # إرسال رسالة "جاري إرسال الملف" كرسالة جديدة
            loading_message = await query.message.reply_text("📤 جاري إرسال ملف الأعمال...")

            # الحصول على مسار الملف من المعالج المشترك
            pdf_path = self.text_processor.get_pdf_file_path(file_type)

            if pdf_path and os.path.exists(pdf_path):
                # تحديد اسم الملف والرسالة حسب النوع
                file_info = {
                    "works": {
                        "filename": "اعمالي.pdf",
                        "caption": "📄 ملف أعمالي\n\nهذا ملف يحتوي على جميع أعمالي ومشاريعي السابقة.",
                        "success_message": "✅ تم إرسال ملف أعمالي بنجاح!\n\nيمكنك الآن تصفح جميع أعمالي ومشاريعي من خلال الملف المرفق."
                    }
                }

                info = file_info.get(file_type, file_info["works"])

                # إرسال ملف PDF
                with open(pdf_path, 'rb') as pdf_file:
                    await context.bot.send_document(
                        chat_id=query.message.chat_id,
                        document=pdf_file,
                        filename=info["filename"],
                        caption=info["caption"],
                        reply_markup=self.get_reply_keyboard()
                    )

                # تحديث رسالة التحميل بنجاح الإرسال
                try:
                    await loading_message.edit_text(info["success_message"])
                    await asyncio.sleep(2)  # عرض رسالة النجاح لثانيتين
                    await loading_message.delete()
                except Exception:
                    # إذا فشل التحديث، احذف الرسالة مباشرة
                    try:
                        await loading_message.delete()
                    except Exception:
                        pass

                # إرسال إشعار مراقبة
                await self.monitoring.send_bot_response_notification(
                    f"تم إرسال ملف PDF: {file_type}",
                    "document"
                )

                logger.info(f"تم إرسال ملف PDF ({file_type}) للمستخدم: {query.from_user.first_name}")

            else:
                await loading_message.edit_text(
                    "❌ عذراً، الملف غير متوفر حالياً.\n\n"
                    "يرجى المحاولة مرة أخرى لاحقاً أو التواصل مع المطور."
                )
                logger.warning(f"ملف PDF غير موجود: {pdf_path}")

        except Exception as e:
            logger.error(f"خطأ في إرسال ملف PDF: {e}")
            try:
                # إرسال رسالة خطأ كرسالة جديدة بدلاً من تعديل الرسالة الحالية
                await query.message.reply_text(
                    "❌ حدث خطأ أثناء إرسال الملف.\n\n"
                    "يرجى المحاولة مرة أخرى لاحقاً."
                )
            except Exception:
                pass

    async def send_text_with_image(self, update: Update, message_key: str, context: ContextTypes.DEFAULT_TYPE = None):
        """إرسال رسالة مع صورة إذا كانت متوفرة (للأوامر)"""
        message_text = MESSAGES[message_key]
        image_path = IMAGES[message_key]

        # التحقق من وجود الصورة
        if os.path.exists(image_path):
            try:
                import asyncio
                from telegram.error import TimedOut, NetworkError, BadRequest

                with open(image_path, 'rb') as photo:
                    # تحديد نوع لوحة المفاتيح حسب نوع الرسالة
                    reply_markup = self.button_processor.get_keyboard_for_message_type(message_key)

                    # محاولة إرسال الصورة مع timeout محسن
                    await asyncio.wait_for(
                        update.message.reply_photo(
                            photo=photo,
                            caption=message_text,
                            parse_mode=ParseMode.HTML,
                            reply_markup=reply_markup,
                            read_timeout=60,    # زيادة timeout للقراءة
                            write_timeout=60,   # زيادة timeout للكتابة
                            connect_timeout=30  # timeout للاتصال
                        ),
                        timeout=90  # مهلة إجمالية 90 ثانية
                    )

                # إرسال إشعار رد البوت للصورة
                await self.monitoring.send_bot_response_notification(
                    message_text[:100] + "..." if len(message_text) > 100 else message_text,
                    "photo_with_text"
                )
                logger.info(f"تم إرسال صورة مع نص: {message_key}")
                return

            except (TimedOut, asyncio.TimeoutError) as e:
                logger.warning(f"انتهت مهلة إرسال الصورة {image_path}: {e}")
                # سيتم إرسال النص فقط أدناه

            except (NetworkError, BadRequest) as e:
                logger.warning(f"خطأ في الشبكة أو طلب خاطئ للصورة {image_path}: {e}")
                # سيتم إرسال النص فقط أدناه

            except Exception as e:
                logger.error(f"خطأ غير متوقع في إرسال الصورة {image_path}: {e}")
                # سيتم إرسال النص فقط أدناه

        # إرسال النص فقط إذا لم تكن الصورة متوفرة
        # تحديد نوع لوحة المفاتيح حسب نوع الرسالة
        reply_markup = self.button_processor.get_keyboard_for_message_type(message_key)

        await update.message.reply_text(
            message_text,
            parse_mode=ParseMode.HTML,
            reply_markup=reply_markup
        )

        # إرسال إشعار رد البوت بدون انتظار
        import asyncio
        asyncio.create_task(self.monitoring.send_bot_response_notification(
            message_text[:50] + "..." if len(message_text) > 50 else message_text,
            "text"
        ))

        logger.info(f"تم إرسال النص فقط لـ {message_key}")
    
    async def send_media_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE,
                                message_key: str, image_key: str):
        """إرسال رسالة مع صورة إذا كانت متوفرة (للأزرار)"""
        query = update.callback_query
        await query.answer()

        message_text = MESSAGES[message_key]
        image_path = IMAGES[image_key]
        user = update.effective_user

        # تحديد نوع المحتوى
        has_photo = os.path.exists(image_path)
        content_type = "مع صورة" if has_photo else "نص فقط"

        # التحقق من وجود الصورة
        if os.path.exists(image_path):
            try:
                # تحديد نوع لوحة المفاتيح حسب نوع الرسالة
                reply_markup = self.button_processor.get_keyboard_for_message_type(message_key)

                # إرسال رسالة جديدة بالصورة
                with open(image_path, 'rb') as photo:
                    await query.message.reply_photo(
                        photo=photo,
                        caption=message_text,
                        parse_mode=ParseMode.HTML,
                        reply_markup=reply_markup
                    )
                # حذف الرسالة الأصلية
                try:
                    await query.message.delete()
                except:
                    pass

                # إرسال إشعار رد البوت
                await self.monitoring.send_bot_response_notification(
                    message_text[:100] + "..." if len(message_text) > 100 else message_text,
                    "photo_with_text"
                )
                return
            except Exception as e:
                logger.warning(f"فشل في إرسال الصورة {image_path}: {e}")

        # إرسال النص فقط إذا لم تكن الصورة متوفرة
        try:
            # تحديد نوع لوحة المفاتيح حسب نوع الرسالة
            reply_markup = self.button_processor.get_keyboard_for_message_type(message_key)

            await query.edit_message_text(
                message_text,
                parse_mode=ParseMode.HTML,
                reply_markup=reply_markup
            )
        except Exception as e:
            # إذا فشل التعديل، أرسل رسالة جديدة
            # تحديد نوع لوحة المفاتيح حسب نوع الرسالة
            reply_markup = self.button_processor.get_keyboard_for_message_type(message_key)

            await query.message.reply_text(
                message_text,
                parse_mode=ParseMode.HTML,
                reply_markup=reply_markup
            )

        # إرسال إشعار رد البوت بدون انتظار
        import asyncio
        asyncio.create_task(self.monitoring.send_bot_response_notification(
            message_text[:50] + "..." if len(message_text) > 50 else message_text,
            "text"
        ))

        logger.info(f"تم إرسال النص فقط لـ {message_key}")
    
    async def start_exa_ai_from_text(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """بدء محادثة إكسا الذكي من الزر النصي"""
        self.ai_assistant.start_conversation(context.user_data)
        
        welcome_message = self.ai_assistant.get_welcome_message()
        
        # إنشاء زر إنهاء المحادثة
        keyboard = [[InlineKeyboardButton("❌ إنهاء المحادثة مع إكسا الذكي العادي", callback_data="end_exa_ai")]]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.message.reply_text(
            welcome_message,
            reply_markup=reply_markup,
            parse_mode=ParseMode.HTML
        )
    
    async def start_exa_ai_conversation(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """بدء محادثة إكسا الذكي من الزر المضمن"""
        query = update.callback_query
        await query.answer()
        
        self.ai_assistant.start_conversation(context.user_data)
        
        welcome_message = self.ai_assistant.get_welcome_message()
        
        await query.edit_message_text(
            welcome_message,
            reply_markup=self.get_exa_ai_chat_keyboard(is_pro=False),
            parse_mode=ParseMode.HTML
        )
    
    async def handle_exa_ai_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالجة رسائل إكسا الذكي"""
        user = update.effective_user

        # فحص ما إذا كان المستخدم مقيد من استخدام إكسا الذكي
        if ban_checker.check_restriction(user.id, 'ai_assistant'):
            is_restricted, restriction_info = ban_checker.is_user_restricted(user.id)
            restriction_message = ban_checker.get_restriction_message('ai_assistant', restriction_info)
            await update.message.reply_text(
                restriction_message,
                parse_mode=ParseMode.MARKDOWN
            )
            return

        user_message = update.message.text

        # إضافة رسالة المستخدم إلى تاريخ المحادثة
        self.ai_assistant.add_to_conversation(context.user_data, "user", user_message)

        # إرسال حالة "جاري الكتابة" أثناء انتظار الرد
        if context.user_data.get("exa_ai_pro_active", False):
            await update.message.chat.send_action(action="typing")
            typing_message = await update.message.reply_text("🧠 جاري التفكير من إكسا الذكي برو...")
        else:
            await update.message.chat.send_action(action="typing")
            typing_message = await update.message.reply_text("🤖 جاري الكتابة من إكسا الذكي...")

        # تحديد نوع المحادثة والحصول على الرد المناسب
        conversation_history = context.user_data.get("exa_conversation", [])

        if context.user_data.get("exa_ai_pro_active", False):
            # استخدام DeepSeek-R1 للوضع برو مع فوترة التوكن
            ai_response, billing_message, success = await self.ai_assistant.get_ai_response_pro(
                user.id, user_message, conversation_history
            )
            reply_markup = self.get_exa_ai_chat_keyboard(is_pro=True)

            # إذا فشل الطلب، عرض رسالة الخطأ
            if not success:
                # حذف رسالة "جاري الكتابة"
                try:
                    await typing_message.edit_text("❌ فشل في المعالجة...")
                    import asyncio
                    await asyncio.sleep(0.5)
                    await typing_message.delete()
                except:
                    pass

                await update.message.reply_text(
                    ai_response,
                    reply_markup=reply_markup,
                    parse_mode=ParseMode.HTML
                )
                return
        else:
            # استخدام النموذج العادي (محدث)
            ai_response, status_message, success = await self.ai_assistant.get_ai_response(
                user.id, user_message, conversation_history
            )
            billing_message = status_message if status_message != "نظام قديم" else ""
            reply_markup = self.get_exa_ai_chat_keyboard(is_pro=False)

            # إذا فشل الطلب، عرض رسالة الخطأ
            if not success:
                # حذف رسالة "جاري الكتابة"
                try:
                    await typing_message.edit_text("❌ فشل في المعالجة...")
                    import asyncio
                    await asyncio.sleep(0.5)
                    await typing_message.delete()
                except:
                    pass

                await update.message.reply_text(
                    ai_response,
                    reply_markup=reply_markup,
                    parse_mode=ParseMode.HTML
                )
                return

        # حذف رسالة "جاري الكتابة" بأنيميشن جميل
        try:
            # تحديث الرسالة بأنيميشن قبل الحذف
            if context.user_data.get("exa_ai_pro_active", False):
                await typing_message.edit_text("🧠 ✅ تم الانتهاء من التفكير...")
            else:
                await typing_message.edit_text("🤖 ✅ تم الانتهاء من الكتابة...")

            # انتظار قصير لإظهار الأنيميشن
            import asyncio
            await asyncio.sleep(0.5)

            # حذف الرسالة
            await typing_message.delete()
        except Exception as e:
            logger.warning(f"فشل في حذف رسالة الكتابة: {e}")

        # إضافة رد إكسا إلى تاريخ المحادثة
        self.ai_assistant.add_to_conversation(context.user_data, "assistant", ai_response)

        # إرسال الرد
        await update.message.reply_text(
            ai_response,
            reply_markup=reply_markup,
            parse_mode=ParseMode.HTML
        )

        # إرسال رسالة الحالة/الفوترة إذا كانت موجودة
        if billing_message:
            if context.user_data.get("exa_ai_pro_active", False):
                # رسالة فوترة للوضع برو
                await update.message.reply_text(
                    f"{billing_message}",
                    parse_mode=ParseMode.HTML
                )
            else:
                # رسالة حالة للوضع العادي (إذا كانت مهمة)
                if "متبقي" in billing_message or "تجاوز" in billing_message or "احتياطي" in billing_message or "المحاولات" in billing_message:
                    await update.message.reply_text(
                        f"{billing_message}",
                        parse_mode=ParseMode.HTML
                    )

        # إرسال إشعار رد البوت لإكسا الذكي
        await self.monitoring.send_bot_response_notification(
            ai_response[:100] + "..." if len(ai_response) > 100 else ai_response,
            "ai_response"
        )
    
    async def end_exa_ai_conversation(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """إنهاء محادثة إكسا الذكي"""
        query = update.callback_query
        await query.answer()
        
        self.ai_assistant.end_conversation(context.user_data)
        
        end_message = """✅︙تم إنهاء المحادثة مع إكسا الذكي العادي

شكراً لك على استخدام المساعد الذكي!
يمكنك العودة إليه في أي وقت من خلال الضغط على زر "🤖 إكسا الذكي"
        """
        
        await query.edit_message_text(
            end_message,
            parse_mode=ParseMode.HTML
        )

        # إرسال رسالة منفصلة للأزرار المضمنة
        await query.message.reply_text(
            "يمكنك أيضاً استخدام الأزرار المضمنة للتنقل السريع ︙",
            reply_markup=self.get_main_keyboard()
        )

        # إرسال إشعار رد البوت لإنهاء إكسا
        await self.monitoring.send_bot_response_notification(
            "تم إنهاء المحادثة مع إكسا الذكي العادي",
            "text"
        )

    async def start_exa_ai_pro_conversation(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """بدء محادثة إكسا الذكي برو من الزر المضمن"""
        query = update.callback_query
        await query.answer()

        user = update.effective_user

        # فحص الرصيد قبل بدء المحادثة
        can_use, balance_message, balance_info = await self.ai_assistant.check_balance_for_ai_pro(user.id)

        if not can_use:
            # عرض رسالة عدم إمكانية الاستخدام
            await query.edit_message_text(
                f"❌ {balance_message}\n\n"
                "يرجى شحن رصيدك أو سداد الدين لاستخدام إكسا الذكي برو.",
                reply_markup=self.get_exa_ai_keyboard()
            )
            return

        # تفعيل وضع إكسا الذكي برو
        context.user_data["exa_ai_pro_active"] = True
        context.user_data["exa_ai_active"] = False  # إيقاف الوضع العادي

        # بدء محادثة جديدة
        self.ai_assistant.start_conversation(context.user_data)

        # إضافة تحذير الرصيد المنخفض إذا لزم الأمر
        balance_warning = ""
        if balance_info.get("has_low_balance", False):
            balance_warning = f"\n\n⚠️ تحذير: رصيدك منخفض ({balance_info.get('balance', 0):.3f} إكسا). يرجى شحن رصيدك قريباً."

        welcome_message = f"""🧠 مرحباً بك في إكسا الذكي برو!

أنا المساعد الذكي المتقدم الخاص بالمالك. هنا لمساعدتك في أي استفسارات أو أسئلة قد تكون لديك.

يمكنني مساعدتك في:
• طرح أسئلة حول التصميم والعلاقات التجارية
• الاستفسار عن خدمات المالك
• طلب نصائح في مجال الجرافيك والمنتمديا
• تحليل المشاريع وتقديم استراتيجيات عملية
• وضع خطط عمل مرحلية للمشاريع المعقدة
• أو أي شيء آخر تحتاج المساعدة فيه

🧠 **مميز:** أستخدم نموذج ExaPro-Q1 المتقدم للتفكير العميق والتحليل المفصل

💰 **ملاحظة:** يتم خصم التوكن حسب الاستخدام الفعلي{balance_warning}

كيف يمكنني مساعدتك اليوم؟ 🤖"""

        await query.edit_message_text(
            welcome_message,
            reply_markup=self.get_exa_ai_chat_keyboard(is_pro=True),
            parse_mode=ParseMode.HTML
        )

        # إرسال إشعار رد البوت لبدء إكسا برو
        await self.monitoring.send_bot_response_notification(
            "تم بدء محادثة إكسا الذكي برو",
            "text"
        )

    async def end_exa_ai_pro_conversation(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """إنهاء محادثة إكسا الذكي برو"""
        query = update.callback_query
        await query.answer()

        # إيقاف وضع إكسا الذكي برو
        context.user_data["exa_ai_pro_active"] = False

        # إنهاء المحادثة
        self.ai_assistant.end_conversation(context.user_data)

        end_message = """✅︙تم إنهاء المحادثة مع إكسا الذكي برو

شكراً لاستخدام إكسا الذكي برو!
يمكنك العودة في أي وقت للحصول على المساعدة المتقدمة."""

        await query.edit_message_text(
            end_message,
            parse_mode=ParseMode.HTML
        )

        # إرسال رسالة منفصلة للأزرار المضمنة
        await query.message.reply_text(
            "يمكنك أيضاً استخدام الأزرار المضمنة للتنقل السريع ︙",
            reply_markup=self.get_main_keyboard()
        )

        # إرسال إشعار رد البوت لإنهاء إكسا برو
        await self.monitoring.send_bot_response_notification(
            "تم إنهاء المحادثة مع إكسا الذكي برو",
            "text"
        )

    async def start_exa_ai_pro_from_text(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """بدء محادثة إكسا الذكي برو من الرسالة النصية"""
        # تفعيل وضع إكسا الذكي برو
        context.user_data["exa_ai_pro_active"] = True
        context.user_data["exa_ai_active"] = False  # إيقاف الوضع العادي

        # بدء محادثة جديدة
        self.ai_assistant.start_conversation(context.user_data)

        welcome_message = """🧠︙مرحباً بك في إكسا الذكي برو!

أنا المساعد الذكي المتقدم الخاص بالمالك. هنا لمساعدتك في أي استفسارات أو أسئلة قد تكون لديك.

يمكنني مساعدتك في︙
• طرح أسئلة حول التصميم والعلاقات التجارية
• الاستفسار عن خدمات المالك
• طلب نصائح في مجال الجرافيك والمنتمديا
• تحليل المشاريع وتقديم استراتيجيات عملية
• وضع خطط عمل مرحلية للمشاريع المعقدة
• أو أي شيء آخر تحتاج المساعدة فيه

🧠︙ **مميز:** أستخدم نموذج ExaPro-Q1 المتقدم للتفكير العميق والتحليل المفصل

كيف يمكنني مساعدتك اليوم ؟ 🤖"""

        await update.message.reply_text(
            welcome_message,
            reply_markup=self.get_exa_ai_pro_keyboard(),
            parse_mode=ParseMode.HTML
        )

        # إرسال إشعار رد البوت لبدء إكسا برو
        await self.monitoring.send_bot_response_notification(
            "تم بدء محادثة إكسا الذكي برو",
            "text"
        )

    async def show_exa_ai_options(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض خيارات إكسا الذكي"""
        query = update.callback_query
        await query.answer()

        options_message = """🤖︙مرحباً بك في إكسا الذكي!

اختر الوضع المناسب لك︙

🧠︙إكسا الذكي برو
• استشارات متقدمة ومعقدة
• تحليل عميق ومفصل
• مناسب للمشاريع والأعمال المهنية
• يستخدم نموذج ExaPro-Q1 المتقدم

🤖︙إكسا الذكي العادي
• محادثات عامة وأسئلة بسيطة
• ردود سريعة ومفيدة
• مناسب للاستفسارات اليومية
• يستخدم نموذج Exa-X1

اختر الوضع الذي تريده︙"""

        try:
            # محاولة تعديل الرسالة أولاً
            await query.edit_message_text(
                options_message,
                reply_markup=self.get_exa_ai_keyboard(),
                parse_mode=ParseMode.MARKDOWN
            )
        except Exception as e:
            # إذا فشل التعديل، أرسل رسالة جديدة
            await query.message.reply_text(
                options_message,
                reply_markup=self.get_exa_ai_keyboard(),
                parse_mode=ParseMode.MARKDOWN
            )

        # إرسال إشعار رد البوت لعرض خيارات إكسا
        await self.monitoring.send_bot_response_notification(
            "تم عرض خيارات إكسا الذكي",
            "text"
        )

    async def show_exa_ai_options_from_text(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض خيارات إكسا الذكي من الرسالة النصية"""
        options_message = """🤖︙مرحباً بك في إكسا الذكي!

اختر الوضع المناسب لك︙

🧠︙إكسا الذكي برو
• استشارات متقدمة ومعقدة
• تحليل عميق ومفصل
• مناسب للمشاريع والأعمال المهنية
• يستخدم نموذج ExaPro-Q1 المتقدم

🤖︙إكسا الذكي العادي
• محادثات عامة وأسئلة بسيطة
• ردود سريعة ومفيدة
• مناسب للاستفسارات اليومية
• يستخدم نموذج Exa-X1

اختر الوضع الذي تريده︙"""

        await update.message.reply_text(
            options_message,
            reply_markup=self.get_exa_ai_keyboard(),
            parse_mode=ParseMode.MARKDOWN
        )

        # إرسال إشعار رد البوت لعرض خيارات إكسا
        await self.monitoring.send_bot_response_notification(
            "تم عرض خيارات إكسا الذكي",
            "text"
        )
    
    def load_users_data(self):
        """تحميل بيانات المستخدمين"""
        return self.user_manager.load_users_data()

    async def error_handler(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج الأخطاء العام"""
        error = context.error

        # تسجيل الخطأ فقط
        logger.warning(f"خطأ في البوت: {error}")

        # تجاهل جميع الأخطاء لعدم توقف البوت
        return

    async def send_admin_notification(self, message: str):
        """إرسال إشعار لبوت الإدارة"""
        try:
            async with httpx.AsyncClient() as client:
                url = f"https://api.telegram.org/bot{self.ADMIN_BOT_TOKEN}/sendMessage"
                data = {
                    "chat_id": self.ADMIN_CHAT_ID,
                    "text": f"🤖 [البوت الرئيسي]\n{message}",
                    "parse_mode": "HTML"
                }
                await client.post(url, data=data, timeout=5)
        except Exception as e:
            logger.error(f"فشل في إرسال إشعار للمدير: {e}")

    async def handle_photo(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج الصور"""
        user = update.effective_user

        # فحص ما إذا كان المستخدم محظور
        if await self.check_user_ban_status(update):
            return

        photo = update.message.photo[-1]  # أكبر حجم
        caption = update.message.caption or "بدون وصف"

        # إعداد معلومات المستخدم للمراقبة
        user_info = self.monitoring.get_user_info(user)

        # تفاصيل الصورة
        action_details = {
            'message_text': caption,
            'file_type': 'صورة',
            'file_size': photo.file_size,
            'has_photo': True,
            'has_file': False
        }

        # إرسال إشعار استلام الصورة
        await self.monitoring.send_user_action_notification(
            user_info,
            "message",
            action_details
        )

        file_info = {
            "type": "photo",
            "name": f"صورة_{photo.width}x{photo.height}",
            "size": photo.file_size or 0,
            "caption": caption
        }

        # رد البوت
        response_text = "شكراً لك على إرسال الصورة! تم استلامها وسيتم مراجعتها."

        await update.message.reply_text(
            f"📷 {response_text}",
            reply_markup=self.get_reply_keyboard()
        )

        # إرسال إشعار رد البوت
        await self.monitoring.send_bot_response_notification(response_text, "text")

    async def handle_document(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج المستندات والملفات"""
        user = update.effective_user

        # فحص ما إذا كان المستخدم محظور
        if await self.check_user_ban_status(update):
            return

        document = update.message.document
        caption = update.message.caption or "بدون وصف"

        # إعداد معلومات المستخدم للمراقبة
        user_info = self.monitoring.get_user_info(user)

        # تفاصيل الملف
        action_details = {
            'message_text': caption,
            'file_type': 'ملف',
            'file_name': document.file_name or "ملف غير محدد",
            'file_size': document.file_size or 0,
            'has_photo': False,
            'has_file': True
        }

        # إرسال إشعار استلام الملف
        await self.monitoring.send_user_action_notification(
            user_info,
            "message",
            action_details
        )

        # رد البوت
        response_text = "شكراً لك على إرسال الملف! تم استلامه وسيتم مراجعته."

        await update.message.reply_text(
            f"📄 {response_text}",
            reply_markup=self.get_reply_keyboard()
        )

        # إرسال إشعار رد البوت
        await self.monitoring.send_bot_response_notification(response_text, "text")

    async def handle_audio(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج الملفات الصوتية"""
        user = update.effective_user

        # فحص ما إذا كان المستخدم محظور
        if await self.check_user_ban_status(update):
            return

        audio = update.message.audio

        await self.send_admin_notification(
            f"🎵 ملف صوتي جديد\n"
            f"👤 المستخدم: {user.first_name}\n"
            f"🎵 العنوان: {audio.title or 'غير محدد'}\n"
            f"👨‍🎤 المؤدي: {audio.performer or 'غير محدد'}\n"
            f"⏱️ المدة: {audio.duration or 'غير محدد'} ثانية"
        )

        await update.message.reply_text(
            "🎵 شكراً لك على إرسال الملف الصوتي!\n"
            "تم استلامه وسيتم مراجعته.",
            reply_markup=self.get_reply_keyboard()
        )

    async def handle_video(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج مقاطع الفيديو"""
        user = update.effective_user
        video = update.message.video
        caption = update.message.caption or "بدون وصف"

        await self.send_admin_notification(
            f"🎬 فيديو جديد\n"
            f"👤 المستخدم: {user.first_name}\n"
            f"📝 الوصف: {caption[:100]}{'...' if len(caption) > 100 else ''}\n"
            f"📏 الأبعاد: {video.width}x{video.height}\n"
            f"⏱️ المدة: {video.duration} ثانية\n"
            f"💾 حجم الملف: {video.file_size or 'غير محدد'} بايت"
        )

        await update.message.reply_text(
            "🎬 شكراً لك على إرسال الفيديو!\n"
            "تم استلامه وسيتم مراجعته.",
            reply_markup=self.get_reply_keyboard()
        )

    async def handle_voice(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج الرسائل الصوتية"""
        user = update.effective_user
        voice = update.message.voice

        await self.send_admin_notification(
            f"🎤 رسالة صوتية جديدة\n"
            f"👤 المستخدم: {user.first_name}\n"
            f"⏱️ المدة: {voice.duration} ثانية\n"
            f"💾 حجم الملف: {voice.file_size or 'غير محدد'} بايت"
        )

        await update.message.reply_text(
            "🎤 شكراً لك على الرسالة الصوتية!\n"
            "تم استلامها وسيتم مراجعتها.",
            reply_markup=self.get_reply_keyboard()
        )

    def send_admin_notification_sync(self, message: str):
        """إرسال إشعار للمدير (نسخة متزامنة)"""
        try:
            asyncio.create_task(self.send_admin_notification(message))
        except Exception as e:
            logger.error(f"فشل في إرسال إشعار للمدير: {e}")





    async def show_help_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض رسالة المساعدة"""
        query = update.callback_query
        await query.answer()

        help_text = """
🤖 **مرحباً بك في البوت!**

استخدم الأزرار التفاعلية للتنقل:

📍 **موقعي** - معلومات الموقع والتواصل
👨‍💼 **نبذة عني** - نبذة شخصية ومهنية
💼 **أعمالي** - المشاريع والأعمال
🎯 **خبرتي** - المهارات والخبرات
🏆 **إنجازاتي** - الجوائز والشهادات
🤖 **إكسا الذكي** - المساعد الذكي

💡 **نصيحة:** استخدم الأزرار للحصول على تجربة أفضل!
        """

        try:
            await query.edit_message_text(
                help_text,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=self.get_back_keyboard()
            )
        except Exception as e:
            await query.message.reply_text(
                help_text,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=self.get_back_keyboard()
            )

    async def setup_bot_commands(self):
        """إعداد الأوامر (مخفية)"""
        try:
            # إخفاء الأوامر العادية - الاعتماد على الأزرار
            commands = []
            await self.application.bot.set_my_commands(commands)
            logger.info("✅ تم إخفاء قائمة الأوامر - الاعتماد على الأزرار السفلية")
            print("✅ تم إخفاء قائمة الأوامر - الاعتماد على الأزرار السفلية")
        except Exception as e:
            logger.error(f"❌ خطأ في إخفاء الأوامر: {e}")
            print(f"❌ خطأ في إخفاء الأوامر: {e}")

    async def check_pending_invoices(self):
        """فحص وإرسال الفواتير المعلقة"""
        try:
            import json
            import os

            # مسار ملف الفواتير المعلقة
            invoices_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'shared', 'invoices')
            pending_invoices_file = os.path.join(invoices_dir, 'pending_invoices.json')

            if not os.path.exists(pending_invoices_file):
                return

            # قراءة الفواتير المعلقة
            with open(pending_invoices_file, 'r', encoding='utf-8') as f:
                pending_invoices = json.load(f)

            if not pending_invoices:
                return

            # إرسال الفواتير وحذفها من القائمة
            invoices_to_remove = []
            for invoice_key, invoice_data in pending_invoices.items():
                try:
                    # التحقق من نوع الفاتورة
                    if invoice_data.get('type') == 'pdf_invoice':
                        # فاتورة PDF - إرسال الملف مباشرة
                        user_id = invoice_data['user_id']
                        await self.send_invoice_files(user_id, invoice_data)
                        invoices_to_remove.append(invoice_key)
                        logger.info(f"✅ تم إرسال فاتورة PDF {invoice_data['invoice_number']} للمستخدم {user_id}")
                    elif invoice_data.get('type') == 'combined_invoice':
                        # فاتورة مدمجة - إرسال النص مع الأزرار في رسالة واحدة
                        user_id = invoice_data['user_id']
                        message_text = invoice_data.get('message', '')
                        await self.send_invoice_files(user_id, invoice_data, include_message_text=True, message_text=message_text)
                        invoices_to_remove.append(invoice_key)
                        logger.info(f"✅ تم إرسال فاتورة مدمجة {invoice_data['invoice_number']} للمستخدم {user_id}")
                    else:
                        # فاتورة نصية تقليدية
                        # التحقق من أن المفتاح هو user_id صحيح
                        try:
                            user_id = int(invoice_key)

                            # إرسال ملفات الفاتورة مع النص المدمج إذا كانت متوفرة
                            if invoice_data.get('has_files', False):
                                # إرسال رسالة واحدة تحتوي على النص والأزرار
                                message_text = invoice_data.get('message', '')
                                await self.send_invoice_files(user_id, invoice_data, include_message_text=True, message_text=message_text)
                            else:
                                # إرسال الرسالة النصية فقط إذا لم تكن هناك ملفات
                                if 'message' in invoice_data:
                                    await self.application.bot.send_message(
                                        chat_id=user_id,
                                        text=invoice_data['message']
                                    )
                                else:
                                    logger.warning(f"⚠️ فاتورة بدون رسالة نصية للمستخدم {user_id}")

                            invoices_to_remove.append(invoice_key)
                            logger.info(f"✅ تم إرسال فاتورة {invoice_data.get('invoice_id', invoice_key)} للمستخدم {user_id}")
                        except ValueError:
                            # المفتاح ليس user_id، تخطي هذه الفاتورة
                            logger.warning(f"⚠️ تخطي فاتورة بمفتاح غير صحيح: {invoice_key}")
                            continue
                except Exception as send_error:
                    # تحديد معرف المستخدم للخطأ
                    error_user_id = invoice_data.get('user_id', invoice_key)
                    logger.error(f"❌ فشل إرسال فاتورة للمستخدم {error_user_id}: {send_error}")

            # حذف الفواتير المرسلة
            for invoice_key in invoices_to_remove:
                del pending_invoices[invoice_key]

            # حفظ الفواتير المحدثة
            if invoices_to_remove:
                with open(pending_invoices_file, 'w', encoding='utf-8') as f:
                    json.dump(pending_invoices, f, ensure_ascii=False, indent=2)

        except Exception as e:
            logger.error(f"❌ خطأ في فحص الفواتير المعلقة: {e}")

    async def send_invoice_files(self, user_id, invoice_data, include_message_text=False, message_text=""):
        """إرسال ملفات الفاتورة PDF مع أزرار تفاعلية"""
        try:
            # الحصول على بيانات الفاتورة
            invoice_number = invoice_data.get('invoice_number', 'غير محدد')
            transaction_type = invoice_data.get('transaction_type', 'معاملة مالية')
            amount = invoice_data.get('amount', 0)

            # الحصول على بيانات إضافية من pending_invoices.json
            wallet_number = invoice_data.get('wallet_number', 'غير محدد')
            user_name = invoice_data.get('user_name', 'غير محدد')
            previous_balance = invoice_data.get('previous_balance', 0)
            new_balance = invoice_data.get('new_balance', 0)
            usd_value = invoice_data.get('usd_value', amount * 3)  # 1 إكسا = 3 دولار
            date = invoice_data.get('date', 'غير محدد')

            # حفظ الفاتورة في النظام التفاعلي
            from shared.invoices.interactive_invoice_manager import interactive_invoice_manager
            pdf_path = invoice_data.get('pdf_path')

            if pdf_path and os.path.exists(pdf_path):
                # حفظ الفاتورة للوصول إليها عبر الأزرار
                interactive_invoice_manager.save_interactive_invoice(
                    invoice_number=invoice_number,
                    pdf_path=pdf_path,
                    user_id=user_id,
                    transaction_type=transaction_type,
                    amount=amount
                )

                # إنشاء الأزرار التفاعلية
                from telegram import InlineKeyboardButton, InlineKeyboardMarkup
                keyboard = [
                    [
                        InlineKeyboardButton("👁️ عرض الفاتورة", callback_data=f"view_invoice_{invoice_number}"),
                        InlineKeyboardButton("🖨️ طباعة الفاتورة", callback_data=f"print_invoice_{invoice_number}")
                    ]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)

                # تحديد النص المراد إرساله
                if include_message_text and message_text:
                    # استخدام النص المُمرر مع إضافة الأزرار
                    notification_text = message_text
                else:
                    # إنشاء نص الفاتورة باستخدام النظام الموحد
                    if invoice_message_manager:
                        # إعداد البيانات للنظام الموحد
                        invoice_data_for_message = {
                            'transaction_type': transaction_type,
                            'invoice_number': invoice_number,
                            'user_name': user_name,
                            'wallet_number': wallet_number,
                            'amount': amount,
                            'previous_balance': previous_balance,
                            'new_balance': new_balance,
                            'date': date
                        }
                        notification_text = invoice_message_manager.get_invoice_message_by_type(invoice_data_for_message)
                    else:
                        # النظام القديم كاحتياطي
                        # حساب قيمة التوكن (بالآلاف)
                        token_amount = usd_value / 3 * 524.288  # تحويل الدولار إلى توكن

                        # تنسيق قيمة التوكن
                        if token_amount >= 1000:  # مليون أو أكثر
                            token_text = f"{token_amount/1000:.2f} مليون توكين"
                        else:
                            token_text = f"{token_amount:.2f} ألف توكين"

                        notification_text = (
                            f"🧾︙فاتورة إضافة رصيد إلكترونية\n"
                            f"🔢︙رقم الفاتورة︙{invoice_number}\n\n"
                            f"👤︙العميل︙{user_name}\n"
                            f"🏦︙رقم المحفظة︙{wallet_number}\n\n"
                            f"💰︙المبلغ المضاف︙{amount:.0f} إكسا\n"
                            f"📊︙الرصيد السابق︙{previous_balance:.0f} إكسا\n"
                            f"💳︙الرصيد الجديد︙{new_balance:.0f} إكسا\n\n"
                            f"💵︙القيمة بالدولار︙{usd_value:.0f} USD\n"
                            f"🪙︙القيمة بالتوكين︙{token_text}\n\n"
                            f"📅︙تاريخ العملية︙{date}\n"
                            f"🏢︙مقدم الخدمة︙نظام المحفظة الإلكترونية\n\n"
                            f"✅︙تم إضافة الرصيد بنجاح - شكراً لك على استخدام خدماتنا"
                        )

                await self.application.bot.send_message(
                    chat_id=user_id,
                    text=notification_text,
                    reply_markup=reply_markup
                )

                logger.info(f"✅ تم إرسال إشعار فاتورة تفاعلية للمستخدم {user_id}")
            else:
                logger.warning(f"⚠️ ملف PDF غير موجود للمستخدم {user_id}: {pdf_path}")

        except Exception as e:
            logger.error(f"❌ خطأ في إرسال إشعار الفاتورة للمستخدم {user_id}: {e}")

    async def safe_query_answer(self, query, text: str, show_alert: bool = False) -> bool:
        """الرد على الاستعلام بأمان مع معالجة الأخطاء"""
        try:
            await query.answer(text, show_alert=show_alert)
            return True
        except Exception as e:
            if "too old" in str(e).lower() or "invalid" in str(e).lower():
                logger.warning(f"⚠️ استعلام قديم أو منتهي الصلاحية: {text}")
                return False
            else:
                logger.error(f"❌ خطأ في الرد على الاستعلام: {e}")
                return False

    async def safe_message_send(self, chat_id, text: str, reply_to_message_id=None):
        """إرسال رسالة بأمان مع معالجة الأخطاء"""
        try:
            if reply_to_message_id:
                return await self.application.bot.send_message(
                    chat_id=chat_id,
                    text=text,
                    reply_to_message_id=reply_to_message_id
                )
            else:
                return await self.application.bot.send_message(chat_id=chat_id, text=text)
        except Exception as e:
            logger.error(f"❌ خطأ في إرسال الرسالة: {e}")
            return None

    async def safe_message_edit(self, message, text: str):
        """تعديل رسالة بأمان مع معالجة الأخطاء"""
        try:
            await message.edit_text(text)
            return True
        except Exception as e:
            if "not found" in str(e).lower() or "deleted" in str(e).lower():
                logger.warning(f"⚠️ الرسالة محذوفة أو غير موجودة: {text}")
                return False
            else:
                logger.error(f"❌ خطأ في تعديل الرسالة: {e}")
                return False

    async def safe_message_delete(self, message):
        """حذف رسالة بأمان مع معالجة الأخطاء"""
        try:
            await message.delete()
            return True
        except Exception as e:
            logger.warning(f"⚠️ لا يمكن حذف الرسالة: {e}")
            return False

    async def handle_view_invoice(self, query, context, invoice_number: str):
        """معالجة زر عرض الفاتورة - تحويل PDF إلى صورة وإرسالها"""
        try:
            # التحقق من صحة الاستعلام أولاً
            if not await self.safe_query_answer(query, "🔄 جاري تحضير عرض الفاتورة..."):
                return

            # الحصول على بيانات الفاتورة
            from shared.invoices.interactive_invoice_manager import interactive_invoice_manager
            invoice_data = interactive_invoice_manager.get_interactive_invoice(invoice_number)

            if not invoice_data:
                await self.safe_message_send(query.message.chat_id, "❌ الفاتورة غير موجودة أو منتهية الصلاحية")
                return

            # إرسال رسالة تحميل
            loading_message = await self.safe_message_send(query.message.chat_id, "🔄 جاري تحضير عرض الفاتورة...")

            # التحقق من وجود الصورة أولاً
            from shared.invoices.pdf_to_image import pdf_converter
            image_exists, existing_image_path = pdf_converter.check_image_exists(invoice_data['pdf_path'])

            if image_exists and existing_image_path:
                # استخدام الصورة الموجودة
                image_path = existing_image_path
                logger.info(f"✅ استخدام صورة موجودة للفاتورة {invoice_number}")
            else:
                # إنشاء صورة جديدة
                success, image_path, error_msg = pdf_converter.convert_pdf_to_high_quality_image(invoice_data['pdf_path'])
                if not success or not image_path:
                    error_text = f"❌ فشل في تحضير عرض الفاتورة: {error_msg}"
                    if loading_message:
                        if not await self.safe_message_edit(loading_message, error_text):
                            await self.safe_message_send(query.message.chat_id, error_text)
                    else:
                        await self.safe_message_send(query.message.chat_id, error_text)
                    return
                logger.info(f"✅ تم إنشاء صورة جديدة للفاتورة {invoice_number}")

            try:
                # إرسال الصورة مع إعدادات timeout محسنة
                import asyncio
                from telegram.error import TimedOut, NetworkError, BadRequest

                # تنسيق المبلغ خارج f-string
                amount_display = f"{invoice_data['amount']:.0f}" if invoice_data['amount'] == int(invoice_data['amount']) else f"{invoice_data['amount']:.2f}"

                with open(image_path, 'rb') as image_file:
                    # محاولة إرسال الصورة مع timeout وإعادة المحاولة
                    max_retries = 3
                    for attempt in range(max_retries):
                        try:
                            await asyncio.wait_for(
                                self.application.bot.send_photo(
                                    chat_id=query.message.chat_id,
                                    photo=image_file,
                                    caption=f"👁️︙عرض الفاتورة\n"
                                           f"🔢︙رقم الفاتورة: {invoice_number}\n"
                                           f"💰︙{invoice_data['transaction_type']}: {amount_display} إكسا",
                                    read_timeout=60,    # زيادة timeout للقراءة
                                    write_timeout=60,   # زيادة timeout للكتابة
                                    connect_timeout=30  # timeout للاتصال
                                ),
                                timeout=90  # مهلة إجمالية 90 ثانية
                            )
                            break  # نجح الإرسال، اخرج من الحلقة

                        except (TimedOut, asyncio.TimeoutError) as e:
                            if attempt < max_retries - 1:
                                logger.warning(f"انتهت مهلة إرسال صورة الفاتورة {invoice_number} - المحاولة {attempt + 1}/{max_retries}")
                                await asyncio.sleep(2)  # انتظار قبل إعادة المحاولة
                                image_file.seek(0)  # إعادة تعيين مؤشر الملف
                                continue
                            else:
                                raise  # فشل في جميع المحاولات

                        except (NetworkError, BadRequest) as e:
                            if attempt < max_retries - 1:
                                logger.warning(f"خطأ في الشبكة لصورة الفاتورة {invoice_number} - المحاولة {attempt + 1}/{max_retries}: {e}")
                                await asyncio.sleep(1)
                                image_file.seek(0)
                                continue
                            else:
                                raise

                # لا نحذف الصورة - نتركها للاستخدام المستقبلي

                # حذف رسالة التحميل بأمان
                if loading_message:
                    await self.safe_message_delete(loading_message)

                logger.info(f"✅ تم عرض فاتورة {invoice_number} كصورة")

            except (TimedOut, asyncio.TimeoutError) as e:
                error_text = "⏰ انتهت مهلة إرسال صورة الفاتورة\n\nيرجى المحاولة مرة أخرى لاحقاً"
                logger.error(f"❌ انتهت مهلة إرسال صورة الفاتورة {invoice_number}: {e}")

                if loading_message:
                    if not await self.safe_message_edit(loading_message, error_text):
                        await self.safe_message_send(query.message.chat_id, error_text)
                else:
                    await self.safe_message_send(query.message.chat_id, error_text)

            except (NetworkError, BadRequest) as e:
                error_text = "🌐 خطأ في الشبكة أثناء إرسال صورة الفاتورة\n\nيرجى التحقق من الاتصال والمحاولة مرة أخرى"
                logger.error(f"❌ خطأ في الشبكة لصورة الفاتورة {invoice_number}: {e}")

                if loading_message:
                    if not await self.safe_message_edit(loading_message, error_text):
                        await self.safe_message_send(query.message.chat_id, error_text)
                else:
                    await self.safe_message_send(query.message.chat_id, error_text)

            except Exception as e:
                error_text = "❌ فشل في عرض الفاتورة\n\nحدث خطأ غير متوقع"
                logger.error(f"❌ خطأ غير متوقع في إرسال صورة الفاتورة {invoice_number}: {e}")

                if loading_message:
                    if not await self.safe_message_edit(loading_message, error_text):
                        await self.safe_message_send(query.message.chat_id, error_text)
                else:
                    await self.safe_message_send(query.message.chat_id, error_text)

        except Exception as e:
            logger.error(f"❌ خطأ في معالجة عرض الفاتورة {invoice_number}: {e}")
            await self.safe_message_send(query.message.chat_id, "❌ حدث خطأ في معالجة الطلب")

    async def handle_print_invoice(self, query, context, invoice_number: str):
        """معالجة زر طباعة الفاتورة - إرسال ملف PDF"""
        try:
            # التحقق من صحة الاستعلام أولاً
            if not await self.safe_query_answer(query, "📤 جاري إرسال ملف الفاتورة للطباعة..."):
                return

            # الحصول على بيانات الفاتورة
            from shared.invoices.interactive_invoice_manager import interactive_invoice_manager
            invoice_data = interactive_invoice_manager.get_interactive_invoice(invoice_number)

            if not invoice_data:
                await self.safe_message_send(query.message.chat_id, "❌ الفاتورة غير موجودة أو منتهية الصلاحية")
                return

            # إرسال رسالة تحميل
            loading_message = await self.safe_message_send(query.message.chat_id, "📤 جاري إرسال ملف الفاتورة للطباعة...")

            # إرسال ملف PDF
            pdf_path = invoice_data['pdf_path']
            if os.path.exists(pdf_path):
                try:
                    # تنسيق المبلغ خارج f-string
                    amount_display = f"{invoice_data['amount']:.0f}" if invoice_data['amount'] == int(invoice_data['amount']) else f"{invoice_data['amount']:.2f}"

                    with open(pdf_path, 'rb') as pdf_file:
                        await self.application.bot.send_document(
                            chat_id=query.message.chat_id,
                            document=pdf_file,
                            filename=f"فاتورة_{invoice_number}.pdf",
                            caption=f"🖨️︙فاتورة للطباعة\n"
                                   f"🔢︙رقم الفاتورة: {invoice_number}\n"
                                   f"💰︙{invoice_data['transaction_type']}: {amount_display} إكسا\n\n"
                                   f"يمكنك طباعة هذا الملف مباشرة"
                        )

                    # حذف رسالة التحميل بأمان
                    if loading_message:
                        try:
                            await loading_message.delete()
                        except:
                            logger.warning(f"⚠️ لا يمكن حذف رسالة التحميل للفاتورة {invoice_number}")

                    logger.info(f"✅ تم إرسال فاتورة {invoice_number} للطباعة")

                except Exception as e:
                    error_text = "❌ فشل في إرسال ملف الفاتورة"
                    logger.error(f"❌ خطأ في إرسال ملف الفاتورة: {e}")

                    if loading_message:
                        try:
                            await loading_message.edit_text(error_text)
                        except:
                            try:
                                await query.message.reply_text(error_text)
                            except:
                                logger.error(f"❌ لا يمكن إرسال رسالة الخطأ للفاتورة {invoice_number}")
            else:
                error_text = "❌ ملف الفاتورة غير موجود"
                if loading_message:
                    try:
                        await loading_message.edit_text(error_text)
                    except:
                        try:
                            await query.message.reply_text(error_text)
                        except:
                            logger.error(f"❌ لا يمكن إرسال رسالة الخطأ للفاتورة {invoice_number}")

        except Exception as e:
            logger.error(f"❌ خطأ في معالجة طباعة الفاتورة {invoice_number}: {e}")
            try:
                await query.message.reply_text("❌ حدث خطأ في معالجة الطلب")
            except:
                logger.warning(f"⚠️ لا يمكن إرسال رسالة الخطأ العامة للفاتورة {invoice_number}")

    async def periodic_invoice_check(self):
        """فحص دوري للفواتير المعلقة"""
        import asyncio
        while True:
            try:
                await asyncio.sleep(5)  # فحص كل 5 ثوان
                await self.check_pending_invoices()

                # تنظيف الفواتير منتهية الصلاحية كل ساعة
                if hasattr(self, '_last_cleanup_time'):
                    if (datetime.now() - self._last_cleanup_time).total_seconds() > 3600:  # ساعة واحدة
                        await self._cleanup_expired_invoices()
                else:
                    self._last_cleanup_time = datetime.now()

            except Exception as e:
                logger.error(f"❌ خطأ في الفحص الدوري للفواتير: {e}")
                await asyncio.sleep(10)  # انتظار أطول في حالة الخطأ

    async def _cleanup_expired_invoices(self):
        """تنظيف الفواتير والصور منتهية الصلاحية"""
        try:
            from shared.invoices.interactive_invoice_manager import interactive_invoice_manager
            from shared.invoices.pdf_to_image import pdf_converter

            # تنظيف الفواتير منتهية الصلاحية
            deleted_invoices = interactive_invoice_manager.cleanup_expired_invoices()

            # تنظيف الصور المؤقتة
            deleted_images = pdf_converter.cleanup_all_temp_images()

            if deleted_invoices > 0 or deleted_images > 0:
                logger.info(f"🧹 تنظيف دوري: حذف {deleted_invoices} فاتورة و {deleted_images} صورة مؤقتة")

            self._last_cleanup_time = datetime.now()

        except Exception as e:
            logger.error(f"❌ خطأ في التنظيف الدوري: {e}")

    def run(self):
        """تشغيل البوت"""
        print("🤖 البوت الرئيسي يعمل الآن...")
        print("⚙️ جاري إعداد قائمة الأوامر...")

        # إعداد قائمة الأوامر عند بدء التشغيل
        async def post_init(application):
            await self.setup_bot_commands()
            # بدء مهمة فحص الفواتير المعلقة
            asyncio.create_task(self.periodic_invoice_check())
            # بدء مهمة فحص الرسائل المعلقة
            asyncio.create_task(self.periodic_message_check())
            # بدء مهمة فحص الفواتير المعلقة للمستخدمين
            asyncio.create_task(self.periodic_user_invoice_check())
            # بدء مهمة تنظيف إشعارات المراقبة القديمة
            asyncio.create_task(self.periodic_monitoring_cleanup())

        self.application.post_init = post_init

        print("✅ البوت الرئيسي جاهز للاستخدام!")
        print("💡 البوت مخصص للمستخدمين العاديين فقط")
        print("🛑 اضغط Ctrl+C لإيقاف البوت")

        # تشغيل البوت مع إعادة المحاولة
        self._run_with_retry()

    def _run_with_retry(self):
        """تشغيل البوت مع إعادة المحاولة في حالة فشل الاتصال"""
        max_retries = 3
        retry_delay = 5

        for attempt in range(max_retries):
            try:
                print(f"🔄 محاولة الاتصال {attempt + 1}/{max_retries}...")
                self.application.run_polling(
                    allowed_updates=Update.ALL_TYPES,
                    drop_pending_updates=True  # تجاهل التحديثات المعلقة
                )
                break  # نجح الاتصال

            except Exception as e:
                error_str = str(e)
                if any(keyword in error_str for keyword in ["ConnectTimeout", "TimedOut", "SSLWantReadError", "ssl.SSLWantReadError", "httpcore.ConnectTimeout", "httpx.ConnectTimeout"]):
                    if attempt < max_retries - 1:
                        print(f"❌ فشل الاتصال: {type(e).__name__}")
                        print(f"⏳ إعادة المحاولة خلال {retry_delay} ثانية...")
                        import time
                        time.sleep(retry_delay)
                        retry_delay = min(retry_delay * 2, 60)  # زيادة وقت الانتظار مع حد أقصى
                    else:
                        print(f"❌ فشل في الاتصال بعد {max_retries} محاولات")
                        print("💡 نصائح لحل المشكلة:")
                        print("   1. تحقق من اتصال الإنترنت")
                        print("   2. جرب استخدام VPN")
                        print("   3. تحقق من إعدادات الجدار الناري")
                        print("   4. تأكد من صحة رمز البوت")
                        print("   5. جرب إعادة تشغيل الراوتر")
                        raise
                else:
                    # خطأ آخر غير متعلق بالشبكة
                    print(f"❌ خطأ غير متوقع: {e}")
                    raise

    async def show_my_wallet(self, query, context):
        """عرض محفظة المستخدم"""
        try:
            user = query.from_user

            # الحصول على معلومات المحفظة
            wallet_info = self.user_manager.get_user_wallet_info(user.id)

            if wallet_info:
                # تحديد حالة المحفظة
                status_emoji = "✅" if wallet_info['status'] == 'active' else "❌"
                status_text = "نشط" if wallet_info['status'] == 'active' else "خامل"

                # الحصول على التاريخ والوقت الحالي
                from datetime import datetime
                now = datetime.now()
                current_day = now.strftime('%A')
                current_date = now.strftime('%Y-%m-%d')
                current_time = now.strftime('%H:%M:%S')

                # ترجمة أيام الأسبوع للعربية
                day_names = {
                    'Monday': 'الاثنين',
                    'Tuesday': 'الثلاثاء',
                    'Wednesday': 'الأربعاء',
                    'Thursday': 'الخميس',
                    'Friday': 'الجمعة',
                    'Saturday': 'السبت',
                    'Sunday': 'الأحد'
                }
                arabic_day = day_names.get(current_day, 'غير محدد')

                # تنسيق الأرقام بدون عشري إذا كانت صحيحة
                balance = wallet_info['balance']
                balance_display = f"{balance:.0f}" if balance == int(balance) else f"{balance:.2f}"

                # معلومات الدين
                debt_amount = wallet_info.get('debt_amount', 0.0)
                has_debt = wallet_info.get('has_debt', False)
                debt_display = f"{debt_amount:.3f}" if debt_amount != int(debt_amount) else f"{int(debt_amount)}"

                # حساب التوكين
                token_value = balance * 524288

                # تنسيق التوكين حسب العدد
                if token_value >= 1000000:
                    token_display = f"{token_value/1000000:.0f}" if (token_value/1000000) == int(token_value/1000000) else f"{token_value/1000000:.2f}"
                    token_unit = "مليون توكين"
                elif token_value >= 1000:
                    token_display = f"{token_value/1000:.0f}" if (token_value/1000) == int(token_value/1000) else f"{token_value/1000:.2f}"
                    token_unit = "الف توكين"
                else:
                    token_display = f"{token_value:.0f}" if token_value == int(token_value) else f"{token_value:.2f}"
                    token_unit = "توكين"

                # إعداد نص الدين
                debt_text = ""
                if has_debt:
                    debt_text = f"\n⚠️︙الدين : {debt_display} إكسا"

                # عرض معلومات المحفظة الحقيقية
                message = f"""💰︙محفظتي︙

💳︙معلومات المحفظة︙
🔢︙رقم المحفظة︙{wallet_info['wallet_number']}
💎︙الرصيد الحالي︙{balance_display} إكسا{debt_text}
🪙︙التوكين الحالي : {token_display} {token_unit}
📊︙الحالة : {status_text} {status_emoji}
✅︙التحقق : {'مُفعل ✅' if wallet_info['is_verified'] else 'غير مُفعل'}
📅︙اليوم : {arabic_day}
📅︙التاريخ : {current_date}
⏰︙الوقت : {current_time}

💡︙خدمات إكسا الذكي ︙
🤖︙• إكسا الذكي العادي : مجاني
🧠︙• إكسا الذكي برو : مدفوع (حسب الاستهلاك)

🌟︙تم إنشاء محفظتك تلقائياً عند دخولك للبوت لأول مرة"""
            else:
                # في حالة عدم وجود محفظة (حالة نادرة)
                message = """💰︙محفظتي

⚠️︙لم يتم العثور على محفظة︙

🔄︙جاري إنشاء محفظة جديدة...

💡︙سيتم إنشاء محفظتك تلقائياً
يرجى المحاولة مرة أخرى خلال لحظات"""

                # محاولة إنشاء محفظة للمستخدم الموجود
                success, wallet_number = self.user_manager.create_wallet_for_existing_user(user.id)
                if success:
                    message += f"\n\n✅︙تم إنشاء المحفظة بنجاح︙\n🔢︙رقم المحفظة︙<code>{wallet_number}</code>"

            # استخدام الأزرار السفلية لخيارات المحفظة
            reply_markup = self.get_wallet_keyboard()

            try:
                # محاولة تعديل الرسالة أولاً
                await query.edit_message_text(
                    message,
                    parse_mode=ParseMode.HTML,
                    reply_markup=reply_markup
                )
            except Exception as edit_error:
                # إذا فشل التعديل، إرسال رسالة جديدة
                await query.message.reply_text(
                    message,
                    parse_mode=ParseMode.HTML,
                    reply_markup=reply_markup
                )
                # حذف الرسالة الأصلية إذا أمكن
                try:
                    await query.message.delete()
                except:
                    pass

        except Exception as e:
            logger.error(f"خطأ في عرض المحفظة: {e}")
            try:
                await query.edit_message_text(
                    "❌ حدث خطأ في عرض المحفظة",
                    reply_markup=self.get_main_keyboard()
                )
            except:
                await query.message.reply_text(
                    "❌ حدث خطأ في عرض المحفظة",
                    reply_markup=self.get_reply_keyboard()
                )

    async def show_my_wallet_from_text(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض محفظة المستخدم من الزر النصي"""
        try:
            user = update.effective_user

            # الحصول على معلومات المحفظة
            wallet_info = self.user_manager.get_user_wallet_info(user.id)

            if wallet_info:
                # تحديد حالة المحفظة
                status_emoji = "✅" if wallet_info['status'] == 'active' else "❌"
                status_text = "نشط" if wallet_info['status'] == 'active' else "خامل"

                # الحصول على التاريخ والوقت الحالي
                from datetime import datetime
                now = datetime.now()
                current_day = now.strftime('%A')
                current_date = now.strftime('%Y-%m-%d')
                current_time = now.strftime('%H:%M:%S')

                # ترجمة أيام الأسبوع للعربية
                day_names = {
                    'Monday': 'الاثنين',
                    'Tuesday': 'الثلاثاء',
                    'Wednesday': 'الأربعاء',
                    'Thursday': 'الخميس',
                    'Friday': 'الجمعة',
                    'Saturday': 'السبت',
                    'Sunday': 'الأحد'
                }
                arabic_day = day_names.get(current_day, 'غير محدد')

                # تنسيق الأرقام بدون عشري إذا كانت صحيحة
                balance = wallet_info['balance']
                balance_display = f"{balance:.0f}" if balance == int(balance) else f"{balance:.2f}"

                # حساب التوكين
                token_value = balance * 524288

                # تنسيق التوكين حسب العدد
                if token_value >= 1000000:
                    token_display = f"{token_value/1000000:.0f}" if (token_value/1000000) == int(token_value/1000000) else f"{token_value/1000000:.2f}"
                    token_unit = "مليون توكين"
                elif token_value >= 1000:
                    token_display = f"{token_value/1000:.0f}" if (token_value/1000) == int(token_value/1000) else f"{token_value/1000:.2f}"
                    token_unit = "الف توكين"
                else:
                    token_display = f"{token_value:.0f}" if token_value == int(token_value) else f"{token_value:.2f}"
                    token_unit = "توكين"

                # عرض معلومات المحفظة الحقيقية
                message = f"""💰︙محفظتي︙

💳︙معلومات المحفظة︙
🔢︙رقم المحفظة︙{wallet_info['wallet_number']}
💎︙الرصيد الحالي︙{balance_display} إكسا
🪙︙التوكين الحالي : {token_display} {token_unit}
📊︙الحالة : {status_text} {status_emoji}
✅︙التحقق : {'مُفعل ✅' if wallet_info['is_verified'] else 'غير مُفعل'}
📅︙اليوم : {arabic_day}
📅︙التاريخ : {current_date}
⏰︙الوقت : {current_time}

💡︙خدمات إكسا الذكي ︙
🤖︙• إكسا الذكي العادي : مجاني
🧠︙• إكسا الذكي برو : مدفوع

🌟︙تم إنشاء محفظتك تلقائياً عند دخولك للبوت لأول مرة"""
            else:
                # في حالة عدم وجود محفظة (حالة نادرة)
                message = """💰︙محفظتي

⚠️︙لم يتم العثور على محفظة︙

🔄︙جاري إنشاء محفظة جديدة...

💡︙سيتم إنشاء محفظتك تلقائياً
يرجى المحاولة مرة أخرى خلال لحظات"""

                # محاولة إنشاء محفظة للمستخدم الموجود
                success, wallet_number = self.user_manager.create_wallet_for_existing_user(user.id)
                if success:
                    message += f"\n\n✅︙تم إنشاء المحفظة بنجاح︙\n🔢︙رقم المحفظة︙<code>{wallet_number}</code>"

            # استخدام الأزرار السفلية لخيارات المحفظة بدلاً من الأزرار المضمنة
            await update.message.reply_text(
                message,
                parse_mode=ParseMode.HTML,
                reply_markup=self.get_wallet_keyboard()
            )

        except Exception as e:
            logger.error(f"خطأ في عرض المحفظة من النص: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في عرض المحفظة",
                reply_markup=self.get_reply_keyboard()
            )

    async def show_my_balance_from_text(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض رصيد المستخدم من الزر النصي"""
        try:
            user = update.effective_user

            # الحصول على معلومات المحفظة
            wallet_info = self.user_manager.wallet_manager.get_user_wallet(user.id)

            if wallet_info:
                # الحصول على التاريخ والوقت الحالي
                from datetime import datetime
                now = datetime.now()
                current_day = now.strftime('%A')
                current_date = now.strftime('%Y-%m-%d')
                current_time = now.strftime('%H:%M:%S')

                # ترجمة أيام الأسبوع للعربية
                day_names = {
                    'Monday': 'الاثنين',
                    'Tuesday': 'الثلاثاء',
                    'Wednesday': 'الأربعاء',
                    'Thursday': 'الخميس',
                    'Friday': 'الجمعة',
                    'Saturday': 'السبت',
                    'Sunday': 'الأحد'
                }
                arabic_day = day_names.get(current_day, 'غير محدد')

                # الحصول على بيانات المحفظة للإحصائيات
                wallet_data = self.user_manager.wallet_manager.get_user_wallet(user.id)
                transaction_count = wallet_data.get('transaction_count', 0) if wallet_data else 0

                # حساب الرصيد المحجوز (افتراضي 0 حالياً)
                reserved_balance = 0.0
                available_balance = wallet_info['balance'] - reserved_balance

                # تنسيق الأرقام بدون عشري إذا كانت صحيحة
                balance = wallet_info['balance']
                balance_display = f"{balance:.0f}" if balance == int(balance) else f"{balance:.2f}"

                # حساب التوكين
                token_value = balance * 524288

                # تنسيق التوكين حسب العدد
                if token_value >= 1000000:
                    token_display = f"{token_value/1000000:.0f}" if (token_value/1000000) == int(token_value/1000000) else f"{token_value/1000000:.2f}"
                    token_unit = "مليون توكين"
                elif token_value >= 1000:
                    token_display = f"{token_value/1000:.0f}" if (token_value/1000) == int(token_value/1000) else f"{token_value/1000:.2f}"
                    token_unit = "الف توكين"
                else:
                    token_display = f"{token_value:.0f}" if token_value == int(token_value) else f"{token_value:.2f}"
                    token_unit = "توكين"

                # تحديد حالة المحفظة
                status_text = "نشط" if wallet_info.get('status', 'active') == 'active' else "خامل"
                status_emoji = "✅" if wallet_info.get('status', 'active') == 'active' else "❌"

                # تحديد حالة التحقق
                verification_status = wallet_data.get('is_verified', False) if wallet_data else False
                verification_text = "مُفعل ✅" if verification_status else "غير مُفعل"

                message = f"""💰︙محفظتي︙

💳︙معلومات المحفظة︙
🔢︙رقم المحفظة︙{wallet_info['wallet_number']}
💎︙الرصيد الحالي︙{balance_display} إكسا
🪙︙التوكين الحالي︙{token_display} {token_unit}
📊︙الحالة︙{status_text} {status_emoji}
✅︙التحقق︙{verification_text}
📅︙اليوم︙{arabic_day}
📅︙التاريخ︙{current_date}
⏰︙الوقت︙{current_time}

💡︙خدمات إكسا الذكي ︙
🤖︙• إكسا الذكي العادي︙مجاني
🧠︙• إكسا الذكي برو︙مدفوع

🌟︙تم إنشاء محفظتك تلقائياً عند دخولك للبوت لأول مرة"""

                await update.message.reply_text(
                    message,
                    parse_mode=ParseMode.HTML,
                    reply_markup=self.get_wallet_keyboard()
                )
            else:
                await update.message.reply_text(
                    "❌ لم يتم العثور على محفظة. يرجى إنشاء محفظة أولاً.",
                    reply_markup=self.get_wallet_keyboard()
                )

        except Exception as e:
            logger.error(f"خطأ في عرض الرصيد من النص: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في عرض الرصيد",
                reply_markup=self.get_wallet_keyboard()
            )

    async def add_balance_from_text(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """إضافة رصيد من الزر النصي"""
        try:
            message = """➕︙إضافة رصيد

💎︙سعر العملة ︙ 1 إكسا = 3 دولار
💎︙مقابلها توكين︙1 إكسا = 524,288 الف توكين

💰︙الطرق المتاحة :
• تحويل بنكي
• بطاقة ائتمان
• محفظة إلكترونية
• عملات رقمية

💰︙التحويل والايداع :
👤︙الاسم : صلاح الدين منصور يعد الدروبي
📞︙الهاتف : 772934757

💡︙ملاحظة مهمة : يرجى التواصل مع الإدارة لإتمام عملية إضافة الرصيد"""

            # إنشاء أزرار مضمنة للتواصل مع الإدارة ونسخ النموذج
            from telegram import InlineKeyboardButton, InlineKeyboardMarkup

            keyboard = [
                [InlineKeyboardButton("📋 نموذج إضافة رصيد", callback_data="copy_balance_form")],
                [InlineKeyboardButton("📞 تواصل مع الإدارة", url="https://t.me/Salahadoroobi")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                message,
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"خطأ في إضافة الرصيد من النص: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في عرض طرق إضافة الرصيد",
                reply_markup=self.get_wallet_keyboard()
            )

    async def show_transactions_from_text(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض المعاملات من الزر النصي"""
        try:
            message = """📊︙عرض المعاملات

💳︙سجل المعاملات︙

📈︙المعاملات الأخيرة︙
• لا توجد معاملات حتى الآن

📊︙إحصائيات︙
• إجمالي المعاملات︙0
• إجمالي المبلغ︙0 إكسا
• آخر معاملة︙لا توجد

💡︙ملاحظة︙
يرجى المحاولة مرة أخرى خلال لحظات"""

            await update.message.reply_text(
                message,
                reply_markup=self.get_wallet_keyboard()
            )

        except Exception as e:
            logger.error(f"خطأ في عرض المعاملات من النص: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في عرض المعاملات",
                reply_markup=self.get_wallet_keyboard()
            )

    async def request_loan_from_text(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """طلب سلفة من الزر النصي"""
        try:
            user = update.effective_user

            # فحص أهلية المستخدم للسلف
            from shared.database.wallet_manager import WalletManager
            wallet_manager = WalletManager()

            eligible, eligibility_message = wallet_manager.is_eligible_for_loan(user.id)

            if eligible:
                message = """💳︙خدمة سلفني

🌟︙مرحباً بك في خدمة سلفني︙

💰︙تفاصيل الخدمة︙
• مبلغ السلفة︙3 إكسا
• قيمة السلفة︙9 دولار أمريكي
• مدة السداد︙عند إضافة رصيد جديد

📋︙شروط الخدمة︙
✅ محفظتك مُحققة ومؤهلة
✅ لا يوجد سلف سابق غير مسدد
✅ رصيدك الحالي 0 إكسا

⚡︙طريقة العمل︙
• نقوم بإضافة 3 إكسا لمحفظتك فوراً
• عند إضافة رصيد جديد يتم خصم السلفة تلقائياً
• تحصل على فاتورة تفصيلية لكل عملية

💡︙ملاحظات مهمة︙
• يتم خصم السلفة من أول رصيد تضيفه
• إذا كان الرصيد المضاف أقل من السلفة، يتم خصم المتاح
• الباقي من السلفة يُخصم من الإضافات التالية

🔄︙للحصول على السلفة︙
اضغط على زر "💳 طلب سلفة" أدناه"""

                keyboard = [
                    [InlineKeyboardButton("💳 طلب سلفة", callback_data="request_loan")],
                    [InlineKeyboardButton("📈 عمليات السلفة", callback_data="loan_history")],
                    [InlineKeyboardButton("📊 حالة السلفة", callback_data="loan_status")],
                    [InlineKeyboardButton("🔙 العودة للمحفظة", callback_data="my_wallet")]
                ]
            else:
                message = f"""💳︙خدمة سلفني

❌︙غير مؤهل للحصول على سلفة︙

🚫︙السبب︙
{eligibility_message}

📋︙شروط الحصول على السلفة︙
• يجب أن تكون محفظتك مُحققة (لديك معاملات سابقة)
• الرصيد الحالي يجب أن يكون 0 إكسا
• لا يوجد سلف سابق غير مسدد

💡︙كيفية تحقيق المحفظة︙
• قم بإضافة رصيد لمحفظتك لأول مرة
• استخدم الخدمات المدفوعة
• بعد ذلك ستصبح محفظتك مُحققة

🔄︙للمحاولة مرة أخرى︙
عد إلى هذه الصفحة بعد تحقيق الشروط"""

                keyboard = [
                    [InlineKeyboardButton("📈 عمليات السلفة", callback_data="loan_history")],
                    [InlineKeyboardButton("📊 حالة السلفة", callback_data="loan_status")],
                    [InlineKeyboardButton("💰 إضافة رصيد", callback_data="add_balance")],
                    [InlineKeyboardButton("🔙 العودة للمحفظة", callback_data="my_wallet")]
                ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                message,
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"خطأ في طلب السلفة من النص: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في عرض خدمة سلفني",
                reply_markup=self.get_wallet_keyboard()
            )

    async def show_usage_report_from_text(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض تقرير الاستخدام من الزر النصي"""
        try:
            from datetime import datetime

            message = f"""📈︙تقرير الاستخدام

👤︙معلومات المستخدم︙
• الاسم︙{update.effective_user.full_name}
• المعرف︙@{update.effective_user.username or 'غير محدد'}
• تاريخ التسجيل︙{datetime.now().strftime('%Y-%m-%d')}

💰︙إحصائيات المحفظة︙
• عدد المعاملات︙0
• إجمالي الإنفاق︙0 إكسا
• متوسط المعاملة︙0 إكسا

📊︙إحصائيات النشاط︙
• عدد الزيارات︙1
• آخر نشاط︙{datetime.now().strftime('%Y-%m-%d %H:%M')}
• الأوامر المستخدمة︙5

🎯︙استخدام الخدمات︙
• إكسا الذكي العادي︙0 مرة
• إكسا الذكي برو︙0 مرة
• خدمات أخرى︙0 مرة

📅︙التاريخ︙{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""

            await update.message.reply_text(
                message,
                reply_markup=self.get_wallet_keyboard()
            )

        except Exception as e:
            logger.error(f"خطأ في عرض تقرير الاستخدام من النص: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في عرض تقرير الاستخدام",
                reply_markup=self.get_wallet_keyboard()
            )

    async def back_to_main_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """العودة للقائمة الرئيسية"""
        try:
            await update.message.reply_text(
                "🔙 تم العودة للقائمة الرئيسية",
                reply_markup=self.get_reply_keyboard()
            )

        except Exception as e:
            logger.error(f"خطأ في العودة للقائمة الرئيسية: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في العودة للقائمة الرئيسية",
                reply_markup=self.get_reply_keyboard()
            )

    async def show_my_balance(self, query, context):
        """عرض رصيد المستخدم"""
        try:
            user = query.from_user

            # الحصول على معلومات المحفظة
            wallet_info = self.user_manager.get_user_wallet_info(user.id)

            if wallet_info:
                # تحديد حالة المحفظة
                status_emoji = "✅" if wallet_info['status'] == 'active' else "❌"
                status_text = "نشط" if wallet_info['status'] == 'active' else "خامل"

                # الحصول على التاريخ والوقت الحالي
                from datetime import datetime
                now = datetime.now()
                current_day = now.strftime('%A')
                current_date = now.strftime('%Y-%m-%d')
                current_time = now.strftime('%H:%M:%S')

                # ترجمة أيام الأسبوع للعربية
                day_names = {
                    'Monday': 'الاثنين',
                    'Tuesday': 'الثلاثاء',
                    'Wednesday': 'الأربعاء',
                    'Thursday': 'الخميس',
                    'Friday': 'الجمعة',
                    'Saturday': 'السبت',
                    'Sunday': 'الأحد'
                }
                arabic_day = day_names.get(current_day, 'غير محدد')

                # الحصول على بيانات المحفظة للإحصائيات
                wallet_data = self.user_manager.wallet_manager.get_user_wallet(user.id)
                transaction_count = wallet_data.get('transaction_count', 0) if wallet_data else 0

                # تنسيق الأرقام بدون عشري إذا كانت صحيحة
                balance = wallet_info['balance']
                balance_display = f"{balance:.0f}" if balance == int(balance) else f"{balance:.2f}"

                # حساب التوكين
                token_value = balance * 524288

                # تنسيق التوكين حسب العدد
                if token_value >= 1000000:
                    token_display = f"{token_value/1000000:.0f}" if (token_value/1000000) == int(token_value/1000000) else f"{token_value/1000000:.2f}"
                    token_unit = "مليون توكين"
                elif token_value >= 1000:
                    token_display = f"{token_value/1000:.0f}" if (token_value/1000) == int(token_value/1000) else f"{token_value/1000:.2f}"
                    token_unit = "الف توكين"
                else:
                    token_display = f"{token_value:.0f}" if token_value == int(token_value) else f"{token_value:.2f}"
                    token_unit = "توكين"

                # تحديد حالة التحقق
                verification_status = wallet_data.get('is_verified', False) if wallet_data else False
                verification_text = "مُفعل ✅" if verification_status else "غير مُفعل"

                message = f"""💰︙محفظتي︙

💳︙معلومات المحفظة︙
🔢︙رقم المحفظة︙{wallet_info['wallet_number']}
💎︙الرصيد الحالي︙{balance_display} إكسا
🪙︙التوكين الحالي : {token_display} {token_unit}
📊︙الحالة : {status_text} {status_emoji}
✅︙التحقق : {verification_text}
📅︙اليوم : {arabic_day}
📅︙التاريخ : {current_date}
⏰︙الوقت : {current_time}

💡︙خدمات إكسا الذكي ︙
🤖︙• إكسا الذكي العادي : مجاني
🧠︙• إكسا الذكي برو : مدفوع

🌟︙تم إنشاء محفظتك تلقائياً عند دخولك للبوت لأول مرة"""
            else:
                message = """💰︙رصيدي

⚠️︙لم يتم العثور على محفظة︙

🔄︙يرجى العودة لقائمة المحفظة الرئيسية
وإنشاء محفظة جديدة"""

            # إنشاء لوحة مفاتيح العودة للمحفظة
            keyboard = [
                [InlineKeyboardButton("🔙 العودة للمحفظة", callback_data="my_wallet")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            try:
                await query.edit_message_text(
                    message,
                    parse_mode=ParseMode.HTML,
                    reply_markup=reply_markup
                )
            except Exception as edit_error:
                await query.message.reply_text(
                    message,
                    parse_mode=ParseMode.HTML,
                    reply_markup=reply_markup
                )
                try:
                    await query.message.delete()
                except:
                    pass

        except Exception as e:
            logger.error(f"خطأ في عرض الرصيد: {e}")
            try:
                await query.edit_message_text(
                    "❌ حدث خطأ في عرض الرصيد",
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 العودة للمحفظة", callback_data="my_wallet")]])
                )
            except:
                await query.message.reply_text(
                    "❌ حدث خطأ في عرض الرصيد",
                    reply_markup=self.get_reply_keyboard()
                )

    async def add_balance(self, query, context):
        """إضافة رصيد للمحفظة"""
        try:
            user = query.from_user

            message = f"""➕︙إضافة رصيد

💎︙سعر العملة ︙ 1 إكسا = 3 دولار
💎︙مقابلها توكين︙1 إكسا = 524,288 الف توكين

💰︙الطرق المتاحة :
• تحويل بنكي
• بطاقة ائتمان
• محفظة إلكترونية
• عملات رقمية

💰︙التحويل والايداع :
👤︙الاسم : صلاح الدين منصور يعد الدروبي
📞︙الهاتف : 772934757

💡︙ملاحظة مهمة : يرجى التواصل مع الإدارة لإتمام عملية إضافة الرصيد"""

            # إنشاء لوحة مفاتيح مع نموذج النسخ والعودة للمحفظة
            keyboard = [
                [InlineKeyboardButton("📋 نموذج إضافة رصيد", callback_data="copy_balance_form")],
                [InlineKeyboardButton("📞 تواصل مع الإدارة", url="https://t.me/Salahadoroobi")],
                [InlineKeyboardButton("🔙 العودة للمحفظة", callback_data="my_wallet")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            try:
                await query.edit_message_text(
                    message,
                    parse_mode=ParseMode.HTML,
                    reply_markup=reply_markup
                )
            except Exception as edit_error:
                await query.message.reply_text(
                    message,
                    parse_mode=ParseMode.HTML,
                    reply_markup=reply_markup
                )
                try:
                    await query.message.delete()
                except:
                    pass

        except Exception as e:
            logger.error(f"خطأ في إضافة الرصيد: {e}")
            try:
                await query.edit_message_text(
                    "❌ حدث خطأ في عرض طرق إضافة الرصيد",
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 العودة للمحفظة", callback_data="my_wallet")]])
                )
            except:
                await query.message.reply_text(
                    "❌ حدث خطأ في عرض طرق إضافة الرصيد",
                    reply_markup=self.get_reply_keyboard()
                )

    async def request_loan(self, query, context):
        """طلب سلفة من المحفظة"""
        try:
            user = query.from_user

            # فحص أهلية المستخدم للسلف
            from shared.database.wallet_manager import WalletManager
            wallet_manager = WalletManager()

            eligible, eligibility_message = wallet_manager.is_eligible_for_loan(user.id)

            if eligible:
                # منح السلفة تلقائياً
                success, message_text, transaction_data = wallet_manager.grant_loan(user.id, 3.0)

                if success:
                    # إنشاء فاتورة السلف
                    await self.create_loan_invoice(user.id, transaction_data)

                    # تنسيق الأرقام بدون عشري
                    amount_display = f"{transaction_data['amount']:.0f}" if transaction_data['amount'] == int(transaction_data['amount']) else f"{transaction_data['amount']:.2f}"
                    old_balance_display = f"{transaction_data['old_balance']:.0f}" if transaction_data['old_balance'] == int(transaction_data['old_balance']) else f"{transaction_data['old_balance']:.2f}"
                    new_balance_display = f"{transaction_data['new_balance']:.0f}" if transaction_data['new_balance'] == int(transaction_data['new_balance']) else f"{transaction_data['new_balance']:.2f}"

                    message = f"""💳︙تم منح السلفة بنجاح︙

✅︙تفاصيل السلفة︙
🔢︙رقم المحفظة : <code>{transaction_data['wallet_number']}</code>
💰︙الرصيد السلفة : {amount_display} إكسا
💎︙الرصيد السابق : {old_balance_display} إكسا
💎︙الرصيد الجديد : {new_balance_display} إكسا
📅︙تاريخ المنح : {transaction_data['timestamp']}

⚡︙طريقة السداد︙
• سيتم خصم السلفة تلقائياً عند إضافة رصيد جديد
• إذا كان الرصيد المضاف أقل من السلفة، يتم خصم المتاح
• الباقي من السلفة يُخصم من الإضافات التالية

💡︙ملاحظات مهمة︙
• تم إصدار فاتورة تفصيلية للسلفة
• يمكنك مراجعة حالة السلفة في أي وقت
• لا يمكن الحصول على سلفة جديدة قبل سداد الحالية

🎉︙استمتع بخدمات البوت!"""

                    keyboard = [
                        [InlineKeyboardButton("📊 حالة السلفة", callback_data="loan_status")],
                        [InlineKeyboardButton("💰 إضافة رصيد", callback_data="add_balance")],
                        [InlineKeyboardButton("🔙 العودة للمحفظة", callback_data="my_wallet")]
                    ]
                else:
                    message = f"""💳︙فشل في منح السلفة︙

❌︙خطأ︙
{message_text}

🔄︙يرجى المحاولة مرة أخرى أو التواصل مع الإدارة"""

                    keyboard = [
                        [InlineKeyboardButton("📞 تواصل مع الإدارة", url="https://t.me/Salahadoroobi")],
                        [InlineKeyboardButton("🔙 العودة للمحفظة", callback_data="my_wallet")]
                    ]
            else:
                message = f"""💳︙غير مؤهل للحصول على سلفة︙

❌︙السبب︙
{eligibility_message}

📋︙شروط الحصول على السلفة︙
• يجب أن تكون محفظتك مُحققة (لديك معاملات سابقة)
• الرصيد الحالي يجب أن يكون 0 إكسا
• لا يوجد سلف سابق غير مسدد

💡︙كيفية تحقيق المحفظة︙
• قم بإضافة رصيد لمحفظتك لأول مرة
• استخدم الخدمات المدفوعة
• بعد ذلك ستصبح محفظتك مُحققة"""

                keyboard = [
                    [InlineKeyboardButton("💰 إضافة رصيد", callback_data="add_balance")],
                    [InlineKeyboardButton("📞 تواصل مع الإدارة", url="https://t.me/Salahadoroobi")],
                    [InlineKeyboardButton("🔙 العودة للمحفظة", callback_data="my_wallet")]
                ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            try:
                await query.edit_message_text(
                    message,
                    parse_mode=ParseMode.HTML,
                    reply_markup=reply_markup
                )
            except Exception as edit_error:
                await query.message.reply_text(
                    message,
                    parse_mode=ParseMode.HTML,
                    reply_markup=reply_markup
                )
                try:
                    await query.message.delete()
                except:
                    pass

        except Exception as e:
            logger.error(f"خطأ في طلب السلفة: {e}")
            try:
                await query.edit_message_text(
                    "❌ حدث خطأ في عرض نظام السلف",
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 العودة للمحفظة", callback_data="my_wallet")]])
                )
            except:
                await query.message.reply_text(
                    "❌ حدث خطأ في عرض نظام السلف",
                    reply_markup=self.get_reply_keyboard()
                )

    async def show_loan_history(self, query, context):
        """عرض عمليات السلفة للمستخدم"""
        try:
            user_id = query.from_user.id

            # الحصول على معلومات السلف
            from shared.database.wallet_manager import WalletManager
            wallet_manager = WalletManager()

            # الحصول على بيانات المحفظة
            wallet_data = wallet_manager.get_user_wallet(user_id)

            if not wallet_data:
                await query.edit_message_text(
                    "❌ لا توجد محفظة مرتبطة بحسابك",
                    reply_markup=InlineKeyboardMarkup([[
                        InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="main_menu")
                    ]])
                )
                return

            # الحصول على تاريخ السلف
            loan_history = wallet_data.get('loan_history', [])

            if not loan_history:
                message = """📈︙جميع عمليات السلفة︙

❌︙لا يوجد عمليات سلفة︙

💡︙ملاحظة︙
لم تقم بأي عمليات سلف حتى الآن

🔄︙للحصول على سلفة︙
يمكنك طلب سلفة من خلال قسم المحفظة"""

                keyboard = [
                    [InlineKeyboardButton("💳 طلب سلفة", callback_data="request_loan")],
                    [InlineKeyboardButton("🔙 العودة للقائمة السابقة", callback_data="loan_service")]
                ]
            else:
                # ترتيب التاريخ من الأحدث للأقدم
                sorted_history = sorted(loan_history, key=lambda x: x.get('granted_at') or x.get('added_at') or x.get('deducted_at', ''), reverse=True)

                # عرض آخر 10 عمليات
                recent_history = sorted_history[:10]

                message = f"""📈︙جميع عمليات السلفة︙

📊︙إجمالي العمليات︙{len(loan_history)} عملية"""

                for i, record in enumerate(recent_history, 1):
                    operation_type = record.get('type', 'غير محدد')
                    amount = record.get('amount', 0)

                    # تنسيق المبلغ
                    amount_display = f"{amount:.0f}" if amount == int(amount) else f"{amount:.2f}"

                    # تحديد التاريخ والوصف والحالة حسب نوع العملية
                    if operation_type == "loan_granted":
                        timestamp = record.get('granted_at', 'غير محدد')
                        operation_text = "منح سلفة"
                        status_text = "تم منح السلفة"
                    elif operation_type == "loan_deduction":
                        timestamp = record.get('deducted_at', 'غير محدد')
                        deducted_amount = record.get('deducted_amount', 0)
                        amount_display = f"{deducted_amount:.0f}" if deducted_amount == int(deducted_amount) else f"{deducted_amount:.2f}"
                        operation_text = "سداد سلفة"
                        is_fully_paid = record.get('is_fully_paid', False)
                        status_text = "مسدد كامل" if is_fully_paid else "مسدد جزئي"
                    elif operation_type == "loan_balance_added":
                        timestamp = record.get('added_at', 'غير محدد')
                        operation_text = "رصيد سلفة"
                        status_text = "من الادارة"
                    elif operation_type == "loan_balance_deducted":
                        timestamp = record.get('deducted_at', 'غير محدد')
                        operation_text = "خصم سلفة"
                        status_text = "من الادارة"
                    else:
                        timestamp = 'غير محدد'
                        operation_text = operation_type
                        status_text = "غير محدد"

                    # تنسيق التاريخ والوقت واليوم
                    if timestamp != 'غير محدد':
                        try:
                            from datetime import datetime
                            date_obj = datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S')

                            # تحديد اليوم بالعربية
                            days_arabic = {
                                0: "الاثنين", 1: "الثلاثاء", 2: "الاربعاء", 3: "الخميس",
                                4: "الجمعة", 5: "السبت", 6: "الاحد"
                            }
                            day_name = days_arabic.get(date_obj.weekday(), "غير محدد")

                            formatted_date = date_obj.strftime('%Y-%m-%d')
                            formatted_time = date_obj.strftime('%H:%M:%S')
                        except:
                            day_name = "غير محدد"
                            formatted_date = "غير محدد"
                            formatted_time = "غير محدد"
                    else:
                        day_name = "غير محدد"
                        formatted_date = "غير محدد"
                        formatted_time = "غير محدد"

                    # إضافة العملية بالتنسيق الجديد
                    message += f"""

🔢︙رقم︙ {i}
💰︙{operation_text}︙{amount_display} إكسا
📊︙الحالة︙{status_text}
📅︙اليوم︙{day_name}
📅︙التاريخ︙{formatted_date}
⏰︙الوقت︙{formatted_time}"""

                # إضافة معلومات إضافية
                current_loan = wallet_data.get('loan_amount', 0)
                has_active_loan = wallet_data.get('has_active_loan', False)

                if has_active_loan and current_loan > 0:
                    current_loan_display = f"{current_loan:.0f}" if current_loan == int(current_loan) else f"{current_loan:.2f}"
                    message += f"\n\n💸︙السلف الحالي︙{current_loan_display} إكسا"

                keyboard = [
                    [InlineKeyboardButton("📊 حالة السلفة", callback_data="loan_status")],
                    [InlineKeyboardButton("🔙 العودة للقائمة السابقة", callback_data="loan_service")]
                ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            try:
                await query.edit_message_text(
                    message,
                    reply_markup=reply_markup
                )
            except Exception as edit_error:
                await query.message.reply_text(
                    message,
                    reply_markup=reply_markup
                )
                try:
                    await query.message.delete()
                except:
                    pass

        except Exception as e:
            logger.error(f"خطأ في عرض عمليات السلفة: {e}")
            try:
                await query.edit_message_text(
                    "❌ حدث خطأ في عرض عمليات السلفة",
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 العودة للقائمة السابقة", callback_data="loan_service")]])
                )
            except:
                await query.message.reply_text(
                    "❌ حدث خطأ في عرض عمليات السلفة",
                    reply_markup=self.get_reply_keyboard()
                )

    async def show_loan_service(self, query, context):
        """عرض صفحة خدمة سلفني"""
        try:
            user = query.from_user

            # فحص أهلية المستخدم للسلف
            from shared.database.wallet_manager import WalletManager
            wallet_manager = WalletManager()

            eligible, eligibility_message = wallet_manager.is_eligible_for_loan(user.id)

            if eligible:
                message = """💳︙خدمة سلفني

🌟︙مرحباً بك في خدمة سلفني︙

💰︙تفاصيل الخدمة︙
• مبلغ السلفة︙3 إكسا
• قيمة السلفة︙9 دولار أمريكي
• مدة السداد︙عند إضافة رصيد جديد

📋︙شروط الخدمة︙
• يجب أن يكون لديك محفظة نشطة
• لا يوجد سلف سابق غير مسدد
• الحد الأدنى للرصيد︙0 إكسا

💡︙ملاحظة︙
• يمكنك مراجعة حالة السلفة في أي وقت
• السداد تلقائي عند إضافة رصيد جديد"""

                keyboard = [
                    [InlineKeyboardButton("💳 طلب سلفة", callback_data="request_loan")],
                    [InlineKeyboardButton("📈 عمليات السلفة", callback_data="loan_history")],
                    [InlineKeyboardButton("📊 حالة السلفة", callback_data="loan_status")],
                    [InlineKeyboardButton("🔙 العودة للمحفظة", callback_data="my_wallet")]
                ]
            else:
                message = f"""💳︙خدمة سلفني

❌︙غير مؤهل للحصول على سلفة︙

🚫︙السبب︙
{eligibility_message}

💡︙نصائح للحصول على السلفة︙
• تأكد من وجود محفظة نشطة
• قم بسداد أي سلف سابق
• تأكد من استيفاء الشروط المطلوبة

🔄︙يمكنك المحاولة مرة أخرى لاحقاً︙"""

                keyboard = [
                    [InlineKeyboardButton("📈 عمليات السلفة", callback_data="loan_history")],
                    [InlineKeyboardButton("📊 حالة السلفة", callback_data="loan_status")],
                    [InlineKeyboardButton("💰 إضافة رصيد", callback_data="add_balance")],
                    [InlineKeyboardButton("🔙 العودة للمحفظة", callback_data="my_wallet")]
                ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            try:
                await query.edit_message_text(
                    message,
                    reply_markup=reply_markup
                )
            except Exception as edit_error:
                await query.message.reply_text(
                    message,
                    reply_markup=reply_markup
                )
                try:
                    await query.message.delete()
                except:
                    pass

        except Exception as e:
            logger.error(f"خطأ في عرض خدمة سلفني: {e}")
            try:
                await query.edit_message_text(
                    "❌ حدث خطأ في عرض خدمة سلفني",
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 العودة للمحفظة", callback_data="my_wallet")]])
                )
            except:
                await query.message.reply_text(
                    "❌ حدث خطأ في عرض خدمة سلفني",
                    reply_markup=self.get_reply_keyboard()
                )

    async def create_loan_invoice(self, user_id: int, transaction_data: Dict):
        """إنشاء وإرسال فاتورة السلف"""
        try:
            from shared.invoices.invoice_manager import InvoiceManager
            import random

            # إنشاء مدير الفواتير
            invoice_manager = InvoiceManager()

            # توليد رقم فاتورة للسلف (يبدأ بـ 301)
            invoice_number = f"301{random.randint(1000000, 9999999)}"

            # إنشاء الفاتورة
            success, pdf_path, final_invoice_number = invoice_manager.create_transaction_invoice(
                wallet_number=transaction_data['wallet_number'],
                user_name=transaction_data['user_name'],
                amount=transaction_data['amount'],
                transaction_type="منح سلفة",
                previous_balance=transaction_data['old_balance'],
                new_balance=transaction_data['new_balance'],
                invoice_number=invoice_number
            )

            if success:
                logger.info(f"💳 تم إنشاء فاتورة السلف {final_invoice_number} للمستخدم {user_id}")

                # إرسال الفاتورة للمستخدم
                await self.send_invoice_to_user(
                    user_id=user_id,
                    pdf_path=pdf_path,
                    invoice_number=final_invoice_number,
                    transaction_type="منح سلفة",
                    amount=transaction_data['amount']
                )

                logger.info(f"📨 تم إرسال فاتورة السلف {final_invoice_number} للمستخدم {user_id}")
            else:
                logger.error(f"❌ فشل في إنشاء فاتورة السلف للمستخدم {user_id}")

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء وإرسال فاتورة السلف للمستخدم {user_id}: {e}")

    async def send_invoice_to_user(self, user_id: int, pdf_path: str, invoice_number: str, transaction_type: str, amount: float):
        """إرسال فاتورة PDF للمستخدم مع أزرار تفاعلية"""
        try:
            # الحصول على بيانات المستخدم والمحفظة
            from shared.database.wallet_manager import WalletManager
            wallet_manager = WalletManager()

            # الحصول على بيانات المحفظة
            wallet_data = wallet_manager.get_user_wallet(user_id)
            if not wallet_data:
                logger.error(f"❌ لا يمكن العثور على محفظة للمستخدم {user_id}")
                return

            # الحصول على بيانات المستخدم
            user_data = self.user_manager.load_users_data().get(str(user_id), {})

            # إعداد بيانات الفاتورة الكاملة للإرسال
            invoice_data = {
                'invoice_number': invoice_number,
                'pdf_path': pdf_path,
                'transaction_type': transaction_type,
                'amount': amount,
                'user_id': user_id,
                'type': 'pdf_invoice',
                # إضافة البيانات المفقودة
                'wallet_number': wallet_data.get('wallet_number', 'غير محدد'),
                'user_name': wallet_data.get('user_name', user_data.get('اسم المستخدم', 'غير محدد')),
                'previous_balance': 0,  # في حالة السلفة، الرصيد السابق دائماً 0
                'new_balance': amount,  # الرصيد الجديد هو مبلغ السلفة
                'usd_value': amount * 3,  # 1 إكسا = 3 دولار
                'date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            # إرسال الفاتورة باستخدام النظام الموجود
            await self.send_invoice_files(user_id, invoice_data)

            logger.info(f"✅ تم إرسال فاتورة {invoice_number} للمستخدم {user_id}")

        except Exception as e:
            logger.error(f"❌ خطأ في إرسال فاتورة {invoice_number} للمستخدم {user_id}: {e}")

    async def periodic_message_check(self):
        """فحص دوري للرسائل المعلقة"""
        while True:
            try:
                await asyncio.sleep(5)  # فحص كل 5 ثوانٍ
                await self.check_pending_messages()
            except Exception as e:
                logger.error(f"خطأ في الفحص الدوري للرسائل المعلقة: {e}")
                await asyncio.sleep(10)  # انتظار أطول في حالة الخطأ

    async def check_pending_messages(self):
        """فحص وإرسال الرسائل المعلقة"""
        try:
            import json
            import os

            # مسار ملف الرسائل المعلقة
            messages_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'shared', 'messages')
            pending_messages_file = os.path.join(messages_dir, 'pending_messages.json')

            if not os.path.exists(pending_messages_file):
                return

            # قراءة الرسائل المعلقة
            with open(pending_messages_file, 'r', encoding='utf-8') as f:
                pending_messages = json.load(f)

            if not pending_messages:
                return

            # إرسال الرسائل وحذفها من القائمة
            messages_to_remove = []
            for message_id, message_data in pending_messages.items():
                try:
                    user_id = message_data['user_id']
                    message_text = message_data['message']

                    # إرسال الرسالة للمستخدم
                    await self.application.bot.send_message(
                        chat_id=user_id,
                        text=message_text
                    )

                    messages_to_remove.append(message_id)
                    logger.info(f"✅ تم إرسال رسالة {message_id} للمستخدم {user_id}")

                except Exception as e:
                    logger.error(f"❌ فشل في إرسال رسالة {message_id}: {e}")
                    # لا نحذف الرسالة في حالة الفشل للمحاولة مرة أخرى

            # حذف الرسائل المرسلة بنجاح
            for message_id in messages_to_remove:
                del pending_messages[message_id]

            # حفظ الملف المحدث
            if messages_to_remove:
                with open(pending_messages_file, 'w', encoding='utf-8') as f:
                    json.dump(pending_messages, f, ensure_ascii=False, indent=2)

        except Exception as e:
            logger.error(f"خطأ في فحص الرسائل المعلقة: {e}")

    async def periodic_user_invoice_check(self):
        """فحص دوري للفواتير المعلقة للمستخدمين"""
        while True:
            try:
                await asyncio.sleep(5)  # فحص كل 5 ثوانٍ
                await self.check_pending_user_invoices()
            except Exception as e:
                logger.error(f"خطأ في الفحص الدوري للفواتير المعلقة للمستخدمين: {e}")
                await asyncio.sleep(10)  # انتظار أطول في حالة الخطأ

    async def periodic_monitoring_cleanup(self):
        """تنظيف دوري لإشعارات المراقبة القديمة"""
        while True:
            try:
                await asyncio.sleep(3600)  # تنظيف كل ساعة

                # تنظيف الإشعارات القديمة (أكثر من 24 ساعة)
                if hasattr(self, 'monitoring_system') and self.monitoring_system:
                    deleted_count = await asyncio.to_thread(
                        self.monitoring_system.cleanup_old_notifications,
                        24  # الاحتفاظ بإشعارات آخر 24 ساعة فقط
                    )

                    if deleted_count > 0:
                        logger.info(f"🧹 تنظيف دوري: تم حذف {deleted_count} إشعار مراقبة قديم")

            except Exception as e:
                logger.error(f"خطأ في التنظيف الدوري لإشعارات المراقبة: {e}")
                await asyncio.sleep(300)  # إعادة المحاولة بعد 5 دقائق في حالة الخطأ

    async def check_pending_user_invoices(self):
        """فحص وإرسال الفواتير المعلقة للمستخدمين"""
        try:
            import json
            import os

            # مسار ملف الفواتير المعلقة
            invoices_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'shared', 'invoices')
            pending_invoices_file = os.path.join(invoices_dir, 'pending_user_invoices.json')

            if not os.path.exists(pending_invoices_file):
                return

            # قراءة الفواتير المعلقة
            with open(pending_invoices_file, 'r', encoding='utf-8') as f:
                pending_invoices = json.load(f)

            if not pending_invoices:
                return

            # إرسال الفواتير وحذفها من القائمة
            invoices_to_remove = []
            for invoice_id, invoice_info in pending_invoices.items():
                try:
                    user_id = invoice_info['user_id']
                    invoice_data = invoice_info['invoice_data']

                    # إرسال الفاتورة للمستخدم مع الأزرار التفاعلية
                    await self.send_user_invoice_with_buttons(user_id, invoice_data)

                    invoices_to_remove.append(invoice_id)
                    logger.info(f"✅ تم إرسال فاتورة {invoice_data['invoice_number']} للمستخدم {user_id}")

                except Exception as e:
                    logger.error(f"❌ فشل في إرسال فاتورة {invoice_id}: {e}")
                    # لا نحذف الفاتورة في حالة الفشل للمحاولة مرة أخرى

            # حذف الفواتير المرسلة بنجاح
            for invoice_id in invoices_to_remove:
                del pending_invoices[invoice_id]

            # حفظ الملف المحدث
            if invoices_to_remove:
                with open(pending_invoices_file, 'w', encoding='utf-8') as f:
                    json.dump(pending_invoices, f, ensure_ascii=False, indent=2)

        except Exception as e:
            logger.error(f"خطأ في فحص الفواتير المعلقة للمستخدمين: {e}")

    async def send_user_invoice_with_buttons(self, user_id, invoice_data):
        """إرسال فاتورة للمستخدم مع الأزرار التفاعلية"""
        try:
            from telegram import InlineKeyboardButton, InlineKeyboardMarkup
            from datetime import datetime

            # تنسيق المبلغ
            amount = invoice_data['amount']
            amount_display = f"{amount:.0f}" if amount == int(amount) else f"{amount:.2f}"

            # استخدام البيانات المحفوظة من المعاملة الأصلية
            old_balance = invoice_data.get('old_balance', 0)
            new_balance = invoice_data.get('new_balance', 0)
            old_loan_amount = invoice_data.get('old_loan_amount', 0)
            new_loan_amount = invoice_data.get('new_loan_amount', 0)
            is_fully_paid = invoice_data.get('is_fully_paid', False)

            # تحديد نوع العملية والمحتوى
            if invoice_data['transaction_type'] == "إضافة سلفة":
                operation_name = "إضافة سلفة"
                amount_label = "الرصيد السلفة"
                additional_info = f"💸︙إجمالي السلف︙{new_loan_amount:.0f} إكسا"
            else:  # خصم سلفة
                operation_name = "خصم سلفة"
                amount_label = "الرصيد المخصوم"
                loan_status = "مسددة بالكامل" if is_fully_paid else "جزئياً مسددة"
                additional_info = f"""💸︙السلف المتبقي︙{new_loan_amount:.0f} إكسا
✅︙حالة السلفة︙{loan_status}"""

            # تنسيق الأرصدة
            old_balance_display = f"{old_balance:.0f}" if old_balance == int(old_balance) else f"{old_balance:.2f}"
            new_balance_display = f"{new_balance:.0f}" if new_balance == int(new_balance) else f"{new_balance:.2f}"

            # حساب القيمة بالدولار والتوكين
            dollar_value = amount * 3
            dollar_display = f"{dollar_value:.0f}" if dollar_value == int(dollar_value) else f"{dollar_value:.2f}"
            token_value = amount * 524288

            # تنسيق التوكن
            if token_value >= 1000:  # مليون أو أكثر
                token_display = f"{token_value/1000:.2f} مليون توكين"
            else:
                token_display = f"{token_value:.2f} ألف توكين"

            # إنشاء رسالة الفاتورة الإلكترونية الكاملة
            message = f"""🧾︙فاتورة رصيد إلكترونية
🧾︙نوع العملية︙{operation_name}
🔢︙رقم الفاتورة︙{invoice_data['invoice_number']}

👤︙العميل︙{invoice_data.get('user_name', 'غير محدد')}
🏦︙رقم المحفظة︙{invoice_data['wallet_number']}

💰︙{amount_label}︙{amount_display} إكسا
📊︙الرصيد السابق︙{old_balance_display} إكسا
💳︙الرصيد الجديد︙{new_balance_display} إكسا
{additional_info}

💵︙القيمة بالدولار︙{dollar_display} USD
🪙︙القيمة بالتوكين︙{token_display}

📅︙تاريخ العملية︙{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🏢︙مقدم الخدمة︙نظام المحفظة الإلكترونية

💡︙ملاحظة︙
تم تنفيذ هذه العملية من قبل الإدارة"""

            # إنشاء الأزرار التفاعلية
            keyboard = [
                [
                    InlineKeyboardButton("👁️ عرض الفاتورة", callback_data=f"view_invoice_{invoice_data['invoice_number']}"),
                    InlineKeyboardButton("🖨️ طباعة الفاتورة", callback_data=f"print_invoice_{invoice_data['invoice_number']}")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            # إرسال الرسالة مع الأزرار
            await self.application.bot.send_message(
                chat_id=user_id,
                text=message,
                reply_markup=reply_markup
            )

            logger.info(f"✅ تم إرسال فاتورة {invoice_data['invoice_number']} مع الأزرار للمستخدم {user_id}")

        except Exception as e:
            logger.error(f"❌ خطأ في إرسال فاتورة مع الأزرار للمستخدم {user_id}: {e}")

    async def loan_status(self, query, context):
        """عرض حالة السلفة"""
        try:
            user = query.from_user

            from shared.database.wallet_manager import WalletManager
            wallet_manager = WalletManager()

            loan_info = wallet_manager.get_user_loan_info(user.id)

            if "error" in loan_info:
                message = f"""📊︙حالة السلفة︙

❌︙خطأ︙
{loan_info['error']}

🔄︙يرجى المحاولة مرة أخرى"""
            else:
                if loan_info['has_active_loan']:
                    loan_amount = loan_info['loan_amount']
                    granted_at = loan_info['loan_granted_at']

                    # تنسيق الأرقام بدون عشري
                    loan_amount_display = f"{loan_amount:.0f}" if loan_amount == int(loan_amount) else f"{loan_amount:.2f}"
                    dollar_value = loan_amount * 3
                    dollar_display = f"{dollar_value:.0f}" if dollar_value == int(dollar_value) else f"{dollar_value:.2f}"

                    message = f"""📊︙حالة السلفة︙

💳︙لديك سلفة نشطة︙

💰︙تفاصيل السلفة︙
• الرصيد السلفة : {loan_amount_display} إكسا
• تاريخ المنح : {granted_at}
• قيمة السلفة : {dollar_display} دولار

⚡︙طريقة السداد︙
• سيتم خصم السلفة تلقائياً عند إضافة رصيد
• إذا كان الرصيد المضاف أقل من السلفة، يتم خصم المتاح
• الباقي من السلفة يُخصم من الإضافات التالية

💡︙ملاحظة︙
لا يمكن الحصول على سلفة جديدة قبل سداد الحالية"""

                    keyboard = [
                        [InlineKeyboardButton("💰 إضافة رصيد", callback_data="add_balance")],
                        [InlineKeyboardButton("🔙 العودة للقائمة السابقة", callback_data="loan_service")]
                    ]
                else:
                    if loan_info['is_eligible']:
                        message = f"""📊︙حالة السلفة︙

✅︙لا توجد سلفة نشطة︙

🎯︙الحالة︙
{loan_info['eligibility_message']}

💰︙يمكنك طلب سلفة جديدة︙
• مبلغ السلفة : 3 إكسا
• قيمة السلفة : 9 دولار
• السداد : تلقائي عند إضافة رصيد

🔄︙للحصول على سلفة︙
اضغط على زر "💳 طلب سلفة" أدناه"""

                        keyboard = [
                            [InlineKeyboardButton("💳 طلب سلفة", callback_data="request_loan")],
                            [InlineKeyboardButton("🔙 العودة للقائمة السابقة", callback_data="loan_service")]
                        ]
                    else:
                        message = f"""📊︙حالة السلفة︙

❌︙غير مؤهل للحصول على سلفة︙

🚫︙السبب︙
{loan_info['eligibility_message']}

📋︙شروط الحصول على السلفة︙
• يجب أن تكون محفظتك مُحققة (لديك معاملات سابقة)
• الرصيد الحالي يجب أن يكون 0 إكسا
• لا يوجد سلف سابق غير مسدد

💡︙كيفية تحقيق المحفظة︙
• قم بإضافة رصيد لمحفظتك لأول مرة
• استخدم الخدمات المدفوعة"""

                        keyboard = [
                            [InlineKeyboardButton("💰 إضافة رصيد", callback_data="add_balance")],
                            [InlineKeyboardButton("🔙 العودة للقائمة السابقة", callback_data="loan_service")]
                        ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            try:
                await query.edit_message_text(
                    message,
                    reply_markup=reply_markup
                )
            except Exception as edit_error:
                await query.message.reply_text(
                    message,
                    reply_markup=reply_markup
                )
                try:
                    await query.message.delete()
                except:
                    pass

        except Exception as e:
            logger.error(f"خطأ في عرض حالة السلفة: {e}")
            try:
                await query.edit_message_text(
                    "❌ حدث خطأ في عرض حالة السلفة",
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 العودة للقائمة السابقة", callback_data="loan_service")]])
                )
            except:
                await query.message.reply_text(
                    "❌ حدث خطأ في عرض حالة السلفة",
                    reply_markup=self.get_reply_keyboard()
                )

    async def show_transactions(self, query, context):
        """عرض عمليات المحفظة"""
        try:
            user = query.from_user

            # الحصول على معلومات المحفظة
            wallet_info = self.user_manager.get_user_wallet_info(user.id)

            if wallet_info:
                # تحديد حالة المحفظة
                status_emoji = "✅" if wallet_info['status'] == 'active' else "❌"
                status_text = "نشط" if wallet_info['status'] == 'active' else "خامل"

                # الحصول على تاريخ المعاملات من مدير المحفظة
                wallet_data = self.user_manager.wallet_manager.get_user_wallet(user.id)

                message = f"""📊︙عرض المعاملات

🏦︙معلومات المحفظة︙
🔢︙رقم المحفظة︙<code>{wallet_info['wallet_number']}</code>
👤︙اسم المالك︙{wallet_info['user_name']}
💰︙الرصيد الحالي︙{wallet_info['balance']:.2f} {wallet_info['currency']}
📊︙الحالة︙{status_text} {status_emoji}

📈︙إحصائيات العمليات︙
🔢︙عدد العمليات︙{wallet_data.get('transaction_count', 0) if wallet_data else 0}
📅︙آخر عملية︙{wallet_data.get('last_transaction', 'لا توجد عمليات') if wallet_data else 'لا توجد عمليات'}

📋︙تاريخ العمليات︙
"""

                # إضافة تاريخ المعاملات إذا كان متوفراً
                if wallet_data and wallet_data.get('transaction_count', 0) > 0:
                    message += "💡︙العمليات الأخيرة︙\n"
                    message += f"📊︙آخر تحديث رصيد︙{wallet_data.get('last_transaction', 'غير محدد')}\n"
                    message += f"🔢︙إجمالي العمليات︙{wallet_data.get('transaction_count', 0)}\n"
                else:
                    message += "📝︙لا توجد عمليات مسجلة حتى الآن\n"
                    message += "💡︙ستظهر العمليات هنا عند إجرائها\n"

                message += f"""
📅︙تاريخ الإنشاء︙{wallet_info['created_at']}
🔐︙مستوى الأمان︙{wallet_data.get('security_level', 'أساسي') if wallet_data else 'أساسي'}

💡︙ملاحظة︙
يمكنك عرض تفاصيل أكثر للعمليات قريباً"""

            else:
                message = f"""📊︙عرض المعاملات

❌︙لا توجد محفظة︙
لم يتم العثور على محفظة مرتبطة بحسابك

💡︙سيتم إنشاء محفظتك تلقائياً
يرجى المحاولة مرة أخرى خلال لحظات"""

            # إنشاء لوحة مفاتيح العودة
            keyboard = [
                [InlineKeyboardButton("🔄 تحديث", callback_data="show_transactions")],
                [InlineKeyboardButton("🔙 العودة للمحفظة", callback_data="my_wallet")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            try:
                await query.edit_message_text(
                    message,
                    parse_mode=ParseMode.HTML,
                    reply_markup=reply_markup
                )
            except Exception:
                await query.message.reply_text(
                    message,
                    parse_mode=ParseMode.HTML,
                    reply_markup=reply_markup
                )
                try:
                    await query.message.delete()
                except:
                    pass

        except Exception as e:
            logger.error(f"خطأ في عرض العمليات: {e}")
            try:
                await query.edit_message_text(
                    "❌ حدث خطأ في عرض العمليات",
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 العودة للمحفظة", callback_data="my_wallet")]])
                )
            except:
                await query.message.reply_text(
                    "❌ حدث خطأ في عرض العمليات",
                    reply_markup=self.get_reply_keyboard()
                )

    async def show_usage_report(self, query, context):
        """عرض تقرير استخدام المحفظة"""
        try:
            user = query.from_user

            # الحصول على معلومات المحفظة
            wallet_info = self.user_manager.get_user_wallet_info(user.id)

            if wallet_info:
                # تحديد حالة المحفظة
                status_emoji = "✅" if wallet_info['status'] == 'active' else "❌"
                status_text = "نشط" if wallet_info['status'] == 'active' else "خامل"

                # الحصول على بيانات المستخدم للإحصائيات
                user_data = self.user_manager.get_user_data(user.id)
                wallet_data = self.user_manager.wallet_manager.get_user_wallet(user.id)

                # حساب إحصائيات الاستخدام
                total_visits = user_data.get('عدد الزيارات', 0) if user_data else 0
                last_activity = user_data.get('آخر نشاط', 'غير محدد') if user_data else 'غير محدد'
                join_date = user_data.get('تاريخ الانضمام', 'غير محدد') if user_data else 'غير محدد'

                message = f"""📈︙تقرير الاستخدام

👤︙معلومات المستخدم︙
🔢︙رقم المحفظة︙<code>{wallet_info['wallet_number']}</code>
👤︙اسم المالك︙{wallet_info['user_name']}
📊︙الحالة︙{status_text} {status_emoji}
📅︙تاريخ الانضمام︙{join_date}

💰︙إحصائيات المحفظة︙
💎︙الرصيد الحالي︙{wallet_info['balance']:.2f} {wallet_info['currency']}
🔢︙عدد العمليات︙{wallet_data.get('transaction_count', 0) if wallet_data else 0}
📅︙آخر معاملة︙{wallet_data.get('last_transaction', 'لا توجد معاملات') if wallet_data else 'لا توجد معاملات'}
📅︙تاريخ إنشاء المحفظة︙{wallet_info['created_at']}

📊︙إحصائيات النشاط︙
🔢︙إجمالي الزيارات︙{total_visits}
📅︙آخر نشاط︙{last_activity}
🔐︙مستوى الأمان︙{wallet_data.get('security_level', 'أساسي') if wallet_data else 'أساسي'}
✅︙حالة التحقق︙{'مُفعل' if wallet_info['is_verified'] else 'غير مُفعل'}

🎯︙استخدام الخدمات︙
🤖︙إكسا الذكي︙متاح
💳︙خدمة السلف︙متاحة
💰︙إضافة الرصيد︙متاحة

📈︙ملخص الأداء︙
• المحفظة نشطة ومتاحة للاستخدام
• جميع الخدمات متاحة
• لا توجد قيود على الحساب

💡︙نصائح لتحسين الاستخدام︙
• استخدم إكسا الذكي للحصول على مساعدة فورية
• راقب رصيدك بانتظام
• استفد من خدمة السلف عند الحاجة"""

            else:
                message = f"""📈︙تقرير الاستخدام

❌︙لا توجد محفظة︙
لم يتم العثور على محفظة مرتبطة بحسابك

💡︙سيتم إنشاء محفظتك تلقائياً
يرجى المحاولة مرة أخرى خلال لحظات

📊︙معلومات أساسية︙
👤︙المستخدم︙{user.full_name}
🆔︙المعرف︙{user.id}
📅︙التاريخ︙{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""

            # إنشاء لوحة مفاتيح العودة
            keyboard = [
                [InlineKeyboardButton("🔄 تحديث التقرير", callback_data="usage_report")],
                [InlineKeyboardButton("🔙 العودة للمحفظة", callback_data="my_wallet")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            try:
                await query.edit_message_text(
                    message,
                    parse_mode=ParseMode.HTML,
                    reply_markup=reply_markup
                )
            except Exception:
                await query.message.reply_text(
                    message,
                    parse_mode=ParseMode.HTML,
                    reply_markup=reply_markup
                )
                try:
                    await query.message.delete()
                except:
                    pass

        except Exception as e:
            logger.error(f"خطأ في عرض تقرير الاستخدام: {e}")
            try:
                await query.edit_message_text(
                    "❌ حدث خطأ في عرض تقرير الاستخدام",
                    reply_markup=InlineKeyboardMarkup([[InlineKeyboardButton("🔙 العودة للمحفظة", callback_data="my_wallet")]])
                )
            except:
                await query.message.reply_text(
                    "❌ حدث خطأ في عرض تقرير الاستخدام",
                    reply_markup=self.get_reply_keyboard()
                )

    async def copy_balance_form(self, query, context: ContextTypes.DEFAULT_TYPE):
        """نسخ نص نموذج إضافة الرصيد"""
        try:
            # نص النموذج للنسخ - كنص واحد قابل للنسخ
            form_text = "💰︙رقم المحفظة︙9099999999\n💎︙ الرصيد المطلوب︙0 إكسا\n💳︙ مقدم الخدمة︙النجم , الكريمي , الحزمي , جيب\n🔢︙ رقم الحواله او الايداع︙123456789"

            # إرسال النص أولاً كرسالة منفصلة للنسخ السهل
            await context.bot.send_message(
                chat_id=query.message.chat_id,
                text=f"`{form_text}`",
                parse_mode=ParseMode.MARKDOWN
            )

            # ثم إرسال رسالة التوضيح
            instructions_text = """💡︙ملاحظة مهمة
اضغط مطولاً على النص أعلاه واختر "نسخ" لنسخه، ثم عدل
• رقم المحفظة
• الرصيد المطلوب
• مقدم الخدمة
• رقم الحوالة او الايداع
• أرفاق توثيق الخدمة
• وأرسله للإدارة لمعالجة طلبك"""

            await context.bot.send_message(
                chat_id=query.message.chat_id,
                text=instructions_text
            )

            # إرسال إشعار للمستخدم
            await query.answer("📋 تم إرسال النموذج! اضغط مطولاً على النص لنسخه", show_alert=False)

        except Exception as e:
            logger.error(f"خطأ في نسخ نموذج الرصيد: {e}")
            await query.answer("❌ حدث خطأ في عرض النموذج", show_alert=True)

def main():
    """الدالة الرئيسية"""
    if BOT_TOKEN == "YOUR_BOT_TOKEN_HERE":
        print("❌ خطأ: يرجى إضافة توكن البوت في ملف config.py")
        print("💡 احصل على التوكن من @BotFather في تلجرام")
        return

    bot = TelegramBot()
    bot.run()

if __name__ == "__main__":
    main()
