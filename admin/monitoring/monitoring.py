    #!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
بوت المراقبة المنفصل
مخصص فقط لاستقبال ومعالجة إشعارات المراقبة من البوت الرئيسي
"""

import asyncio
import logging
import json
import os
import sys
from datetime import datetime
from telegram import Update, Bot
from telegram.ext import Application, MessageHandler, filters, ContextTypes
from telegram.constants import ParseMode

# إضافة المسار للوصول للإعدادات
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'core'))

try:
    from admin_config import ADMIN_BOT_TOKEN, ADMIN_USER_ID
except ImportError:
    # قيم افتراضية في حالة عدم وجود الملف
    ADMIN_BOT_TOKEN = "7633053725:AAH6Gkm6MJ9HSUcaruoAwhhZEc_BjhoQ6JQ"
    ADMIN_USER_ID = 591967813

# إعداد نظام السجلات
log_dir = os.path.join(os.path.dirname(__file__), 'logs')
os.makedirs(log_dir, exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(log_dir, 'monitoring.log'), encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class MonitoringBot:
    """بوت المراقبة المتخصص"""
    
    def __init__(self):
        self.token = ADMIN_BOT_TOKEN
        self.admin_id = ADMIN_USER_ID
        self.app = None
        self.monitoring_data = {
            "total_notifications": 0,
            "button_clicks": 0,
            "user_messages": 0,
            "ai_conversations": 0,
            "files_received": 0,
            "errors": 0,
            "start_time": datetime.now().isoformat()
        }
        # حفظ ملف البيانات في مجلد المراقبة
        data_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'shared', 'database')
        os.makedirs(data_dir, exist_ok=True)
        self.data_file = os.path.join(data_dir, "monitoring_data.json")
        self.load_monitoring_data()
    
    def load_monitoring_data(self):
        """تحميل بيانات المراقبة"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    self.monitoring_data.update(json.load(f))
        except Exception as e:
            logger.error(f"خطأ في تحميل بيانات المراقبة: {e}")
    
    def save_monitoring_data(self):
        """حفظ بيانات المراقبة"""
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.monitoring_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"خطأ في حفظ بيانات المراقبة: {e}")
    
    def parse_monitoring_message(self, text):
        """تحليل رسالة المراقبة"""
        try:
            # تحليل نوع الإشعار
            if "ضغط على زر" in text:
                self.monitoring_data["button_clicks"] += 1
                return "button_click"
            elif "أرسل رسالة" in text:
                self.monitoring_data["user_messages"] += 1
                return "user_message"
            elif "محادثة إكسا" in text:
                self.monitoring_data["ai_conversations"] += 1
                return "ai_conversation"
            elif "أرسل ملف" in text:
                self.monitoring_data["files_received"] += 1
                return "file_received"
            elif "خطأ" in text or "error" in text.lower():
                self.monitoring_data["errors"] += 1
                return "error"
            else:
                return "general"
        except Exception as e:
            logger.error(f"خطأ في تحليل رسالة المراقبة: {e}")
            return "unknown"
    
    def parse_notification_details(self, text):
        """تحليل تفاصيل الإشعار من النص"""
        details = {
            'notification_number': self.monitoring_data["total_notifications"] + 1,
            'user_status': 'غير محدد',
            'user_name': 'غير محدد',
            'user_id': 'غير محدد',
            'user_username': 'غير محدد',
            'button_name': 'غير محدد',
            'content_type': 'غير محدد',
            'day_name': 'غير محدد',
            'date': 'غير محدد',
            'time': 'غير محدد',
            'total_clicks': 0,
            'message_text': 'غير محدد',
            'bot_status': 'غير محدد'
        }

        try:
            # استخراج التفاصيل من النص
            lines = text.split('\n')
            for line in lines:
                if 'حالة المستخدم:' in line:
                    details['user_status'] = line.split(':')[1].strip()
                elif 'المستخدم:' in line and 'حالة' not in line:
                    details['user_name'] = line.split(':')[1].strip()
                elif 'ايدي المستخدم:' in line:
                    details['user_id'] = line.split(':')[1].strip()
                elif 'معرف المستخدم:' in line:
                    details['user_username'] = line.split(':')[1].strip()
                elif 'ضغط على زر:' in line:
                    details['button_name'] = line.split(':')[1].strip()
                elif 'نوع المحتوى:' in line:
                    details['content_type'] = line.split(':')[1].strip()
                elif 'اليوم:' in line:
                    details['day_name'] = line.split(':')[1].strip()
                elif 'التاريخ:' in line:
                    details['date'] = line.split(':')[1].strip()
                elif 'الوقت:' in line:
                    details['time'] = line.split(':')[1].strip()
                elif 'إجمالي الضغطات:' in line:
                    details['total_clicks'] = line.split(':')[1].strip()
                elif 'النص:' in line:
                    details['message_text'] = line.split(':')[1].strip()
                elif 'الحالة البوت:' in line:
                    details['bot_status'] = line.split(':')[1].strip()
        except:
            pass

        return details

    async def handle_monitoring_notification(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالجة إشعارات المراقبة"""
        try:
            # التحقق من أن الرسالة من البوت الرئيسي أو المدير
            if update.effective_user.id != self.admin_id:
                return

            text = update.message.text
            logger.info(f"📨 تم استلام إشعار مراقبة: {text[:50]}...")

            # تحليل نوع الإشعار
            notification_type = self.parse_monitoring_message(text)

            # تحديث الإحصائيات
            self.monitoring_data["total_notifications"] += 1

            # تحليل تفاصيل الإشعار
            details = self.parse_notification_details(text)

            # تحديد نوع الرسالة وتنسيقها
            if "ضغط على زر" in text:
                # رسالة ضغط زر
                formatted_message = f"""📊︙إشعار مراقبة
🔢︙رقم الإشعار : {details['notification_number']}

🤖︙البوت الرئيسي
👤︙حالة : {details['user_status']}
👤︙الاسم : {details['user_name']}
📧︙معرف : {details['user_username']}
🆔︙ايدي : {details['user_id']}
💬︙أرسل رسالة : {details['button_name']}
📄︙نوع المحتوى : {details['content_type']}
📅︙اليوم : {details['day_name']}
📅︙التاريخ : {details['date']}
⏰︙الوقت : {details['time']}"""

            elif "رد" in text or "رسالة" in text:
                # رسالة رد البوت
                formatted_message = f"""📊︙إشعار مراقبة
🔢︙رقم الإشعار︙{details['notification_number']}

🤖︙البوت الرئيسي
🤖︙الحالة︙رد
💬︙رسالة نصية
👤︙الاسم︙{details['user_name']}
📝︙النص︙{details['message_text']}



📅︙اليوم︙{details['day_name']}
📅︙التاريخ︙{details['date']}
⏰︙الوقت︙{details['time']}"""

            else:
                # رسالة عامة
                formatted_message = f"""📊︙إشعار مراقبة
🔢︙رقم الإشعار : {details['notification_number']}

{text}

📈︙إحصائيات سريعة:
• إجمالي الإشعارات: {self.monitoring_data['total_notifications']}
• ضغطات الأزرار: {self.monitoring_data['button_clicks']}
• رسائل المستخدمين: {self.monitoring_data['user_messages']}"""

            # إرسال الإشعار المنسق
            await context.bot.send_message(
                chat_id=self.admin_id,
                text=formatted_message
            )

            # حفظ البيانات
            self.save_monitoring_data()

            logger.info(f"✅ تم معالجة إشعار المراقبة: {notification_type}")

        except Exception as e:
            logger.error(f"❌ خطأ في معالجة إشعار المراقبة: {e}")
    
    async def send_monitoring_summary(self):
        """إرسال ملخص دوري للمراقبة"""
        try:
            start_time = datetime.fromisoformat(self.monitoring_data['start_time'])
            uptime = datetime.now() - start_time
            
            summary = f"""
📊 **ملخص المراقبة الدوري**

⏰ **وقت التشغيل:** {str(uptime).split('.')[0]}

📈 **إحصائيات النشاط:**
• إجمالي الإشعارات: {self.monitoring_data['total_notifications']}
• ضغطات الأزرار: {self.monitoring_data['button_clicks']}
• رسائل المستخدمين: {self.monitoring_data['user_messages']}
• محادثات إكسا: {self.monitoring_data['ai_conversations']}
• ملفات مستلمة: {self.monitoring_data['files_received']}
• أخطاء مسجلة: {self.monitoring_data['errors']}

🔄 **معدل النشاط:**
• إشعارات/ساعة: {round(self.monitoring_data['total_notifications'] / max(uptime.total_seconds() / 3600, 1), 2)}
• نشاط المستخدمين: {self.monitoring_data['user_messages'] + self.monitoring_data['button_clicks']}

📅 تم إنشاء التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            """
            
            await self.app.bot.send_message(
                chat_id=self.admin_id,
                text=summary,
                parse_mode=ParseMode.MARKDOWN
            )
            
            logger.info("📊 تم إرسال ملخص المراقبة الدوري")
            
        except Exception as e:
            logger.error(f"❌ خطأ في إرسال ملخص المراقبة: {e}")
    
    def setup_handlers(self):
        """إعداد معالجات الرسائل"""
        # معالج جميع الرسائل النصية (إشعارات المراقبة)
        self.app.add_handler(
            MessageHandler(
                filters.TEXT & ~filters.COMMAND,
                self.handle_monitoring_notification
            )
        )
        
        logger.info("✅ تم إعداد معالجات بوت المراقبة")
    
    async def periodic_summary(self):
        """إرسال ملخص دوري كل ساعة"""
        while True:
            try:
                await asyncio.sleep(3600)  # كل ساعة
                await self.send_monitoring_summary()
            except Exception as e:
                logger.error(f"خطأ في الملخص الدوري: {e}")
                await asyncio.sleep(60)  # إعادة المحاولة بعد دقيقة
    
    def run_sync(self):
        """تشغيل بوت المراقبة"""
        try:
            logger.info("🔍 بدء تشغيل بوت المراقبة...")
            
            # إنشاء التطبيق
            self.app = Application.builder().token(self.token).build()
            
            # إعداد المعالجات
            self.setup_handlers()
            
            logger.info("🔍 بوت المراقبة يعمل الآن...")
            
            # تشغيل البوت
            self.app.run_polling(drop_pending_updates=True)
            
        except Exception as e:
            logger.error(f"❌ خطأ في تشغيل بوت المراقبة: {e}")
            raise

def main():
    """الدالة الرئيسية"""
    monitoring_bot = MonitoringBot()
    monitoring_bot.run_sync()

if __name__ == "__main__":
    main()
