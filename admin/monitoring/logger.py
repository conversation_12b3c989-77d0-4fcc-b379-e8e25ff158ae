#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام مراقبة وتتبع البوت
يرسل تقارير مفصلة عن استخدام البوت عبر التلجرام
"""

import logging
import asyncio
import json
import os
import sys
from datetime import datetime
from telegram import Bot
from telegram.error import TelegramError

# إضافة المجلد الرئيسي لمسار Python
root_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, root_dir)

from core.config import BOT_TOKEN, ADMIN_CHAT_ID, ENABLE_LOGGING

logger = logging.getLogger(__name__)

class BotLogger:
    """نظام مراقبة وتتبع البوت"""
    
    def __init__(self):
        """تهيئة نظام المراقبة"""
        self.bot = Bot(token=BOT_TOKEN)
        self.admin_chat_id = ADMIN_CHAT_ID
        self.enabled = ENABLE_LOGGING and ADMIN_CHAT_ID != "YOUR_CHAT_ID_HERE"
        self.users_file = "shared/database/users_data.json"
        self.message_queue = []
        self.processing_queue = False

        # إنشاء مجلد البيانات إذا لم يكن موجوداً
        os.makedirs("shared/database", exist_ok=True)

        # إنشاء ملف المستخدمين إذا لم يكن موجوداً
        if not os.path.exists(self.users_file):
            self.save_users_data({})

        # ملف إحصائيات الرسائل
        self.stats_file = "shared/database/bot_stats.json"
        if not os.path.exists(self.stats_file):
            self.save_stats_data({
                "total_messages_sent": 0,
                "total_messages_received": 0,
                "start_date": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            })

        if not self.enabled:
            logger.warning("نظام المراقبة معطل - يرجى تعيين ADMIN_CHAT_ID في config.py")
    
    def get_user_info(self, update):
        """استخراج معلومات المستخدم"""
        user = update.effective_user
        chat = update.effective_chat
        
        user_info = {
            "user_id": user.id,
            "username": user.username or "غير محدد",
            "first_name": user.first_name or "غير محدد",
            "last_name": user.last_name or "غير محدد",
            "full_name": user.full_name,
            "chat_type": chat.type,
            "chat_id": chat.id,
            "language_code": user.language_code or "غير محدد"
        }
        
        return user_info
    
    def format_user_display(self, user_info):
        """تنسيق عرض معلومات المستخدم بالترتيب المطلوب"""
        lines = []
        lines.append(f"👤 **اسم المستخدم**: `{user_info['full_name']}`")
        lines.append(f"@ **معرف المستخدم**: `@{user_info['username']}`")
        lines.append(f"🆔 **أيدي المستخدم**: `{user_info['user_id']}`")

        return "\n".join(lines)

    def load_users_data(self):
        """تحميل بيانات المستخدمين من الملف"""
        try:
            with open(self.users_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return {}

    def save_users_data(self, users_data):
        """حفظ بيانات المستخدمين في الملف"""
        try:
            with open(self.users_file, 'w', encoding='utf-8') as f:
                json.dump(users_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"خطأ في حفظ بيانات المستخدمين: {e}")

    def save_user_info(self, user_info):
        """حفظ معلومات مستخدم جديد"""
        try:
            users_data = self.load_users_data()
            user_id = str(user_info['user_id'])

            # فحص ما إذا كان المستخدم جديد قبل إضافته
            is_new_user = user_id not in users_data

            # إضافة المستخدم إذا لم يكن موجوداً (بتنسيق موحد مع النظام الرئيسي)
            if is_new_user:
                users_data[user_id] = {
                    "اسم المستخدم": user_info['full_name'],
                    "معرف المستخدم": f"@{user_info['username']}" if user_info['username'] != "غير محدد" else "غير محدد",
                    "أيدي المستخدم": user_info['user_id'],
                    "اللغة": user_info['language_code'],
                    "تاريخ التسجيل": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    "تاريخ أول دخول": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    "آخر نشاط": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    "عدد الزيارات": 1
                }
                logger.info(f"تم إضافة مستخدم جديد: {user_info['full_name']} ({user_id})")
            else:
                # تحديث آخر نشاط وزيادة عدد الزيارات
                users_data[user_id]["آخر نشاط"] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                users_data[user_id]["عدد الزيارات"] = users_data[user_id].get("عدد الزيارات", 0) + 1

                # تحديث المعلومات إذا تغيرت
                users_data[user_id]["اسم المستخدم"] = user_info['full_name']
                users_data[user_id]["معرف المستخدم"] = f"@{user_info['username']}" if user_info['username'] != "غير محدد" else "غير محدد"
                users_data[user_id]["اللغة"] = user_info['language_code']
                logger.info(f"تم تحديث بيانات المستخدم: {user_info['full_name']} ({user_id})")

            self.save_users_data(users_data)
            return is_new_user  # إرجاع True إذا كان مستخدم جديد

        except Exception as e:
            logger.error(f"خطأ في حفظ معلومات المستخدم: {e}")
            return False

    def load_stats_data(self):
        """تحميل إحصائيات الرسائل"""
        try:
            with open(self.stats_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return {
                "total_messages_sent": 0,
                "total_messages_received": 0,
                "start_date": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

    def save_stats_data(self, stats_data):
        """حفظ إحصائيات الرسائل"""
        try:
            with open(self.stats_file, 'w', encoding='utf-8') as f:
                json.dump(stats_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"خطأ في حفظ إحصائيات الرسائل: {e}")

    def increment_message_received(self):
        """زيادة عداد الرسائل المستلمة"""
        try:
            stats = self.load_stats_data()
            stats["total_messages_received"] = stats.get("total_messages_received", 0) + 1
            self.save_stats_data(stats)
        except Exception as e:
            logger.error(f"خطأ في تحديث عداد الرسائل المستلمة: {e}")

    def increment_message_sent(self):
        """زيادة عداد الرسائل المرسلة"""
        try:
            stats = self.load_stats_data()
            stats["total_messages_sent"] = stats.get("total_messages_sent", 0) + 1
            self.save_stats_data(stats)
        except Exception as e:
            logger.error(f"خطأ في تحديث عداد الرسائل المرسلة: {e}")

    async def add_to_queue(self, message):
        """إضافة رسالة إلى طابور الإرسال"""
        if not self.enabled:
            return

        self.message_queue.append(message)

        # معالجة الطابور إذا لم تكن قيد المعالجة
        if not self.processing_queue:
            asyncio.create_task(self.process_queue())

    async def process_queue(self):
        """معالجة طابور الرسائل في الخلفية"""
        if self.processing_queue:
            return

        self.processing_queue = True

        try:
            while self.message_queue:
                message = self.message_queue.pop(0)
                await self.send_log_message_direct(message)
                # انتظار قصير لتجنب حدود API
                await asyncio.sleep(0.1)
        except Exception as e:
            logger.error(f"خطأ في معالجة طابور الرسائل: {e}")
        finally:
            self.processing_queue = False
    
    async def log_user_start(self, update):
        """تسجيل دخول مستخدم جديد للبوت"""
        if not self.enabled:
            return

        try:
            user_info = self.get_user_info(update)
            is_new_user = self.save_user_info(user_info)

            # تسجيل رسالة مستلمة
            self.increment_message_received()

            # إرسال تقرير فقط للمستخدمين الجدد لتقليل الرسائل
            if is_new_user:
                user_display = self.format_user_display(user_info)
                message = f"""
🆕 **مستخدم جديد دخل البوت**

{user_display}
🌍 **اللغة**: {user_info['language_code']}
💬 **نوع المحادثة**: {user_info['chat_type']}
🕐 **الوقت**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                """
                await self.add_to_queue(message)

        except Exception as e:
            logger.error(f"خطأ في تسجيل دخول المستخدم: {e}")
    
    async def log_button_click(self, update, button_data):
        """تسجيل ضغط زر"""
        if not self.enabled:
            return
        
        try:
            user_info = self.get_user_info(update)
            user_display = self.format_user_display(user_info)
            
            # ترجمة أسماء الأزرار
            button_names = {
                "location": "📍 موقعي",
                "about": "👨‍💼 نبذة عني", 
                "works": "💼 أعمالي",
                "experience": "🎯 خبرتي",
                "achievements": "🏆 إنجازاتي",
                "help": "❓ المساعدة",
                "exa_ai": "🤖 إكسا الذكي",
                "end_exa_ai": "❌ إنهاء المحادثة",
                "main_menu": "🔙 العودة للقائمة الرئيسية"
            }
            
            button_name = button_names.get(button_data, button_data)
            
            message = f"""
🔘 **ضغط زر**

{user_display}
🎯 **الزر**: {button_name}
🕐 **الوقت**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            """
            
            await self.add_to_queue(message)
            
        except Exception as e:
            logger.error(f"خطأ في تسجيل ضغط الزر: {e}")
    
    async def log_text_message(self, update, message_type="رسالة نصية"):
        """تسجيل رسالة نصية"""
        if not self.enabled:
            return
        
        try:
            user_info = self.get_user_info(update)
            user_display = self.format_user_display(user_info)
            message_text = update.message.text[:100] + "..." if len(update.message.text) > 100 else update.message.text
            
            message = f"""
💬 **{message_type}**

{user_display}
📝 **النص**: {message_text}
🕐 **الوقت**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            """
            
            # تقليل رسائل النصوص العادية - إرسال عينة فقط
            if hash(str(user_info['user_id'])) % 3 == 0:
                await self.add_to_queue(message)

        except Exception as e:
            logger.error(f"خطأ في تسجيل الرسالة النصية: {e}")

    async def log_exa_ai_conversation(self, update, user_message, ai_response):
        """تسجيل محادثة إكسا الذكي"""
        if not self.enabled:
            return

        try:
            user_info = self.get_user_info(update)
            user_display = self.format_user_display(user_info)

            # اختصار الرسائل الطويلة
            user_msg_short = user_message[:150] + "..." if len(user_message) > 150 else user_message
            ai_msg_short = ai_response[:150] + "..." if len(ai_response) > 150 else ai_response

            message = f"""
🤖 **محادثة إكسا الذكي**

{user_display}
❓ **سؤال المستخدم**: {user_msg_short}
🤖 **رد إكسا**: {ai_msg_short}
🕐 **الوقت**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            """

            await self.add_to_queue(message)
            
        except Exception as e:
            logger.error(f"خطأ في تسجيل محادثة إكسا الذكي: {e}")
    
    async def log_command_usage(self, update, command):
        """تسجيل استخدام أمر"""
        if not self.enabled:
            return
        
        try:
            user_info = self.get_user_info(update)
            user_display = self.format_user_display(user_info)
            
            message = f"""
⚡ **استخدام أمر**

{user_display}
🔧 **الأمر**: /{command}
🕐 **الوقت**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            """
            
            # تقليل رسائل الأوامر - إرسال الأوامر المهمة فقط
            important_commands = ['start', 'help']
            if command in important_commands:
                await self.add_to_queue(message)

        except Exception as e:
            logger.error(f"خطأ في تسجيل استخدام الأمر: {e}")

    async def send_log_message_direct(self, message):
        """إرسال رسالة المراقبة مباشرة للمدير"""
        try:
            await self.bot.send_message(
                chat_id=self.admin_chat_id,
                text=message,
                parse_mode='Markdown'
            )
            # تسجيل رسالة مرسلة
            self.increment_message_sent()
        except TelegramError as e:
            logger.error(f"خطأ في إرسال رسالة المراقبة: {e}")

    async def send_log_message(self, message):
        """إرسال رسالة المراقبة للمدير (عبر الطابور)"""
        await self.add_to_queue(message)
    
    async def send_daily_stats(self, stats):
        """إرسال إحصائيات يومية"""
        if not self.enabled:
            return
        
        try:
            message = f"""
📊 **إحصائيات البوت اليومية**

👥 **المستخدمون الجدد**: {stats.get('new_users', 0)}
🔘 **ضغطات الأزرار**: {stats.get('button_clicks', 0)}
💬 **الرسائل النصية**: {stats.get('text_messages', 0)}
🤖 **محادثات إكسا الذكي**: {stats.get('exa_conversations', 0)}
⚡ **الأوامر المستخدمة**: {stats.get('commands_used', 0)}

📅 **التاريخ**: {datetime.now().strftime('%Y-%m-%d')}
            """
            
            await self.send_log_message(message)
            
        except Exception as e:
            logger.error(f"خطأ في إرسال الإحصائيات اليومية: {e}")

# إنشاء مثيل عام لنظام المراقبة
bot_logger = BotLogger()
