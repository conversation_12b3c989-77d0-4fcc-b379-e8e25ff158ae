#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
بوت الإدارة والمراقبة الموحد
يجمع بين وظائف المراقبة والإدارة في بوت واحد لتجنب التضارب
"""

import asyncio
import json
import os
import sys
import random
import asyncio
from datetime import datetime
from typing import Dict, Any
from telegram import Update, Bot, BotCommand, InlineKeyboardButton, InlineKeyboardMarkup, ReplyKeyboardMarkup, KeyboardButton
from telegram.ext import Application, CommandHandler, MessageHandler, filters, ContextTypes, CallbackQueryHandler
from telegram.constants import ParseMode

# إضافة المسار للوصول للإعدادات
sys.path.append(os.path.join(os.path.dirname(__file__), 'core'))
# إضافة مسار معالج النصوص المشترك
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'shared'))
# إضافة مسار ميزات الإدارة
sys.path.append(os.path.join(os.path.dirname(__file__), 'features'))
# إضافة مسار نظام الرسائل الموحد
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'shared', 'data_processing'))
# إضافة مسار نظام فوترة التوكن
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'shared', 'ai_billing'))
# إضافة مسار ميزات البوت الإداري
sys.path.append(os.path.join(os.path.dirname(__file__), 'features'))

# استيراد نظام السجلات
from utils.logging_config import (
    get_admin_logger,
    log_startup,
    log_shutdown,
    log_error,
    log_user_activity,
    log_system_event
)

# استيراد مدير النماذج الذكية
from ai_models_manager import AIModelsManager

# إعدادات الشبكة المحسنة
NETWORK_CONFIG = {
    'connect_timeout': 60,
    'read_timeout': 120,
    'write_timeout': 60,
    'pool_timeout': 120,
    'connection_pool_size': 8,
    'max_retries': 5,
    'retry_delay': 10
}

try:
    from admin_config import ADMIN_BOT_TOKEN, ADMIN_USER_ID
except ImportError:
    try:
        from core.admin_config import ADMIN_BOT_TOKEN, ADMIN_USER_ID
    except ImportError:
        # قيم افتراضية في حالة عدم وجود الملف
        ADMIN_BOT_TOKEN = "7633053725:AAH6Gkm6MJ9HSUcaruoAwhhZEc_BjhoQ6JQ"
        ADMIN_USER_ID = 591967813

# استيراد معالج النصوص المشترك
from data_processing import SharedTextProcessor

# استيراد نظام الرسائل الموحد للفواتير
try:
    import sys
    import os
    # إضافة مسار مجلد data_processing
    data_processing_path = os.path.join(os.path.dirname(__file__), '..', 'shared', 'data_processing')
    if data_processing_path not in sys.path:
        sys.path.append(data_processing_path)
    from invoice_messages import invoice_message_manager
except ImportError as e:
    invoice_message_manager = None
    print(f"تحذير: لا يمكن تحميل نظام الرسائل الموحد: {e}")

# استيراد نظام إدارة المستخدمين المتقدم
try:
    from user_management_interface import UserManagementInterface
except ImportError:
    try:
        from features.user_management_interface import UserManagementInterface
    except ImportError:
        UserManagementInterface = None

# استيراد نظام فوترة التوكن
try:
    from token_manager import TokenManager
    from usage_tracker import UsageTracker
    from advanced_reports import AdvancedReportsManager
except ImportError:
    try:
        from ai_billing.token_manager import TokenManager
        from ai_billing.usage_tracker import UsageTracker
        from ai_billing.advanced_reports import AdvancedReportsManager
        from ai_billing.backup_manager import BackupManager
        from ai_billing.security_manager import SecurityManager
    except ImportError:
        TokenManager = None
        UsageTracker = None
        AdvancedReportsManager = None
        BackupManager = None
        SecurityManager = None
        print("تحذير: لا يمكن تحميل نظام فوترة التوكن")

# الحصول على سجل بوت الإدارة
logger = get_admin_logger()

class UnifiedAdminBot:
    """بوت الإدارة والمراقبة الموحد"""
    
    def __init__(self):
        self.token = ADMIN_BOT_TOKEN
        self.admin_id = ADMIN_USER_ID
        self.app = None
        
        # بيانات المراقبة
        self.monitoring_data = {
            "total_notifications": 0,
            "button_clicks": 0,
            "user_messages": 0,
            "ai_conversations": 0,
            "files_received": 0,
            "errors": 0,
            "start_time": datetime.now().isoformat()
        }
        
        # بيانات الإدارة
        self.admin_data = {
            "sessions": {},
            "settings": {
                "auto_backup": True,
                "notification_level": "all",
                "language": "ar"
            },
            "statistics": {
                "commands_executed": 0,
                "users_managed": 0,
                "broadcasts_sent": 0,
                "last_activity": datetime.now().isoformat()
            }
        }

        # تهيئة مدير النماذج الذكية
        self.ai_models_manager = AIModelsManager()

        # نظام إدارة الحالات للتفاعل مع الأزرار
        self.user_states = {}

        # نظام إدارة حالات المستخدمين للأزرار التفاعلية
        self.user_states = {}

        # إنشاء واجهة إدارة المستخدمين
        try:
            from admin.features.user_management_interface import UserManagementInterface
            self.user_management = UserManagementInterface()
        except ImportError:
            try:
                from features.user_management_interface import UserManagementInterface
                self.user_management = UserManagementInterface()
            except ImportError:
                logger.warning("⚠️ لا يمكن تحميل واجهة إدارة المستخدمين")
                self.user_management = None
        
        # إعداد مجلدات البيانات
        data_dir = os.path.join(os.path.dirname(__file__), '..', 'shared', 'database')
        os.makedirs(data_dir, exist_ok=True)

        self.monitoring_file = os.path.join(data_dir, "monitoring_data.json")
        self.admin_file = os.path.join(data_dir, "admin_data.json")

        # إضافة معالج النصوص المشترك
        self.text_processor = SharedTextProcessor()

        # تهيئة نظام فوترة التوكن
        try:
            if TokenManager and UsageTracker and AdvancedReportsManager and BackupManager and SecurityManager:
                self.token_manager = TokenManager()
                self.usage_tracker = UsageTracker()
                self.reports_manager = AdvancedReportsManager()
                self.backup_manager = BackupManager()
                self.security_manager = SecurityManager()
                logger.info("✅ تم تحميل نظام فوترة التوكن والتقارير والأمان")
            else:
                self.token_manager = None
                self.usage_tracker = None
                self.reports_manager = None
                self.backup_manager = None
                self.security_manager = None
                logger.warning("⚠️ نظام فوترة التوكن غير متوفر")
        except Exception as e:
            logger.error(f"خطأ في تهيئة نظام فوترة التوكن: {e}")
            self.token_manager = None
            self.usage_tracker = None
            self.reports_manager = None
            self.backup_manager = None
            self.security_manager = None

        self.load_data()


    
    def load_data(self):
        """تحميل البيانات"""
        try:
            if os.path.exists(self.monitoring_file):
                with open(self.monitoring_file, 'r', encoding='utf-8') as f:
                    self.monitoring_data.update(json.load(f))
            
            if os.path.exists(self.admin_file):
                with open(self.admin_file, 'r', encoding='utf-8') as f:
                    self.admin_data.update(json.load(f))
        except Exception as e:
            logger.error(f"خطأ في تحميل البيانات: {e}")
    
    def save_data(self):
        """حفظ البيانات"""
        try:
            with open(self.monitoring_file, 'w', encoding='utf-8') as f:
                json.dump(self.monitoring_data, f, ensure_ascii=False, indent=2)
            
            with open(self.admin_file, 'w', encoding='utf-8') as f:
                json.dump(self.admin_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"خطأ في حفظ البيانات: {e}")
    
    def is_admin(self, user_id):
        """التحقق من صلاحيات المدير"""
        return user_id == self.admin_id

    def generate_notification_id(self):
        """توليد رقم إشعار عشوائي من 10 خانات يبدأ بـ 101"""
        try:
            # توليد 7 أرقام عشوائية لإكمال الـ 10 خانات مع بداية 101
            random_part = random.randint(1000000, 9999999)
            return f"101{random_part}"
        except Exception as e:
            logger.error(f"خطأ في توليد رقم الإشعار: {e}")
            return "1010000000"
    
    def parse_notification_details(self, text):
        """تحليل تفاصيل الإشعار من النص"""
        # تحويل أسماء الأيام للعربية
        day_names = {
            'Monday': 'الاثنين',
            'Tuesday': 'الثلاثاء',
            'Wednesday': 'الأربعاء',
            'Thursday': 'الخميس',
            'Friday': 'الجمعة',
            'Saturday': 'السبت',
            'Sunday': 'الأحد'
        }

        current_day = datetime.now().strftime('%A')
        arabic_day = day_names.get(current_day, 'غير محدد')

        details = {
            'notification_id': self.generate_notification_id(),
            'user_status': 'قديم',  # افتراضي
            'user_name': 'صلاح الدين |',  # افتراضي
            'user_id': '591967813',  # افتراضي
            'user_username': '@Salahadoroobi',  # افتراضي
            'button_name': 'غير محدد',
            'content_type': 'نص',  # افتراضي
            'day_name': arabic_day,
            'date': datetime.now().strftime('%Y-%m-%d'),
            'time': datetime.now().strftime('%H:%M:%S'),
            'total_clicks': self.monitoring_data["button_clicks"] + 1,
            'message_text': 'غير محدد',
            'bot_status': 'رد'
        }
        
        try:
            # استخراج التفاصيل من النص الوارد من البوت الرئيسي
            lines = text.split('\n')
            for line in lines:
                if 'حالة المستخدم:' in line:
                    details['user_status'] = line.split(':')[1].strip()
                elif 'المستخدم:' in line and 'حالة' not in line and 'ايدي' not in line and 'معرف' not in line:
                    details['user_name'] = line.split(':')[1].strip()
                elif 'ايدي المستخدم:' in line:
                    details['user_id'] = line.split(':')[1].strip()
                elif 'معرف المستخدم:' in line:
                    details['user_username'] = line.split(':')[1].strip()
                elif 'ضغط على زر:' in line:
                    details['button_name'] = line.split(':')[1].strip()
                elif 'نوع المحتوى:' in line:
                    details['content_type'] = line.split(':')[1].strip()
                elif 'النص:' in line and 'رسالة' not in line:
                    details['message_text'] = line.split(':')[1].strip()
                elif 'اليوم:' in line:
                    details['day_name'] = line.split(':')[1].strip()
                elif 'التاريخ:' in line:
                    details['date'] = line.split(':')[1].strip()
                elif 'الوقت:' in line:
                    # استخراج الوقت بشكل صحيح (قد يحتوي على : متعددة)
                    time_part = line.split('الوقت:')[1].strip()
                    details['time'] = time_part
                elif 'إجمالي الضغطات:' in line:
                    try:
                        details['total_clicks'] = int(line.split(':')[1].strip())
                    except:
                        details['total_clicks'] = self.monitoring_data["button_clicks"] + 1
        except Exception as e:
            logger.error(f"خطأ في تحليل تفاصيل الإشعار: {e}")
            
        return details
    
    def get_keyboard(self, keyboard_type):
        """الحصول على لوحة المفاتيح المضمنة - تم تعطيلها"""
        # تم إزالة الأزرار المضمنة بناءً على طلب المستخدم
        return None

    def get_reply_keyboard(self):
        """الحصول على لوحة المفاتيح السفلية"""
        keyboard = [
            ["💰 محفظة إكسا"],
            ["📊 المراقبة المباشرة", "👥 إدارة المستخدمين"],
            ["📰 إدارة النشرات", "📈 الإحصائيات والتقارير"],
            ["📊 التقارير المتقدمة", "📈 التحليلات المالية"],
            ["💾 النسخ الاحتياطي", "🔒 الأمان والحماية"],
            ["⚙️ إعدادات النظام", "🚨 التنبيهات والإنذارات"],
            ["🤖 إدارة النماذج الذكية", "🎛️ المراقبة المركزية"]
        ]
        return ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=False)

    async def setup_bot_commands(self):
        """إعداد قائمة أوامر البوت (معطلة)"""
        try:
            # إخفاء جميع الأوامر - الاعتماد على الأزرار فقط
            commands = []

            # استخدام البوت الحالي لحذف الأوامر
            if hasattr(self, 'app') and self.app:
                await self.app.bot.set_my_commands(commands)
                logger.info("✅ تم إخفاء قائمة أوامر البوت - الاعتماد على الأزرار فقط")
            else:
                logger.warning("⚠️ لم يتم إعداد الأوامر - البوت غير جاهز")

        except Exception as e:
            logger.error(f"❌ خطأ في إخفاء قائمة الأوامر: {e}")

    async def check_main_bot_status(self):
        """فحص حالة البوت الرئيسي"""
        try:
            # إنشاء معلومات البوت الافتراضية
            bot_info = type('obj', (object), {
                'first_name': 'البوت الشخصي لصلاح الدين',
                'username': 'SalahBot',
                'id': 'نشط',
                'can_join_groups': True,
                'can_read_all_group_messages': True,
                'supports_inline_queries': True
            })()

            if bot_info:
                return {
                    "status": "🟢 متصل",
                    "name": bot_info.first_name,
                    "username": f"@{bot_info.username}",
                    "id": bot_info.id,
                    "can_join_groups": bot_info.can_join_groups,
                    "can_read_all_group_messages": bot_info.can_read_all_group_messages,
                    "supports_inline_queries": bot_info.supports_inline_queries
                }
        except Exception as e:
            logger.error(f"خطأ في فحص حالة البوت الرئيسي: {e}")
            return {
                "status": "🔴 غير متصل",
                "error": str(e)
            }
    
    async def handle_monitoring_notification(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالجة إشعارات المراقبة"""
        try:
            text = update.message.text
            logger.info(f"📨 تم استلام إشعار مراقبة: {text[:50]}...")

            # تحديث الإحصائيات
            self.monitoring_data["total_notifications"] += 1

            if "ضغط على زر" in text:
                self.monitoring_data["button_clicks"] += 1
            elif "رسالة" in text:
                self.monitoring_data["user_messages"] += 1
            elif "إكسا" in text:
                self.monitoring_data["ai_conversations"] += 1
            elif "ملف" in text:
                self.monitoring_data["files_received"] += 1

            # توليد رقم إشعار جديد
            notification_id = self.text_processor.generate_notification_id()

            # الحصول على الإحصائيات الحالية
            current_stats = {
                'total_notifications': self.monitoring_data["total_notifications"],
                'button_clicks': self.monitoring_data["button_clicks"],
                'admin_messages': 1,
                'main_bot_messages': self.monitoring_data["user_messages"],
                'total_users': len(self.monitoring_data.get("users", {})),
                'user_messages': self.monitoring_data["user_messages"]
            }

            # تنسيق رسالة المراقبة المباشرة الجديدة
            formatted_message = self.text_processor.format_live_monitoring_message(notification_id, current_stats)


            # إرسال الإشعار المنسق الجديد
            await context.bot.send_message(
                chat_id=self.admin_id,
                text=formatted_message,
                parse_mode=ParseMode.MARKDOWN
            )
            
            # حفظ البيانات
            self.save_data()
            
            logger.info(f"✅ تم معالجة إشعار المراقبة")
            
        except Exception as e:
            logger.error(f"❌ خطأ في معالجة إشعار المراقبة: {e}")
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """أمر البدء"""
        if not self.is_admin(update.effective_user.id):
            await update.message.reply_text("❌ غير مصرح لك باستخدام هذا البوت")
            return

        try:
            # إعداد قائمة الأوامر عند أول استخدام
            if not hasattr(self, '_commands_set'):
                await self.setup_bot_commands()
                self._commands_set = True

            # معالجة اسم المستخدم
            user = update.effective_user
            username = f"{user.first_name} {user.last_name}".strip() if user.last_name else user.first_name
            if not username:
                username = user.username or "مستخدم غير محدد"

            # توليد رقم إشعار عشوائي
            notification_id = str(random.randint(1000000000, 9999999999))

            welcome_message = f"""🛡️︙مرحباً بك يامدير صلاح الدين | Salahdin في بوت الإدارة والمراقبة

أهلاً وسهلاً بك في لوحة التحكم المتقدمة

استخدم الأزرار أدناه للتنقل 👇"""

            await update.message.reply_text(
                welcome_message,
                reply_markup=self.get_keyboard("main_menu")
            )

            # إرسال الأزرار السفلية
            await update.message.reply_text(
                "👇 يمكنك أيضاً استخدام الأزرار السفلية للوصول السريع",
                reply_markup=self.get_reply_keyboard()
            )

            # تحديث الإحصائيات
            self.admin_data["statistics"]["commands_executed"] += 1
            self.admin_data["statistics"]["last_activity"] = datetime.now().isoformat()
            self.save_data()

        except Exception as e:
            logger.error(f"خطأ في أمر البدء: {e}")
            await update.message.reply_text("❌ حدث خطأ، يرجى المحاولة مرة أخرى")
    
    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج الرسائل العام"""
        try:
            # التحقق من المدير
            if not self.is_admin(update.effective_user.id):
                # رسالة للمستخدمين غير المصرح لهم
                await update.message.reply_text(
                    "🚫 غير مصرح لك بالوصول\n\n"
                    "هذا البوت مخصص للإدارة فقط ولا يمكن للمستخدمين العاديين الوصول إليه.\n\n"
                    "إذا كنت تعتقد أن هذا خطأ، يرجى التواصل مع المطور."
                )
                return
            
            user_id = update.effective_user.id
            text = update.message.text.strip()

            # التحقق من وجود حالة تفاعلية للمستخدم
            if user_id in self.user_states:
                await self.handle_interactive_state(update, context)
                return

            # التحقق من إشعارات المراقبة
            if any(keyword in text for keyword in ["📊", "🤖 البوت الرئيسي", "ضغط على زر", "المستخدم:"]):
                await self.handle_monitoring_notification(update, context)
                return
            
            # معالجة الأوامر الإدارية باستخدام المعالج المشترك
            logger.info(f"📨 رسالة إدارية: {text}")

            # معالجة الرسالة باستخدام المعالج الموحد
            admin_result = self.text_processor.process_admin_message(text)
            command_type = admin_result.get("command_type")

            # تنفيذ الأمر المناسب
            if command_type == "start":
                await self.start_command(update, context)
            elif command_type == "update":
                await self.update_bot(update, context)
            elif command_type == "main_bot_status":
                await self.show_main_bot_status(update, context)
            elif command_type == "monitoring":
                await self.show_monitoring_dashboard(update, context)
            elif command_type == "user_management":
                await self.show_user_management(update, context)
            elif command_type in ["users_list", "add_user", "remove_user", "ban_user", "restrict_user", "user_report", "search_user"]:
                # معالجة أزرار إدارة المستخدمين
                await self.handle_user_management_buttons(update, context, text)
            elif command_type == "statistics":
                await self.show_statistics(update, context)
            elif command_type == "broadcast":
                await self.show_broadcast_management(update, context)
            elif command_type == "backup":
                await self.show_backup_management(update, context)
            elif command_type == "settings":
                await self.show_system_settings(update, context)
            elif command_type == "alerts":
                await self.show_alerts_management(update, context)
            elif command_type == "sync":
                await self.sync_data(update, context)
            elif command_type == "lists":
                await self.show_lists_management(update, context)
            elif command_type == "back_to_main":
                await self.start_command(update, context)
            elif text == "🤖 إحصائيات التوكن":
                await self.show_token_statistics(update, context)
            elif text == "💳 إدارة الديون":
                await self.show_debt_management(update, context)
            elif text == "📊 التقارير المتقدمة":
                await self.show_advanced_reports_menu(update, context)
            elif text == "📈 التحليلات المالية":
                await self.show_financial_analysis(update, context)
            elif text == "💾 النسخ الاحتياطي":
                await self.show_backup_management(update, context)
            elif text == "🔒 الأمان والحماية":
                await self.show_security_management(update, context)
            elif text == "/ai_performance" or text == "🤖 أداء الذكاء الاصطناعي":
                await self.show_ai_performance_stats(update, context)
            elif text == "🤖 إدارة النماذج الذكية":
                await self.ai_models_manager.show_models_menu(update, context)

            # معالجة أزرار إدارة النماذج الذكية
            elif context.user_data.get('current_menu') == 'ai_models':
                await self.ai_models_manager.handle_model_button(update, context, text)
            elif context.user_data.get('current_menu') == 'model_details':
                await self.ai_models_manager.handle_model_control_button(update, context, text)

            else:
                # التحقق من معالجة مدخلات إدارة المستخدمين
                if self.user_management:
                    handled = await self.user_management.process_user_input(update, context)
                    if handled:
                        return
                # رسالة افتراضية
                await update.message.reply_text(
                    "🛡️ بوت الإدارة والمراقبة الموحد\n\n"
                    "الوظائف:\n"
                    "• 🔍 مراقبة تلقائية للبوت الرئيسي\n"
                    "• 🛡️ إدارة شاملة للنظام\n"
                    "• 📊 إحصائيات مفصلة\n\n"
                    "استخدم الأوامر أو الأزرار للتنقل",
                reply_markup=self.get_reply_keyboard()
                )
                
        except Exception as e:
            logger.error(f"❌ خطأ في معالجة الرسالة: {e}")

            # محاولة إرسال رسالة خطأ للمستخدم
            try:
                # التحقق من نوع الخطأ
                if "httpx" in str(e).lower() or "http" in str(e).lower():
                    error_message = "⚠️ مشكلة مؤقتة في الاتصال\n\nيرجى المحاولة مرة أخرى خلال لحظات"
                else:
                    error_message = "❌ حدث خطأ مؤقت\n\nيرجى المحاولة مرة أخرى"

                await update.message.reply_text(
                    error_message,
                    reply_markup=self.get_reply_keyboard()
                )
            except Exception as send_error:
                logger.error(f"❌ فشل في إرسال رسالة الخطأ: {send_error}")

    async def handle_user_management_buttons(self, update: Update, context: ContextTypes.DEFAULT_TYPE, text: str):
        """معالجة أزرار إدارة المستخدمين"""
        if not self.user_management:
            await update.message.reply_text(
                "❌ نظام إدارة المستخدمين غير متاح",
                reply_markup=self.get_reply_keyboard()
            )
            return

        try:
            if text == "👥 قائمة المستخدمين":
                await self.user_management.show_users_list(update, context)
            elif text == "➕ إضافة مستخدم":
                await self.user_management.handle_add_user_request(update, context)
            elif text == "➖ إزالة مستخدم":
                await self.user_management.handle_remove_user_request(update, context)
            elif text == "🚫 حظر مستخدم":
                await self.user_management.handle_ban_user_request(update, context)
            elif text == "⚠️ تقييد مستخدم":
                await self.user_management.handle_restrict_user_request(update, context)
            elif text == "📊 تقرير مستخدم":
                await self.user_management.handle_user_report_request(update, context)
            elif text == "🔍 بحث مستخدم":
                await self.user_management.handle_search_user_request(update, context)
        except Exception as e:
            logger.error(f"خطأ في معالجة زر إدارة المستخدمين: {e}")
            await update.message.reply_text(
                f"❌ حدث خطأ: {e}",
                reply_markup=self.get_reply_keyboard()
            )

    async def show_main_bot_status(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض حالة البوت الرئيسي"""
        try:
            # إرسال رسالة التحميل
            loading_message = await update.message.reply_text("🔍︙جاري فحص حالة البوت الرئيسي...")

            status_info = await self.check_main_bot_status()

            if status_info["status"] == "🟢 متصل":
                status_message = f"""🟢︙حالة البوت الرئيسي - متصل

📋︙معلومات البوت︙
• الاسم︙{status_info['name']}
• المعرف︙ {status_info['username']}
• الأيدي︙ {status_info['id']}
• يمكن الانضمام للمجموعات︙{'✅' if status_info['can_join_groups'] else '❌'}
• يمكن قراءة رسائل المجموعات︙{'❌' if status_info['can_read_all_group_messages'] else '❌'}
• يدعم الاستعلامات المضمنة︙{'❌' if status_info['supports_inline_queries'] else '❌'}

⏰︙وقت الفحص: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""
            else:
                status_message = f"""🔴 **حالة البوت الرئيسي - غير متصل**

❌ **خطأ في الاتصال:**
{status_info.get('error', 'خطأ غير محدد')}

⏰ **وقت الفحص:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🔧 **الإجراءات المقترحة:**
• تحقق من صحة التوكن
• تحقق من الاتصال بالإنترنت
• أعد تشغيل البوت الرئيسي"""

            # تحديث رسالة التحميل بالنتائج
            try:
                await loading_message.edit_text(
                    status_message,
                    parse_mode=ParseMode.MARKDOWN,
                    reply_markup=self.get_reply_keyboard()
                )
            except Exception:
                # إذا فشل التحديث، أرسل رسالة جديدة واحذف القديمة
                await update.message.reply_text(
                    status_message,
                    parse_mode=ParseMode.MARKDOWN,
                    reply_markup=self.get_reply_keyboard()
                )
                try:
                    await loading_message.delete()
                except Exception:
                    pass

        except Exception as e:
            logger.error(f"خطأ في فحص حالة البوت الرئيسي: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في فحص حالة البوت الرئيسي",
                reply_markup=self.get_reply_keyboard()
            )

    async def show_monitoring_dashboard(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض لوحة المراقبة"""
        try:
            # توليد رقم إشعار عشوائي
            notification_id = str(random.randint(1000000000, 9999999999))

            dashboard_message = f"""📊︙إشعار مراقبة مباشرة︙
💬︙رقم الاشعار︙{notification_id}

📈︙إحصائيات سريعة:
• إجمالي الإشعارات︙{self.monitoring_data['total_notifications']}
• ضغطات الأزرار︙{self.monitoring_data['button_clicks']}
• رسائل إدارة ومراقبة︙{self.monitoring_data['ai_conversations']}
• رسائل الرئيسي︙{self.monitoring_data['files_received']}
• عدد المستخدمين︙{len(self.monitoring_data.get('users', {}))}
• رسائل المستخدمين︙{self.monitoring_data['user_messages']}"""

            await update.message.reply_text(
                dashboard_message,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=self.get_reply_keyboard()
            )

        except Exception as e:
            logger.error(f"خطأ في عرض لوحة المراقبة: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في عرض لوحة المراقبة",
                reply_markup=self.get_reply_keyboard()
            )

    async def show_user_management(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض إدارة المستخدمين المتقدمة"""
        try:
            if self.user_management:
                await self.user_management.show_user_management_menu(update, context)
            else:
                await update.message.reply_text(
                    "❌ نظام إدارة المستخدمين غير متاح\n\n"
                    "يرجى التحقق من تثبيت المكونات المطلوبة",
                    reply_markup=self.get_reply_keyboard()
                )
        except Exception as e:
            self.logger.error(f"خطأ في معالجة زر إدارة المستخدمين: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في عرض إدارة المستخدمين\n"
                "يرجى المحاولة مرة أخرى أو التواصل مع المطور",
                reply_markup=self.get_reply_keyboard()
            )



    async def show_statistics(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض الإحصائيات"""
        await update.message.reply_text(
            "📈 **الإحصائيات والتقارير**\n\n"
            "هذه الميزة قيد التطوير...\n"
            "ستتضمن:\n"
            "• تقارير يومية وأسبوعية\n"
            "• رسوم بيانية\n"
            "• تحليلات متقدمة",
            parse_mode=ParseMode.MARKDOWN,
                reply_markup=self.get_reply_keyboard()
        )

    async def show_broadcast_management(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض إدارة النشرات"""
        await update.message.reply_text(
            "📰 **إدارة النشرات**\n\n"
            "هذه الميزة قيد التطوير...\n"
            "ستتضمن:\n"
            "• إرسال رسائل جماعية\n"
            "• جدولة الرسائل\n"
            "• إحصائيات الوصول",
            parse_mode=ParseMode.MARKDOWN,
                reply_markup=self.get_reply_keyboard()
        )

    async def show_backup_management(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض إدارة النسخ الاحتياطية"""
        await update.message.reply_text(
            "🗄️ **النسخ الاحتياطية**\n\n"
            "هذه الميزة قيد التطوير...\n"
            "ستتضمن:\n"
            "• إنشاء نسخ احتياطية\n"
            "• استعادة البيانات\n"
            "• جدولة النسخ التلقائية",
            parse_mode=ParseMode.MARKDOWN,
                reply_markup=self.get_reply_keyboard()
        )

    async def show_system_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض إعدادات النظام"""
        await update.message.reply_text(
            "⚙️ **إعدادات النظام**\n\n"
            "هذه الميزة قيد التطوير...\n"
            "ستتضمن:\n"
            "• إعدادات المراقبة\n"
            "• إعدادات الأمان\n"
            "• إعدادات الإشعارات",
            parse_mode=ParseMode.MARKDOWN,
                reply_markup=self.get_reply_keyboard()
        )

    async def show_alerts_management(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض إدارة التنبيهات"""
        await update.message.reply_text(
            "🚨 **التنبيهات والإنذارات**\n\n"
            "هذه الميزة قيد التطوير...\n"
            "ستتضمن:\n"
            "• إعداد التنبيهات\n"
            "• سجل الإنذارات\n"
            "• إشعارات مخصصة",
            parse_mode=ParseMode.MARKDOWN,
                reply_markup=self.get_reply_keyboard()
        )

    async def sync_data(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """مزامنة البيانات"""
        try:
            await update.message.reply_text("🔄 جاري مزامنة البيانات...")

            # إعادة تحميل البيانات
            self.load_data()

            await update.message.reply_text(
                "✅︙تم مزامنة البيانات بنجاح\n\n"
                "تم تحديث︙\n"
                "• بيانات المراقبة\n"
                "• بيانات الإدارة\n"
                "• الإحصائيات",
                reply_markup=self.get_reply_keyboard()
            )

        except Exception as e:
            logger.error(f"خطأ في مزامنة البيانات: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في مزامنة البيانات",
                reply_markup=self.get_reply_keyboard()
            )

    async def show_lists_management(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض إدارة القوائم"""
        await update.message.reply_text(
            "📋 **القوائم**\n\n"
            "هذه الميزة قيد التطوير...\n"
            "ستتضمن:\n"
            "• قوائم المستخدمين\n"
            "• قوائم الرسائل\n"
            "• قوائم الإحصائيات",
            parse_mode=ParseMode.MARKDOWN,
                reply_markup=self.get_reply_keyboard()
        )

    async def update_bot(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """تحديث البوت"""
        try:
            user = update.effective_user

            # إرسال رسالة التحديث
            loading_message = await update.message.reply_text("🔄 جاري عمل تحديث بوت الإدارة والمراقبة...")

            # إعادة تحميل البيانات
            self.load_data()

            # معالجة اسم المستخدم
            username = f"{user.first_name} {user.last_name}".strip() if user.last_name else user.first_name
            if not username:
                username = user.username or "مستخدم غير محدد"

            # حذف رسالة التحديث وإرسال رسالة النجاح
            await loading_message.delete()

            await update.message.reply_text(
                f"✅ تم تحديث بوت الإدارة والمراقبة بنجاح! شكراً {username}\n"
                "🔹 المراقبة محدثة\n"
                "🔹 الإدارة محدثة\n"
                "🔹 البيانات محدثة"
            )

            await update.message.reply_text(
                f"🛡️ مرحباً بك {username} في بوت الإدارة والمراقبة المحدث!\n\n"
                "جميع الوظائف محدثة وجاهزة للاستخدام",
                reply_markup=self.get_reply_keyboard()
            )

        except Exception as e:
            logger.error(f"خطأ في تحديث البوت: {e}")
            await update.message.reply_text("❌ حدث خطأ في التحديث")
    
    def setup_handlers(self):
        """إعداد معالجات الأوامر"""
        # الأوامر الإنجليزية
        self.app.add_handler(CommandHandler("start", self.start_command))
        self.app.add_handler(CommandHandler("admin", self.start_command))
        self.app.add_handler(CommandHandler("update", self.update_bot))

        # أوامر التشخيص (مؤقتة)
        self.app.add_handler(CommandHandler("clear_cache", self.clear_cache_command))
        self.app.add_handler(CommandHandler("diagnose", self.diagnose_command))
        self.app.add_handler(CommandHandler("cleanup", self.cleanup_command))
        self.app.add_handler(CommandHandler("force_unrestrict", self.force_unrestrict_command))

        # معالجات الأزرار السفلية
        self.app.add_handler(MessageHandler(filters.Regex("^📊 المراقبة المباشرة$"), self.start_command))
        self.app.add_handler(MessageHandler(filters.Regex("^👥 إدارة المستخدمين$"), self.show_user_management))
        self.app.add_handler(MessageHandler(filters.Regex("^📰 إدارة النشرات$"), self.placeholder_command))
        self.app.add_handler(MessageHandler(filters.Regex("^📈 الإحصائيات والتقارير$"), self.placeholder_command))
        self.app.add_handler(MessageHandler(filters.Regex("^⚙️ إعدادات النظام$"), self.system_settings_command))
        self.app.add_handler(MessageHandler(filters.Regex("^🚨 التنبيهات والإنذارات$"), self.placeholder_command))
        self.app.add_handler(MessageHandler(filters.Regex("^🎛️ المراقبة المركزية$"), self.central_monitoring_command))
        self.app.add_handler(MessageHandler(filters.Regex("^💰 محفظة إكسا$"), self.exa_wallet_command))
        self.app.add_handler(MessageHandler(filters.Regex("^📈 إدارة الإحصائيات$"), self.wallet_statistics_command))
        self.app.add_handler(MessageHandler(filters.Regex("^➕ إدارة الرصيد$"), self.add_balance_command))
        self.app.add_handler(MessageHandler(filters.Regex("^📊 إدارة التقارير$"), self.wallet_reports_command))
        self.app.add_handler(MessageHandler(filters.Regex("^💳 تقارير سلفني$"), self.loan_reports_command))

        # معالجات أزرار إدارة الرصيد الفرعية
        self.app.add_handler(MessageHandler(filters.Regex("^📋 تفاصيل المحفظة$"), self.wallet_details_button))
        self.app.add_handler(MessageHandler(filters.Regex("^➕ إضافة رصيد$"), self.add_balance_button))
        self.app.add_handler(MessageHandler(filters.Regex("^➖ خصم رصيد$"), self.deduct_balance_button))
        self.app.add_handler(MessageHandler(filters.Regex("^💳 إضافة سلفة$"), self.add_loan_button))
        self.app.add_handler(MessageHandler(filters.Regex("^💸 خصم سلفة$"), self.deduct_loan_button))
        self.app.add_handler(MessageHandler(filters.Regex("^📊 عرض المعاملات$"), self.show_transactions_button))
        self.app.add_handler(MessageHandler(filters.Regex("^💰 المعاملات الرصيد$"), self.balance_transactions_button))
        self.app.add_handler(MessageHandler(filters.Regex("^🏦 المعاملات السلفة$"), self.loan_transactions_button))

        # معالجات أزرار المراقبة المركزية
        self.app.add_handler(MessageHandler(filters.Regex("^🛡️ البوت إدارة ومراقبة$"), self.admin_bot_monitoring))
        self.app.add_handler(MessageHandler(filters.Regex("^🤖 البوت الرئيسي$"), self.show_main_bot_status))
        self.app.add_handler(MessageHandler(filters.Regex("^💻 نظام التشغيل العام$"), self.system_monitoring))
        self.app.add_handler(MessageHandler(filters.Regex("^🔙 العودة للقائمة الرئيسية$"), self.start_command))
        self.app.add_handler(MessageHandler(filters.Regex("^🔙 العودة للمراقبة المركزية$"), self.central_monitoring_command))
        self.app.add_handler(MessageHandler(filters.Regex("^🏠 القائمة الرئيسية$"), self.start_command))
        self.app.add_handler(MessageHandler(filters.Regex("^🔙 العودة لمحفظة إكسا$"), self.exa_wallet_command))

        # معالجات أوامر إدارة المحافظ
        self.app.add_handler(CommandHandler("wallet_info", self.wallet_info_command))
        self.app.add_handler(CommandHandler("add_balance", self.add_balance_wallet_command))
        self.app.add_handler(CommandHandler("deduct_balance", self.deduct_balance_command))
        self.app.add_handler(CommandHandler("balance_history", self.balance_history_command))
        self.app.add_handler(CommandHandler("loan_stats", self.get_loan_statistics))

        # معالجات أوامر إدارة السلف الجديدة
        self.app.add_handler(CommandHandler("add_loan_balance", self.add_loan_balance_command))
        self.app.add_handler(CommandHandler("deduct_loan_balance", self.deduct_loan_balance_command))

        # معالجات أزرار إعدادات النظام
        self.app.add_handler(MessageHandler(filters.Regex("^🗄️ النسخ الاحتياطية$"), self.backup_management_command))
        self.app.add_handler(MessageHandler(filters.Regex("^🔄 مزامنة البيانات$"), self.sync_management_command))
        self.app.add_handler(MessageHandler(filters.Regex("^🔧 إعدادات عامة$"), self.placeholder_command))
        self.app.add_handler(MessageHandler(filters.Regex("^🛠️ صيانة النظام$"), self.placeholder_command))
        self.app.add_handler(MessageHandler(filters.Regex("^📝 سجلات النظام$"), self.placeholder_command))
        self.app.add_handler(MessageHandler(filters.Regex("^🔐 الأمان والحماية$"), self.placeholder_command))
        self.app.add_handler(MessageHandler(filters.Regex("^🔙 العودة لإعدادات النظام$"), self.system_settings_command))

        # معالج الرسائل النصية
        self.app.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_message))

        # معالجات أزرار تأكيد إزالة المستخدمين (يجب أن تكون قبل معالج التقييد)
        from telegram.ext import CallbackQueryHandler
        self.app.add_handler(CallbackQueryHandler(self.handle_remove_users_callback, pattern="^(confirm_remove_users|cancel_remove_users)$"))

        # معالجات أزرار الإلغاء المتضمنة لإدارة المستخدمين
        self.app.add_handler(CallbackQueryHandler(self.handle_cancel_user_operations_callback, pattern="^cancel_(add_user|remove_user|ban_user|restrict_user|search_user|user_report)$"))

        # معالج الأزرار المضمنة لنظام التقييد
        self.app.add_handler(CallbackQueryHandler(self.handle_restriction_callback))

        # معالج الأزرار المضمنة لإدارة النماذج الذكية
        self.app.add_handler(CallbackQueryHandler(self.handle_ai_models_callback, pattern="^(models_|model_|config_|test_|toggle_status_)"))

        # معالج الأخطاء العام
        self.app.add_error_handler(self.error_handler)

        logger.info("✅ تم إعداد معالجات بوت الإدارة والمراقبة الموحد")

    async def handle_ai_models_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج الأزرار المضمنة لإدارة النماذج الذكية"""
        try:
            query = update.callback_query

            # التحقق من المدير
            if not self.is_admin(query.from_user.id):
                await query.answer("❌ غير مصرح لك باستخدام هذا البوت", show_alert=True)
                return

            await query.answer()  # إجابة سريعة لتجنب timeout

            callback_data = query.data
            logger.info(f"🤖 معالجة callback للنماذج الذكية: {callback_data}")

            # معالجة الأوامر المختلفة
            if callback_data == "models_menu":
                await self.ai_models_manager.show_models_menu(update, context)
            elif callback_data == "models_stats":
                await self.ai_models_manager.show_models_statistics(update, context)
            elif callback_data.startswith("model_") and not callback_data.startswith("model_start_") and not callback_data.startswith("model_stop_") and not callback_data.startswith("model_restart_") and not callback_data.startswith("model_config_") and not callback_data.startswith("model_test_"):
                # عرض تفاصيل نموذج محدد
                model_key = callback_data.replace("model_", "")
                await self.ai_models_manager.show_model_details(update, context, model_key)
            elif callback_data.startswith("toggle_status_"):
                # تبديل حالة النموذج مباشرة
                model_key = callback_data.replace("toggle_status_", "")
                await self.ai_models_manager.toggle_model_status_direct(update, context, model_key)
            elif callback_data.startswith("model_start_"):
                # تشغيل نموذج
                model_key = callback_data.replace("model_start_", "")
                await self.ai_models_manager.toggle_model_status(update, context, model_key, "start")
            elif callback_data.startswith("model_stop_"):
                # إيقاف نموذج
                model_key = callback_data.replace("model_stop_", "")
                await self.ai_models_manager.toggle_model_status(update, context, model_key, "stop")
            elif callback_data.startswith("model_restart_"):
                # إعادة تشغيل نموذج
                model_key = callback_data.replace("model_restart_", "")
                await self.ai_models_manager.toggle_model_status(update, context, model_key, "restart")
            elif callback_data.startswith("model_config_"):
                # عرض إعدادات نموذج
                model_key = callback_data.replace("model_config_", "")
                await self.ai_models_manager.show_model_config(update, context, model_key)
            elif callback_data.startswith("model_test_"):
                # اختبار نموذج
                model_key = callback_data.replace("model_test_", "")
                await self.ai_models_manager.test_model(update, context, model_key)
            elif callback_data.startswith("config_"):
                # تحديث إعدادات النموذج
                parts = callback_data.split("_")
                if len(parts) >= 4:
                    config_type = parts[1]
                    action = parts[2]
                    model_key = "_".join(parts[3:])
                    await self.ai_models_manager.update_model_config(update, context, model_key, config_type, action)
            elif callback_data.startswith("test_"):
                # تشغيل اختبار محدد
                parts = callback_data.split("_", 2)
                if len(parts) >= 3:
                    test_type = parts[1]
                    model_key = parts[2]
                    await self.ai_models_manager.run_model_test(update, context, model_key, test_type)
            elif callback_data == "back_to_main":
                await self.start_command(update, context)
            elif callback_data == "separator":
                # تجاهل النقر على الفاصل
                await query.answer("ℹ️ هذا مجرد فاصل للتنظيم")
            else:
                await query.answer("❌ أمر غير معروف", show_alert=True)

        except Exception as e:
            logger.error(f"❌ خطأ في معالجة callback النماذج الذكية: {e}")
            try:
                await update.callback_query.answer("❌ حدث خطأ في معالجة الطلب", show_alert=True)
            except:
                pass

    async def handle_restriction_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج الأزرار المضمنة لنظام التقييد"""
        try:
            query = update.callback_query

            # التحقق من المدير
            if not self.is_admin(query.from_user.id):
                await query.answer("❌ غير مصرح لك باستخدام هذا البوت", show_alert=True)
                return

            # التحقق من أن الطلب متعلق بنظام التقييد
            callback_data = query.data
            restriction_callbacks = [
                "toggle_restriction_", "remove_all_restrictions_",
                "apply_restrictions_", "cancel_restriction",
                "copy_name_", "copy_username_", "copy_userid_"
            ]

            is_restriction_callback = any(callback_data.startswith(prefix) for prefix in restriction_callbacks)

            if is_restriction_callback and self.user_management:
                if callback_data.startswith(("copy_name_", "copy_username_", "copy_userid_")):
                    await self.user_management.handle_copy_callback(update, context)
                else:
                    await self.user_management.handle_restriction_callback(update, context)
            else:
                await query.answer("❌ طلب غير معروف", show_alert=True)

        except Exception as e:
            logger.error(f"❌ خطأ في معالجة الزر المضمن: {e}")
            try:
                await update.callback_query.answer("❌ حدث خطأ في معالجة الطلب", show_alert=True)
            except:
                pass

    async def handle_remove_users_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج أزرار تأكيد إزالة المستخدمين"""
        try:
            query = update.callback_query
            logger.info(f"تم استلام callback_data: {query.data}")

            # التحقق من المدير
            if not self.is_admin(query.from_user.id):
                await query.answer("❌ غير مصرح لك باستخدام هذا البوت", show_alert=True)
                return

            # معالجة الزر المضغوط
            if query.data == "confirm_remove_users":
                logger.info("معالجة تأكيد إزالة المستخدمين")
                await self.user_management.processors.confirm_remove_users(update, context)
            elif query.data == "cancel_remove_users":
                logger.info("معالجة إلغاء إزالة المستخدمين")
                await self.user_management.processors.cancel_remove_users(update, context)
            else:
                logger.warning(f"callback_data غير معروف: {query.data}")
                await query.answer("❌ طلب غير معروف", show_alert=True)

        except Exception as e:
            logger.error(f"❌ خطأ في معالجة أزرار إزالة المستخدمين: {e}")
            import traceback
            traceback.print_exc()
            try:
                await update.callback_query.answer("❌ حدث خطأ في معالجة الطلب", show_alert=True)
            except:
                pass

    async def handle_cancel_user_operations_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج أزرار الإلغاء المتضمنة لعمليات إدارة المستخدمين"""
        try:
            query = update.callback_query
            logger.info(f"تم استلام callback_data للإلغاء: {query.data}")

            # التحقق من المدير
            if not self.is_admin(query.from_user.id):
                await query.answer("❌ غير مصرح لك باستخدام هذا البوت", show_alert=True)
                return

            # معالجة أزرار الإلغاء المختلفة
            if query.data == "cancel_add_user":
                await self.user_management.cancel_add_user(update, context)
            elif query.data == "cancel_remove_user":
                await self.user_management.cancel_remove_user(update, context)
            elif query.data == "cancel_ban_user":
                await self.user_management.cancel_ban_user(update, context)
            elif query.data == "cancel_restrict_user":
                await self.user_management.cancel_restrict_user(update, context)
            elif query.data == "cancel_search_user":
                await self.user_management.cancel_search_user(update, context)
            elif query.data == "cancel_user_report":
                await self.user_management.cancel_user_report(update, context)
            else:
                logger.warning(f"callback_data غير معروف للإلغاء: {query.data}")
                await query.answer("❌ طلب إلغاء غير معروف", show_alert=True)

        except Exception as e:
            logger.error(f"❌ خطأ في معالجة أزرار الإلغاء: {e}")
            import traceback
            traceback.print_exc()
            try:
                await update.callback_query.answer("❌ حدث خطأ في معالجة طلب الإلغاء", show_alert=True)
            except:
                pass

    async def clear_cache_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """أمر مسح التخزين المؤقت"""
        if not self.is_admin(update.effective_user.id):
            await update.message.reply_text("❌ غير مصرح لك باستخدام هذا الأمر")
            return

        if self.user_management:
            await self.user_management.clear_restrictions_cache(update, context)
        else:
            await update.message.reply_text("❌ نظام إدارة المستخدمين غير متاح")

    async def diagnose_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """أمر تشخيص المستخدم"""
        if not self.is_admin(update.effective_user.id):
            await update.message.reply_text("❌ غير مصرح لك باستخدام هذا الأمر")
            return

        # استخراج معرف المستخدم من الأمر
        if context.args and len(context.args) > 0:
            user_id = context.args[0]
            if self.user_management:
                await self.user_management.diagnose_user_restrictions(update, context, user_id)
            else:
                await update.message.reply_text("❌ نظام إدارة المستخدمين غير متاح")
        else:
            await update.message.reply_text(
                "❌ يرجى إدخال معرف المستخدم\n"
                "مثال: <code>/diagnose 123456789</code>",
                parse_mode=ParseMode.HTML)

    async def cleanup_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """أمر تنظيف البيانات"""
        if not self.is_admin(update.effective_user.id):
            await update.message.reply_text("❌ غير مصرح لك باستخدام هذا الأمر")
            return

        try:
            loading_msg = await update.message.reply_text("🧹 جاري تنظيف النظام...")

            # تنظيف من جانب الإدارة
            if self.user_management:
                await self.user_management.cleanup_empty_restrictions()
                await self.user_management.force_reload_restrictions()

            # تنظيف قوي من جانب البوت الرئيسي
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'main', 'features'))
            from ban_checker import ban_checker

            removed_count = ban_checker.force_clean_restrictions_file()

            # مسح التخزين المؤقت
            ban_checker.clear_cache()

            await loading_msg.edit_text(
                f"✅ **تم تنظيف النظام بنجاح**\n\n"
                f"📊 **النتائج:**\n"
                f"• تم إزالة {removed_count} مستخدم بقيود فارغة\n"
                f"• تم مسح التخزين المؤقت\n"
                f"• تم إرسال إشارة إعادة التحميل\n\n"
                f"🔄 **الحالة:** النظام جاهز للاستخدام",
                parse_mode=ParseMode.MARKDOWN
            )

            # إرسال رسالة منفصلة مع لوحة المفاتيح
            await update.message.reply_text(
                "✅ تم الانتهاء من التنظيف",
                reply_markup=self.get_reply_keyboard()
            )

        except Exception as e:
            logger.error(f"خطأ في تنظيف النظام: {e}")
            await update.message.reply_text(
                f"❌ خطأ في تنظيف النظام: {e}",
                reply_markup=self.get_reply_keyboard()
            )

    async def force_unrestrict_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """أمر إزالة قوية للقيود"""
        if not self.is_admin(update.effective_user.id):
            await update.message.reply_text("❌ غير مصرح لك باستخدام هذا الأمر")
            return

        try:
            # التحقق من وجود معرف المستخدم
            if not context.args:
                await update.message.reply_text(
                    "❌ يرجى تحديد معرف المستخدم\n"
                    "مثال: <code>/force_unrestrict 123456789</code>",
                reply_markup=self.get_reply_keyboard()
                )
                return

            user_id = context.args[0]
            loading_msg = await update.message.reply_text(f"🔄 جاري إزالة جميع القيود عن المستخدم {user_id}...")

            # إزالة من الإدارة
            removed_from_admin = False
            try:
                if self.user_management and hasattr(self.user_management, 'user_manager'):
                    restricted_users = self.user_management.user_manager.load_restricted_users()
                    if user_id in restricted_users:
                        del restricted_users[user_id]
                        self.user_management.user_manager.save_restricted_users(restricted_users)
                        removed_from_admin = True
            except Exception as e:
                logger.error(f"خطأ في إزالة من الإدارة: {e}")

            # إزالة من ملف البوت الرئيسي مباشرة
            import os
            import json
            base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            restrictions_file = os.path.join(base_dir, "data", "restricted_users.json")

            removed_from_main = False
            if os.path.exists(restrictions_file):
                try:
                    with open(restrictions_file, 'r', encoding='utf-8') as f:
                        main_data = json.load(f)

                    if user_id in main_data:
                        del main_data[user_id]
                        removed_from_main = True

                        with open(restrictions_file, 'w', encoding='utf-8') as f:
                            json.dump(main_data, f, ensure_ascii=False, indent=2)

                        logger.info(f"تم حذف المستخدم {user_id} من ملف البوت الرئيسي")
                except Exception as e:
                    logger.error(f"خطأ في تعديل ملف البوت الرئيسي: {e}")

            # مسح التخزين المؤقت
            if self.user_management:
                await self.user_management.clear_restrictions_cache(update, context)

            # إرسال إشارة للبوت الرئيسي لإعادة التحميل
            try:
                import sys
                sys.path.append(os.path.join(base_dir, 'main', 'features'))
                from ban_checker import ban_checker
                ban_checker.clear_cache()
                ban_checker.force_clean_restrictions_file()
            except Exception as e:
                logger.error(f"خطأ في مسح تخزين البوت الرئيسي: {e}")

            # رسالة النتيجة
            result_msg = f"✅ **تم إلغاء جميع القيود عن المستخدم {user_id}**\n\n"
            result_msg += f"📊 **التفاصيل:**\n"
            result_msg += f"• الإدارة: {'✅ تم الحذف' if removed_from_admin else '❌ لم يكن موجود'}\n"
            result_msg += f"• البوت الرئيسي: {'✅ تم الحذف' if removed_from_main else '❌ لم يكن موجود'}\n"
            result_msg += f"• التخزين المؤقت: ✅ تم المسح\n\n"
            result_msg += f"🔄 **الحالة:** المستخدم حر تماماً الآن"

            await loading_msg.edit_text(result_msg, parse_mode=ParseMode.MARKDOWN)

            # إرسال رسالة منفصلة مع لوحة المفاتيح
            await update.message.reply_text(
                "✅ تم الانتهاء من إزالة القيود",
                reply_markup=self.get_reply_keyboard()
            )

        except Exception as e:
            logger.error(f"خطأ في إزالة القيود: {e}")
            await update.message.reply_text(
                f"❌ خطأ في إزالة القيود: {e}",
                reply_markup=self.get_reply_keyboard()
            )

    async def central_monitoring_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض المراقبة المركزية"""
        try:
            # التحقق من المدير
            if not self.is_admin(update.effective_user.id):
                await update.message.reply_text("❌ غير مصرح لك بالوصول")
                return

            # إنشاء لوحة مفاتيح المراقبة المركزية
            keyboard = [
                ["🛡️ البوت إدارة ومراقبة", "🤖 البوت الرئيسي"],
                ["💻 نظام التشغيل العام"],
                ["🔙 العودة للقائمة الرئيسية"]
            ]
            reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=False)

            message = """🎛️︙المراقبة المركزية

📊︙اختر النظام المراد مراقبته︙

🛡️︙البوت إدارة ومراقبة︙مراقبة حالة بوت الإدارة والمراقبة
🤖︙البوت الرئيسي︙مراقبة حالة البوت الرئيسي للمستخدمين
💻︙نظام التشغيل العام︙مراقبة حالة الخادم والنظام

📈︙معلومات شاملة عن الأداء والحالة لكل نظام"""

            await update.message.reply_text(
                message,
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"❌ خطأ في المراقبة المركزية: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في عرض المراقبة المركزية",
                reply_markup=self.get_reply_keyboard()
            )

    async def exa_wallet_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض محفظة إكسا"""
        try:
            # التحقق من المدير
            if not self.is_admin(update.effective_user.id):
                await update.message.reply_text("❌ غير مصرح لك بالوصول")
                return

            message = """💰︙محفظة إكسا

💎︙سعر العملة ︙ 1 إكسا = 3 دولار
💎︙مقابلها توكين︙1 إكسا = 524,288 الف توكين

🏦︙إدارة المحافظ︙
• إدارة الرصيد للمستخدمين للخدمات المدفوعة
• إدارة التقارير لتتبع كل مستخدم
• تقارير سلفني لتتبع طلبات الخدمة
• إدارة الإحصائيات الشاملة للنظام المالي

👇︙اختر الخدمة المطلوبة من الأزرار أدناه"""

            # إنشاء لوحة مفاتيح خاصة بمحفظة إكسا
            from telegram import ReplyKeyboardMarkup
            exa_wallet_keyboard = [
                ["➕ إدارة الرصيد", "📊 إدارة التقارير"],
                ["💳 تقارير سلفني", "📈 إدارة الإحصائيات"],
                ["🤖 إحصائيات التوكن", "💳 إدارة الديون"],
                ["🔙 العودة للقائمة الرئيسية"]
            ]
            reply_markup = ReplyKeyboardMarkup(exa_wallet_keyboard, resize_keyboard=True, one_time_keyboard=False)

            await update.message.reply_text(
                message,
                reply_markup=reply_markup
            )

            # إعادة تعيين الأوامر الافتراضية عند دخول محفظة إكسا
            await self.reset_default_commands()

        except Exception as e:
            logger.error(f"❌ خطأ في محفظة إكسا: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في عرض محفظة إكسا",
                reply_markup=self.get_reply_keyboard()
            )

    async def wallet_statistics_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """إدارة الإحصائيات"""
        try:
            # التحقق من المدير
            if not self.is_admin(update.effective_user.id):
                await update.message.reply_text("❌ غير مصرح لك بالوصول")
                return

            # استدعاء دالة إدارة الإحصائيات من واجهة إدارة المستخدمين
            await self.user_management.show_wallet_statistics(update, context)

        except Exception as e:
            logger.error(f"❌ خطأ في إدارة الإحصائيات: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في عرض إدارة الإحصائيات",
                reply_markup=self.get_reply_keyboard()
            )

    async def add_balance_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """إدارة الرصيد للمستخدمين"""
        try:
            # التحقق من المدير
            if not self.is_admin(update.effective_user.id):
                await update.message.reply_text("❌ غير مصرح لك بالوصول")
                return

            message = """➕︙إدارة الرصيد للمستخدمين

💎︙سعر العملة ︙ 1 إكسا = 3 دولار
💎︙مقابلها توكين︙1 إكسا = 524,288 الف توكين

🔍︙طرق التعامل مع المحافظ :
💰︙رقم المحفظة︙👤︙الاسم︙📧︙المعرف︙🆔︙الأيدي︙

💰︙العمليات المتاحة︙
استخدم الأزرار أدناه لتنفيذ العمليات بسهولة
بدلاً من كتابة الأوامر يدوياً

👇︙اختر العملية المطلوبة من الأزرار أدناه"""

            # إنشاء لوحة مفاتيح إدارة الرصيد
            from telegram import ReplyKeyboardMarkup
            balance_management_keyboard = [
                ["📋 تفاصيل المحفظة", "➕ إضافة رصيد"],
                ["➖ خصم رصيد", "💳 إضافة سلفة"],
                ["💸 خصم سلفة", "📊 عرض المعاملات"],
                ["💰 المعاملات الرصيد", "🏦 المعاملات السلفة"],
                ["🔙 العودة لمحفظة إكسا"]
            ]
            reply_markup = ReplyKeyboardMarkup(balance_management_keyboard, resize_keyboard=True, one_time_keyboard=False)

            await update.message.reply_text(
                message,
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"❌ خطأ في إدارة الرصيد: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في عرض إدارة الرصيد",
                reply_markup=self.get_reply_keyboard()
            )

    async def wallet_reports_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """إدارة التقارير"""
        try:
            # التحقق من المدير
            if not self.is_admin(update.effective_user.id):
                await update.message.reply_text("❌ غير مصرح لك بالوصول")
                return

            # الحصول على بيانات المحافظ
            from shared.database.wallet_manager import WalletManager
            wallet_manager = WalletManager()
            wallets_data = wallet_manager.load_wallets_data()

            if not wallets_data:
                await update.message.reply_text(
                    "📊︙إدارة التقارير\n\n❌ لا توجد محافظ مسجلة في النظام",
                reply_markup=self.get_reply_keyboard()
                )
                return

            # إحصائيات عامة
            total_wallets = len(wallets_data)
            active_wallets = sum(1 for w in wallets_data.values() if w.get('status') == 'active')
            total_balance = sum(float(w.get('balance', 0)) for w in wallets_data.values())
            total_transactions = sum(int(w.get('transaction_count', 0)) for w in wallets_data.values())

            # تنسيق الأرقام بدون عشري إذا كانت صحيحة
            balance_display = f"{total_balance:.0f}" if total_balance == int(total_balance) else f"{total_balance:.2f}"
            dollar_value = total_balance * 3
            dollar_display = f"{dollar_value:.0f}" if dollar_value == int(dollar_value) else f"{dollar_value:.2f}"
            token_value = total_balance * 524288
            token_display = f"{token_value:.0f}" if token_value == int(token_value) else f"{token_value:.2f}"

            message = f"""📊︙إدارة التقارير

💎︙سعر العملة ︙ 1 إكسا = 3 دولار
💎︙مقابلها توكين︙1 إكسا = 524,288 الف توكين

📈︙الإحصائيات العامة︙
🏦︙إجمالي المحافظ︙{total_wallets}
✅︙المحافظ النشطة︙{active_wallets}
❌︙المحافظ المعطلة︙{total_wallets - active_wallets}
💰︙إجمالي الأرصدة︙{balance_display} إكسا
🔄︙إجمالي المعاملات︙{total_transactions}

💵︙القيمة بالدولار︙{dollar_display} USD
🪙︙القيمة بالتوكين︙{token_display} توكين

📋︙لعرض تفاصيل محفظة معينة︙
أرسل: `/wallet_info رقم_المحفظة`

📊︙لعرض أعلى المحافظ رصيداً︙
أرسل: `/top_wallets`"""

            # إنشاء لوحة مفاتيح العودة
            from telegram import ReplyKeyboardMarkup
            back_keyboard = [
                ["🔙 العودة لمحفظة إكسا"]
            ]
            reply_markup = ReplyKeyboardMarkup(back_keyboard, resize_keyboard=True, one_time_keyboard=False)

            await update.message.reply_text(
                message,
                reply_markup=reply_markup,
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            logger.error(f"❌ خطأ في إدارة التقارير: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في عرض إدارة التقارير",
                reply_markup=self.get_reply_keyboard()
            )

    async def loan_reports_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """تقارير سلفني"""
        try:
            # التحقق من المدير
            if not self.is_admin(update.effective_user.id):
                await update.message.reply_text("❌ غير مصرح لك بالوصول")
                return

            from shared.database.wallet_manager import WalletManager
            wallet_manager = WalletManager()

            loan_stats = wallet_manager.get_loan_statistics()

            if loan_stats:
                total_active = loan_stats.get('total_active_loans', 0)
                total_amount = loan_stats.get('total_loan_amount', 0)
                total_granted = loan_stats.get('total_loans_granted', 0)
                total_paid = loan_stats.get('total_loans_paid', 0)
                average_amount = loan_stats.get('average_loan_amount', 0)

                # تنسيق الأرقام
                total_amount_display = f"{total_amount:.0f}" if total_amount == int(total_amount) else f"{total_amount:.2f}"
                average_display = f"{average_amount:.0f}" if average_amount == int(average_amount) else f"{average_amount:.2f}"

                # حساب معدل السداد
                payment_rate = (total_paid/total_granted*100) if total_granted > 0 else 0

                message = f"""💳︙تقارير سلفني

💎︙سعر العملة ︙ 1 إكسا = 3 دولار
💎︙مقابلها توكين︙1 إكسا = 524,288 الف توكين

📊︙إحصائيات خدمة سلفني︙
🔄︙إجمالي السلف الممنوحة︙{total_granted}
✅︙السلف المسددة︙{total_paid}
💳︙السلف النشطة︙{total_active}
📈︙معدل السداد︙{payment_rate:.1f}%

💰︙الإحصائيات المالية︙
💵︙إجمالي المبالغ النشطة︙{total_amount_display} إكسا
💵︙القيمة بالدولار︙{total_amount * 3:.0f} USD
📈︙متوسط السلفة︙{average_display} إكسا

👥︙المستخدمون الذين لديهم سلف نشط︙{total_active}"""

                # عرض المستخدمين الذين لديهم سلف نشط
                users_with_loans = loan_stats.get('users_with_active_loans', [])
                if users_with_loans:
                    message += "\n\n🔝︙أكبر السلف النشطة︙\n"
                    # ترتيب المستخدمين حسب مبلغ السلف
                    sorted_users = sorted(users_with_loans, key=lambda x: x.get('loan_amount', 0), reverse=True)
                    for i, user_loan in enumerate(sorted_users[:5], 1):  # أول 5 مستخدمين
                        loan_amount = user_loan.get('loan_amount', 0)
                        loan_display = f"{loan_amount:.0f}" if loan_amount == int(loan_amount) else f"{loan_amount:.2f}"
                        wallet_number = user_loan.get('wallet_number', 'غير محدد')
                        message += f"{i}. المحفظة {wallet_number} - {loan_display} إكسا\n"

                    if len(users_with_loans) > 5:
                        message += f"... و {len(users_with_loans) - 5} مستخدم آخر"

                message += f"""

⏰︙آخر تحديث︙{loan_stats.get('last_updated', 'غير محدد')}"""

            else:
                message = """💳︙تقارير سلفني

💎︙سعر العملة ︙ 1 إكسا = 3 دولار
💎︙مقابلها توكين︙1 إكسا = 524,288 الف توكين

📊︙إحصائيات خدمة سلفني︙
🔄︙إجمالي السلف الممنوحة︙0
✅︙السلف المسددة︙0
💳︙السلف النشطة︙0
📈︙معدل السداد︙0%

💰︙الإحصائيات المالية︙
💵︙إجمالي المبالغ النشطة︙0 إكسا
💵︙القيمة بالدولار︙0 USD
📈︙متوسط السلفة︙0 إكسا

💡︙ملاحظة︙
لم يتم منح أي سلف حتى الآن"""

            # إنشاء لوحة مفاتيح العودة
            from telegram import ReplyKeyboardMarkup
            back_keyboard = [
                ["🔙 العودة لمحفظة إكسا"]
            ]
            reply_markup = ReplyKeyboardMarkup(back_keyboard, resize_keyboard=True, one_time_keyboard=False)

            await update.message.reply_text(
                message,
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"❌ خطأ في تقارير سلفني: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في عرض تقارير سلفني",
                reply_markup=self.get_reply_keyboard()
            )

    async def setup_wallet_commands(self):
        """إعداد قائمة أوامر خاصة بإدارة المحافظ (معطلة)"""
        try:
            # إخفاء جميع الأوامر - الاعتماد على الأزرار فقط
            wallet_commands = []

            await self.app.bot.set_my_commands(wallet_commands)
            logger.info("✅ تم إخفاء قائمة أوامر إدارة المحافظ - الاعتماد على الأزرار فقط")

        except Exception as e:
            logger.error(f"❌ خطأ في إخفاء أوامر المحافظ: {e}")

    async def reset_default_commands(self):
        """إعادة تعيين الأوامر الافتراضية (معطلة)"""
        try:
            # إخفاء جميع الأوامر - الاعتماد على الأزرار فقط
            default_commands = []

            await self.app.bot.set_my_commands(default_commands)
            logger.info("✅ تم إخفاء الأوامر الافتراضية - الاعتماد على الأزرار فقط")

        except Exception as e:
            logger.error(f"❌ خطأ في إخفاء الأوامر الافتراضية: {e}")

    async def wallet_info_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض تفاصيل المحفظة"""
        try:
            # التحقق من المدير
            if not self.is_admin(update.effective_user.id):
                await update.message.reply_text("❌ غير مصرح لك بالوصول")
                return

            # التحقق من وجود رقم المحفظة
            if not context.args:
                await update.message.reply_text(
                    "❌ يرجى إدخال رقم المحفظة\n"
                    "مثال: `/wallet_info 9091234567`", parse_mode=ParseMode.MARKDOWN)
                return

            wallet_number = context.args[0]

            # الحصول على معلومات المحفظة
            from shared.database.wallet_manager import WalletManager
            wallet_manager = WalletManager()
            wallet_info = wallet_manager.get_wallet_by_number(wallet_number)

            if not wallet_info:
                await update.message.reply_text(f"❌ المحفظة {wallet_number} غير موجودة")
                return

            # عرض تفاصيل المحفظة
            balance = wallet_info.get('balance', 0)
            currency = wallet_info.get('currency', 'إكسا')
            status = wallet_info.get('status', 'غير محدد')
            created_at = wallet_info.get('created_at', 'غير محدد')
            transaction_count = wallet_info.get('transaction_count', 0)
            user_name = wallet_info.get('user_name', 'غير محدد')
            username = wallet_info.get('username', 'غير محدد')

            # تنسيق الأرقام بدون عشري إذا كانت صحيحة
            balance_display = f"{balance:.0f}" if balance == int(balance) else f"{balance:.2f}"
            dollar_value = balance * 3
            dollar_display = f"{dollar_value:.0f}" if dollar_value == int(dollar_value) else f"{dollar_value:.2f}"
            token_value = balance * 524288

            # تنسيق التوكين حسب العدد
            if token_value >= 1000000:
                token_display = f"{token_value/1000000:.0f}" if (token_value/1000000) == int(token_value/1000000) else f"{token_value/1000000:.2f}"
                token_unit = "مليون توكين"
            elif token_value >= 1000:
                token_display = f"{token_value/1000:.0f}" if (token_value/1000) == int(token_value/1000) else f"{token_value/1000:.2f}"
                token_unit = "الف توكين"
            else:
                token_display = f"{token_value:.0f}" if token_value == int(token_value) else f"{token_value:.2f}"
                token_unit = "توكين"

            # تحديد حالة المحفظة بالعربية
            status_ar = "نشط" if status == "active" else "معطل" if status == "inactive" else status

            # تنسيق الأرقام بدون عشري
            balance_display_clean = f"{balance:.0f}" if balance == int(balance) else f"{balance:.2f}"
            dollar_display_clean = f"{dollar_value:.0f}" if dollar_value == int(dollar_value) else f"{dollar_value:.2f}"

            # تنسيق اسم المستخدم (إزالة @ المزدوجة)
            if not username:
                username_display = "@غير_محدد"
            else:
                # إزالة @ المتعددة من البداية
                username_clean = username.lstrip('@')
                # إضافة @ واحدة فقط
                username_display = f"@{username_clean}" if username_clean else "@غير_محدد"

            message = f"""💰︙تفاصيل المحفظة

🏦︙رقم المحفظة︙{wallet_number}
👤︙اسم︙{user_name}
📧︙المعرف︙{username_display}
💳︙الرصيد الحالي︙{balance_display_clean} إكسا
📊︙حالة المحفظة︙{status_ar}
📅︙تاريخ الإنشاء︙{created_at}
🔄︙عدد المعاملات︙{transaction_count}

💵︙القيمة بالدولار︙{dollar_display_clean} USD
🪙︙القيمة بالتوكين︙{token_display} {token_unit}"""

            await update.message.reply_text(message)

        except Exception as e:
            logger.error(f"❌ خطأ في عرض تفاصيل المحفظة: {e}")
            await update.message.reply_text("❌ حدث خطأ في عرض تفاصيل المحفظة")

    async def add_balance_wallet_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """إضافة رصيد للمحفظة"""
        try:
            # التحقق من المدير
            if not self.is_admin(update.effective_user.id):
                await update.message.reply_text("❌ غير مصرح لك بالوصول")
                return

            # التحقق من المعاملات
            if len(context.args) < 2:
                await update.message.reply_text(
                    "❌ يرجى إدخال رقم المحفظة والمبلغ\n"
                    "مثال: `/add_balance 9091234567 50`", parse_mode=ParseMode.MARKDOWN)
                return

            wallet_number = context.args[0]
            try:
                amount = float(context.args[1])
            except ValueError:
                await update.message.reply_text("❌ المبلغ يجب أن يكون رقماً صحيحاً")
                return

            if amount <= 0:
                await update.message.reply_text("❌ المبلغ يجب أن يكون أكبر من صفر")
                return

            # إضافة الرصيد
            from shared.database.wallet_manager import WalletManager
            wallet_manager = WalletManager()

            # الحصول على الرصيد الحالي
            wallet_info = wallet_manager.get_wallet_by_number(wallet_number)
            if not wallet_info:
                await update.message.reply_text(f"❌ المحفظة {wallet_number} غير موجودة")
                return

            current_balance = wallet_info.get('balance', 0)
            new_balance = current_balance + amount

            # فحص وجود سلف قبل إضافة الرصيد
            wallet_info_before = wallet_manager.get_wallet_by_number(wallet_number)
            has_loan_before = wallet_info_before.get('has_active_loan', False)
            loan_amount_before = wallet_info_before.get('loan_amount', 0.0)

            # تحديث الرصيد مع معالجة السلف تلقائياً
            success, loan_deduction_data = wallet_manager.update_wallet_balance(wallet_number, new_balance, "admin_add")

            if success:
                # توليد رقم إشعار إداري يبدأ بـ 808 (للإدارة)
                admin_notification_id = f"808{random.randint(1000000, 9999999)}"

                # توليد رقم فاتورة منفصل للمستخدم يبدأ بـ 502 (فاتورة مستخدمين عامة)
                user_invoice_id = f"502{random.randint(1000000, 9999999)}"

                # الحصول على الرصيد النهائي بعد خصم السلف (إن وجد)
                updated_wallet_info = wallet_manager.get_wallet_by_number(wallet_number)
                final_balance = updated_wallet_info.get('balance', new_balance)

                # تحديد الرصيد السابق الصحيح ونوعه
                if has_loan_before and loan_amount_before > 0:
                    # إذا كان هناك سلف نشط، نعرض مبلغ السلف كرصيد سابق
                    display_previous_balance = loan_amount_before
                    balance_type_display = " (سلفة)"
                else:
                    # إذا لم يكن هناك سلف، نعرض الرصيد الفعلي بدون كلمة "رصيد"
                    display_previous_balance = current_balance
                    balance_type_display = ""

                # تنسيق الأرقام بدون عشري إذا كانت صحيحة
                amount_display = f"{amount:.0f}" if amount == int(amount) else f"{amount:.2f}"
                previous_display = f"{display_previous_balance:.0f}" if display_previous_balance == int(display_previous_balance) else f"{display_previous_balance:.2f}"
                final_display = f"{final_balance:.0f}" if final_balance == int(final_balance) else f"{final_balance:.2f}"
                dollar_value = amount * 3
                dollar_display = f"{dollar_value:.0f}" if dollar_value == int(dollar_value) else f"{dollar_value:.2f}"
                token_value = amount * 524288

                # تنسيق التوكين حسب العدد
                if token_value >= 1000000:
                    token_display = f"{token_value/1000000:.0f}" if (token_value/1000000) == int(token_value/1000000) else f"{token_value/1000000:.2f}"
                    token_unit = "مليون توكين"
                elif token_value >= 1000:
                    token_display = f"{token_value/1000:.0f}" if (token_value/1000) == int(token_value/1000) else f"{token_value/1000:.2f}"
                    token_unit = "الف توكين"
                else:
                    token_display = f"{token_value:.0f}" if token_value == int(token_value) else f"{token_value:.2f}"
                    token_unit = "توكين"

                # إنشاء رسالة الإشعار الإداري باستخدام النظام الموحد
                if invoice_message_manager:
                    notification_data = {
                        'transaction_type': 'إضافة رصيد',
                        'notification_id': admin_notification_id,
                        'wallet_number': wallet_number,
                        'amount': amount,
                        'previous_balance': current_balance,
                        'new_balance': final_balance
                    }
                    message = invoice_message_manager.create_admin_notification_message(notification_data)
                else:
                    # النظام القديم كاحتياطي
                    message = f"""✅︙تم إضافة الرصيد بنجاح
🔢︙رقم الإشعار︙{admin_notification_id}

🏦︙رقم المحفظة︙{wallet_number}
💰︙الرصيد المضاف︙{amount_display} إكسا
📊︙الرصيد السابق︙{previous_display} إكسا{balance_type_display}
💳︙الرصيد النهائي︙{final_display} إكسا

💵︙القيمة المضافة بالدولار︙{dollar_display} USD
🪙︙القيمة المضافة بالتوكين︙{token_display} {token_unit}"""

                # إضافة معلومات خصم السلف إن وجد
                if loan_deduction_data:
                    deducted_amount = loan_deduction_data.get('deducted_amount', 0)
                    remaining_loan = loan_deduction_data.get('remaining_loan', 0)
                    is_fully_paid = loan_deduction_data.get('is_fully_paid', False)

                    deducted_display = f"{deducted_amount:.0f}" if deducted_amount == int(deducted_amount) else f"{deducted_amount:.2f}"
                    remaining_display = f"{remaining_loan:.0f}" if remaining_loan == int(remaining_loan) else f"{remaining_loan:.2f}"

                    message += f"""

💳︙خصم السلفة التلقائي︙
💰︙المبلغ المخصوم︙{deducted_display} إكسا
📊︙المتبقي من السلفة︙{remaining_display} إكسا
✅︙حالة السلفة︙{"مسددة بالكامل" if is_fully_paid else "جزئياً مسددة"}"""

                await update.message.reply_text(message)

                # إنشاء وإرسال فاتورة PDF مدمجة مع النص (باستخدام رقم الفاتورة المنفصل)
                await self.create_and_send_combined_invoice(wallet_info, amount, "إضافة رصيد", current_balance, final_balance, user_invoice_id)

                # إنشاء فاتورة خصم السلف إن وجد (باستخدام رقم فاتورة منفصل)
                if loan_deduction_data:
                    loan_deduction_invoice_id = f"305{random.randint(1000000, 9999999)}"  # فاتورة عملية تلقائية
                    await self.create_loan_deduction_invoice(wallet_info, loan_deduction_data, loan_deduction_invoice_id)
            else:
                await update.message.reply_text("❌ فشل في إضافة الرصيد")

        except Exception as e:
            logger.error(f"❌ خطأ في إضافة الرصيد: {e}")
            await update.message.reply_text("❌ حدث خطأ في إضافة الرصيد")

    async def send_balance_invoice_to_user(self, wallet_info, amount, notification_id, old_balance, new_balance):
        """إرسال فاتورة إلكترونية للمستخدم عند إضافة الرصيد"""
        try:
            user_id = wallet_info.get('user_id')
            if not user_id:
                logger.error("لا يمكن إرسال الفاتورة - معرف المستخدم غير موجود")
                return

            # إنشاء الفاتورة الإلكترونية
            invoice_data = await self.create_balance_invoice(wallet_info, amount, notification_id, old_balance, new_balance)

            # تنسيق الأرقام بشكل صحيح
            amount_display = f"{amount:.0f}" if amount == int(amount) else f"{amount:.2f}"
            old_balance_display = f"{old_balance:.0f}" if old_balance == int(old_balance) else f"{old_balance:.2f}"
            new_balance_display = f"{new_balance:.0f}" if new_balance == int(new_balance) else f"{new_balance:.2f}"
            dollar_value = amount * 3
            dollar_display = f"{dollar_value:.0f}" if dollar_value == int(dollar_value) else f"{dollar_value:.2f}"

            # حساب التوكين
            token_value = amount * 524288
            if token_value >= 1000000:
                token_display = f"{token_value/1000000:.0f}" if (token_value/1000000) == int(token_value/1000000) else f"{token_value/1000000:.2f}"
                token_unit = "مليون توكين"
            elif token_value >= 1000:
                token_display = f"{token_value/1000:.0f}" if (token_value/1000) == int(token_value/1000) else f"{token_value/1000:.2f}"
                token_unit = "الف توكين"
            else:
                token_display = f"{token_value:.0f}" if token_value == int(token_value) else f"{token_value:.2f}"
                token_unit = "توكين"

            # إرسال رسالة نصية أولاً (باستخدام رقم الفاتورة المنفصل)
            invoice_message = f"""🧾︙فاتورة رصيد إلكترونية
🧾︙نوع العملية︙اضافة رصيد
🔢︙رقم الفاتورة︙{notification_id}

👤︙العميل︙{wallet_info.get('user_name', 'غير محدد')}
🏦︙رقم المحفظة︙{wallet_info['wallet_number']}

💰︙الرصيد المضاف︙{amount_display} إكسا
📊︙الرصيد السابق︙{old_balance_display} إكسا
💳︙الرصيد الجديد︙{new_balance_display} إكسا

💵︙القيمة بالدولار︙{dollar_display} USD
🪙︙القيمة بالتوكين︙{token_display} {token_unit}

📅︙تاريخ العملية︙{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🏢︙مقدم الخدمة︙نظام المحفظة الإلكترونية

✅︙تم إضافة الرصيد بنجاح - شكراً لك على استخدام خدماتنا"""

            # حفظ الفاتورة في ملف مشترك ليقرأها البوت الرئيسي (بدون ملفات)
            await self.save_invoice_for_main_bot(user_id, invoice_message, notification_id, None)

            logger.info(f"✅ تم إرسال فاتورة إضافة الرصيد رقم {notification_id} للمستخدم {user_id} ({wallet_info.get('user_name', 'غير محدد')})")
            logger.info(f"📋 تفاصيل الفاتورة: المبلغ {amount_display} إكسا - المحفظة {wallet_info['wallet_number']}")

        except Exception as e:
            logger.error(f"❌ خطأ في إرسال الفاتورة للمستخدم: {e}")

    async def save_invoice_for_main_bot(self, user_id, invoice_message, notification_id, invoice_files=None):
        """حفظ الفاتورة في ملف مشترك ليقرأها البوت الرئيسي"""
        try:
            import json
            import os

            # مجلد الفواتير المشتركة
            invoices_dir = os.path.join(os.path.dirname(__file__), '..', 'shared', 'invoices')
            os.makedirs(invoices_dir, exist_ok=True)

            # ملف الفواتير المعلقة
            pending_invoices_file = os.path.join(invoices_dir, 'pending_invoices.json')

            # قراءة الفواتير المعلقة الحالية
            pending_invoices = {}
            if os.path.exists(pending_invoices_file):
                try:
                    with open(pending_invoices_file, 'r', encoding='utf-8') as f:
                        pending_invoices = json.load(f)
                except:
                    pending_invoices = {}

            # إضافة الفاتورة الجديدة
            invoice_entry = {
                'invoice_id': notification_id,
                'message': invoice_message,
                'timestamp': datetime.now().isoformat(),
                'status': 'pending'
            }

            # إضافة مسارات الملفات إذا كانت متوفرة
            if invoice_files and invoice_files.get('success'):
                invoice_entry['pdf_path'] = invoice_files.get('pdf_path')
                invoice_entry['png_path'] = invoice_files.get('png_path')
                invoice_entry['has_files'] = True
            else:
                invoice_entry['has_files'] = False

            pending_invoices[str(user_id)] = invoice_entry

            # حفظ الفواتير المحدثة
            with open(pending_invoices_file, 'w', encoding='utf-8') as f:
                json.dump(pending_invoices, f, ensure_ascii=False, indent=2)

            logger.info(f"💾 تم حفظ فاتورة {notification_id} للمستخدم {user_id} في الملف المشترك")

        except Exception as e:
            logger.error(f"❌ خطأ في حفظ الفاتورة في الملف المشترك: {e}")

    async def create_balance_invoice(self, wallet_info, amount, notification_id, old_balance, new_balance):
        """إنشاء بيانات الفاتورة الإلكترونية"""
        try:
            from datetime import datetime

            invoice_data = {
                'invoice_id': notification_id,
                'wallet_number': wallet_info['wallet_number'],
                'user_name': wallet_info.get('user_name', 'غير محدد'),
                'amount_added': amount,
                'old_balance': old_balance,
                'new_balance': new_balance,
                'dollar_value': amount * 3,
                'token_value': amount * 524288,
                'transaction_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'currency': 'إكسا',
                'transaction_type': 'إضافة رصيد'
            }

            return invoice_data

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء بيانات الفاتورة: {e}")
            return {}

    async def deduct_balance_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """خصم رصيد من المحفظة"""
        try:
            # التحقق من المدير
            if not self.is_admin(update.effective_user.id):
                await update.message.reply_text("❌ غير مصرح لك بالوصول")
                return

            # التحقق من المعاملات
            if len(context.args) < 2:
                await update.message.reply_text(
                    "❌ يرجى إدخال رقم المحفظة والمبلغ\n"
                    "مثال: `/deduct_balance 9091234567 50`", parse_mode=ParseMode.MARKDOWN)
                return

            wallet_number = context.args[0]
            try:
                amount = float(context.args[1])
            except ValueError:
                await update.message.reply_text("❌ المبلغ يجب أن يكون رقماً صحيحاً")
                return

            if amount <= 0:
                await update.message.reply_text("❌ المبلغ يجب أن يكون أكبر من صفر")
                return

            # خصم الرصيد
            from shared.database.wallet_manager import WalletManager
            wallet_manager = WalletManager()

            # الحصول على الرصيد الحالي
            wallet_info = wallet_manager.get_wallet_by_number(wallet_number)
            if not wallet_info:
                await update.message.reply_text(f"❌ المحفظة {wallet_number} غير موجودة")
                return

            current_balance = wallet_info.get('balance', 0)

            if current_balance < amount:
                # تنسيق الأرقام بدون عشري إذا كانت صحيحة
                current_display = f"{current_balance:.0f}" if current_balance == int(current_balance) else f"{current_balance:.2f}"
                amount_display = f"{amount:.0f}" if amount == int(amount) else f"{amount:.2f}"

                await update.message.reply_text(
                    f"❌ الرصيد غير كافي\n"
                    f"الرصيد الحالي: {current_display} إكسا\n"
                    f"المبلغ المطلوب: {amount_display} إكسا"
                )
                return

            new_balance = current_balance - amount

            # تحديث الرصيد
            success = wallet_manager.update_wallet_balance(wallet_number, new_balance, "admin_deduct")

            if success:
                # توليد رقم إشعار يبدأ بـ 808 و 7 خانات عشوائية
                notification_id = f"808{random.randint(1000000, 9999999)}"

                # تنسيق الأرقام بدون عشري إذا كانت صحيحة
                amount_display = f"{amount:.0f}" if amount == int(amount) else f"{amount:.2f}"
                current_display = f"{current_balance:.0f}" if current_balance == int(current_balance) else f"{current_balance:.2f}"
                new_display = f"{new_balance:.0f}" if new_balance == int(new_balance) else f"{new_balance:.2f}"
                dollar_value = amount * 3
                dollar_display = f"{dollar_value:.0f}" if dollar_value == int(dollar_value) else f"{dollar_value:.2f}"
                token_value = amount * 524288

                # تنسيق التوكين حسب العدد
                if token_value >= 1000000:
                    token_display = f"{token_value/1000000:.0f}" if (token_value/1000000) == int(token_value/1000000) else f"{token_value/1000000:.2f}"
                    token_unit = "مليون توكين"
                elif token_value >= 1000:
                    token_display = f"{token_value/1000:.0f}" if (token_value/1000) == int(token_value/1000) else f"{token_value/1000:.2f}"
                    token_unit = "الف توكين"
                else:
                    token_display = f"{token_value:.0f}" if token_value == int(token_value) else f"{token_value:.2f}"
                    token_unit = "توكين"

                message = f"""✅︙تم خصم الرصيد بنجاح
🔢︙رقم الإشعار︙{notification_id}

🏦︙رقم المحفظة︙{wallet_number}
💰︙المبلغ المخصوم︙{amount_display} إكسا
📊︙الرصيد السابق︙{current_display} إكسا
💳︙الرصيد الجديد︙{new_display} إكسا

💵︙القيمة المخصومة بالدولار︙{dollar_display} USD
🪙︙القيمة المخصومة بالتوكين︙{token_display} {token_unit}"""

                await update.message.reply_text(message)

                # إنشاء رقم فاتورة منفصل للمستخدم (يبدأ بـ 502)
                user_invoice_id = f"502{random.randint(1000000, 9999999)}"

                # إنشاء وإرسال فاتورة خصم مدمجة مع النص (باستخدام رقم الفاتورة المنفصل)
                await self.create_and_send_combined_deduct_invoice(wallet_info, amount, "خصم رصيد", current_balance, new_balance, user_invoice_id)
            else:
                await update.message.reply_text("❌ فشل في خصم الرصيد")

        except Exception as e:
            logger.error(f"❌ خطأ في خصم الرصيد: {e}")
            await update.message.reply_text("❌ حدث خطأ في خصم الرصيد")

    async def send_deduct_invoice_to_user(self, wallet_info, amount, notification_id, old_balance, new_balance):
        """إرسال فاتورة إلكترونية للمستخدم عند خصم الرصيد"""
        try:
            user_id = wallet_info.get('user_id')
            if not user_id:
                logger.error("لا يمكن إرسال الفاتورة - معرف المستخدم غير موجود")
                return

            # إنشاء الفاتورة الإلكترونية
            invoice_data = await self.create_deduct_invoice(wallet_info, amount, notification_id, old_balance, new_balance)

            # تنسيق الأرقام بشكل صحيح
            amount_display = f"{amount:.0f}" if amount == int(amount) else f"{amount:.2f}"
            old_balance_display = f"{old_balance:.0f}" if old_balance == int(old_balance) else f"{old_balance:.2f}"
            new_balance_display = f"{new_balance:.0f}" if new_balance == int(new_balance) else f"{new_balance:.2f}"
            dollar_value = amount * 3
            dollar_display = f"{dollar_value:.0f}" if dollar_value == int(dollar_value) else f"{dollar_value:.2f}"

            # حساب التوكين
            token_value = amount * 524288
            if token_value >= 1000000:
                token_display = f"{token_value/1000000:.0f}" if (token_value/1000000) == int(token_value/1000000) else f"{token_value/1000000:.2f}"
                token_unit = "مليون توكين"
            elif token_value >= 1000:
                token_display = f"{token_value/1000:.0f}" if (token_value/1000) == int(token_value/1000) else f"{token_value/1000:.2f}"
                token_unit = "الف توكين"
            else:
                token_display = f"{token_value:.0f}" if token_value == int(token_value) else f"{token_value:.2f}"
                token_unit = "توكين"

            # إرسال رسالة نصية أولاً
            invoice_message = f"""🧾︙فاتورة رصيد إلكترونية
🧾︙نوع العملية︙خصم رصيد
🔢︙رقم الفاتورة︙{notification_id}

👤︙العميل︙{wallet_info.get('user_name', 'غير محدد')}
🏦︙رقم المحفظة︙{wallet_info['wallet_number']}

💰︙الرصيد المخصوم︙{amount_display} إكسا
📊︙الرصيد السابق︙{old_balance_display} إكسا
💳︙الرصيد الجديد︙{new_balance_display} إكسا

💵︙القيمة بالدولار︙{dollar_display} USD
🪙︙القيمة بالتوكين︙{token_display} {token_unit}

📅︙تاريخ العملية︙{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🏢︙مقدم الخدمة︙نظام المحفظة الإلكترونية

✅︙تم خصم الرصيد بنجاح - شكراً لك على استخدام خدماتنا"""

            # حفظ الفاتورة في ملف مشترك ليقرأها البوت الرئيسي (بدون ملفات)
            await self.save_invoice_for_main_bot(user_id, invoice_message, notification_id, None)

            logger.info(f"✅ تم إرسال فاتورة خصم الرصيد رقم {notification_id} للمستخدم {user_id} ({wallet_info.get('user_name', 'غير محدد')})")
            logger.info(f"📋 تفاصيل الفاتورة: المبلغ {amount_display} إكسا - المحفظة {wallet_info['wallet_number']}")

        except Exception as e:
            logger.error(f"❌ خطأ في إرسال فاتورة الخصم للمستخدم: {e}")

    async def create_and_send_combined_deduct_invoice(self, wallet_info, amount, transaction_type, previous_balance, new_balance, user_invoice_id):
        """إنشاء وإرسال فاتورة خصم مدمجة (نص + PDF + أزرار) للمستخدم"""
        try:
            from shared.invoices.invoice_manager import InvoiceManager

            # إنشاء مدير الفواتير
            invoice_manager = InvoiceManager()

            # التحقق من وجود ملف PDF أولاً (باستخدام رقم فاتورة المستخدم)
            pdf_exists, existing_pdf_path = invoice_manager.check_pdf_exists(user_invoice_id)

            if pdf_exists:
                # استخدام الملف الموجود
                success = True
                pdf_path = existing_pdf_path
                invoice_number = user_invoice_id
                logger.info(f"✅ استخدام ملف PDF موجود للفاتورة {user_invoice_id}")
            else:
                # إنشاء الفاتورة باستخدام رقم فاتورة المستخدم المنفصل
                success, pdf_path, invoice_number = invoice_manager.create_transaction_invoice(
                    wallet_number=wallet_info['wallet_number'],
                    user_name=wallet_info.get('user_name', 'غير محدد'),
                    amount=amount,
                    transaction_type=transaction_type,
                    previous_balance=previous_balance,
                    new_balance=new_balance,
                    invoice_number=user_invoice_id  # تمرير رقم فاتورة المستخدم المنفصل
                )
                logger.info(f"✅ تم إنشاء ملف PDF جديد للفاتورة {user_invoice_id}")

            if success and pdf_path and os.path.exists(pdf_path):
                # إنشاء نص فاتورة الخصم (باستخدام رقم فاتورة المستخدم)
                user_id = wallet_info['user_id']
                invoice_message = await self.create_deduct_invoice_message_text(wallet_info, amount, user_invoice_id, previous_balance, new_balance)

                # حفظ معلومات فاتورة الخصم المدمجة لإرسالها عبر البوت الرئيسي
                await self.save_combined_invoice_for_main_bot(
                    user_id=user_id,
                    pdf_path=pdf_path,
                    invoice_number=invoice_number,
                    notification_id=user_invoice_id,  # استخدام رقم فاتورة المستخدم
                    transaction_type=transaction_type,
                    amount=amount,
                    wallet_number=wallet_info['wallet_number'],
                    user_name=wallet_info.get('user_name', 'غير محدد'),
                    previous_balance=previous_balance,
                    new_balance=new_balance,
                    invoice_message=invoice_message
                )

                logger.info(f"✅ تم إنشاء فاتورة خصم مدمجة رقم {invoice_number} للمستخدم {user_id}")
            else:
                logger.error(f"❌ فشل في إنشاء فاتورة PDF للمستخدم {wallet_info['user_id']}")

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء وإرسال فاتورة خصم مدمجة: {e}")

    async def create_and_send_combined_invoice(self, wallet_info, amount, transaction_type, previous_balance, new_balance, user_invoice_id):
        """إنشاء وإرسال فاتورة مدمجة (نص + PDF + أزرار) للمستخدم"""
        try:
            from shared.invoices.invoice_manager import InvoiceManager

            # إنشاء مدير الفواتير
            invoice_manager = InvoiceManager()

            # التحقق من وجود ملف PDF أولاً (باستخدام رقم الفاتورة المنفصل)
            pdf_exists, existing_pdf_path = invoice_manager.check_pdf_exists(user_invoice_id)

            if pdf_exists:
                # استخدام الملف الموجود
                success = True
                pdf_path = existing_pdf_path
                invoice_number = user_invoice_id
                logger.info(f"✅ استخدام ملف PDF موجود للفاتورة {user_invoice_id}")
            else:
                # إنشاء الفاتورة باستخدام رقم الفاتورة المنفصل (وليس رقم الإشعار)
                success, pdf_path, invoice_number = invoice_manager.create_transaction_invoice(
                    wallet_number=wallet_info['wallet_number'],
                    user_name=wallet_info.get('user_name', 'غير محدد'),
                    amount=amount,
                    transaction_type=transaction_type,
                    previous_balance=previous_balance,
                    new_balance=new_balance,
                    invoice_number=user_invoice_id  # رقم الفاتورة المنفصل (502xxxxxxx)
                )
                logger.info(f"✅ تم إنشاء ملف PDF جديد للفاتورة {user_invoice_id} (رقم فاتورة منفصل)")

            if success and pdf_path and os.path.exists(pdf_path):
                # إنشاء نص الفاتورة (باستخدام رقم الفاتورة المنفصل)
                user_id = wallet_info['user_id']
                invoice_message = await self.create_invoice_message_text(wallet_info, amount, user_invoice_id, previous_balance, new_balance)

                # حفظ معلومات الفاتورة المدمجة لإرسالها عبر البوت الرئيسي
                await self.save_combined_invoice_for_main_bot(
                    user_id=user_id,
                    pdf_path=pdf_path,
                    invoice_number=invoice_number,
                    notification_id=user_invoice_id,  # استخدام رقم الفاتورة المنفصل
                    transaction_type=transaction_type,
                    amount=amount,
                    wallet_number=wallet_info['wallet_number'],
                    user_name=wallet_info.get('user_name', 'غير محدد'),
                    previous_balance=previous_balance,
                    new_balance=new_balance,
                    invoice_message=invoice_message
                )

                logger.info(f"✅ تم إنشاء فاتورة مدمجة رقم {invoice_number} للمستخدم {user_id}")
            else:
                logger.error(f"❌ فشل في إنشاء فاتورة PDF للمستخدم {wallet_info['user_id']}")

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء وإرسال فاتورة مدمجة: {e}")

    async def create_and_send_pdf_invoice(self, wallet_info, amount, transaction_type, previous_balance, new_balance, notification_id):
        """إنشاء وإرسال فاتورة PDF للمستخدم"""
        try:
            from shared.invoices.invoice_manager import InvoiceManager

            # إنشاء مدير الفواتير
            invoice_manager = InvoiceManager()

            # إنشاء الفاتورة باستخدام نفس رقم الفاتورة من الإشعار
            success, pdf_path, invoice_number = invoice_manager.create_transaction_invoice(
                wallet_number=wallet_info['wallet_number'],
                user_name=wallet_info.get('user_name', 'غير محدد'),
                amount=amount,
                transaction_type=transaction_type,
                previous_balance=previous_balance,
                new_balance=new_balance,
                invoice_number=notification_id  # تمرير رقم الفاتورة من الإشعار
            )

            if success and pdf_path and os.path.exists(pdf_path):
                # حفظ معلومات الفاتورة لإرسالها عبر البوت الرئيسي
                await self.save_pdf_invoice_for_main_bot(
                    user_id=wallet_info['user_id'],
                    pdf_path=pdf_path,
                    invoice_number=invoice_number,
                    notification_id=notification_id,
                    transaction_type=transaction_type,
                    amount=amount
                )

                logger.info(f"✅ تم إنشاء فاتورة PDF رقم {invoice_number} للمستخدم {wallet_info['user_id']}")
            else:
                logger.error(f"❌ فشل في إنشاء فاتورة PDF للمستخدم {wallet_info['user_id']}")

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء وإرسال فاتورة PDF: {e}")

    async def save_pdf_invoice_for_main_bot(self, user_id, pdf_path, invoice_number, notification_id, transaction_type, amount):
        """حفظ معلومات فاتورة PDF لإرسالها عبر البوت الرئيسي"""
        try:
            import json
            import os

            # مجلد الفواتير المشتركة
            invoices_dir = os.path.join(os.path.dirname(__file__), '..', 'shared', 'invoices')
            os.makedirs(invoices_dir, exist_ok=True)

            # ملف الفواتير المعلقة
            pending_invoices_file = os.path.join(invoices_dir, 'pending_invoices.json')

            # قراءة الفواتير المعلقة الحالية
            pending_invoices = {}
            if os.path.exists(pending_invoices_file):
                try:
                    with open(pending_invoices_file, 'r', encoding='utf-8') as f:
                        pending_invoices = json.load(f)
                except:
                    pending_invoices = {}

            # إضافة معلومات فاتورة PDF
            pdf_invoice_data = {
                'user_id': user_id,
                'pdf_path': pdf_path,
                'invoice_number': invoice_number,
                'notification_id': notification_id,
                'transaction_type': transaction_type,
                'amount': amount,
                'created_at': datetime.now().isoformat(),
                'status': 'pending_pdf_send',
                'type': 'pdf_invoice'
            }

            # استخدام رقم الإشعار كمفتاح للفاتورة PDF
            pdf_key = f"PDF_{notification_id}"
            pending_invoices[pdf_key] = pdf_invoice_data

            # حفظ الملف
            with open(pending_invoices_file, 'w', encoding='utf-8') as f:
                json.dump(pending_invoices, f, ensure_ascii=False, indent=2)

            logger.info(f"✅ تم حفظ معلومات فاتورة PDF {invoice_number} للمستخدم {user_id}")

        except Exception as e:
            logger.error(f"❌ خطأ في حفظ معلومات فاتورة PDF: {e}")

    async def create_invoice_message_text(self, wallet_info, amount, notification_id, old_balance, new_balance):
        """إنشاء نص رسالة الفاتورة"""
        try:
            from datetime import datetime

            # تنسيق الأرقام بشكل صحيح
            amount_display = f"{amount:.0f}" if amount == int(amount) else f"{amount:.2f}"
            old_balance_display = f"{old_balance:.0f}" if old_balance == int(old_balance) else f"{old_balance:.2f}"
            new_balance_display = f"{new_balance:.0f}" if new_balance == int(new_balance) else f"{new_balance:.2f}"
            dollar_value = amount * 3
            dollar_display = f"{dollar_value:.0f}" if dollar_value == int(dollar_value) else f"{dollar_value:.2f}"
            token_value = amount * 524.288  # بالآلاف

            # إنشاء نص الفاتورة باستخدام النظام الموحد
            if invoice_message_manager:
                invoice_data = {
                    'transaction_type': 'إضافة رصيد',
                    'invoice_number': notification_id,
                    'user_name': wallet_info.get('user_name', 'غير محدد'),
                    'wallet_number': wallet_info['wallet_number'],
                    'amount': amount,
                    'previous_balance': old_balance,
                    'new_balance': new_balance,
                    'date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
                invoice_message = invoice_message_manager.create_balance_invoice_message(invoice_data)
            else:
                # النظام القديم كاحتياطي
                # تنسيق التوكن
                if token_value >= 1000:  # مليون أو أكثر
                    token_display = f"{token_value/1000:.2f} مليون توكين"
                else:
                    token_display = f"{token_value:.2f} ألف توكين"

                invoice_message = f"""🧾︙فاتورة رصيد إلكترونية
🧾︙نوع العملية︙إضافة رصيد
🔢︙رقم الفاتورة︙{notification_id}

👤︙العميل︙{wallet_info.get('user_name', 'غير محدد')}
🏦︙رقم المحفظة︙{wallet_info['wallet_number']}

💰︙الرصيد المضاف︙{amount_display} إكسا
📊︙الرصيد السابق︙{old_balance_display} إكسا
💳︙الرصيد الجديد︙{new_balance_display} إكسا

💵︙القيمة بالدولار︙{dollar_display} USD
🪙︙القيمة بالتوكين︙{token_display}

📅︙تاريخ العملية︙{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🏢︙مقدم الخدمة︙نظام المحفظة الإلكترونية

✅︙تم إضافة الرصيد بنجاح - شكراً لك على استخدام خدماتنا"""

            return invoice_message

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء نص الفاتورة: {e}")
            return "خطأ في إنشاء نص الفاتورة"

    async def create_deduct_invoice_message_text(self, wallet_info, amount, notification_id, old_balance, new_balance):
        """إنشاء نص رسالة فاتورة الخصم"""
        try:
            from datetime import datetime

            # تنسيق الأرقام بشكل صحيح
            amount_display = f"{amount:.0f}" if amount == int(amount) else f"{amount:.2f}"
            old_balance_display = f"{old_balance:.0f}" if old_balance == int(old_balance) else f"{old_balance:.2f}"
            new_balance_display = f"{new_balance:.0f}" if new_balance == int(new_balance) else f"{new_balance:.2f}"
            dollar_value = amount * 3
            dollar_display = f"{dollar_value:.0f}" if dollar_value == int(dollar_value) else f"{dollar_value:.2f}"
            token_value = amount * 524.288  # بالآلاف

            # إنشاء نص فاتورة الخصم باستخدام النظام الموحد
            if invoice_message_manager:
                invoice_data = {
                    'transaction_type': 'خصم رصيد',
                    'invoice_number': notification_id,
                    'user_name': wallet_info.get('user_name', 'غير محدد'),
                    'wallet_number': wallet_info['wallet_number'],
                    'amount': amount,
                    'previous_balance': old_balance,
                    'new_balance': new_balance,
                    'date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
                invoice_message = invoice_message_manager.create_balance_invoice_message(invoice_data)
            else:
                # النظام القديم كاحتياطي
                # تنسيق التوكن
                if token_value >= 1000:  # مليون أو أكثر
                    token_display = f"{token_value/1000:.2f} مليون توكين"
                else:
                    token_display = f"{token_value:.2f} ألف توكين"

                invoice_message = f"""🧾︙فاتورة رصيد إلكترونية
🧾︙نوع العملية︙خصم رصيد
🔢︙رقم الفاتورة︙{notification_id}

👤︙العميل︙{wallet_info.get('user_name', 'غير محدد')}
🏦︙رقم المحفظة︙{wallet_info['wallet_number']}

💰︙المبلغ المخصوم︙{amount_display} إكسا
📊︙الرصيد السابق︙{old_balance_display} إكسا
💳︙الرصيد الجديد︙{new_balance_display} إكسا

💵︙القيمة بالدولار︙{dollar_display} USD
🪙︙القيمة بالتوكين︙{token_display}

📅︙تاريخ العملية︙{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🏢︙مقدم الخدمة︙نظام المحفظة الإلكترونية

✅︙تم خصم الرصيد بنجاح - شكراً لك على استخدام خدماتنا"""

            return invoice_message

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء نص فاتورة الخصم: {e}")
            return "خطأ في إنشاء نص فاتورة الخصم"

    async def save_combined_invoice_for_main_bot(self, user_id, pdf_path, invoice_number, notification_id,
                                               transaction_type, amount, wallet_number, user_name,
                                               previous_balance, new_balance, invoice_message):
        """حفظ معلومات فاتورة مدمجة لإرسالها عبر البوت الرئيسي"""
        try:
            import json
            import os
            from datetime import datetime

            # مجلد الفواتير المشتركة
            invoices_dir = os.path.join(os.path.dirname(__file__), '..', 'shared', 'invoices')
            os.makedirs(invoices_dir, exist_ok=True)

            # ملف الفواتير المعلقة
            pending_invoices_file = os.path.join(invoices_dir, 'pending_invoices.json')

            # قراءة الفواتير المعلقة الحالية
            pending_invoices = {}
            if os.path.exists(pending_invoices_file):
                try:
                    with open(pending_invoices_file, 'r', encoding='utf-8') as f:
                        pending_invoices = json.load(f)
                except:
                    pending_invoices = {}

            # إضافة معلومات الفاتورة المدمجة
            combined_invoice_data = {
                'user_id': user_id,
                'pdf_path': pdf_path,
                'invoice_number': invoice_number,
                'notification_id': notification_id,
                'transaction_type': transaction_type,
                'amount': amount,
                'wallet_number': wallet_number,
                'user_name': user_name,
                'previous_balance': previous_balance,
                'new_balance': new_balance,
                'usd_value': amount * 3,
                'date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'message': invoice_message,
                'has_files': True,
                'created_at': datetime.now().isoformat(),
                'status': 'pending_combined_send',
                'type': 'combined_invoice'
            }

            # استخدام رقم الإشعار كمفتاح للفاتورة المدمجة
            combined_key = f"COMBINED_{notification_id}"
            pending_invoices[combined_key] = combined_invoice_data

            # حفظ الملف
            with open(pending_invoices_file, 'w', encoding='utf-8') as f:
                json.dump(pending_invoices, f, ensure_ascii=False, indent=2)

            logger.info(f"✅ تم حفظ معلومات فاتورة مدمجة {invoice_number} للمستخدم {user_id}")

        except Exception as e:
            logger.error(f"❌ خطأ في حفظ معلومات فاتورة مدمجة: {e}")

    async def generate_invoice_files(self, invoice_data, operation_type):
        """إنشاء ملفات الفاتورة - تم إزالة نظام الفواتير"""
        try:
            logger.info(f"📄 نظام الفواتير تم إزالته - تخطي إنشاء ملفات {operation_type}")

            return {
                'pdf_path': None,
                'png_path': None,
                'success': False
            }

        except Exception as e:
            logger.error(f"❌ خطأ في دالة الفواتير المحذوفة: {e}")
            return {
                'pdf_path': None,
                'png_path': None,
                'success': False
            }

    async def create_deduct_invoice(self, wallet_info, amount, notification_id, old_balance, new_balance):
        """إنشاء بيانات فاتورة خصم الرصيد"""
        try:
            from datetime import datetime

            invoice_data = {
                'invoice_id': notification_id,
                'wallet_number': wallet_info['wallet_number'],
                'user_name': wallet_info.get('user_name', 'غير محدد'),
                'amount_deducted': amount,
                'old_balance': old_balance,
                'new_balance': new_balance,
                'dollar_value': amount * 3,
                'token_value': amount * 524288,
                'transaction_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'currency': 'إكسا',
                'transaction_type': 'خصم رصيد'
            }

            return invoice_data

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء بيانات فاتورة الخصم: {e}")
            return {}

    async def add_loan_balance_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """إضافة رصيد سلفة للمحفظة"""
        try:
            # التحقق من المدير
            if not self.is_admin(update.effective_user.id):
                await update.message.reply_text("❌ غير مصرح لك بالوصول")
                return

            # التحقق من المعاملات
            if len(context.args) < 2:
                await update.message.reply_text(
                    "❌ يرجى إدخال رقم المحفظة ومبلغ السلفة\n"
                    "مثال: `/add_loan_balance 9091234567 30`", parse_mode=ParseMode.MARKDOWN)
                return

            wallet_number = context.args[0]
            try:
                loan_amount = float(context.args[1])
            except ValueError:
                await update.message.reply_text("❌ مبلغ السلفة يجب أن يكون رقماً صحيحاً")
                return

            if loan_amount <= 0:
                await update.message.reply_text("❌ مبلغ السلفة يجب أن يكون أكبر من صفر")
                return

            # التحقق من وجود المحفظة
            from shared.database.wallet_manager import WalletManager
            wallet_manager = WalletManager()

            wallet_info = wallet_manager.get_wallet_by_number(wallet_number)
            if not wallet_info:
                await update.message.reply_text("❌ المحفظة غير موجودة")
                return

            # إضافة رصيد السلفة
            success, message_text, transaction_data = wallet_manager.add_loan_balance(wallet_number, loan_amount)

            if success:
                # توليد رقم إشعار يبدأ بـ 809 و 7 خانات عشوائية
                notification_id = f"809{random.randint(1000000, 9999999)}"

                # تنسيق الأرقام بدون عشري إذا كانت صحيحة
                amount_display = f"{loan_amount:.0f}" if loan_amount == int(loan_amount) else f"{loan_amount:.2f}"
                old_balance_display = f"{transaction_data['old_balance']:.0f}" if transaction_data['old_balance'] == int(transaction_data['old_balance']) else f"{transaction_data['old_balance']:.2f}"
                new_balance_display = f"{transaction_data['new_balance']:.0f}" if transaction_data['new_balance'] == int(transaction_data['new_balance']) else f"{transaction_data['new_balance']:.2f}"
                old_loan_display = f"{transaction_data['old_loan_amount']:.0f}" if transaction_data['old_loan_amount'] == int(transaction_data['old_loan_amount']) else f"{transaction_data['old_loan_amount']:.2f}"
                new_loan_display = f"{transaction_data['new_loan_amount']:.0f}" if transaction_data['new_loan_amount'] == int(transaction_data['new_loan_amount']) else f"{transaction_data['new_loan_amount']:.2f}"

                dollar_value = loan_amount * 3
                dollar_display = f"{dollar_value:.0f}" if dollar_value == int(dollar_value) else f"{dollar_value:.2f}"
                token_value = loan_amount * 524288

                # تنسيق التوكن
                if token_value >= 1000:  # مليون أو أكثر
                    token_display = f"{token_value/1000:.2f} مليون توكين"
                else:
                    token_display = f"{token_value:.2f} ألف توكين"

                message = f"""✅︙تم إضافة رصيد السلفة بنجاح
🔢︙رقم الإشعار︙{notification_id}

👤︙العميل︙{wallet_info.get('user_name', 'غير محدد')}
🏦︙رقم المحفظة︙{wallet_number}

💰︙مبلغ السلفة المضاف︙{amount_display} إكسا
📊︙الرصيد السابق︙{old_balance_display} إكسا
💳︙الرصيد الجديد︙{new_balance_display} إكسا

💸︙السلف السابق︙{old_loan_display} إكسا
💸︙إجمالي السلف︙{new_loan_display} إكسا

💵︙القيمة بالدولار︙{dollar_display} USD
🪙︙القيمة بالتوكين︙{token_display}

📅︙تاريخ العملية︙{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🏢︙مقدم الخدمة︙نظام المحفظة الإلكترونية"""

                await update.message.reply_text(message)

                # إنشاء وإرسال فاتورة PDF
                await self.create_and_send_loan_add_invoice(wallet_info, transaction_data, notification_id)

                # إرسال إشعار تلقائي للمدير
                await self.send_loan_operation_notification(wallet_info, transaction_data, "إضافة رصيد سلفة")

                # إرسال إشعار وفاتورة للمستخدم
                await self.send_user_loan_notification_with_invoice(wallet_info, transaction_data, "إضافة رصيد سلفة")
            else:
                await update.message.reply_text(f"❌ {message_text}")

        except Exception as e:
            logger.error(f"❌ خطأ في إضافة رصيد السلفة: {e}")
            await update.message.reply_text("❌ حدث خطأ في إضافة رصيد السلفة")

    async def deduct_loan_balance_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """خصم رصيد سلفة من المحفظة"""
        try:
            # التحقق من المدير
            if not self.is_admin(update.effective_user.id):
                await update.message.reply_text("❌ غير مصرح لك بالوصول")
                return

            # التحقق من المعاملات
            if len(context.args) < 2:
                await update.message.reply_text(
                    "❌ يرجى إدخال رقم المحفظة ومبلغ السلفة المخصوم\n"
                    "مثال: `/deduct_loan_balance 9091234567 20`", parse_mode=ParseMode.MARKDOWN)
                return

            wallet_number = context.args[0]
            try:
                deduct_amount = float(context.args[1])
            except ValueError:
                await update.message.reply_text("❌ مبلغ السلفة المخصوم يجب أن يكون رقماً صحيحاً")
                return

            if deduct_amount <= 0:
                await update.message.reply_text("❌ مبلغ السلفة المخصوم يجب أن يكون أكبر من صفر")
                return

            # التحقق من وجود المحفظة
            from shared.database.wallet_manager import WalletManager
            wallet_manager = WalletManager()

            wallet_info = wallet_manager.get_wallet_by_number(wallet_number)
            if not wallet_info:
                await update.message.reply_text("❌ المحفظة غير موجودة")
                return

            # خصم رصيد السلفة
            success, message_text, transaction_data = wallet_manager.deduct_loan_balance(wallet_number, deduct_amount)

            if success:
                # توليد رقم إشعار يبدأ بـ 810 و 7 خانات عشوائية
                notification_id = f"810{random.randint(1000000, 9999999)}"

                # تنسيق الأرقام بدون عشري إذا كانت صحيحة
                amount_display = f"{deduct_amount:.0f}" if deduct_amount == int(deduct_amount) else f"{deduct_amount:.2f}"
                old_balance_display = f"{transaction_data['old_balance']:.0f}" if transaction_data['old_balance'] == int(transaction_data['old_balance']) else f"{transaction_data['old_balance']:.2f}"
                new_balance_display = f"{transaction_data['new_balance']:.0f}" if transaction_data['new_balance'] == int(transaction_data['new_balance']) else f"{transaction_data['new_balance']:.2f}"
                old_loan_display = f"{transaction_data['old_loan_amount']:.0f}" if transaction_data['old_loan_amount'] == int(transaction_data['old_loan_amount']) else f"{transaction_data['old_loan_amount']:.2f}"
                new_loan_display = f"{transaction_data['new_loan_amount']:.0f}" if transaction_data['new_loan_amount'] == int(transaction_data['new_loan_amount']) else f"{transaction_data['new_loan_amount']:.2f}"

                dollar_value = deduct_amount * 3
                dollar_display = f"{dollar_value:.0f}" if dollar_value == int(dollar_value) else f"{dollar_value:.2f}"
                token_value = deduct_amount * 524288

                # تنسيق التوكن
                if token_value >= 1000:  # مليون أو أكثر
                    token_display = f"{token_value/1000:.2f} مليون توكين"
                else:
                    token_display = f"{token_value:.2f} ألف توكين"

                # تحديد حالة السلف
                loan_status = "مسددة بالكامل" if transaction_data.get('is_fully_paid', False) else "جزئياً مسددة"

                message = f"""✅︙تم خصم رصيد السلفة بنجاح
🔢︙رقم الإشعار︙{notification_id}

👤︙العميل︙{wallet_info.get('user_name', 'غير محدد')}
🏦︙رقم المحفظة︙{wallet_number}

💰︙مبلغ السلفة المخصوم︙{amount_display} إكسا
📊︙الرصيد السابق︙{old_balance_display} إكسا
💳︙الرصيد الجديد︙{new_balance_display} إكسا

💸︙السلف السابق︙{old_loan_display} إكسا
💸︙السلف المتبقي︙{new_loan_display} إكسا
✅︙حالة السلفة︙{loan_status}

💵︙القيمة بالدولار︙{dollar_display} USD
🪙︙القيمة بالتوكين︙{token_display}

📅︙تاريخ العملية︙{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🏢︙مقدم الخدمة︙نظام المحفظة الإلكترونية"""

                await update.message.reply_text(message)

                # إنشاء وإرسال فاتورة PDF
                await self.create_and_send_loan_deduct_invoice(wallet_info, transaction_data, notification_id)

                # إرسال إشعار تلقائي للمدير
                await self.send_loan_operation_notification(wallet_info, transaction_data, "خصم رصيد سلفة")

                # إرسال إشعار وفاتورة للمستخدم
                await self.send_user_loan_notification_with_invoice(wallet_info, transaction_data, "خصم رصيد سلفة")
            else:
                await update.message.reply_text(f"❌ {message_text}")

        except Exception as e:
            logger.error(f"❌ خطأ في خصم رصيد السلفة: {e}")
            await update.message.reply_text("❌ حدث خطأ في خصم رصيد السلفة")

    async def create_and_send_loan_add_invoice(self, wallet_info, transaction_data, notification_id):
        """إنشاء وإرسال فاتورة إضافة رصيد سلفة"""
        try:
            from shared.invoices.invoice_manager import InvoiceManager

            # إنشاء مدير الفواتير
            invoice_manager = InvoiceManager()

            # توليد رقم فاتورة لإضافة رصيد السلفة (يبدأ بـ 303)
            invoice_number = f"303{random.randint(1000000, 9999999)}"

            # إنشاء الفاتورة
            success, pdf_path, final_invoice_number = invoice_manager.create_transaction_invoice(
                wallet_number=transaction_data['wallet_number'],
                user_name=transaction_data['user_name'],
                amount=transaction_data['amount'],
                transaction_type="إضافة رصيد سلفة",
                previous_balance=transaction_data['old_balance'],
                new_balance=transaction_data['new_balance'],
                invoice_number=invoice_number
            )

            if success:
                logger.info(f"💳 تم إنشاء فاتورة إضافة رصيد السلفة {final_invoice_number}")
            else:
                logger.error(f"❌ فشل في إنشاء فاتورة إضافة رصيد السلفة")

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء فاتورة إضافة رصيد السلفة: {e}")

    async def create_and_send_loan_deduct_invoice(self, wallet_info, transaction_data, notification_id):
        """إنشاء وإرسال فاتورة خصم رصيد سلفة"""
        try:
            from shared.invoices.invoice_manager import InvoiceManager

            # إنشاء مدير الفواتير
            invoice_manager = InvoiceManager()

            # توليد رقم فاتورة لخصم رصيد السلفة (يبدأ بـ 304)
            invoice_number = f"304{random.randint(1000000, 9999999)}"

            # إنشاء الفاتورة
            success, pdf_path, final_invoice_number = invoice_manager.create_transaction_invoice(
                wallet_number=transaction_data['wallet_number'],
                user_name=transaction_data['user_name'],
                amount=transaction_data['amount'],
                transaction_type="خصم رصيد سلفة",
                previous_balance=transaction_data['old_balance'],
                new_balance=transaction_data['new_balance'],
                invoice_number=invoice_number
            )

            if success:
                logger.info(f"💳 تم إنشاء فاتورة خصم رصيد السلفة {final_invoice_number}")
            else:
                logger.error(f"❌ فشل في إنشاء فاتورة خصم رصيد السلفة")

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء فاتورة خصم رصيد السلفة: {e}")

    async def send_loan_operation_notification(self, wallet_info, transaction_data, operation_type):
        """إرسال إشعار تلقائي لعمليات السلف"""
        try:
            # إضافة مسار البوت الرئيسي للاستيراد
            import sys
            import os

            # إضافة مسار main/core للاستيراد المباشر
            core_path = os.path.join(os.path.dirname(__file__), '..', 'main', 'core')
            if core_path not in sys.path:
                sys.path.insert(0, core_path)  # استخدام insert للأولوية

            # استيراد مباشر من ملف monitoring.py
            import importlib.util
            monitoring_file = os.path.join(core_path, 'monitoring.py')
            spec = importlib.util.spec_from_file_location("monitoring", monitoring_file)
            monitoring_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(monitoring_module)
            EnhancedMonitoringSystem = monitoring_module.EnhancedMonitoringSystem

            # استيراد الإعدادات مباشرة من الملف
            admin_config_path = os.path.join(os.path.dirname(__file__), 'core', 'admin_config.py')
            import importlib.util
            spec = importlib.util.spec_from_file_location("admin_config", admin_config_path)
            admin_config = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(admin_config)

            # إنشاء نظام المراقبة
            monitoring = EnhancedMonitoringSystem(
                admin_bot_token=admin_config.ADMIN_BOT_TOKEN,
                admin_chat_id=admin_config.ADMIN_CHAT_ID
            )

            # إعداد بيانات المستخدم للإشعار
            user_info = {
                'first_name': wallet_info.get('user_name', 'غير محدد'),
                'username': wallet_info.get('username', 'غير محدد'),
                'id': wallet_info.get('user_id', 'غير محدد')
            }

            # إرسال إشعار عملية السلف
            await monitoring.send_loan_operation_notification(
                user_info=user_info,
                wallet_number=transaction_data['wallet_number'],
                operation_type=operation_type,
                amount=transaction_data['amount'],
                transaction_data=transaction_data
            )

            logger.info(f"📨 تم إرسال إشعار {operation_type} للمحفظة {transaction_data['wallet_number']}")

        except Exception as e:
            logger.error(f"❌ خطأ في إرسال إشعار {operation_type}: {e}")

    async def send_user_loan_notification(self, wallet_info, transaction_data, operation_type):
        """إرسال إشعار عملية السلف للمستخدم"""
        try:
            user_id = wallet_info.get('user_id')
            if not user_id:
                logger.warning("❌ لا يوجد معرف مستخدم لإرسال الإشعار")
                return

            # تنسيق المبلغ
            amount = transaction_data.get('amount', 0)
            amount_display = f"{amount:.0f}" if amount == int(amount) else f"{amount:.2f}"

            # توليد رقم فاتورة عشوائي
            import random
            invoice_number = f"808{random.randint(1000000, 9999999)}"

            # حساب القيم المالية
            old_balance = transaction_data.get('old_balance', 0)
            new_balance = transaction_data.get('new_balance', 0)
            old_balance_display = f"{old_balance:.0f}" if old_balance == int(old_balance) else f"{old_balance:.2f}"
            new_balance_display = f"{new_balance:.0f}" if new_balance == int(new_balance) else f"{new_balance:.2f}"

            # حساب القيمة بالدولار والتوكين
            dollar_value = amount * 3
            dollar_display = f"{dollar_value:.0f}" if dollar_value == int(dollar_value) else f"{dollar_value:.2f}"
            token_value = amount * 524288

            # تنسيق التوكن
            if token_value >= 1000:  # مليون أو أكثر
                token_display = f"{token_value/1000:.2f} مليون توكين"
            else:
                token_display = f"{token_value:.2f} ألف توكين"

            # إنشاء رسالة الإشعار باستخدام النظام الموحد
            if invoice_message_manager:
                # تحديد نوع العملية
                transaction_type = "إضافة سلفة" if operation_type == "إضافة رصيد سلفة" else "خصم سلفة"

                # إعداد البيانات للنظام الموحد
                invoice_data = {
                    'transaction_type': transaction_type,
                    'invoice_number': invoice_number,
                    'user_name': wallet_info.get('user_name', 'غير محدد'),
                    'wallet_number': transaction_data.get('wallet_number', 'غير محدد'),
                    'amount': amount,
                    'previous_balance': old_balance,
                    'new_balance': new_balance,
                    'total_loan': transaction_data.get('new_loan_amount', 0),
                    'loan_status': "مسددة بالكامل" if transaction_data.get('is_fully_paid', False) else "نشط",
                    'date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }

                message = invoice_message_manager.create_loan_invoice_message(invoice_data)
            else:
                # النظام القديم كاحتياطي
                # تحديد نوع العملية والمحتوى حسب العملية
                if operation_type == "إضافة رصيد سلفة":
                    operation_name = "إضافة سلفة"
                    amount_label = "الرصيد السلفة"
                    additional_info = f"💸︙إجمالي السلف︙{transaction_data.get('new_loan_amount', 0):.0f} إكسا"
                else:  # خصم رصيد سلفة
                    operation_name = "خصم سلفة"
                    amount_label = "الرصيد المخصوم"
                    is_fully_paid = transaction_data.get('is_fully_paid', False)
                    loan_status = "مسددة بالكامل" if is_fully_paid else "جزئياً مسددة"
                    additional_info = f"""💸︙السلف المتبقي︙{transaction_data.get('new_loan_amount', 0):.0f} إكسا
✅︙حالة السلفة︙{loan_status}"""

                message = f"""🧾︙فاتورة رصيد إلكترونية
🧾︙نوع العملية︙{operation_name}
🔢︙رقم الفاتورة︙{invoice_number}

👤︙العميل︙{wallet_info.get('user_name', 'غير محدد')}
🏦︙رقم المحفظة︙{transaction_data.get('wallet_number', 'غير محدد')}

💰︙{amount_label}︙{amount_display} إكسا
📊︙الرصيد السابق︙{old_balance_display} إكسا
💳︙الرصيد الجديد︙{new_balance_display} إكسا
{additional_info}

💵︙القيمة بالدولار︙{dollar_display} USD
🪙︙القيمة بالتوكين︙{token_display}

📅︙تاريخ العملية︙{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🏢︙مقدم الخدمة︙نظام المحفظة الإلكترونية

💡︙ملاحظة︙
تم تنفيذ هذه العملية من قبل الإدارة"""

            # إرسال الرسالة للمستخدم عبر البوت الرئيسي
            await self.send_message_to_user_via_main_bot(user_id, message)

            logger.info(f"📨 تم إرسال إشعار {operation_type} للمستخدم {user_id}")

        except Exception as e:
            logger.error(f"❌ خطأ في إرسال إشعار {operation_type} للمستخدم: {e}")

    async def send_message_to_user_via_main_bot(self, user_id, message):
        """إرسال رسالة للمستخدم عبر البوت الرئيسي"""
        try:
            # حفظ الرسالة في ملف مشترك ليقرأها البوت الرئيسي
            import json
            import os
            from datetime import datetime

            # مجلد الرسائل المشتركة
            messages_dir = os.path.join(os.path.dirname(__file__), '..', 'shared', 'messages')
            os.makedirs(messages_dir, exist_ok=True)

            # ملف الرسائل المعلقة
            pending_messages_file = os.path.join(messages_dir, 'pending_messages.json')

            # قراءة الرسائل المعلقة الحالية
            pending_messages = {}
            if os.path.exists(pending_messages_file):
                try:
                    with open(pending_messages_file, 'r', encoding='utf-8') as f:
                        pending_messages = json.load(f)
                except:
                    pending_messages = {}

            # إضافة الرسالة الجديدة
            message_id = f"admin_msg_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{user_id}"
            pending_messages[message_id] = {
                'user_id': user_id,
                'message': message,
                'created_at': datetime.now().isoformat(),
                'type': 'loan_notification',
                'status': 'pending'
            }

            # حفظ الملف
            with open(pending_messages_file, 'w', encoding='utf-8') as f:
                json.dump(pending_messages, f, ensure_ascii=False, indent=2)

            logger.info(f"✅ تم حفظ رسالة للمستخدم {user_id} في الملف المشترك")

        except Exception as e:
            logger.error(f"❌ خطأ في حفظ رسالة للمستخدم {user_id}: {e}")

    async def send_user_loan_notification_with_invoice(self, wallet_info, transaction_data, operation_type):
        """إرسال إشعار عملية السلف مع فاتورة PDF للمستخدم"""
        try:
            user_id = wallet_info.get('user_id')
            if not user_id:
                logger.warning("❌ لا يوجد معرف مستخدم لإرسال الإشعار والفاتورة")
                return

            # إنشاء فاتورة PDF أولاً
            invoice_data = await self.create_user_loan_invoice(wallet_info, transaction_data, operation_type)

            if not invoice_data:
                logger.error("❌ فشل في إنشاء فاتورة PDF للمستخدم")
                return

            # حفظ الفاتورة في النظام التفاعلي
            await self.save_user_invoice_interactive(invoice_data, user_id)

            # إرسال الفاتورة مع الأزرار التفاعلية
            await self.send_invoice_to_user_via_main_bot(user_id, invoice_data)

            logger.info(f"✅ تم إرسال إشعار وفاتورة {operation_type} للمستخدم {user_id}")

        except Exception as e:
            logger.error(f"❌ خطأ في إرسال إشعار وفاتورة {operation_type} للمستخدم: {e}")

    async def create_user_loan_invoice(self, wallet_info, transaction_data, operation_type):
        """إنشاء فاتورة PDF لعملية السلف"""
        try:
            from shared.invoices.invoice_manager import InvoiceManager
            import random

            # إنشاء مدير الفواتير
            invoice_manager = InvoiceManager()

            # تحديد نوع الفاتورة ورقمها
            if operation_type == "إضافة رصيد سلفة":
                invoice_number = f"303{random.randint(1000000, 9999999)}"
                transaction_type = "إضافة سلفة"
            else:  # خصم رصيد سلفة
                invoice_number = f"304{random.randint(1000000, 9999999)}"
                transaction_type = "خصم سلفة"

            # إنشاء الفاتورة
            success, pdf_path, final_invoice_number = invoice_manager.create_transaction_invoice(
                wallet_number=transaction_data['wallet_number'],
                user_name=transaction_data.get('user_name', wallet_info.get('user_name', 'غير محدد')),
                amount=transaction_data['amount'],
                transaction_type=transaction_type,
                previous_balance=transaction_data['old_balance'],
                new_balance=transaction_data['new_balance'],
                invoice_number=invoice_number
            )

            if success:
                logger.info(f"✅ تم إنشاء فاتورة {transaction_type} {final_invoice_number}")

                return {
                    'invoice_number': final_invoice_number,
                    'pdf_path': pdf_path,
                    'transaction_type': transaction_type,
                    'amount': transaction_data['amount'],
                    'user_id': wallet_info.get('user_id'),
                    'wallet_number': transaction_data['wallet_number'],
                    'user_name': wallet_info.get('user_name', 'غير محدد'),
                    'old_balance': transaction_data.get('old_balance', 0),
                    'new_balance': transaction_data.get('new_balance', 0),
                    'old_loan_amount': transaction_data.get('old_loan_amount', 0),
                    'new_loan_amount': transaction_data.get('new_loan_amount', 0),
                    'is_fully_paid': transaction_data.get('is_fully_paid', False)
                }
            else:
                logger.error(f"❌ فشل في إنشاء فاتورة {transaction_type}")
                return None

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء فاتورة {operation_type}: {e}")
            return None

    async def save_user_invoice_interactive(self, invoice_data, user_id):
        """حفظ الفاتورة في النظام التفاعلي"""
        try:
            from shared.invoices.interactive_invoice_manager import interactive_invoice_manager

            interactive_invoice_manager.save_interactive_invoice(
                invoice_number=invoice_data['invoice_number'],
                pdf_path=invoice_data['pdf_path'],
                user_id=user_id,
                transaction_type=invoice_data['transaction_type'],
                amount=invoice_data['amount']
            )

            logger.info(f"✅ تم حفظ فاتورة {invoice_data['invoice_number']} في النظام التفاعلي")

        except Exception as e:
            logger.error(f"❌ خطأ في حفظ الفاتورة في النظام التفاعلي: {e}")

    async def send_invoice_to_user_via_main_bot(self, user_id, invoice_data):
        """إرسال الفاتورة للمستخدم عبر البوت الرئيسي مع الأزرار التفاعلية"""
        try:
            import json
            import os
            from datetime import datetime

            # مجلد الفواتير المشتركة
            invoices_dir = os.path.join(os.path.dirname(__file__), '..', 'shared', 'invoices')
            os.makedirs(invoices_dir, exist_ok=True)

            # ملف الفواتير المعلقة
            pending_invoices_file = os.path.join(invoices_dir, 'pending_user_invoices.json')

            # قراءة الفواتير المعلقة الحالية
            pending_invoices = {}
            if os.path.exists(pending_invoices_file):
                try:
                    with open(pending_invoices_file, 'r', encoding='utf-8') as f:
                        pending_invoices = json.load(f)
                except:
                    pending_invoices = {}

            # إضافة الفاتورة الجديدة
            invoice_id = f"user_invoice_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{user_id}"
            pending_invoices[invoice_id] = {
                'user_id': user_id,
                'invoice_data': invoice_data,
                'created_at': datetime.now().isoformat(),
                'type': 'loan_invoice',
                'status': 'pending'
            }

            # حفظ الملف
            with open(pending_invoices_file, 'w', encoding='utf-8') as f:
                json.dump(pending_invoices, f, ensure_ascii=False, indent=2)

            logger.info(f"✅ تم حفظ فاتورة {invoice_data['invoice_number']} للإرسال للمستخدم {user_id}")

        except Exception as e:
            logger.error(f"❌ خطأ في حفظ فاتورة للإرسال للمستخدم {user_id}: {e}")

    async def create_loan_deduction_invoice(self, wallet_info, loan_deduction_data, notification_id):
        """إنشاء فاتورة خصم السلف"""
        try:
            from shared.invoices.invoice_manager import InvoiceManager
            import random

            # إنشاء مدير الفواتير
            invoice_manager = InvoiceManager()

            # توليد رقم فاتورة لخصم السلف (يبدأ بـ 302)
            loan_invoice_number = f"302{random.randint(1000000, 9999999)}"

            deducted_amount = loan_deduction_data.get('deducted_amount', 0)
            remaining_loan = loan_deduction_data.get('remaining_loan', 0)
            original_loan = loan_deduction_data.get('original_loan', 0)

            # إنشاء الفاتورة
            success, pdf_path, final_invoice_number = invoice_manager.create_transaction_invoice(
                wallet_number=wallet_info['wallet_number'],
                user_name=wallet_info.get('user_name', 'غير محدد'),
                amount=deducted_amount,
                transaction_type="خصم سلفة",
                previous_balance=original_loan,
                new_balance=remaining_loan,
                invoice_number=loan_invoice_number
            )

            if success:
                logger.info(f"💳 تم إنشاء فاتورة خصم السلف {final_invoice_number} للمحفظة {wallet_info['wallet_number']}")
            else:
                logger.error(f"❌ فشل في إنشاء فاتورة خصم السلف للمحفظة {wallet_info['wallet_number']}")

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء فاتورة خصم السلف: {e}")

    async def get_loan_statistics(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """الحصول على إحصائيات السلف"""
        try:
            # التحقق من المدير
            if not self.is_admin(update.effective_user.id):
                await update.message.reply_text("❌ غير مصرح لك بالوصول")
                return

            from shared.database.wallet_manager import WalletManager
            wallet_manager = WalletManager()

            loan_stats = wallet_manager.get_loan_statistics()

            if loan_stats:
                total_active = loan_stats.get('total_active_loans', 0)
                total_amount = loan_stats.get('total_loan_amount', 0)
                total_granted = loan_stats.get('total_loans_granted', 0)
                total_paid = loan_stats.get('total_loans_paid', 0)
                average_amount = loan_stats.get('average_loan_amount', 0)

                # تنسيق الأرقام
                total_amount_display = f"{total_amount:.0f}" if total_amount == int(total_amount) else f"{total_amount:.2f}"
                average_display = f"{average_amount:.0f}" if average_amount == int(average_amount) else f"{average_amount:.2f}"

                message = f"""📊︙إحصائيات السلف︙

💳︙السلف النشطة︙
🔢︙العدد︙{total_active}
💰︙إجمالي المبلغ︙{total_amount_display} إكسا
📈︙متوسط السلفة︙{average_display} إكسا

📈︙الإحصائيات العامة︙
✅︙إجمالي السلف الممنوحة︙{total_granted}
💳︙إجمالي السلف المسددة︙{total_paid}
📊︙معدل السداد︙{(total_paid/total_granted*100) if total_granted > 0 else 0:.1f}%

⏰︙آخر تحديث︙{loan_stats.get('last_updated', 'غير محدد')}"""

                # عرض المستخدمين الذين لديهم سلف نشط
                users_with_loans = loan_stats.get('users_with_active_loans', [])
                if users_with_loans:
                    message += "\n\n👥︙المستخدمون الذين لديهم سلف نشط︙\n"
                    for i, user_loan in enumerate(users_with_loans[:5], 1):  # أول 5 مستخدمين
                        loan_amount = user_loan.get('loan_amount', 0)
                        loan_display = f"{loan_amount:.0f}" if loan_amount == int(loan_amount) else f"{loan_amount:.2f}"
                        message += f"{i}. {user_loan.get('user_name', 'غير محدد')} - {loan_display} إكسا\n"

                    if len(users_with_loans) > 5:
                        message += f"... و {len(users_with_loans) - 5} مستخدم آخر"

            else:
                message = """📊︙إحصائيات السلف︙

❌︙لا توجد بيانات متاحة︙

💡︙ملاحظة︙
لم يتم منح أي سلف حتى الآن"""

            await update.message.reply_text(message)

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على إحصائيات السلف: {e}")
            await update.message.reply_text("❌ حدث خطأ في الحصول على إحصائيات السلف")

    async def balance_history_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض تاريخ معاملات المحفظة"""
        try:
            # التحقق من المدير
            if not self.is_admin(update.effective_user.id):
                await update.message.reply_text("❌ غير مصرح لك بالوصول")
                return

            # التحقق من وجود رقم المحفظة
            if not context.args:
                await update.message.reply_text(
                    "❌ يرجى إدخال رقم المحفظة\n"
                    "مثال: `/balance_history 9091234567`", parse_mode=ParseMode.MARKDOWN)
                return

            wallet_number = context.args[0]

            # الحصول على معلومات المحفظة
            from shared.database.wallet_manager import WalletManager
            wallet_manager = WalletManager()
            wallet_info = wallet_manager.get_wallet_by_number(wallet_number)

            if not wallet_info:
                await update.message.reply_text(f"❌ المحفظة {wallet_number} غير موجودة")
                return

            # عرض تاريخ المعاملات
            balance = wallet_info.get('balance', 0)
            currency = wallet_info.get('currency', 'إكسا')
            transaction_count = wallet_info.get('transaction_count', 0)
            last_transaction = wallet_info.get('last_transaction', 'لا توجد معاملات')
            created_at = wallet_info.get('created_at', 'غير محدد')
            user_name = wallet_info.get('user_name', 'غير محدد')

            # تنسيق الأرقام بدون عشري إذا كانت صحيحة
            balance_display = f"{balance:.0f}" if balance == int(balance) else f"{balance:.2f}"
            dollar_value = balance * 3
            dollar_display = f"{dollar_value:.0f}" if dollar_value == int(dollar_value) else f"{dollar_value:.2f}"
            token_value = balance * 524288
            token_display = f"{token_value:.0f}" if token_value == int(token_value) else f"{token_value:.2f}"

            message = f"""📊︙تاريخ معاملات المحفظة

🏦︙رقم المحفظة︙{wallet_number}
👤︙اسم︙{user_name}
💳︙الرصيد الحالي︙{balance_display} {currency}
🔄︙عدد المعاملات︙{transaction_count}
📅︙تاريخ الإنشاء︙{created_at}
⏰︙آخر معاملة︙{last_transaction}

💵︙القيمة الحالية بالدولار︙{dollar_display} USD
🪙︙القيمة الحالية بالتوكين︙{token_display} توكين

💡︙ملاحظة︙
لعرض تفاصيل أكثر للمعاملات، يمكن تطوير نظام سجل المعاملات المفصل"""

            await update.message.reply_text(message)

        except Exception as e:
            logger.error(f"❌ خطأ في عرض تاريخ المعاملات: {e}")
            await update.message.reply_text("❌ حدث خطأ في عرض تاريخ المعاملات")

    async def admin_bot_monitoring(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """مراقبة البوت الإداري"""
        try:
            # التحقق من المدير
            if not self.is_admin(update.effective_user.id):
                await update.message.reply_text("❌ غير مصرح لك بالوصول")
                return

            import datetime
            current_time = datetime.datetime.now()
            arabic_days = {
                'Monday': 'الاثنين', 'Tuesday': 'الثلاثاء', 'Wednesday': 'الأربعاء',
                'Thursday': 'الخميس', 'Friday': 'الجمعة', 'Saturday': 'السبت', 'Sunday': 'الأحد'
            }
            arabic_day = arabic_days.get(current_time.strftime('%A'), current_time.strftime('%A'))

            message = f"""🛡️︙البوت إدارة ومراقبة

📋︙معلومات البوت︙
👤︙الأسم︙<code>بوت الإدارة والمراقبة</code>
📧︙المعرف︙<code>@AdminBot</code>
🆔︙الأيدي︙<code>7075985420</code>
📊︙الحالة︙🟢 متصل ونشط

⚙️︙الوظائف النشطة︙
• إدارة المستخدمين︙✅ نشط
• المراقبة المباشرة︙✅ نشط
• إدارة النشرات︙✅ نشط
• النسخ الاحتياطية︙✅ نشط
• التنبيهات︙✅ نشط

📊︙إحصائيات الأداء︙
• وقت التشغيل︙{current_time.strftime('%H:%M:%S')}
• استهلاك الذاكرة︙طبيعي
• سرعة الاستجابة︙ممتازة

📅︙اليوم︙{arabic_day}
📅︙التاريخ︙{current_time.strftime('%Y-%m-%d')}
⏰︙الوقت︙{current_time.strftime('%H:%M:%S')}"""

            # إنشاء لوحة مفاتيح العودة
            keyboard = [
                ["🔙 العودة للمراقبة المركزية", "🏠 القائمة الرئيسية"]
            ]
            reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=False)

            await update.message.reply_text(
                message,
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"❌ خطأ في مراقبة البوت الإداري: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في مراقبة البوت الإداري",
                reply_markup=self.get_reply_keyboard()
            )

    async def system_monitoring(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """مراقبة نظام التشغيل"""
        try:
            # التحقق من المدير
            if not self.is_admin(update.effective_user.id):
                await update.message.reply_text("❌ غير مصرح لك بالوصول")
                return

            import datetime
            import psutil
            import platform

            current_time = datetime.datetime.now()
            arabic_days = {
                'Monday': 'الاثنين', 'Tuesday': 'الثلاثاء', 'Wednesday': 'الأربعاء',
                'Thursday': 'الخميس', 'Friday': 'الجمعة', 'Saturday': 'السبت', 'Sunday': 'الأحد'
            }
            arabic_day = arabic_days.get(current_time.strftime('%A'), current_time.strftime('%A'))

            # معلومات النظام
            try:
                cpu_percent = psutil.cpu_percent(interval=1)
                memory = psutil.virtual_memory()
                disk = psutil.disk_usage('/')
                system_info = platform.system()
                python_version = platform.python_version()
            except:
                cpu_percent = "غير متاح"
                memory = type('obj', (object), {'percent': 0, 'total': 0, 'available': 0})()
                disk = type('obj', (object), {'percent': 0, 'total': 0, 'free': 0})()
                system_info = "Windows"
                python_version = "3.x"

            message = f"""💻︙نظام التشغيل العام

🖥️︙معلومات النظام︙
💾︙نظام التشغيل︙<code>{system_info}</code>
🐍︙إصدار Python︙<code>{python_version}</code>
📊︙الحالة︙🟢 يعمل بشكل طبيعي

⚡︙الأداء︙
🔥︙استخدام المعالج︙{cpu_percent}%
💾︙استخدام الذاكرة︙{getattr(memory, 'percent', 0):.1f}%
💿︙استخدام القرص الصلب︙{getattr(disk, 'percent', 0):.1f}%

🔋︙حالة الموارد︙
• المعالج︙{'🟢 طبيعي' if isinstance(cpu_percent, (int, float)) and cpu_percent < 80 else '🟡 مرتفع'}
• الذاكرة︙{'🟢 طبيعي' if getattr(memory, 'percent', 0) < 80 else '🟡 مرتفع'}
• القرص︙{'🟢 طبيعي' if getattr(disk, 'percent', 0) < 80 else '🟡 مرتفع'}

📅︙اليوم︙{arabic_day}
📅︙التاريخ︙{current_time.strftime('%Y-%m-%d')}
⏰︙الوقت︙{current_time.strftime('%H:%M:%S')}"""

            # إنشاء لوحة مفاتيح العودة
            keyboard = [
                ["🔙 العودة للمراقبة المركزية", "🏠 القائمة الرئيسية"]
            ]
            reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=False)

            await update.message.reply_text(
                message,
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"❌ خطأ في مراقبة النظام: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في مراقبة النظام",
                reply_markup=self.get_reply_keyboard()
            )

    async def placeholder_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """دالة مؤقتة للأزرار قيد التطوير"""
        try:
            # التحقق من المدير
            if not self.is_admin(update.effective_user.id):
                await update.message.reply_text("❌ غير مصرح لك بالوصول")
                return

            await update.message.reply_text(
                "🚧 هذه الميزة قيد التطوير وستكون متاحة قريباً",
                reply_markup=self.get_reply_keyboard()
            )

        except Exception as e:
            logger.error(f"❌ خطأ في الدالة المؤقتة: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ",
                reply_markup=self.get_reply_keyboard()
            )

    async def system_settings_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض إعدادات النظام"""
        try:
            # التحقق من المدير
            if not self.is_admin(update.effective_user.id):
                await update.message.reply_text("❌ غير مصرح لك بالوصول")
                return

            # إنشاء لوحة مفاتيح إعدادات النظام
            keyboard = [
                ["🗄️ النسخ الاحتياطية", "🔄 مزامنة البيانات"],
                ["🔧 إعدادات عامة", "🛠️ صيانة النظام"],
                ["📝 سجلات النظام", "🔐 الأمان والحماية"],
                ["🔙 العودة للقائمة الرئيسية"]
            ]
            reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=False)

            message = """⚙️︙إعدادات النظام

🛠️︙إدارة شاملة لإعدادات النظام︙

🗄️︙النسخ الاحتياطية︙إنشاء واستعادة النسخ الاحتياطية
🔄︙مزامنة البيانات︙مزامنة البيانات بين الأنظمة
🔧︙إعدادات عامة︙تكوين الإعدادات العامة للنظام
🛠️︙صيانة النظام︙أدوات الصيانة والتحسين
📝︙سجلات النظام︙عرض وإدارة سجلات النظام
🔐︙الأمان والحماية︙إعدادات الأمان والحماية

⚡︙إدارة متقدمة لجميع جوانب النظام"""

            await update.message.reply_text(
                message,
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"❌ خطأ في إعدادات النظام: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في عرض إعدادات النظام",
                reply_markup=self.get_reply_keyboard()
            )

    async def backup_management_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """إدارة النسخ الاحتياطية"""
        try:
            # التحقق من المدير
            if not self.is_admin(update.effective_user.id):
                await update.message.reply_text("❌ غير مصرح لك بالوصول")
                return

            message = """🗄️︙النسخ الاحتياطية

📦︙إدارة النسخ الاحتياطية للنظام︙

💾︙العمليات المتاحة︙
• إنشاء نسخة احتياطية جديدة
• استعادة من نسخة احتياطية
• عرض النسخ المتاحة
• حذف النسخ القديمة
• جدولة النسخ التلقائية

📊︙معلومات النسخ︙
• آخر نسخة احتياطية︙قيد التطوير
• حجم النسخ︙قيد التطوير
• عدد النسخ المحفوظة︙قيد التطوير

⚠️︙ملاحظة︙هذه الميزة قيد التطوير وستكون متاحة قريباً"""

            # إنشاء لوحة مفاتيح العودة
            keyboard = [
                ["🔙 العودة لإعدادات النظام", "🏠 القائمة الرئيسية"]
            ]
            reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=False)

            await update.message.reply_text(
                message,
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"❌ خطأ في النسخ الاحتياطية: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في النسخ الاحتياطية",
                reply_markup=self.get_reply_keyboard()
            )

    async def sync_management_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """إدارة مزامنة البيانات"""
        try:
            # التحقق من المدير
            if not self.is_admin(update.effective_user.id):
                await update.message.reply_text("❌ غير مصرح لك بالوصول")
                return

            message = """🔄︙مزامنة البيانات

🔗︙مزامنة البيانات بين الأنظمة︙

⚡︙العمليات المتاحة︙
• مزامنة بيانات المستخدمين
• مزامنة الإعدادات
• مزامنة السجلات
• مزامنة النشرات
• مزامنة التقارير

📊︙حالة المزامنة︙
• آخر مزامنة︙قيد التطوير
• حالة الاتصال︙قيد التطوير
• البيانات المتزامنة︙قيد التطوير

🔧︙إعدادات المزامنة︙
• تكرار المزامنة التلقائية
• اختيار البيانات للمزامنة
• إعدادات الأمان

⚠️︙ملاحظة︙هذه الميزة قيد التطوير وستكون متاحة قريباً"""

            # إنشاء لوحة مفاتيح العودة
            keyboard = [
                ["🔙 العودة لإعدادات النظام", "🏠 القائمة الرئيسية"]
            ]
            reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=False)

            await update.message.reply_text(
                message,
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"❌ خطأ في مزامنة البيانات: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في مزامنة البيانات",
                reply_markup=self.get_reply_keyboard()
            )

    async def error_handler(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج الأخطاء العام"""
        error = context.error

        # تسجيل الخطأ
        logger.error(f"❌ خطأ في البوت الإداري: {error}")

        # محاولة إرسال رسالة للمستخدم إذا كان ممكناً
        if update and update.effective_user:
            try:
                # التحقق من نوع الخطأ
                if "httpx" in str(error).lower() or "http" in str(error).lower():
                    error_message = "⚠️ مشكلة مؤقتة في الاتصال\n\nيرجى المحاولة مرة أخرى خلال لحظات"
                elif "timeout" in str(error).lower():
                    error_message = "⏱️ انتهت مهلة الاتصال\n\nيرجى المحاولة مرة أخرى"
                else:
                    error_message = "❌ حدث خطأ مؤقت\n\nيرجى المحاولة مرة أخرى"

                if update.message:
                    await update.message.reply_text(
                        error_message,
                reply_markup=self.get_reply_keyboard()
                    )
                elif update.callback_query:
                    await update.callback_query.message.reply_text(
                        error_message,
                reply_markup=self.get_reply_keyboard()
                    )
            except Exception as send_error:
                logger.error(f"❌ فشل في إرسال رسالة الخطأ: {send_error}")







    # ==================== وظائف الأزرار التفاعلية لإدارة الرصيد ====================

    async def wallet_details_button(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """زر تفاصيل المحفظة"""
        try:
            if not self.is_admin(update.effective_user.id):
                await update.message.reply_text("❌ غير مصرح لك بالوصول")
                return

            # تعيين حالة المستخدم
            user_id = update.effective_user.id
            self.user_states[user_id] = {
                'action': 'wallet_details',
                'step': 'waiting_wallet_number'
            }

            await update.message.reply_text(
                "📋︙تفاصيل المحفظة\n\n"
                "🔍︙أدخل رقم المحفظة للحصول على التفاصيل:\n"
                "مثال: `9091234567`",
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            logger.error(f"❌ خطأ في زر تفاصيل المحفظة: {e}")
            await update.message.reply_text("❌ حدث خطأ في العملية")

    async def add_balance_button(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """زر إضافة رصيد"""
        try:
            if not self.is_admin(update.effective_user.id):
                await update.message.reply_text("❌ غير مصرح لك بالوصول")
                return

            # تعيين حالة المستخدم
            user_id = update.effective_user.id
            self.user_states[user_id] = {
                'action': 'add_balance',
                'step': 'waiting_wallet_number'
            }

            await update.message.reply_text(
                "➕︙إضافة رصيد\n\n"
                "🔍︙أدخل رقم المحفظة والمبلغ المراد إضافته:\n"
                "التنسيق: `رقم_المحفظة المبلغ`\n"
                "مثال: `9091234567 50`",
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            logger.error(f"❌ خطأ في زر إضافة رصيد: {e}")
            await update.message.reply_text("❌ حدث خطأ في العملية")

    async def deduct_balance_button(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """زر خصم رصيد"""
        try:
            if not self.is_admin(update.effective_user.id):
                await update.message.reply_text("❌ غير مصرح لك بالوصول")
                return

            # تعيين حالة المستخدم
            user_id = update.effective_user.id
            self.user_states[user_id] = {
                'action': 'deduct_balance',
                'step': 'waiting_wallet_number'
            }

            await update.message.reply_text(
                "➖︙خصم رصيد\n\n"
                "🔍︙أدخل رقم المحفظة والمبلغ المراد خصمه:\n"
                "التنسيق: `رقم_المحفظة المبلغ`\n"
                "مثال: `9091234567 25`",
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            logger.error(f"❌ خطأ في زر خصم رصيد: {e}")
            await update.message.reply_text("❌ حدث خطأ في العملية")

    async def add_loan_button(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """زر إضافة سلفة"""
        try:
            if not self.is_admin(update.effective_user.id):
                await update.message.reply_text("❌ غير مصرح لك بالوصول")
                return

            # تعيين حالة المستخدم
            user_id = update.effective_user.id
            self.user_states[user_id] = {
                'action': 'add_loan',
                'step': 'waiting_wallet_number'
            }

            await update.message.reply_text(
                "💳︙إضافة سلفة\n\n"
                "🔍︙أدخل رقم المحفظة ومبلغ السلفة:\n"
                "التنسيق: `رقم_المحفظة المبلغ`\n"
                "مثال: `9091234567 30`",
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            logger.error(f"❌ خطأ في زر إضافة سلفة: {e}")
            await update.message.reply_text("❌ حدث خطأ في العملية")

    async def deduct_loan_button(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """زر خصم سلفة"""
        try:
            if not self.is_admin(update.effective_user.id):
                await update.message.reply_text("❌ غير مصرح لك بالوصول")
                return

            # تعيين حالة المستخدم
            user_id = update.effective_user.id
            self.user_states[user_id] = {
                'action': 'deduct_loan',
                'step': 'waiting_wallet_number'
            }

            await update.message.reply_text(
                "💸︙خصم سلفة\n\n"
                "🔍︙أدخل رقم المحفظة ومبلغ السلفة المراد خصمه:\n"
                "التنسيق: `رقم_المحفظة المبلغ`\n"
                "مثال: `9091234567 20`",
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            logger.error(f"❌ خطأ في زر خصم سلفة: {e}")
            await update.message.reply_text("❌ حدث خطأ في العملية")

    async def show_transactions_button(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """زر عرض المعاملات"""
        try:
            if not self.is_admin(update.effective_user.id):
                await update.message.reply_text("❌ غير مصرح لك بالوصول")
                return

            # تعيين حالة المستخدم
            user_id = update.effective_user.id
            self.user_states[user_id] = {
                'action': 'show_transactions',
                'step': 'waiting_wallet_number'
            }

            await update.message.reply_text(
                "📊︙عرض المعاملات\n\n"
                "🔍︙أدخل رقم المحفظة لعرض جميع المعاملات:\n"
                "مثال: `9091234567`",
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            logger.error(f"❌ خطأ في زر عرض المعاملات: {e}")
            await update.message.reply_text("❌ حدث خطأ في العملية")

    async def balance_transactions_button(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """زر المعاملات الرصيد"""
        try:
            if not self.is_admin(update.effective_user.id):
                await update.message.reply_text("❌ غير مصرح لك بالوصول")
                return

            # تعيين حالة المستخدم
            user_id = update.effective_user.id
            self.user_states[user_id] = {
                'action': 'balance_transactions',
                'step': 'waiting_wallet_number'
            }

            await update.message.reply_text(
                "💰︙المعاملات الرصيد\n\n"
                "🔍︙أدخل رقم المحفظة لعرض معاملات الرصيد فقط:\n"
                "مثال: `9091234567`",
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            logger.error(f"❌ خطأ في زر المعاملات الرصيد: {e}")
            await update.message.reply_text("❌ حدث خطأ في العملية")

    async def loan_transactions_button(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """زر المعاملات السلفة"""
        try:
            if not self.is_admin(update.effective_user.id):
                await update.message.reply_text("❌ غير مصرح لك بالوصول")
                return

            # تعيين حالة المستخدم
            user_id = update.effective_user.id
            self.user_states[user_id] = {
                'action': 'loan_transactions',
                'step': 'waiting_wallet_number'
            }

            await update.message.reply_text(
                "🏦︙المعاملات السلفة\n\n"
                "🔍︙أدخل رقم المحفظة لعرض معاملات السلفة فقط:\n"
                "مثال: `9091234567`",
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            logger.error(f"❌ خطأ في زر المعاملات السلفة: {e}")
            await update.message.reply_text("❌ حدث خطأ في العملية")

    # ==================== وظائف الأزرار التفاعلية لإدارة الرصيد ====================

    def get_balance_management_keyboard(self):
        """الحصول على لوحة مفاتيح إدارة الرصيد"""
        from telegram import ReplyKeyboardMarkup
        balance_management_keyboard = [
            ["📋 تفاصيل المحفظة", "➕ إضافة رصيد"],
            ["➖ خصم رصيد", "💳 إضافة سلفة"],
            ["💸 خصم سلفة", "📊 عرض المعاملات"],
            ["💰 المعاملات الرصيد", "🏦 المعاملات السلفة"],
            ["🔙 العودة لمحفظة إكسا"]
        ]
        return ReplyKeyboardMarkup(balance_management_keyboard, resize_keyboard=True, one_time_keyboard=False)

    async def wallet_details_button(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """زر تفاصيل المحفظة - تفاعلي"""
        try:
            # التحقق من المدير
            if not self.is_admin(update.effective_user.id):
                await update.message.reply_text("❌ غير مصرح لك بالوصول")
                return

            user_id = update.effective_user.id

            # تعيين حالة المستخدم
            self.user_states[user_id] = {
                'action': 'wallet_details',
                'step': 'waiting_wallet_number'
            }

            message = """📋︙تفاصيل المحفظة

🔍︙أدخل رقم المحفظة لعرض تفاصيلها الكاملة

💡︙يمكنك إدخال:
• رقم المحفظة (مثل: 9091234567)
• عدة أرقام محافظ مفصولة بمسافات للعرض المتعدد
• كلمة "الكل" لعرض جميع المحافظ

📝︙مثال:
`9091234567`
`9091234567 9092345678 9093456789`
`الكل`"""

            await update.message.reply_text(
                message,
                reply_markup=self.get_balance_management_keyboard(),
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            logger.error(f"❌ خطأ في زر تفاصيل المحفظة: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في عرض تفاصيل المحفظة",
                reply_markup=self.get_balance_management_keyboard()
            )

    async def add_balance_button(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """زر إضافة رصيد - تفاعلي"""
        try:
            # التحقق من المدير
            if not self.is_admin(update.effective_user.id):
                await update.message.reply_text("❌ غير مصرح لك بالوصول")
                return

            user_id = update.effective_user.id

            # تعيين حالة المستخدم
            self.user_states[user_id] = {
                'action': 'add_balance',
                'step': 'waiting_wallet_and_amount'
            }

            message = """➕︙إضافة رصيد

💰︙أدخل رقم المحفظة والمبلغ المراد إضافته

💡︙التنسيق:
`رقم_المحفظة المبلغ`

📝︙أمثلة:
• إضافة 50 إكسا: `9091234567 50`
• إضافة 100.5 إكسا: `9091234567 100.5`

🔢︙للعمليات المتعددة:
`9091234567 50`
`9092345678 75`
`9093456789 25`

💎︙سعر الصرف: 1 إكسا = 3 دولار = 524,288 ألف توكين"""

            await update.message.reply_text(
                message,
                reply_markup=self.get_balance_management_keyboard(),
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            logger.error(f"❌ خطأ في زر إضافة رصيد: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في إضافة الرصيد",
                reply_markup=self.get_balance_management_keyboard()
            )

    async def deduct_balance_button(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """زر خصم رصيد - تفاعلي"""
        try:
            # التحقق من المدير
            if not self.is_admin(update.effective_user.id):
                await update.message.reply_text("❌ غير مصرح لك بالوصول")
                return

            user_id = update.effective_user.id

            # تعيين حالة المستخدم
            self.user_states[user_id] = {
                'action': 'deduct_balance',
                'step': 'waiting_wallet_and_amount'
            }

            message = """➖︙خصم رصيد

💰︙أدخل رقم المحفظة والمبلغ المراد خصمه

💡︙التنسيق:
`رقم_المحفظة المبلغ`

📝︙أمثلة:
• خصم 25 إكسا: `9091234567 25`
• خصم 50.5 إكسا: `9091234567 50.5`

🔢︙للعمليات المتعددة:
`9091234567 25`
`9092345678 30`
`9093456789 15`

⚠️︙تأكد من وجود رصيد كافي قبل الخصم"""

            await update.message.reply_text(
                message,
                reply_markup=self.get_balance_management_keyboard(),
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            logger.error(f"❌ خطأ في زر خصم رصيد: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في خصم الرصيد",
                reply_markup=self.get_balance_management_keyboard()
            )

    async def add_loan_button(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """زر إضافة سلفة - تفاعلي"""
        try:
            # التحقق من المدير
            if not self.is_admin(update.effective_user.id):
                await update.message.reply_text("❌ غير مصرح لك بالوصول")
                return

            user_id = update.effective_user.id

            # تعيين حالة المستخدم
            self.user_states[user_id] = {
                'action': 'add_loan',
                'step': 'waiting_wallet_and_amount'
            }

            message = """💳︙إضافة سلفة

💰︙أدخل رقم المحفظة ومبلغ السلفة المراد إضافتها

💡︙التنسيق:
`رقم_المحفظة مبلغ_السلفة`

📝︙أمثلة:
• إضافة سلفة 30 إكسا: `9091234567 30`
• إضافة سلفة 75.5 إكسا: `9091234567 75.5`

🔢︙للعمليات المتعددة:
`9091234567 30`
`9092345678 50`
`9093456789 25`

💡︙السلفة تُضاف للرصيد وتُسجل كدين على المستخدم"""

            await update.message.reply_text(
                message,
                reply_markup=self.get_balance_management_keyboard(),
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            logger.error(f"❌ خطأ في زر إضافة سلفة: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في إضافة السلفة",
                reply_markup=self.get_balance_management_keyboard()
            )

    async def deduct_loan_button(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """زر خصم سلفة - تفاعلي"""
        try:
            # التحقق من المدير
            if not self.is_admin(update.effective_user.id):
                await update.message.reply_text("❌ غير مصرح لك بالوصول")
                return

            user_id = update.effective_user.id

            # تعيين حالة المستخدم
            self.user_states[user_id] = {
                'action': 'deduct_loan',
                'step': 'waiting_wallet_and_amount'
            }

            message = """💸︙خصم سلفة

💰︙أدخل رقم المحفظة ومبلغ السلفة المراد خصمها

💡︙التنسيق:
`رقم_المحفظة مبلغ_الخصم`

📝︙أمثلة:
• خصم سلفة 20 إكسا: `9091234567 20`
• خصم سلفة 35.5 إكسا: `9091234567 35.5`

🔢︙للعمليات المتعددة:
`9091234567 20`
`9092345678 25`
`9093456789 15`

💡︙يتم خصم المبلغ من رصيد السلفة المستحقة"""

            await update.message.reply_text(
                message,
                reply_markup=self.get_balance_management_keyboard(),
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            logger.error(f"❌ خطأ في زر خصم سلفة: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في خصم السلفة",
                reply_markup=self.get_balance_management_keyboard()
            )

    async def show_transactions_button(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """زر عرض المعاملات - تفاعلي"""
        try:
            # التحقق من المدير
            if not self.is_admin(update.effective_user.id):
                await update.message.reply_text("❌ غير مصرح لك بالوصول")
                return

            user_id = update.effective_user.id

            # تعيين حالة المستخدم
            self.user_states[user_id] = {
                'action': 'show_transactions',
                'step': 'waiting_wallet_number'
            }

            message = """📊︙عرض المعاملات

🔍︙أدخل رقم المحفظة لعرض جميع معاملاتها

💡︙يمكنك إدخال:
• رقم المحفظة (مثل: 9091234567)
• عدة أرقام محافظ مفصولة بمسافات
• كلمة "الكل" لعرض معاملات جميع المحافظ

📝︙مثال:
`9091234567`
`9091234567 9092345678`
`الكل`

📋︙سيتم عرض:
• معاملات الرصيد العادي
• معاملات السلف
• التواريخ والمبالغ
• الأرصدة قبل وبعد كل معاملة"""

            await update.message.reply_text(
                message,
                reply_markup=self.get_balance_management_keyboard(),
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            logger.error(f"❌ خطأ في زر عرض المعاملات: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في عرض المعاملات",
                reply_markup=self.get_balance_management_keyboard()
            )

    async def balance_transactions_button(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """زر المعاملات الرصيد - تفاعلي"""
        try:
            # التحقق من المدير
            if not self.is_admin(update.effective_user.id):
                await update.message.reply_text("❌ غير مصرح لك بالوصول")
                return

            user_id = update.effective_user.id

            # تعيين حالة المستخدم
            self.user_states[user_id] = {
                'action': 'balance_transactions',
                'step': 'waiting_wallet_number'
            }

            message = """💰︙المعاملات الرصيد

🔍︙أدخل رقم المحفظة لعرض معاملات الرصيد العادي فقط

💡︙يمكنك إدخال:
• رقم المحفظة (مثل: 9091234567)
• عدة أرقام محافظ مفصولة بمسافات
• كلمة "الكل" لعرض معاملات الرصيد لجميع المحافظ

📝︙مثال:
`9091234567`
`9091234567 9092345678`
`الكل`

📋︙سيتم عرض:
• عمليات إضافة الرصيد
• عمليات خصم الرصيد
• التواريخ والمبالغ
• الأرصدة قبل وبعد كل معاملة
• إجمالي الإضافات والخصومات"""

            await update.message.reply_text(
                message,
                reply_markup=self.get_balance_management_keyboard(),
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            logger.error(f"❌ خطأ في زر المعاملات الرصيد: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في عرض معاملات الرصيد",
                reply_markup=self.get_balance_management_keyboard()
            )

    async def loan_transactions_button(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """زر المعاملات السلفة - تفاعلي"""
        try:
            # التحقق من المدير
            if not self.is_admin(update.effective_user.id):
                await update.message.reply_text("❌ غير مصرح لك بالوصول")
                return

            user_id = update.effective_user.id

            # تعيين حالة المستخدم
            self.user_states[user_id] = {
                'action': 'loan_transactions',
                'step': 'waiting_wallet_number'
            }

            message = """🏦︙المعاملات السلفة

🔍︙أدخل رقم المحفظة لعرض معاملات السلف فقط

💡︙يمكنك إدخال:
• رقم المحفظة (مثل: 9091234567)
• عدة أرقام محافظ مفصولة بمسافات
• كلمة "الكل" لعرض معاملات السلف لجميع المحافظ

📝︙مثال:
`9091234567`
`9091234567 9092345678`
`الكل`

📋︙سيتم عرض:
• عمليات إضافة السلف
• عمليات خصم السلف
• التواريخ والمبالغ
• أرصدة السلف قبل وبعد كل معاملة
• إجمالي السلف المستحقة والمسددة"""

            await update.message.reply_text(
                message,
                reply_markup=self.get_balance_management_keyboard(),
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            logger.error(f"❌ خطأ في زر المعاملات السلفة: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في عرض معاملات السلفة",
                reply_markup=self.get_balance_management_keyboard()
            )

    async def handle_interactive_state(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج الحالات التفاعلية"""
        try:
            user_id = update.effective_user.id
            user_state = self.user_states.get(user_id)

            if not user_state:
                return

            action = user_state.get('action')
            text = update.message.text.strip()

            # معالجة الإلغاء
            if text.lower() in ['إلغاء', 'cancel', 'الغاء']:
                del self.user_states[user_id]
                await update.message.reply_text(
                    "❌ تم إلغاء العملية",
                    reply_markup=self.get_balance_management_keyboard()
                )
                return

            # معالجة العمليات المختلفة
            if action == 'wallet_details':
                await self.process_wallet_details(update, context, text)
            elif action == 'add_balance':
                await self.process_add_balance(update, context, text)
            elif action == 'deduct_balance':
                await self.process_deduct_balance(update, context, text)
            elif action == 'add_loan':
                await self.process_add_loan(update, context, text)
            elif action == 'deduct_loan':
                await self.process_deduct_loan(update, context, text)
            elif action == 'show_transactions':
                await self.process_show_transactions(update, context, text)
            elif action == 'balance_transactions':
                await self.process_balance_transactions(update, context, text)
            elif action == 'loan_transactions':
                await self.process_loan_transactions(update, context, text)

            # إزالة الحالة بعد المعالجة
            if user_id in self.user_states:
                del self.user_states[user_id]

        except Exception as e:
            logger.error(f"❌ خطأ في معالج الحالات التفاعلية: {e}")
            user_id = update.effective_user.id
            if user_id in self.user_states:
                del self.user_states[user_id]
            await update.message.reply_text(
                "❌ حدث خطأ في العملية",
                reply_markup=self.get_balance_management_keyboard()
            )

    async def process_wallet_details(self, update: Update, context: ContextTypes.DEFAULT_TYPE, text: str):
        """معالجة طلب تفاصيل المحفظة"""
        try:
            if text.lower() == 'الكل':
                # عرض جميع المحافظ
                await self.show_all_wallets_details(update, context)
            else:
                # معالجة أرقام المحافظ
                wallet_numbers = text.split()
                await self.show_specific_wallets_details(update, context, wallet_numbers)

        except Exception as e:
            logger.error(f"❌ خطأ في معالجة تفاصيل المحفظة: {e}")
            await update.message.reply_text("❌ حدث خطأ في عرض تفاصيل المحفظة")

    async def process_add_balance(self, update: Update, context: ContextTypes.DEFAULT_TYPE, text: str):
        """معالجة طلب إضافة رصيد"""
        try:
            lines = text.strip().split('\n')
            for line in lines:
                parts = line.strip().split()
                if len(parts) >= 2:
                    wallet_number = parts[0]
                    try:
                        amount = float(parts[1])
                        # محاكاة استدعاء الأمر الأصلي
                        context.args = [wallet_number, str(amount)]
                        await self.add_balance_wallet_command(update, context)
                    except ValueError:
                        await update.message.reply_text(f"❌ المبلغ غير صحيح في السطر: {line}")
                else:
                    await update.message.reply_text(f"❌ تنسيق غير صحيح في السطر: {line}")

        except Exception as e:
            logger.error(f"❌ خطأ في معالجة إضافة الرصيد: {e}")
            await update.message.reply_text("❌ حدث خطأ في إضافة الرصيد")

    async def process_deduct_balance(self, update: Update, context: ContextTypes.DEFAULT_TYPE, text: str):
        """معالجة طلب خصم رصيد"""
        try:
            lines = text.strip().split('\n')
            for line in lines:
                parts = line.strip().split()
                if len(parts) >= 2:
                    wallet_number = parts[0]
                    try:
                        amount = float(parts[1])
                        # محاكاة استدعاء الأمر الأصلي
                        context.args = [wallet_number, str(amount)]
                        await self.deduct_balance_command(update, context)
                    except ValueError:
                        await update.message.reply_text(f"❌ المبلغ غير صحيح في السطر: {line}")
                else:
                    await update.message.reply_text(f"❌ تنسيق غير صحيح في السطر: {line}")

        except Exception as e:
            logger.error(f"❌ خطأ في معالجة خصم الرصيد: {e}")
            await update.message.reply_text("❌ حدث خطأ في خصم الرصيد")

    async def process_add_loan(self, update: Update, context: ContextTypes.DEFAULT_TYPE, text: str):
        """معالجة طلب إضافة سلفة"""
        try:
            lines = text.strip().split('\n')
            for line in lines:
                parts = line.strip().split()
                if len(parts) >= 2:
                    wallet_number = parts[0]
                    try:
                        amount = float(parts[1])
                        # محاكاة استدعاء الأمر الأصلي
                        context.args = [wallet_number, str(amount)]
                        await self.add_loan_balance_command(update, context)
                    except ValueError:
                        await update.message.reply_text(f"❌ المبلغ غير صحيح في السطر: {line}")
                else:
                    await update.message.reply_text(f"❌ تنسيق غير صحيح في السطر: {line}")

        except Exception as e:
            logger.error(f"❌ خطأ في معالجة إضافة السلفة: {e}")
            await update.message.reply_text("❌ حدث خطأ في إضافة السلفة")

    async def process_deduct_loan(self, update: Update, context: ContextTypes.DEFAULT_TYPE, text: str):
        """معالجة طلب خصم سلفة"""
        try:
            lines = text.strip().split('\n')
            for line in lines:
                parts = line.strip().split()
                if len(parts) >= 2:
                    wallet_number = parts[0]
                    try:
                        amount = float(parts[1])
                        # محاكاة استدعاء الأمر الأصلي
                        context.args = [wallet_number, str(amount)]
                        await self.deduct_loan_balance_command(update, context)
                    except ValueError:
                        await update.message.reply_text(f"❌ المبلغ غير صحيح في السطر: {line}")
                else:
                    await update.message.reply_text(f"❌ تنسيق غير صحيح في السطر: {line}")

        except Exception as e:
            logger.error(f"❌ خطأ في معالجة خصم السلفة: {e}")
            await update.message.reply_text("❌ حدث خطأ في خصم السلفة")

    async def process_show_transactions(self, update: Update, context: ContextTypes.DEFAULT_TYPE, text: str):
        """معالجة طلب عرض المعاملات"""
        try:
            if text.lower() == 'الكل':
                # عرض معاملات جميع المحافظ
                await self.show_all_transactions(update, context)
            else:
                # معالجة أرقام المحافظ
                wallet_numbers = text.split()
                for wallet_number in wallet_numbers:
                    if wallet_number.isdigit() and len(wallet_number) == 10:
                        # محاكاة استدعاء الأمر الأصلي
                        context.args = [wallet_number]
                        await self.balance_history_command(update, context)
                    else:
                        await update.message.reply_text(f"❌ رقم المحفظة غير صحيح: {wallet_number}")

        except Exception as e:
            logger.error(f"❌ خطأ في معالجة عرض المعاملات: {e}")
            await update.message.reply_text("❌ حدث خطأ في عرض المعاملات")

    async def process_balance_transactions(self, update: Update, context: ContextTypes.DEFAULT_TYPE, text: str):
        """معالجة طلب معاملات الرصيد"""
        try:
            if text.lower() == 'الكل':
                # عرض معاملات الرصيد لجميع المحافظ
                await self.show_all_balance_transactions(update, context)
            else:
                # معالجة أرقام المحافظ
                wallet_numbers = text.split()
                for wallet_number in wallet_numbers:
                    if wallet_number.isdigit() and len(wallet_number) == 10:
                        # عرض معاملات الرصيد فقط
                        await self.show_wallet_balance_transactions(update, context, wallet_number)
                    else:
                        await update.message.reply_text(f"❌ رقم المحفظة غير صحيح: {wallet_number}")

        except Exception as e:
            logger.error(f"❌ خطأ في معالجة معاملات الرصيد: {e}")
            await update.message.reply_text("❌ حدث خطأ في عرض معاملات الرصيد")

    async def process_loan_transactions(self, update: Update, context: ContextTypes.DEFAULT_TYPE, text: str):
        """معالجة طلب معاملات السلفة"""
        try:
            if text.lower() == 'الكل':
                # عرض معاملات السلفة لجميع المحافظ
                await self.show_all_loan_transactions(update, context)
            else:
                # معالجة أرقام المحافظ
                wallet_numbers = text.split()
                for wallet_number in wallet_numbers:
                    if wallet_number.isdigit() and len(wallet_number) == 10:
                        # عرض معاملات السلفة فقط
                        await self.show_wallet_loan_transactions(update, context, wallet_number)
                    else:
                        await update.message.reply_text(f"❌ رقم المحفظة غير صحيح: {wallet_number}")

        except Exception as e:
            logger.error(f"❌ خطأ في معالجة معاملات السلفة: {e}")
            await update.message.reply_text("❌ حدث خطأ في عرض معاملات السلفة")

    # ==================== وظائف مساعدة للعرض ====================

    async def show_all_wallets_details(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض تفاصيل جميع المحافظ"""
        try:
            from shared.database.wallet_manager import WalletManager
            wallet_manager = WalletManager()
            wallets_data = wallet_manager.load_wallets_data()

            if not wallets_data:
                await update.message.reply_text(
                    "❌ لا توجد محافظ مسجلة في النظام",
                    reply_markup=self.get_balance_management_keyboard()
                )
                return

            message = "📋︙تفاصيل جميع المحافظ\n\n"

            # ترقيم المحافظ
            wallet_counter = 1
            for wallet_number, wallet_info in wallets_data.items():
                balance = wallet_info.get('balance', 0)
                loan_balance = wallet_info.get('loan_balance', 0)
                user_name = wallet_info.get('user_name', 'غير محدد')
                username = wallet_info.get('username', 'غير محدد')
                user_id = wallet_info.get('user_id', 'غير محدد')

                # تنسيق الأرقام بدون عشري إذا كانت صحيحة
                balance_display = f"{balance:.0f}" if balance == int(balance) else f"{balance:.2f}"
                loan_display = f"{loan_balance:.0f}" if loan_balance == int(loan_balance) else f"{loan_balance:.2f}"

                # تنسيق المعرف
                username_display = f"@{username}" if username != 'غير محدد' and not username.startswith('@') else username

                message += f"🔢︙رقم︙ {wallet_counter}\n"
                message += f"💰︙المحفظة︙ {wallet_number}\n"
                message += f"👤︙الأسم︙ {user_name}\n"
                message += f"📧︙المعرف︙ {username_display}\n"
                message += f"🆔︙الأيدي︙ {user_id}\n"
                message += f"💎︙الرصيد︙{balance_display} إكسا\n"
                message += f"💳︙السلفة︙{loan_display} إكسا\n\n"

                wallet_counter += 1

            await update.message.reply_text(
                message,
                reply_markup=self.get_balance_management_keyboard()
            )

        except Exception as e:
            logger.error(f"❌ خطأ في عرض جميع المحافظ: {e}")
            await update.message.reply_text("❌ حدث خطأ في عرض المحافظ")

    async def show_specific_wallets_details(self, update: Update, context: ContextTypes.DEFAULT_TYPE, wallet_numbers: list):
        """عرض تفاصيل محافظ محددة"""
        try:
            from shared.database.wallet_manager import WalletManager
            wallet_manager = WalletManager()
            wallets_data = wallet_manager.load_wallets_data()

            if not wallets_data:
                await update.message.reply_text(
                    "❌ لا توجد محافظ مسجلة في النظام",
                    reply_markup=self.get_balance_management_keyboard()
                )
                return

            message = "📋︙تفاصيل المحافظ المحددة\n\n"
            wallet_counter = 1
            found_wallets = 0

            for wallet_number in wallet_numbers:
                if wallet_number.isdigit() and len(wallet_number) == 10:
                    if wallet_number in wallets_data:
                        wallet_info = wallets_data[wallet_number]
                        balance = wallet_info.get('balance', 0)
                        loan_balance = wallet_info.get('loan_balance', 0)
                        user_name = wallet_info.get('user_name', 'غير محدد')
                        username = wallet_info.get('username', 'غير محدد')
                        user_id = wallet_info.get('user_id', 'غير محدد')

                        # تنسيق الأرقام بدون عشري إذا كانت صحيحة
                        balance_display = f"{balance:.0f}" if balance == int(balance) else f"{balance:.2f}"
                        loan_display = f"{loan_balance:.0f}" if loan_balance == int(loan_balance) else f"{loan_balance:.2f}"

                        # تنسيق المعرف
                        username_display = f"@{username}" if username != 'غير محدد' and not username.startswith('@') else username

                        message += f"🔢︙رقم︙ {wallet_counter}\n"
                        message += f"💰︙المحفظة︙ {wallet_number}\n"
                        message += f"👤︙الأسم︙ {user_name}\n"
                        message += f"📧︙المعرف︙ {username_display}\n"
                        message += f"🆔︙الأيدي︙ {user_id}\n"
                        message += f"💎︙الرصيد︙{balance_display} إكسا\n"
                        message += f"💳︙السلفة︙{loan_display} إكسا\n\n"

                        wallet_counter += 1
                        found_wallets += 1
                    else:
                        message += f"❌ المحفظة {wallet_number} غير موجودة\n\n"
                else:
                    message += f"❌ رقم المحفظة غير صحيح: {wallet_number}\n\n"

            if found_wallets == 0:
                message += "❌ لم يتم العثور على أي محفظة صحيحة"

            await update.message.reply_text(
                message,
                reply_markup=self.get_balance_management_keyboard()
            )

        except Exception as e:
            logger.error(f"❌ خطأ في عرض المحافظ المحددة: {e}")
            await update.message.reply_text("❌ حدث خطأ في عرض تفاصيل المحافظ")

    async def show_all_transactions(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض جميع المعاملات"""
        try:
            await update.message.reply_text(
                "📊︙عرض جميع المعاملات\n\n"
                "🔄 جاري تحضير التقرير الشامل...",
                reply_markup=self.get_balance_management_keyboard()
            )
            # يمكن إضافة منطق عرض المعاملات هنا

        except Exception as e:
            logger.error(f"❌ خطأ في عرض جميع المعاملات: {e}")
            await update.message.reply_text("❌ حدث خطأ في عرض المعاملات")

    async def show_all_balance_transactions(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض جميع معاملات الرصيد"""
        try:
            await update.message.reply_text(
                "💰︙معاملات الرصيد لجميع المحافظ\n\n"
                "🔄 جاري تحضير تقرير معاملات الرصيد...",
                reply_markup=self.get_balance_management_keyboard()
            )
            # يمكن إضافة منطق عرض معاملات الرصيد هنا

        except Exception as e:
            logger.error(f"❌ خطأ في عرض معاملات الرصيد: {e}")
            await update.message.reply_text("❌ حدث خطأ في عرض معاملات الرصيد")

    async def show_all_loan_transactions(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض جميع معاملات السلفة"""
        try:
            await update.message.reply_text(
                "🏦︙معاملات السلفة لجميع المحافظ\n\n"
                "🔄 جاري تحضير تقرير معاملات السلفة...",
                reply_markup=self.get_balance_management_keyboard()
            )
            # يمكن إضافة منطق عرض معاملات السلفة هنا

        except Exception as e:
            logger.error(f"❌ خطأ في عرض معاملات السلفة: {e}")
            await update.message.reply_text("❌ حدث خطأ في عرض معاملات السلفة")

    async def show_wallet_balance_transactions(self, update: Update, context: ContextTypes.DEFAULT_TYPE, wallet_number: str):
        """عرض معاملات الرصيد لمحفظة محددة"""
        try:
            await update.message.reply_text(
                f"💰︙معاملات الرصيد للمحفظة {wallet_number}\n\n"
                "🔄 جاري تحضير التقرير...",
                reply_markup=self.get_balance_management_keyboard()
            )
            # يمكن إضافة منطق عرض معاملات الرصيد للمحفظة هنا

        except Exception as e:
            logger.error(f"❌ خطأ في عرض معاملات رصيد المحفظة: {e}")
            await update.message.reply_text("❌ حدث خطأ في عرض معاملات الرصيد")

    async def show_wallet_loan_transactions(self, update: Update, context: ContextTypes.DEFAULT_TYPE, wallet_number: str):
        """عرض معاملات السلفة لمحفظة محددة"""
        try:
            await update.message.reply_text(
                f"🏦︙معاملات السلفة للمحفظة {wallet_number}\n\n"
                "🔄 جاري تحضير التقرير...",
                reply_markup=self.get_balance_management_keyboard()
            )
            # يمكن إضافة منطق عرض معاملات السلفة للمحفظة هنا

        except Exception as e:
            logger.error(f"❌ خطأ في عرض معاملات سلفة المحفظة: {e}")
            await update.message.reply_text("❌ حدث خطأ في عرض معاملات السلفة")

    def run_sync(self):
        """تشغيل البوت"""
        try:
            logger.info("🛡️ بدء تشغيل بوت الإدارة والمراقبة الموحد...")
            
            # إنشاء التطبيق مع إعدادات شبكة محسنة
            self.app = (
                Application.builder()
                .token(self.token)
                .connect_timeout(NETWORK_CONFIG['connect_timeout'])
                .read_timeout(NETWORK_CONFIG['read_timeout'])
                .write_timeout(NETWORK_CONFIG['write_timeout'])
                .pool_timeout(NETWORK_CONFIG['pool_timeout'])
                .connection_pool_size(NETWORK_CONFIG['connection_pool_size'])
                .build()
            )
            
            # إعداد المعالجات
            self.setup_handlers()

            # تم إزالة فحص الاتصال لتجنب مشاكل event loop

            logger.info("🛡️ بوت الإدارة والمراقبة الموحد يعمل الآن...")

            # تشغيل البوت مع إعادة المحاولة
            max_retries = NETWORK_CONFIG['max_retries']
            retry_delay = NETWORK_CONFIG['retry_delay']

            for attempt in range(max_retries):
                try:
                    logger.info(f"🔄 محاولة الاتصال {attempt + 1}/{max_retries}...")
                    self.app.run_polling(
                        drop_pending_updates=True,
                        allowed_updates=["message", "callback_query"]
                    )
                    break  # نجح الاتصال
                except Exception as polling_error:
                    error_msg = str(polling_error)
                    if "httpx" in error_msg.lower() or "http" in error_msg.lower():
                        logger.error(f"❌ خطأ شبكة في المحاولة {attempt + 1}: مشكلة في الاتصال بـ Telegram")
                    elif "conflict" in error_msg.lower():
                        logger.error(f"❌ تضارب في المحاولة {attempt + 1}: يوجد بوت آخر يعمل")
                    else:
                        logger.error(f"❌ فشل في المحاولة {attempt + 1}: {polling_error}")

                    if attempt < max_retries - 1:
                        logger.info(f"⏳ انتظار {retry_delay} ثانية قبل المحاولة التالية...")
                        import time
                        time.sleep(retry_delay)
                    else:
                        logger.error("❌ فشل في جميع محاولات الاتصال")
                        raise polling_error

        except Exception as e:
            logger.error(f"❌ خطأ في تشغيل بوت الإدارة والمراقبة: {e}")
            raise

    # ==================== إحصائيات التوكن والديون ====================

    async def show_token_statistics(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض إحصائيات التوكن والاستخدام"""
        try:
            if not self.usage_tracker:
                await update.message.reply_text(
                    "❌ نظام إحصائيات التوكن غير متوفر",
                    reply_markup=self.get_reply_keyboard()
                )
                return

            # الحصول على الإحصائيات العامة
            overall_stats = self.usage_tracker.get_overall_usage_stats()
            daily_stats = self.usage_tracker.get_daily_usage_stats(7)  # آخر 7 أيام
            top_users = self.usage_tracker.get_top_users_by_usage(5)  # أكثر 5 مستخدمين
            unusual_usage = self.usage_tracker.detect_unusual_usage()

            # تنسيق الرسالة
            message = "🤖 **إحصائيات التوكن والاستخدام**\n\n"

            # الإحصائيات العامة
            message += "📊 **الإحصائيات العامة:**\n"
            message += f"• إجمالي الطلبات: {overall_stats.get('total_requests', 0)}\n"
            message += f"• إجمالي المستخدمين: {overall_stats.get('total_users', 0)}\n"
            message += f"• إجمالي التوكن: {overall_stats.get('total_tokens', 0):,}\n"
            message += f"• إجمالي التكلفة: {overall_stats.get('total_cost', 0):.6f} إكسا\n"
            message += f"• متوسط التوكن/طلب: {overall_stats.get('avg_tokens_per_request', 0):.0f}\n"
            message += f"• متوسط التكلفة/طلب: {overall_stats.get('avg_cost_per_request', 0):.6f} إكسا\n\n"

            # أكثر المستخدمين استخداماً
            if top_users:
                message += "👥 **أكثر المستخدمين استخداماً:**\n"
                for i, user in enumerate(top_users[:3], 1):
                    message += f"{i}. {user['user_name']} - {user['total_tokens']:,} توكن\n"
                message += "\n"

            # تحذيرات الاستخدام غير الطبيعي
            if unusual_usage:
                message += "⚠️ **تحذيرات الاستخدام:**\n"
                for user in unusual_usage[:2]:
                    message += f"• {user['user_name']}: {user['max_daily_tokens']:,} توكن/يوم\n"
                message += "\n"

            # معلومات إضافية
            message += f"📅 **آخر تحديث:** {overall_stats.get('last_updated', 'غير محدد')}\n"
            message += f"🕐 **أول استخدام:** {overall_stats.get('first_usage', 'غير محدد')}\n"
            message += f"🕐 **آخر استخدام:** {overall_stats.get('last_usage', 'غير محدد')}"

            await update.message.reply_text(
                message,
                reply_markup=self.get_reply_keyboard(),
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            logger.error(f"خطأ في عرض إحصائيات التوكن: {e}")
            await update.message.reply_text(
                f"❌ حدث خطأ في عرض الإحصائيات: {e}",
                reply_markup=self.get_reply_keyboard()
            )

    async def show_debt_management(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض إدارة الديون"""
        try:
            if not self.token_manager:
                await update.message.reply_text(
                    "❌ نظام إدارة الديون غير متوفر",
                    reply_markup=self.get_reply_keyboard()
                )
                return

            # الحصول على إحصائيات الديون من مدير المحافظ
            from database.wallet_manager import WalletManager
            wallet_manager = WalletManager()
            debt_stats = wallet_manager.get_debt_statistics()

            if not debt_stats:
                await update.message.reply_text(
                    "❌ لا يمكن الحصول على إحصائيات الديون",
                    reply_markup=self.get_reply_keyboard()
                )
                return

            # تنسيق الرسالة
            message = "💳 **إدارة الديون**\n\n"

            # الإحصائيات العامة
            message += "📊 **الإحصائيات العامة:**\n"
            message += f"• إجمالي المستخدمين المدينين: {debt_stats.get('total_users_with_debt', 0)}\n"
            message += f"• إجمالي مبلغ الديون: {debt_stats.get('total_debt_amount', 0):.3f} إكسا\n"
            message += f"• متوسط الدين/مستخدم: {debt_stats.get('average_debt_amount', 0):.3f} إكسا\n\n"

            # قائمة المستخدمين المدينين
            users_with_debt = debt_stats.get('users_with_debt', [])
            if users_with_debt:
                message += "👥 **المستخدمون المدينون:**\n"
                for user in users_with_debt[:5]:  # أول 5 مستخدمين
                    message += f"• {user['user_name']}: {user['debt_amount']:.3f} إكسا\n"
                    message += f"  📱 المحفظة: `{user['wallet_number']}`\n"

                if len(users_with_debt) > 5:
                    message += f"... و {len(users_with_debt) - 5} مستخدم آخر\n"
            else:
                message += "✅ **لا يوجد مستخدمون مدينون حالياً**\n"

            message += f"\n📅 **آخر تحديث:** {debt_stats.get('last_updated', 'غير محدد')}"

            await update.message.reply_text(
                message,
                reply_markup=self.get_reply_keyboard(),
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            logger.error(f"خطأ في عرض إدارة الديون: {e}")
            await update.message.reply_text(
                f"❌ حدث خطأ في عرض إدارة الديون: {e}",
                reply_markup=self.get_reply_keyboard()
            )

    async def show_advanced_reports_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض قائمة التقارير المتقدمة"""
        try:
            if not self.reports_manager:
                await update.message.reply_text(
                    "❌ نظام التقارير المتقدمة غير متوفر",
                    reply_markup=self.get_reply_keyboard()
                )
                return

            # إنشاء لوحة مفاتيح للتقارير
            keyboard = [
                ["📊 تقرير مالي شامل", "📈 تحليل الاستخدام"],
                ["💳 تحليل الديون", "📋 تقرير شامل"],
                ["🔙 العودة للقائمة الرئيسية"]
            ]

            reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True)

            message = """📊 **التقارير المتقدمة**

اختر نوع التقرير المطلوب:

📊 **تقرير مالي شامل** - تحليل الإيرادات والتكاليف
📈 **تحليل الاستخدام** - أنماط استخدام المستخدمين
💳 **تحليل الديون** - حالة الديون والتوصيات
📋 **تقرير شامل** - جميع التحليلات معاً

🔍 جميع التقارير تشمل رسوم بيانية وتحليلات متقدمة"""

            await update.message.reply_text(
                message,
                reply_markup=reply_markup,
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            logger.error(f"خطأ في عرض قائمة التقارير المتقدمة: {e}")
            await update.message.reply_text(
                f"❌ حدث خطأ في عرض التقارير: {e}",
                reply_markup=self.get_reply_keyboard()
            )

    async def show_financial_analysis(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض التحليل المالي"""
        try:
            if not self.reports_manager:
                await update.message.reply_text(
                    "❌ نظام التقارير غير متوفر",
                    reply_markup=self.get_reply_keyboard()
                )
                return

            # إنشاء التقرير المالي لآخر 30 يوم
            financial_report = self.reports_manager.generate_financial_report(30)

            if "error" in financial_report:
                await update.message.reply_text(
                    f"❌ خطأ في إنشاء التقرير المالي: {financial_report['error']}",
                    reply_markup=self.get_reply_keyboard()
                )
                return

            # تنسيق الرسالة
            message = "📈 **التحليل المالي - آخر 30 يوم**\n\n"

            # الإحصائيات الأساسية
            message += "💰 **الإيرادات:**\n"
            message += f"• إجمالي الإيرادات: {financial_report.get('total_revenue', 0):.6f} إكسا\n"
            message += f"• متوسط يومي: {financial_report.get('daily_avg_revenue', 0):.6f} إكسا\n"
            message += f"• أفضل يوم: {financial_report.get('best_day', {}).get('date', 'غير محدد')} ({financial_report.get('best_day', {}).get('revenue', 0):.6f} إكسا)\n\n"

            # تحليل الدفع
            message += "💳 **طرق الدفع:**\n"
            message += f"• من الرصيد: {financial_report.get('balance_percentage', 0):.1f}% ({financial_report.get('balance_revenue', 0):.6f} إكسا)\n"
            message += f"• بالدين: {financial_report.get('debt_percentage', 0):.1f}% ({financial_report.get('debt_amount', 0):.6f} إكسا)\n\n"

            # إحصائيات الاستخدام
            message += "📊 **الاستخدام:**\n"
            message += f"• إجمالي الطلبات: {financial_report.get('total_requests', 0):,}\n"
            message += f"• المستخدمين النشطين: {financial_report.get('unique_users', 0)}\n"
            message += f"• متوسط الإيراد/مستخدم: {financial_report.get('avg_revenue_per_user', 0):.6f} إكسا\n"
            message += f"• متوسط الإيراد/طلب: {financial_report.get('avg_revenue_per_request', 0):.6f} إكسا\n\n"

            # حالة المحافظ
            message += "🏦 **حالة المحافظ:**\n"
            message += f"• إجمالي المحافظ: {financial_report.get('total_wallets', 0)}\n"
            message += f"• إجمالي الرصيد: {financial_report.get('total_balance', 0):.3f} إكسا\n"
            message += f"• إجمالي الديون: {financial_report.get('total_debt', 0):.3f} إكسا\n"
            message += f"• نسبة الديون للإيرادات: {financial_report.get('debt_to_revenue_ratio', 0):.1f}%\n"
            message += f"• نسبة المستخدمين النشطين: {financial_report.get('active_users_percentage', 0):.1f}%"

            await update.message.reply_text(
                message,
                reply_markup=self.get_reply_keyboard(),
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            logger.error(f"خطأ في عرض التحليل المالي: {e}")
            await update.message.reply_text(
                f"❌ حدث خطأ في التحليل المالي: {e}",
                reply_markup=self.get_reply_keyboard()
            )

    async def show_backup_management(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض إدارة النسخ الاحتياطي"""
        try:
            if not self.backup_manager:
                await update.message.reply_text(
                    "❌ نظام النسخ الاحتياطي غير متوفر",
                    reply_markup=self.get_reply_keyboard()
                )
                return

            # الحصول على إحصائيات النسخ الاحتياطية
            backup_stats = self.backup_manager.get_backup_statistics()
            backups_list = self.backup_manager.list_backups()

            # تنسيق الرسالة
            message = "💾 **إدارة النسخ الاحتياطي**\n\n"

            # الإحصائيات العامة
            message += "📊 **الإحصائيات:**\n"
            message += f"• إجمالي النسخ: {backup_stats.get('total_backups', 0)}\n"
            message += f"• الحجم الإجمالي: {backup_stats.get('total_size_mb', 0):.2f} ميجابايت\n"
            message += f"• متوسط الحجم: {backup_stats.get('avg_size_mb', 0):.2f} ميجابايت\n\n"

            # آخر نسخة احتياطية
            latest_backup = backup_stats.get('latest_backup')
            if latest_backup:
                message += "📅 **آخر نسخة احتياطية:**\n"
                message += f"• الاسم: {latest_backup['name']}\n"
                message += f"• التاريخ: {latest_backup['created_at']}\n"
                message += f"• الحجم: {latest_backup['size_mb']:.2f} ميجابايت\n\n"

            # قائمة النسخ الأخيرة
            if backups_list:
                message += "📋 **النسخ الأخيرة:**\n"
                for backup in backups_list[:3]:  # أول 3 نسخ
                    status = "🗜️" if backup['is_compressed'] else "📁"
                    message += f"{status} {backup['name']} ({backup['size_mb']:.1f} MB)\n"

                if len(backups_list) > 3:
                    message += f"... و {len(backups_list) - 3} نسخة أخرى\n"
            else:
                message += "📋 **لا توجد نسخ احتياطية**\n"

            message += "\n💡 **الأوامر المتاحة:**\n"
            message += "• `/create_backup` - إنشاء نسخة احتياطية جديدة\n"
            message += "• `/list_backups` - عرض جميع النسخ\n"
            message += "• `/backup_stats` - إحصائيات مفصلة"

            await update.message.reply_text(
                message,
                reply_markup=self.get_reply_keyboard(),
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            logger.error(f"خطأ في عرض إدارة النسخ الاحتياطي: {e}")
            await update.message.reply_text(
                f"❌ حدث خطأ في إدارة النسخ الاحتياطي: {e}",
                reply_markup=self.get_reply_keyboard()
            )

    async def show_security_management(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض إدارة الأمان والحماية"""
        try:
            if not self.security_manager:
                await update.message.reply_text(
                    "❌ نظام الأمان غير متوفر",
                    reply_markup=self.get_reply_keyboard()
                )
                return

            # الحصول على إحصائيات الأمان
            security_stats = self.security_manager.get_security_statistics()

            # تنسيق الرسالة
            message = "🔒 **الأمان والحماية**\n\n"

            # الإحصائيات العامة
            message += "📊 **إحصائيات الأمان:**\n"
            message += f"• إجمالي الأحداث: {security_stats.get('total_events', 0)}\n"
            message += f"• أحداث آخر 24 ساعة: {security_stats.get('recent_events_24h', 0)}\n"
            message += f"• المحاولات الفاشلة المتتبعة: {security_stats.get('failed_attempts_tracking', 0)}\n"
            message += f"• معدل الطلبات المتتبع: {security_stats.get('rate_limit_tracking', 0)}\n\n"

            # حالة الأنظمة
            message += "⚙️ **حالة الأنظمة:**\n"
            encryption_status = "🟢 مفعل" if security_stats.get('encryption_enabled') else "🔴 معطل"
            audit_status = "🟢 مفعل" if security_stats.get('audit_log_enabled') else "🔴 معطل"

            message += f"• التشفير: {encryption_status}\n"
            message += f"• سجل التدقيق: {audit_status}\n\n"

            # أنواع الأحداث
            event_types = security_stats.get('event_types', {})
            if event_types:
                message += "📋 **أنواع الأحداث:**\n"
                for event_type, count in list(event_types.items())[:5]:  # أول 5 أنواع
                    message += f"• {event_type}: {count}\n"
                message += "\n"

            # تحذيرات الأمان
            message += "⚠️ **تحذيرات الأمان:**\n"
            if security_stats.get('recent_events_24h', 0) > 100:
                message += "• نشاط مرتفع في آخر 24 ساعة\n"
            if security_stats.get('failed_attempts_tracking', 0) > 10:
                message += "• عدد كبير من المحاولات الفاشلة\n"

            if not any([
                security_stats.get('recent_events_24h', 0) > 100,
                security_stats.get('failed_attempts_tracking', 0) > 10
            ]):
                message += "• لا توجد تحذيرات حالياً ✅\n"

            message += f"\n📅 **آخر تحديث:** {security_stats.get('last_updated', 'غير محدد')}"

            await update.message.reply_text(
                message,
                reply_markup=self.get_reply_keyboard(),
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            logger.error(f"خطأ في عرض إدارة الأمان: {e}")
            await update.message.reply_text(
                f"❌ حدث خطأ في إدارة الأمان: {e}",
                reply_markup=self.get_reply_keyboard()
            )

    async def show_ai_performance_stats(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض إحصائيات أداء الذكاء الاصطناعي"""
        try:
            # محاولة الحصول على إحصائيات الأداء من إكسا برو
            try:
                from main.features.exa_ai_pro import ExaAiPro
                exa_pro = ExaAiPro()
                performance_stats = exa_pro.get_performance_stats()

                message = "🤖 **إحصائيات أداء الذكاء الاصطناعي**\n\n"

                # الإحصائيات الأساسية
                message += "📊 **الإحصائيات العامة:**\n"
                message += f"• إجمالي الطلبات: {performance_stats.get('total_requests', 0)}\n"
                message += f"• الطلبات الناجحة: {performance_stats.get('successful_requests', 0)}\n"
                message += f"• الطلبات الفاشلة: {performance_stats.get('failed_requests', 0)}\n"
                message += f"• أخطاء Timeout: {performance_stats.get('timeout_errors', 0)}\n\n"

                # معدلات النجاح
                message += "📈 **معدلات الأداء:**\n"
                message += f"• معدل النجاح: {performance_stats.get('success_rate', 0):.1f}%\n"
                message += f"• معدل Timeout: {performance_stats.get('timeout_rate', 0):.1f}%\n"
                message += f"• متوسط وقت الاستجابة: {performance_stats.get('avg_response_time', 0):.2f} ثانية\n\n"

                # آخر طلب
                last_request = performance_stats.get('last_request_time')
                if last_request:
                    message += f"🕐 **آخر طلب:** {last_request}\n\n"

                # تقييم الأداء
                success_rate = performance_stats.get('success_rate', 0)
                if success_rate >= 95:
                    message += "✅ **حالة النظام:** ممتاز\n"
                elif success_rate >= 85:
                    message += "🟡 **حالة النظام:** جيد\n"
                elif success_rate >= 70:
                    message += "🟠 **حالة النظام:** يحتاج تحسين\n"
                else:
                    message += "🔴 **حالة النظام:** مشاكل كبيرة\n"

                # توصيات
                timeout_rate = performance_stats.get('timeout_rate', 0)
                if timeout_rate > 20:
                    message += "\n⚠️ **تحذير:** معدل timeout مرتفع، يُنصح بمراجعة الاتصال\n"
                elif timeout_rate > 10:
                    message += "\n💡 **ملاحظة:** معدل timeout متوسط، مراقبة مستمرة مطلوبة\n"

            except Exception as import_error:
                message = "🤖 **إحصائيات أداء الذكاء الاصطناعي**\n\n"
                message += f"❌ لا يمكن الحصول على الإحصائيات: {import_error}\n\n"
                message += "💡 **الأوامر المتاحة:**\n"
                message += "• `/ai_performance` - عرض إحصائيات الأداء\n"
                message += "• تأكد من تشغيل النظام بشكل صحيح"

            await update.message.reply_text(
                message,
                reply_markup=self.get_reply_keyboard(),
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            logger.error(f"خطأ في عرض إحصائيات أداء الذكاء الاصطناعي: {e}")
            await update.message.reply_text(
                f"❌ حدث خطأ في عرض إحصائيات الأداء: {e}",
                reply_markup=self.get_reply_keyboard()
            )

def main():
    """الدالة الرئيسية"""
    unified_bot = UnifiedAdminBot()
    unified_bot.run_sync()

if __name__ == "__main__":
    main()
