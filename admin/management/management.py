#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
بوت الإدارة المنفصل
مخصص فقط للوظائف الإدارية: إدارة المستخدمين، الإحصائيات، الرسائل الجماعية، إلخ
"""

import asyncio
import logging
import json
import os
import sys
from datetime import datetime
from telegram import Update, Bot, BotCommand, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, MessageHandler, filters, ContextTypes
from telegram.constants import ParseMode

# إضافة المسار للوصول للإعدادات
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'core'))

try:
    from admin_config import ADMIN_BOT_TOKEN, ADMIN_USER_ID, ADMIN_KEYBOARDS
except ImportError:
    # قيم افتراضية في حالة عدم وجود الملف
    ADMIN_BOT_TOKEN = "7633053725:AAH6Gkm6MJ9HSUcaruoAwhhZEc_BjhoQ6JQ"
    ADMIN_USER_ID = 591967813
    ADMIN_KEYBOARDS = {}

# إعداد نظام السجلات
log_dir = os.path.join(os.path.dirname(__file__), 'logs')
os.makedirs(log_dir, exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(log_dir, 'management.log'), encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class ManagementBot:
    """بوت الإدارة المنفصل"""
    
    def __init__(self):
        """تهيئة بوت الإدارة"""
        self.token = ADMIN_BOT_TOKEN
        self.admin_id = ADMIN_USER_ID
        self.app = None
        
        # بيانات الإدارة
        self.admin_data = {
            "users_count": 0,
            "banned_users": 0,
            "active_users": 0,
            "total_messages": 0,
            "last_backup": None,
            "system_status": "active"
        }
        
        # تحميل البيانات
        self.load_admin_data()
        
    def load_admin_data(self):
        """تحميل بيانات الإدارة"""
        try:
            data_file = os.path.join(os.path.dirname(__file__), '..', 'data', 'admin_data.json')
            if os.path.exists(data_file):
                with open(data_file, 'r', encoding='utf-8') as f:
                    self.admin_data.update(json.load(f))
        except Exception as e:
            logger.error(f"خطأ في تحميل بيانات الإدارة: {e}")
    
    def save_admin_data(self):
        """حفظ بيانات الإدارة"""
        try:
            data_dir = os.path.join(os.path.dirname(__file__), '..', 'data')
            os.makedirs(data_dir, exist_ok=True)
            data_file = os.path.join(data_dir, 'admin_data.json')
            
            with open(data_file, 'w', encoding='utf-8') as f:
                json.dump(self.admin_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"خطأ في حفظ بيانات الإدارة: {e}")
    
    def setup_handlers(self):
        """إعداد معالجات الأوامر والرسائل"""
        # معالجات الأوامر
        self.app.add_handler(CommandHandler("start", self.start_command))
        self.app.add_handler(CommandHandler("help", self.help_command))
        self.app.add_handler(CommandHandler("stats", self.stats_command))
        self.app.add_handler(CommandHandler("users", self.users_command))
        self.app.add_handler(CommandHandler("broadcast", self.broadcast_command))
        
        # معالج الرسائل العامة
        self.app.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_message))
        
        logger.info("✅ تم إعداد معالجات بوت الإدارة")
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج أمر البداية"""
        if update.effective_user.id != self.admin_id:
            await update.message.reply_text("❌ غير مصرح لك باستخدام هذا البوت")
            return
        
        welcome_message = """
🛡️ **مرحباً بك في بوت الإدارة**

هذا البوت مخصص للوظائف الإدارية فقط:

📊 **الوظائف المتاحة:**
• `/stats` - عرض الإحصائيات
• `/users` - إدارة المستخدمين  
• `/broadcast` - إرسال رسالة جماعية
• `/help` - المساعدة

🔧 **حالة النظام:** نشط
⏰ **الوقت:** {time}
        """.format(time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        
        await update.message.reply_text(welcome_message, parse_mode=ParseMode.MARKDOWN)
        logger.info(f"المدير {update.effective_user.first_name} بدأ جلسة إدارة")
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج أمر المساعدة"""
        if update.effective_user.id != self.admin_id:
            return
        
        help_text = """
❓ **مساعدة بوت الإدارة**

📋 **الأوامر المتاحة:**

🏠 `/start` - بدء البوت وعرض الترحيب
📊 `/stats` - عرض إحصائيات مفصلة
👥 `/users` - إدارة المستخدمين
📢 `/broadcast <رسالة>` - إرسال رسالة جماعية
❓ `/help` - عرض هذه المساعدة

🔧 **ملاحظات:**
• جميع الأوامر متاحة للمدير فقط
• يتم تسجيل جميع العمليات
• البوت يعمل بشكل منفصل عن بوت المراقبة
        """
        
        await update.message.reply_text(help_text, parse_mode=ParseMode.MARKDOWN)
    
    async def stats_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج أمر الإحصائيات"""
        if update.effective_user.id != self.admin_id:
            return
        
        stats_message = f"""
📊 **إحصائيات النظام**

👥 **المستخدمون:**
• إجمالي المستخدمين: {self.admin_data['users_count']}
• المستخدمون النشطون: {self.admin_data['active_users']}
• المستخدمون المحظورون: {self.admin_data['banned_users']}

💬 **الرسائل:**
• إجمالي الرسائل: {self.admin_data['total_messages']}

🔧 **النظام:**
• حالة النظام: {self.admin_data['system_status']}
• آخر نسخة احتياطية: {self.admin_data.get('last_backup', 'لم يتم')}

⏰ **وقت التحديث:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        """
        
        await update.message.reply_text(stats_message, parse_mode=ParseMode.MARKDOWN)
        logger.info("تم عرض الإحصائيات للمدير")
    
    async def users_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج أمر إدارة المستخدمين"""
        if update.effective_user.id != self.admin_id:
            return
        
        users_message = """
👥 **إدارة المستخدمين**

🔧 **الوظائف المتاحة:**
• عرض قائمة المستخدمين
• حظر/إلغاء حظر مستخدمين
• عرض تفاصيل مستخدم محدد
• إحصائيات المستخدمين

📝 **ملاحظة:** هذه الوظائف قيد التطوير
        """
        
        await update.message.reply_text(users_message, parse_mode=ParseMode.MARKDOWN)
    
    async def broadcast_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج أمر الرسائل الجماعية"""
        if update.effective_user.id != self.admin_id:
            return
        
        if not context.args:
            await update.message.reply_text(
                "📢 **إرسال رسالة جماعية**\n\n"
                "الاستخدام: `/broadcast <رسالتك هنا>`\n\n"
                "مثال: `/broadcast مرحباً بجميع المستخدمين!`",
                parse_mode=ParseMode.MARKDOWN
            )
            return
        
        message = " ".join(context.args)
        
        # هنا يمكن إضافة منطق إرسال الرسالة لجميع المستخدمين
        await update.message.reply_text(
            f"📢 **تم تحضير الرسالة للإرسال:**\n\n{message}\n\n"
            "⚠️ **ملاحظة:** وظيفة الإرسال الجماعي قيد التطوير",
            parse_mode=ParseMode.MARKDOWN
        )
        
        logger.info(f"تم تحضير رسالة جماعية: {message[:50]}...")
    
    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج الرسائل العامة"""
        if update.effective_user.id != self.admin_id:
            await update.message.reply_text("❌ غير مصرح لك باستخدام هذا البوت")
            return
        
        # تسجيل الرسالة
        logger.info(f"رسالة من المدير: {update.message.text[:50]}...")
        
        # رد تلقائي
        await update.message.reply_text(
            "📝 تم استلام رسالتك. استخدم الأوامر المتاحة للتفاعل مع البوت."
        )
    
    def run_sync(self):
        """تشغيل بوت الإدارة"""
        try:
            logger.info("🛡️ بدء تشغيل بوت الإدارة...")
            
            # إنشاء التطبيق
            self.app = Application.builder().token(self.token).build()
            
            # إعداد المعالجات
            self.setup_handlers()
            
            logger.info("🛡️ بوت الإدارة يعمل الآن...")
            
            # تشغيل البوت
            self.app.run_polling(drop_pending_updates=True)
            
        except Exception as e:
            logger.error(f"❌ خطأ في تشغيل بوت الإدارة: {e}")
            raise

def main():
    """الدالة الرئيسية"""
    management_bot = ManagementBot()
    management_bot.run_sync()

if __name__ == "__main__":
    main()
