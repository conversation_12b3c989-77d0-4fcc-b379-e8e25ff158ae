#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
معالجات العمليات المتقدمة لإدارة المستخدمين
"""

import logging
from datetime import datetime
from telegram import Update
from telegram.ext import ContextTypes
from telegram.constants import ParseMode
try:
    from .advanced_user_management import AdvancedUserManager
except ImportError:
    from advanced_user_management import AdvancedUserManager

logger = logging.getLogger(__name__)

class UserManagementProcessors:
    """معالجات عمليات إدارة المستخدمين"""
    
    def __init__(self, user_manager: AdvancedUserManager, keyboard_func):
        self.user_manager = user_manager
        self.get_keyboard = keyboard_func

    async def process_add_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE, text: str):
        """معالجة إضافة مستخدمين"""
        identifiers = self.user_manager.parse_user_identifiers(text)

        if not identifiers:
            await update.message.reply_text(
                "❌ لم يتم العثور على معرفات صحيحة\nيرجى المحاولة مرة أخرى",
                reply_markup=self.get_keyboard()
            )
            return

        message = f"➕︙نتائج إضافة المستخدمين\n\n"
        message += f"📝︙تم معالجة {len(identifiers)} معرف\n\n"

        for identifier in identifiers:
            # هنا يمكن إضافة منطق إرسال الدعوات
            # حالياً سنعرض فقط المعرفات المعالجة
            message += f"✅︙تم إرسال دعوة إلى : {identifier}\n"

        message += f"\n💡︙ملاحظة :\nتم إرسال دعوات الانضمام لجميع المعرفات"

        await update.message.reply_text(
            message,
                reply_markup=self.get_keyboard()
        )

    async def process_remove_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE, text: str):
        """معالجة إزالة مستخدمين مع رسالة تأكيد"""
        identifiers = self.user_manager.parse_user_identifiers(text)

        if not identifiers:
            await update.message.reply_text(
                "❌ لم يتم العثور على معرفات صحيحة",
                reply_markup=self.get_keyboard()
            )
            return

        found_users = self.user_manager.find_users_by_identifiers(identifiers)

        if not found_users:
            await update.message.reply_text(
                "❌ لم يتم العثور على أي مستخدمين مطابقين",
                reply_markup=self.get_keyboard()
            )
            return

        # تحديد النص حسب عدد المستخدمين (مفرد/جمع)
        user_count_total = len(found_users)
        if user_count_total == 1:
            title_text = "المستخدم"
            removal_text = "المستخدم التالي ومحفظته"
            question_text = "هل أنت متأكد من إزالة هذا المستخدم ومحفظته؟"
        else:
            title_text = "المستخدمين"
            removal_text = "المستخدمين التاليين ومحافظهم"
            question_text = "هل أنت متأكد من إزالة هؤلاء المستخدمين ومحافظهم؟"

        # إنشاء رسالة تأكيد مع تفاصيل المستخدمين
        confirmation_message = f"""⚠️︙تأكيد إزالة {title_text}

📋︙سيتم إزالة {removal_text} نهائياً :

"""

        # إضافة تفاصيل كل مستخدم
        user_count = 0
        for user_id, user_info in found_users.items():
            user_count += 1
            name = user_info.get('اسم المستخدم', 'غير محدد')
            username = user_info.get('معرف المستخدم', 'غير محدد')

            # الحصول على معلومات المحفظة
            wallet_info = self.user_manager.wallet_manager.get_user_wallet(int(user_id))
            wallet_text = wallet_info['wallet_number'] if wallet_info else "غير متوفرة"

            # إضافة رقم فقط إذا كان أكثر من مستخدم
            if user_count_total > 1:
                confirmation_message += f"🔢︙رقم : {user_count}\n"

            confirmation_message += f"""👤︙الاسم : {name}
📧︙المعرف : {username}
🆔︙الأيدي : {user_id}
💰︙المحفظة : {wallet_text}

"""

        confirmation_message += f"""🚨︙تحذير :
هذه العملية لا يمكن التراجع عنها!

{question_text}"""

        # إنشاء أزرار التأكيد
        from telegram import InlineKeyboardButton, InlineKeyboardMarkup

        # حفظ معرفات المستخدمين في السياق
        context.user_data['users_to_remove'] = list(found_users.keys())

        keyboard = [
            [
                InlineKeyboardButton("✅ تأكيد الإزالة", callback_data="confirm_remove_users"),
                InlineKeyboardButton("❌ إلغاء", callback_data="cancel_remove_users")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.message.reply_text(
            confirmation_message,
                reply_markup=reply_markup
        )

    async def confirm_remove_users(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """تأكيد إزالة المستخدمين"""
        try:
            query = update.callback_query
            await query.answer()

            logger.info("بدء عملية تأكيد إزالة المستخدمين")
            users_to_remove = context.user_data.get('users_to_remove', [])

            if not users_to_remove:
                await query.edit_message_text(
                    "❌ لا توجد مستخدمين للإزالة",
                reply_markup=None
                )
                return

            # تحديد النص حسب عدد المستخدمين (مفرد/جمع)
            user_text = "المستخدم" if len(users_to_remove) == 1 else "المستخدمين"

            # تنفيذ عملية الإزالة
            message = f"➖ نتائج إزالة {user_text}\n\n"

            removed_count = 0
            failed_count = 0
            removed_users_info = []  # لحفظ معلومات المستخدمين المحذوفين

            # معالجة كل مستخدم
            for user_id in users_to_remove:
                # الحصول على معلومات المستخدم قبل الحذف
                users_data = self.user_manager.load_users_data()
                user_info = users_data.get(user_id, {})
                user_name = user_info.get('اسم المستخدم', 'غير محدد')
                username = user_info.get('معرف المستخدم', 'غير محدد')

                # الحصول على معلومات المحفظة قبل الحذف
                wallet_info = self.user_manager.wallet_manager.get_user_wallet(int(user_id))
                wallet_number = wallet_info['wallet_number'] if wallet_info else "غير متوفرة"

                # حفظ المعلومات قبل الحذف
                user_data_backup = {
                    'user_id': user_id,
                    'name': user_name,
                    'username': username,
                    'wallet_number': wallet_number
                }

                # حذف المحفظة أولاً
                wallet_deleted, wallet_msg = self.user_manager.wallet_manager.delete_user_wallet(int(user_id))

                # ثم حذف المستخدم
                user_deleted, user_msg = self.user_manager.remove_user(user_id)

                if user_deleted:
                    removed_count += 1
                    # إضافة معلومات المستخدم المحذوف للقائمة
                    user_data_backup['wallet_deleted'] = wallet_deleted
                    user_data_backup['wallet_msg'] = wallet_msg
                    removed_users_info.append(user_data_backup)
                else:
                    failed_count += 1

            # إنشاء رسالة النتائج
            if removed_count > 0:
                # تحديد النص حسب عدد المحذوفين (مفرد/جمع)
                if removed_count == 1:
                    message += f"📝︙تم ازالة : المستخدم\n\n"
                else:
                    message += f"📝︙تم ازالة : {removed_count} من {len(users_to_remove)} مستخدم\n\n"

                # عرض تفاصيل المستخدمين المحذوفين من المعلومات المحفوظة
                for index, user_data in enumerate(removed_users_info, 1):
                    # إضافة رقم فقط إذا كان أكثر من مستخدم محذوف
                    if removed_count > 1:
                        message += f"🔢︙رقم : {index}\n"

                    message += f"👤︙الاسم : {user_data['name']}\n"
                    message += f"📧︙المعرف : {user_data['username']}\n"
                    message += f"🆔︙الأيدي : {user_data['user_id']}\n"
                    message += f"💰︙المحفظة : {user_data['wallet_number']}\n\n"

            # رسالة الملخص النهائية
            if removed_count > 0 and failed_count == 0:
                final_text = "المستخدم" if removed_count == 1 else "المستخدمين"
                message += f"✅︙تم حذف {final_text} ومحافظهم بنجاح"
            elif removed_count > 0 and failed_count > 0:
                message += f"⚠️︙تم حذف {removed_count} مستخدم، فشل حذف {failed_count} مستخدم"
            else:
                message += "❌︙لم يتم حذف أي مستخدم"

            # مسح البيانات من السياق
            context.user_data.pop('users_to_remove', None)

            await query.edit_message_text(
                message,
                reply_markup=None
            )
            logger.info("تم إكمال عملية إزالة المستخدمين بنجاح")

        except Exception as e:
            logger.error(f"خطأ في تأكيد إزالة المستخدمين: {e}")
            import traceback
            traceback.print_exc()
            try:
                await query.edit_message_text(
                    "❌ حدث خطأ في عملية الإزالة",
                reply_markup=None
                )
            except:
                pass

    async def cancel_remove_users(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """إلغاء إزالة المستخدمين"""
        try:
            query = update.callback_query
            await query.answer()

            logger.info("تم إلغاء عملية إزالة المستخدمين")

            # مسح البيانات من السياق
            context.user_data.pop('users_to_remove', None)

            await query.edit_message_text(
                "❌ تم إلغاء عملية إزالة المستخدمين\n\n"
                "لم يتم حذف أي مستخدم أو محفظة",
                reply_markup=None
            )

        except Exception as e:
            logger.error(f"خطأ في إلغاء إزالة المستخدمين: {e}")
            import traceback
            traceback.print_exc()
            try:
                await query.edit_message_text(
                    "❌ حدث خطأ في إلغاء العملية",
                reply_markup=None
                )
            except:
                pass

    
    async def process_ban_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE, text: str):
        """معالجة حظر مستخدمين"""
        identifiers = self.user_manager.parse_user_identifiers(text)
        
        if not identifiers:
            await update.message.reply_text(
                "❌ لم يتم العثور على معرفات صحيحة",
                reply_markup=self.get_keyboard()
            )
            return
        
        found_users = self.user_manager.find_users_by_identifiers(identifiers)
        
        message = f"🚫︙نتائج حظر المستخدمين\n\n"
        
        if not found_users:
            message += "❌ لم يتم العثور على أي مستخدمين مطابقين"
        else:
            banned_count = 0
            for user_id, user_info in found_users.items():
                success, msg = self.user_manager.ban_user(user_id, user_info, "حظر من المدير")
                if success:
                    banned_count += 1
                    name = user_info.get('اسم المستخدم', 'غير محدد')
                    username = user_info.get('معرف المستخدم', 'غير محدد')
                    message += f"🔢︙رقم︙ {banned_count}\n"
                    message += f"👤︙الأسم︙ <code>{name}</code>\n"
                    message += f"📧︙المعرف︙ <code>{username}</code>\n"
                    message += f"🆔︙الأيدي︙ <code>{user_id}</code>\n\n"
                else:
                    message += f"❌︙فشل حظر︙{user_id} - {msg}\n"
            
            message += f"\n📊︙الملخص: تم حظر {banned_count} من {len(found_users)} مستخدم"
        
        await update.message.reply_text(
            message,
            parse_mode=ParseMode.HTML,
            reply_markup=self.get_keyboard()
        )
    
    async def process_restrict_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE, text: str):
        """معالجة تقييد مستخدمين - واجهة متقدمة"""
        identifiers = self.user_manager.parse_user_identifiers(text)

        if not identifiers:
            await update.message.reply_text(
                "❌ لم يتم العثور على معرفات صحيحة\n\n"
                "يرجى إدخال معرف واحد مثل:\n"
                "• @username\n"
                "• <code>123456789</code>\n"
                "• أحمد محمد",
                reply_markup=self.get_keyboard()
            )
            return

        found_users = self.user_manager.find_users_by_identifiers(identifiers)

        if not found_users:
            await update.message.reply_text(
                "❌ لم يتم العثور على أي مستخدمين مطابقين",
                reply_markup=self.get_keyboard()
            )
            return

        if len(found_users) > 1:
            await update.message.reply_text(
                "⚠️ يرجى إدخال معرف مستخدم واحد فقط للتقييد المتقدم\n\nللتقييد السريع لعدة مستخدمين، استخدم الطريقة التقليدية",
                reply_markup=self.get_keyboard()
            )
            return

        # الحصول على المستخدم الوحيد
        user_id, user_info = next(iter(found_users.items()))

        # استيراد واجهة إدارة المستخدمين
        try:
            from .user_management_interface import UserManagementInterface
            interface = UserManagementInterface()
            await interface.show_restriction_interface(update, context, user_id, user_info)
        except Exception as e:
            logger.error(f"خطأ في عرض واجهة التقييد: {e}")
            # العودة للطريقة التقليدية
            restrictions = ["ai_assistant"]  # قيود افتراضية
            success, msg = self.user_manager.restrict_user(user_id, user_info, restrictions, "تقييد سريع من المدير")

            if success:
                name = user_info.get('اسم المستخدم', 'غير محدد')
                username = user_info.get('معرف المستخدم', 'غير محدد')
                message = f"✅︙تم تقييد المستخدم بنجاح\n\n"
                message += f"👤︙الأسم : <code>{name}</code>\n"
                message += f"📧︙المعرف : <code>{username}</code>\n"
                message += f"🆔︙الأيدي : <code>{user_id}</code>\n"
                message += f"🚫︙القيود المطبقة︙منع إكسا الذكي"
            else:
                message = f"❌ فشل في تقييد المستخدم: {msg}"

            await update.message.reply_text(
                message,
                reply_markup=self.get_keyboard()
            )
    
    async def process_search_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE, text: str):
        """معالجة البحث عن مستخدمين"""
        identifiers = self.user_manager.parse_user_identifiers(text)
        
        if not identifiers:
            await update.message.reply_text(
                "❌ لم يتم العثور على معرفات صحيحة",
                reply_markup=self.get_keyboard()
            )
            return
        
        found_users = self.user_manager.find_users_by_identifiers(identifiers)
        
        message = f"🔍︙نتائج البحث\n\n"
        message += f"📝︙تم البحث عن {len(identifiers)} معرف\n"
        message += f"✅︙تم العثور على {len(found_users)} مستخدم\n\n"
        
        if not found_users:
            message += "❌ لم يتم العثور على أي مستخدمين مطابقين\n\n"
            message += "💡︙اقتراحات:\n"
            message += "• تأكد من صحة المعرفات\n"
            message += "• جرب البحث بطرق مختلفة\n"
            message += "• استخدم قائمة المستخدمين للتصفح"
        else:
            for user_id, user_info in found_users.items():
                name = user_info.get('اسم المستخدم', 'غير محدد')
                username = user_info.get('معرف المستخدم', 'غير محدد')
                last_activity = user_info.get('آخر نشاط', 'غير محدد')
                visits = user_info.get('عدد الزيارات', 0)
                
                # التحقق من حالة المستخدم
                status = self.user_manager.get_user_status(user_id)
                status_text = "🚫 محظور" if status['is_banned'] else "⚠️ مقيد" if status['is_restricted'] else "✅ نشط"
                
                message += f"👤︙الأسم︙ <code>{name}</code>\n"
                message += f"📧︙المعرف︙ <code>{username}</code>\n"
                message += f"🆔︙الأيدي︙ <code>{user_id}</code>\n"
                message += f"📊︙الحالة︙ {status_text}\n"
                message += f"📅︙آخر نشاط︙ <code>{last_activity}</code>\n"
                message += f"📊︙الزيارات︙ {visits}\n\n"

        # إضافة ملاحظة النسخ إذا تم العثور على مستخدمين
        if found_users:
            message += "\n💡︙ملاحظة︙\n• اضغط على النص المقابل لنسخه"

        await update.message.reply_text(
            message,
            parse_mode=ParseMode.HTML,
            reply_markup=self.get_keyboard()
        )
    
    async def process_user_report(self, update: Update, context: ContextTypes.DEFAULT_TYPE, text: str):
        """معالجة إنشاء تقارير المستخدمين"""
        identifiers = self.user_manager.parse_user_identifiers(text)
        
        if not identifiers:
            await update.message.reply_text(
                "❌ لم يتم العثور على معرفات صحيحة",
                reply_markup=self.get_keyboard()
            )
            return
        
        found_users = self.user_manager.find_users_by_identifiers(identifiers)
        
        if not found_users:
            await update.message.reply_text(
                "❌ لم يتم العثور على أي مستخدمين مطابقين",
                reply_markup=self.get_keyboard()
            )
            return
        
        # إرسال تقرير لكل مستخدم منفصل
        for user_id, user_info in found_users.items():
            report = self.user_manager.get_user_report(user_id)
            
            if not report['exists']:
                continue
            
            # إنشاء التقرير المفصل
            name = user_info.get('اسم المستخدم', 'غير محدد')
            username = user_info.get('معرف المستخدم', 'غير محدد')
            
            message = f"📊︙تقرير شامل - {name}\n\n"

            # المعلومات الأساسية
            message += "📋︙المعلومات الأساسية :\n"
            message += f"👤︙الاسم︙ <code>{name}</code>\n"
            message += f"📧︙المعرف︙ <code>{username}</code>\n"
            message += f"🆔︙الأيدي︙ <code>{user_id}</code>\n"
            message += f"🌐︙اللغة : {user_info.get('اللغة', 'غير محدد')}\n\n"
            
            # إحصائيات النشاط
            stats = report['statistics']
            message += "📈︙إحصائيات النشاط:\n"
            message += f"• تاريخ التسجيل: {stats['registration_date']}\n"
            message += f"• أول زيارة: {stats['first_visit']}\n"
            message += f"• آخر نشاط: {stats['last_activity']}\n"
            message += f"• إجمالي الزيارات: {stats['total_visits']}\n\n"
            
            # حالة الحساب
            status = report['status']
            message += "🔐︙حالة الحساب:\n"
            
            if status['is_banned']:
                ban_info = status['ban_info']
                message += f"🚫︙محظور\n"
                message += f"• تاريخ الحظر: {ban_info.get('ban_date', 'غير محدد')}\n"
                message += f"• السبب: {ban_info.get('reason', 'غير محدد')}\n"
                message += f"• بواسطة: {ban_info.get('banned_by', 'غير محدد')}\n"
            elif status['is_restricted']:
                restriction_info = status['restriction_info']
                message += f"⚠️︙مقيد\n"
                message += f"• تاريخ التقييد: {restriction_info.get('restriction_date', 'غير محدد')}\n"
                message += f"• القيود: {', '.join(restriction_info.get('restrictions', []))}\n"
                message += f"• السبب: {restriction_info.get('reason', 'غير محدد')}\n"
            else:
                message += "✅︙نشط - لا توجد قيود\n"
            
            message += f"\n📅︙تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            
            await update.message.reply_text(
                message,
                parse_mode=ParseMode.HTML,
                reply_markup=self.get_keyboard()
            )
        
        # رسالة ملخص
        summary_message = f"✅︙تم إنشاء {len(found_users)} تقرير بنجاح\n\n"
        summary_message += "📋︙التقارير تم إرسالها أعلاه بشكل منفصل لكل مستخدم"
        
        await update.message.reply_text(
            summary_message,
                reply_markup=self.get_keyboard()
        )
