#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة المستخدمين المتقدم لبوت الإدارة والمراقبة
يدعم البحث المتعدد والعمليات المتقدمة
"""

import json
import os
import re
import sys
import logging
from datetime import datetime
from typing import List, Dict, Any, Tuple, Optional
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ReplyKeyboardMarkup
from telegram.ext import ContextTypes
from telegram.constants import ParseMode

# إضافة مسار المكتبات المشتركة
current_dir = os.path.dirname(os.path.abspath(__file__))
shared_dir = os.path.join(os.path.dirname(os.path.dirname(current_dir)), 'shared')
sys.path.insert(0, shared_dir)

# استيراد مدير المحافظ المشترك
from database.wallet_manager import WalletManager

logger = logging.getLogger(__name__)

class AdvancedUserManager:
    """مدير المستخدمين المتقدم"""
    
    def __init__(self):
        self.users_file = "shared/database/users_data.json"
        self.banned_users_file = "shared/database/banned_users.json"
        self.restricted_users_file = "shared/database/restricted_users.json"

        # إنشاء الملفات إذا لم تكن موجودة
        self._ensure_files_exist()

        # تهيئة مدير المحافظ المشترك
        self.wallet_manager = WalletManager()
    
    def _ensure_files_exist(self):
        """التأكد من وجود ملفات البيانات"""
        os.makedirs("shared/database", exist_ok=True)
        
        files = [self.users_file, self.banned_users_file, self.restricted_users_file]
        for file_path in files:
            if not os.path.exists(file_path):
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump({}, f, ensure_ascii=False, indent=2)
    
    def load_users_data(self) -> Dict[str, Any]:
        """تحميل بيانات المستخدمين"""
        try:
            with open(self.users_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"خطأ في تحميل بيانات المستخدمين: {e}")
            return {}
    
    def load_banned_users(self) -> Dict[str, Any]:
        """تحميل قائمة المستخدمين المحظورين"""
        try:
            with open(self.banned_users_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"خطأ في تحميل قائمة المحظورين: {e}")
            return {}
    
    def load_restricted_users(self) -> Dict[str, Any]:
        """تحميل قائمة المستخدمين المقيدين"""
        try:
            with open(self.restricted_users_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"خطأ في تحميل قائمة المقيدين: {e}")
            return {}
    
    def save_users_data(self, data: Dict[str, Any]):
        """حفظ بيانات المستخدمين"""
        try:
            with open(self.users_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"خطأ في حفظ بيانات المستخدمين: {e}")
    
    def save_banned_users(self, data: Dict[str, Any]):
        """حفظ قائمة المستخدمين المحظورين"""
        try:
            with open(self.banned_users_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"خطأ في حفظ قائمة المحظورين: {e}")
    
    def save_restricted_users(self, data: Dict[str, Any]):
        """حفظ قائمة المستخدمين المقيدين"""
        try:
            with open(self.restricted_users_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"خطأ في حفظ قائمة المقيدين: {e}")
    
    def parse_user_identifiers(self, text: str) -> List[str]:
        """تحليل معرفات المستخدمين من النص (يدعم متعدد)"""
        identifiers = []
        
        # إزالة المسافات الزائدة وتقسيم النص
        text = text.strip()
        
        # البحث عن معرفات متعددة مفصولة بفواصل أو مسافات أو أسطر جديدة
        parts = re.split(r'[,\s\n]+', text)
        
        for part in parts:
            part = part.strip()
            if not part:
                continue
            
            # إزالة @ من بداية المعرف
            if part.startswith('@'):
                part = part[1:]
            
            # التحقق من صحة المعرف
            if part and (part.isdigit() or re.match(r'^[a-zA-Z0-9_]+$', part)):
                identifiers.append(part)
        
        return identifiers
    
    def find_users_by_identifiers(self, identifiers: List[str]) -> Dict[str, Dict[str, Any]]:
        """البحث عن مستخدمين بمعرفات متعددة"""
        users_data = self.load_users_data()
        found_users = {}
        
        for identifier in identifiers:
            # البحث بالأيدي
            if identifier.isdigit():
                user_id = identifier
                if user_id in users_data:
                    found_users[user_id] = users_data[user_id]
                    continue
            
            # البحث بالمعرف أو الاسم
            for user_id, user_info in users_data.items():
                username = user_info.get('معرف المستخدم', '').replace('@', '')
                full_name = user_info.get('اسم المستخدم', '')
                
                if (identifier == username or 
                    identifier.lower() in full_name.lower() or
                    identifier == str(user_info.get('أيدي المستخدم', ''))):
                    found_users[user_id] = user_info
                    break
        
        return found_users
    
    def get_user_status(self, user_id: str) -> Dict[str, Any]:
        """الحصول على حالة المستخدم (عادي/محظور/مقيد)"""
        banned_users = self.load_banned_users()
        restricted_users = self.load_restricted_users()
        
        status = {
            'is_banned': user_id in banned_users,
            'is_restricted': user_id in restricted_users,
            'ban_info': banned_users.get(user_id, {}),
            'restriction_info': restricted_users.get(user_id, {})
        }
        
        return status
    
    def ban_user(self, user_id: str, user_info: Dict[str, Any], reason: str = "غير محدد") -> Tuple[bool, str]:
        """حظر مستخدم"""
        try:
            banned_users = self.load_banned_users()
            
            banned_users[user_id] = {
                'user_info': user_info,
                'ban_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'reason': reason,
                'banned_by': 'المدير'
            }
            
            self.save_banned_users(banned_users)
            logger.info(f"تم حظر المستخدم {user_id}: {reason}")
            return True, "تم حظر المستخدم بنجاح"
            
        except Exception as e:
            logger.error(f"خطأ في حظر المستخدم {user_id}: {e}")
            return False, f"خطأ في حظر المستخدم: {e}"
    
    def unban_user(self, user_id: str) -> Tuple[bool, str]:
        """إلغاء حظر مستخدم"""
        try:
            banned_users = self.load_banned_users()
            
            if user_id in banned_users:
                del banned_users[user_id]
                self.save_banned_users(banned_users)
                logger.info(f"تم إلغاء حظر المستخدم {user_id}")
                return True, "تم إلغاء حظر المستخدم بنجاح"
            else:
                return False, "المستخدم غير محظور"
                
        except Exception as e:
            logger.error(f"خطأ في إلغاء حظر المستخدم {user_id}: {e}")
            return False, f"خطأ في إلغاء الحظر: {e}"
    
    def restrict_user(self, user_id: str, user_info: Dict[str, Any], restrictions: List[str], reason: str = "غير محدد") -> Tuple[bool, str]:
        """تقييد مستخدم"""
        try:
            restricted_users = self.load_restricted_users()
            
            restricted_users[user_id] = {
                'user_info': user_info,
                'restriction_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'restrictions': restrictions,
                'reason': reason,
                'restricted_by': 'المدير'
            }
            
            self.save_restricted_users(restricted_users)
            logger.info(f"تم تقييد المستخدم {user_id}: {restrictions}")
            return True, "تم تقييد المستخدم بنجاح"
            
        except Exception as e:
            logger.error(f"خطأ في تقييد المستخدم {user_id}: {e}")
            return False, f"خطأ في تقييد المستخدم: {e}"
    
    def remove_user(self, user_id: str) -> Tuple[bool, str]:
        """إزالة مستخدم ومحفظته نهائياً من جميع البيانات"""
        try:
            # حذف المحفظة أولاً
            wallet_deleted, wallet_msg = self.wallet_manager.delete_user_wallet(int(user_id))

            # إزالة من بيانات المستخدمين
            users_data = self.load_users_data()
            if user_id in users_data:
                del users_data[user_id]
                self.save_users_data(users_data)

            # إزالة من قائمة المحظورين
            banned_users = self.load_banned_users()
            if user_id in banned_users:
                del banned_users[user_id]
                self.save_banned_users(banned_users)

            # إزالة من قائمة المقيدين
            restricted_users = self.load_restricted_users()
            if user_id in restricted_users:
                del restricted_users[user_id]
                self.save_restricted_users(restricted_users)

            # إنشاء رسالة النتيجة
            result_msg = "تم حذف المستخدم نهائياً من جميع البيانات"
            if wallet_deleted:
                result_msg += f" وتم حذف المحفظة ({wallet_msg})"
            else:
                result_msg += f" (المحفظة: {wallet_msg})"

            logger.info(f"تم حذف المستخدم {user_id} نهائياً مع المحفظة")
            return True, result_msg

        except Exception as e:
            logger.error(f"خطأ في حذف المستخدم {user_id}: {e}")
            return False, f"خطأ في حذف المستخدم: {e}"
    
    def get_user_report(self, user_id: str) -> Dict[str, Any]:
        """إنشاء تقرير شامل عن مستخدم"""
        users_data = self.load_users_data()
        user_info = users_data.get(user_id, {})
        
        if not user_info:
            return {'exists': False}
        
        status = self.get_user_status(user_id)
        
        report = {
            'exists': True,
            'basic_info': user_info,
            'status': status,
            'statistics': {
                'total_visits': user_info.get('عدد الزيارات', 0),
                'registration_date': user_info.get('تاريخ التسجيل', 'غير محدد'),
                'last_activity': user_info.get('آخر نشاط', 'غير محدد'),
                'first_visit': user_info.get('تاريخ أول دخول', 'غير محدد')
            }
        }
        
        return report
