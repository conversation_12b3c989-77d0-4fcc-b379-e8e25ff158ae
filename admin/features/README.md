# نظام إدارة المستخدمين المتقدم

## نظرة عامة

نظام إدارة مستخدمين متقدم لبوت الإدارة والمراقبة يوفر إمكانيات شاملة لإدارة المستخدمين مع دعم العمليات المتعددة.

## الميزات الرئيسية

### 🔧 العمليات المدعومة

1. **👥 قائمة المستخدمين**
   - عرض جميع المستخدمين مع التصفح بالصفحات
   - عرض حالة كل مستخدم (نشط/محظور/مقيد)
   - إحصائيات سريعة لكل مستخدم

2. **➕ إضافة مستخدم**
   - إرسال دعوات انضمام للمستخدمين الجدد
   - دعم إضافة عدة مستخدمين في عملية واحدة

3. **➖ إزالة مستخدم**
   - حذف نهائي للمستخدم من جميع البيانات
   - إزالة من قوائم المحظورين والمقيدين
   - عملية غير قابلة للتراجع

4. **🚫 حظر مستخدم**
   - منع المستخدم من استخدام البوت
   - عرض رسالة سبب الحظر للمستخدم المحظور
   - إمكانية إلغاء الحظر لاحقاً

5. **⚠️ تقييد مستخدم**
   - تقييد وصول المستخدم لخدمات معينة
   - قيود قابلة للتخصيص (إكسا الذكي، الملفات، إلخ)
   - إمكانية تعديل القيود لاحقاً

6. **📊 تقرير مستخدم**
   - تقارير شاملة ومفصلة عن المستخدمين
   - إحصائيات النشاط والتفاعل
   - معلومات الحالة والقيود

7. **🔍 بحث مستخدم**
   - بحث متقدم بطرق متعددة
   - دعم البحث الجزئي بالأسماء
   - عرض نتائج مفصلة

### 🎯 المعالجة المتعددة

جميع العمليات تدعم معالجة عدة مستخدمين في عملية واحدة:

- **بالمعرف**: `@user1, @user2, @user3`
- **بالأيدي**: `123456789, 987654321`
- **بالاسم**: `أحمد محمد, فاطمة علي`
- **مختلط**: `@user1, 123456789, أحمد`

### 📁 هيكل الملفات

```
admin/features/
├── __init__.py                      # ملف التهيئة
├── advanced_user_management.py      # المدير الأساسي
├── user_management_interface.py     # واجهة المستخدم
├── user_management_processors.py    # معالجات العمليات
└── README.md                       # هذا الملف
```

### 💾 ملفات البيانات

```
data/
├── users_data.json          # بيانات المستخدمين الأساسية
├── banned_users.json        # قائمة المستخدمين المحظورين
└── restricted_users.json    # قائمة المستخدمين المقيدين
```

## طريقة الاستخدام

### في بوت الإدارة

1. اختر "👥 إدارة المستخدمين" من القائمة الرئيسية
2. اختر العملية المطلوبة من الأزرار
3. أدخل معرفات المستخدمين (يدعم متعدد)
4. تأكيد العملية

### أمثلة على المعرفات

```
# مستخدم واحد
@username

# عدة مستخدمين
@user1, @user2, @user3

# مختلط
@user1, 123456789, أحمد محمد

# بالأسطر
@user1
123456789
أحمد محمد
```

## الأمان والحماية

- ✅ التحقق من صحة المعرفات
- ✅ تسجيل جميع العمليات
- ✅ رسائل تأكيد للعمليات الحساسة
- ✅ معالجة الأخطاء الشاملة
- ✅ نسخ احتياطية تلقائية

## المتطلبات

- Python 3.8+
- python-telegram-bot
- ملفات البيانات في مجلد `data/`
- صلاحيات الكتابة في مجلد البيانات

## الإعداد

النظام يعمل تلقائياً عند تشغيل بوت الإدارة. لا حاجة لإعداد إضافي.

## الدعم

للمساعدة أو الإبلاغ عن مشاكل، يرجى مراجعة سجلات النظام أو التواصل مع المطور.
