#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة إدارة المستخدمين المتقدمة لبوت الإدارة والمراقبة
"""

import logging
import sys
import os
from datetime import datetime
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ReplyKeyboardMarkup
from telegram.ext import ContextTypes
from telegram.constants import ParseMode

# استيراد المعالج الموحد
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'shared'))
from data_processing import SharedTextProcessor

# استيراد مدير المحافظ المشترك
from database.wallet_manager import WalletManager

try:
    from .advanced_user_management import AdvancedUserManager
    from .user_management_processors import UserManagementProcessors
except ImportError:
    from advanced_user_management import AdvancedUserManager
    from user_management_processors import UserManagementProcessors

logger = logging.getLogger(__name__)

class UserManagementInterface:
    """واجهة إدارة المستخدمين"""
    
    def __init__(self):
        self.user_manager = AdvancedUserManager()
        self.processors = UserManagementProcessors(self.user_manager, self.get_user_management_keyboard)
        self.text_processor = SharedTextProcessor()
        self.wallet_manager = WalletManager()
    
    def get_user_management_keyboard(self) -> ReplyKeyboardMarkup:
        """الحصول على لوحة مفاتيح إدارة المستخدمين"""
        keyboard = [
            ["👥 قائمة المستخدمين", "➕ إضافة مستخدم"],
            ["➖ إزالة مستخدم", "🚫 حظر مستخدم"],
            ["⚠️ تقييد مستخدم", "📊 تقرير مستخدم"],
            ["🔍 بحث مستخدم", "🔙 العودة للقائمة الرئيسية"]
        ]
        return ReplyKeyboardMarkup(keyboard, resize_keyboard=True, one_time_keyboard=False)
    
    def get_user_management_inline_keyboard(self) -> None:
        """الحصول على لوحة مفاتيح مضمنة لإدارة المستخدمين - تم تعطيلها"""
        # تم إزالة الأزرار المضمنة بناءً على طلب المستخدم
        return None
    
    async def show_user_management_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض قائمة إدارة المستخدمين"""
        users_data = self.user_manager.load_users_data()
        banned_users = self.user_manager.load_banned_users()
        restricted_users = self.user_manager.load_restricted_users()
        
        total_users = len(users_data)
        total_banned = len(banned_users)
        total_restricted = len(restricted_users)
        
        message = f"""👥︙إدارة المستخدمين المتقدمة︙

📊︙إحصائيات سريعة︙
👥︙إجمالي المستخدمين︙ {total_users}
👥︙المحظورون︙ {total_banned}
👥︙المقيدون︙ {total_restricted}
👥︙النشطون︙ {total_users - total_banned}


💡︙ملاحظة مهمه︙
جميع العمليات تدعم المعالجة المتعددة
يمكنك إدخال عدة معرفات مفصولة بفواصل أو مسافات

استخدم الأزرار أدناه للبدء 👇"""

        await update.message.reply_text(
            message,
            reply_markup=self.get_user_management_keyboard()
        )
    
    async def show_users_list(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض قائمة جميع المستخدمين"""
        users_data = self.user_manager.load_users_data()
        
        if not users_data:
            await update.message.reply_text(
                "📭 **لا يوجد مستخدمون مسجلون**\n\nلم يتم تسجيل أي مستخدمين في البوت بعد.",
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=self.get_user_management_keyboard()
            )
            return
        
        # تقسيم المستخدمين إلى صفحات (10 مستخدمين لكل صفحة)
        users_per_page = 10
        total_users = len(users_data)
        total_pages = (total_users + users_per_page - 1) // users_per_page
        
        # الحصول على رقم الصفحة من السياق
        page = context.user_data.get('users_list_page', 1)
        start_idx = (page - 1) * users_per_page
        end_idx = min(start_idx + users_per_page, total_users)
        
        message = f"👥︙قائمة المستخدمين (الصفحة {page}/{total_pages})\n\n"

        users_list = list(users_data.items())[start_idx:end_idx]

        for i, (user_id, user_info) in enumerate(users_list, start_idx + 1):
            import html

            name = html.escape(user_info.get('اسم المستخدم', 'غير محدد'))
            username = html.escape(user_info.get('معرف المستخدم', 'غير محدد'))
            last_activity = html.escape(user_info.get('آخر نشاط', 'غير محدد'))
            visits = user_info.get('عدد الزيارات', 0)

            # الحصول على اليوم الحالي
            from datetime import datetime
            current_day = datetime.now().strftime('%A')
            day_names = {
                'Monday': 'الاثنين',
                'Tuesday': 'الثلاثاء',
                'Wednesday': 'الاربعاء',
                'Thursday': 'الخميس',
                'Friday': 'الجمعة',
                'Saturday': 'السبت',
                'Sunday': 'الأحد'
            }
            arabic_day = day_names.get(current_day, 'غير محدد')

            # التحقق من حالة المستخدم
            status = self.user_manager.get_user_status(user_id)
            status_text = "🚫 حظر" if status['is_banned'] else "⚠️ تقييد" if status['is_restricted'] else "✅ نشط"

            # الحصول على معلومات المحفظة
            wallet_info = self.wallet_manager.get_user_wallet(int(user_id))
            wallet_text = wallet_info['wallet_number'] if wallet_info else "غير متوفر"

            # إصلاح عرض الرصيد لإزالة .00 وتحويل العملة إلى إكسا
            if wallet_info:
                balance = wallet_info['balance']
                if balance == int(balance):  # إذا كان الرصيد عدد صحيح
                    wallet_balance = f"{int(balance)} إكسا"
                else:
                    wallet_balance = f"{balance} إكسا"
            else:
                wallet_balance = "0 إكسا"

            message += f"🔢︙رقم︙ {i}\n"
            message += f"👤︙الأسم︙ <code>{name}</code>\n"
            message += f"📧︙المعرف︙ <code>{username}</code>\n"
            message += f"🆔︙الأيدي︙ <code>{user_id}</code>\n"
            message += f"💰︙المحفظة︙ <code>{wallet_text}</code>\n"
            message += f"💎︙الرصيد︙ {wallet_balance}\n"
            message += f"📊︙الحالة︙ {status_text}\n"
            message += f"📅︙اليوم︙ {arabic_day}\n"
            message += f"📅︙آخر نشاط︙ <code>{last_activity}</code>\n"
            message += f"📊︙الزيارات︙ {visits}\n\n"
        
        # إضافة أزرار التنقل إذا كان هناك أكثر من صفحة
        keyboard = []
        if total_pages > 1:
            nav_buttons = []
            if page > 1:
                nav_buttons.append(InlineKeyboardButton("⬅️ السابق", callback_data=f"users_page_{page-1}"))
            if page < total_pages:
                nav_buttons.append(InlineKeyboardButton("➡️ التالي", callback_data=f"users_page_{page+1}"))
            if nav_buttons:
                keyboard.append(nav_buttons)
        
        # إضافة ملاحظة النسخ إذا كان هناك مستخدمون
        if total_users > 0:
            message += "\n💡︙ملاحظة︙\n• اضغط على النص المقابل لنسخه"

        await update.message.reply_text(
            message,
            parse_mode=ParseMode.HTML,
            reply_markup=self.get_user_management_keyboard()
        )
    
    async def handle_add_user_request(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالجة طلب إضافة مستخدم"""
        context.user_data['waiting_for'] = 'add_user'

        message = """➕︙إضافة مستخدم جديد︙

🔹︙الطرق المدعومة︙
📧︙المعرف︙🆔︙الأيدي︙💈︙خليط︙

💡︙ملاحظات︙
• يمكنك إضافة عدة مستخدمين في رسالة واحدة
• افصل بين المعرفات بفواصل أو مسافات
• سيتم إرسال دعوة انضمام لكل مستخدم

✍️︙أرسل المعرفات الآن︙"""

        # إنشاء زر الإلغاء المتضمن
        from telegram import InlineKeyboardButton, InlineKeyboardMarkup
        keyboard = [[InlineKeyboardButton("❌ إلغاء", callback_data="cancel_add_user")]]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.message.reply_text(
            message,
            parse_mode=ParseMode.MARKDOWN,
            reply_markup=reply_markup
        )
    
    async def handle_remove_user_request(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالجة طلب إزالة مستخدم"""
        context.user_data['waiting_for'] = 'remove_user'

        message = """➖︙إزالة مستخدم︙

🔹︙الطرق المدعومة︙
👤︙الاسم︙📧︙المعرف︙🆔︙الأيدي︙💈︙خليط︙

💡︙ملاحظات︙
• يمكنك إزالة عدة مستخدمين في رسالة واحدة
• سيتم حذف جميع بيانات المستخدم ومحفظته
• لا يمكن التراجع عن هذه العملية

✍️︙أرسل المعرفات الآن︙"""

        # إنشاء زر الإلغاء المتضمن
        from telegram import InlineKeyboardButton, InlineKeyboardMarkup
        keyboard = [[InlineKeyboardButton("❌ إلغاء", callback_data="cancel_remove_user")]]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.message.reply_text(
            message,
            reply_markup=reply_markup
        )
    
    async def handle_ban_user_request(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالجة طلب حظر مستخدم"""
        context.user_data['waiting_for'] = 'ban_user'

        message = """🚫︙حظر مستخدم︙

🔹︙الطرق المدعومة︙
👤︙الاسم︙📧︙المعرف︙🆔︙الأيدي︙💈︙خليط︙

💡︙ملاحظات︙
• يمكنك حظر عدة مستخدمين في رسالة واحدة
• المستخدم المحظور لن يتمكن من استخدام البوت
• سيظهر له رسالة سبب الحظر عند المحاولة
• يمكن إلغاء الحظر لاحقاً

✍️︙أرسل المعرفات الآن︙"""

        # إنشاء زر الإلغاء المتضمن
        from telegram import InlineKeyboardButton, InlineKeyboardMarkup
        keyboard = [[InlineKeyboardButton("❌ إلغاء", callback_data="cancel_ban_user")]]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.message.reply_text(
            message,
            parse_mode=ParseMode.MARKDOWN,
            reply_markup=reply_markup
        )
    
    async def handle_restrict_user_request(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالجة طلب تقييد مستخدم"""
        context.user_data['waiting_for'] = 'restrict_user'

        message = """⚠️︙تقييد مستخدم متقدم︙

🔹︙الطرق المدعومة︙
👤︙الاسم︙📧︙المعرف︙🆔︙الأيدي︙💈︙خليط︙

🚫︙القيود المتاحة︙
🤖︙إكسا عادي︙🧠︙إكسا برو︙📍︙الموقع︙👨‍💼︙النبذة
💼︙الأعمال︙🎯︙الخبرة︙🏆︙الإنجازات︙📎︙الملفات
❓︙المساعدة

💡︙ملاحظات︙
• سيتم عرض واجهة تفاعلية لاختيار القيود
• يمكن تطبيق عدة قيود على المستخدم الواحد
• يمكن إضافة أو إزالة القيود في أي وقت

✍️︙أرسل معرف المستخدم الآن︙"""

        # إنشاء زر الإلغاء المتضمن
        from telegram import InlineKeyboardButton, InlineKeyboardMarkup
        keyboard = [[InlineKeyboardButton("❌ إلغاء", callback_data="cancel_restrict_user")]]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.message.reply_text(
            message,
            parse_mode=ParseMode.MARKDOWN,
            reply_markup=reply_markup
        )
    
    async def handle_search_user_request(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالجة طلب البحث عن مستخدم"""
        context.user_data['waiting_for'] = 'search_user'

        message = """🔍︙البحث عن مستخدم︙

🔹︙الطرق المدعومة︙
👤︙الاسم︙📧︙المعرف︙🆔︙الأيدي︙💈︙خليط︙

💡︙ملاحظات︙
• يمكنك البحث عن عدة مستخدمين في رسالة واحدة
• البحث بالاسم يدعم المطابقة الجزئية
• سيتم عرض معلومات مفصلة عن كل مستخدم

✍️︙أرسل معرفات البحث الآن︙"""

        # إنشاء زر الإلغاء المتضمن
        from telegram import InlineKeyboardButton, InlineKeyboardMarkup
        keyboard = [[InlineKeyboardButton("❌ إلغاء", callback_data="cancel_search_user")]]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.message.reply_text(
            message,
            parse_mode=ParseMode.MARKDOWN,
            reply_markup=reply_markup
        )
    
    async def handle_user_report_request(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالجة طلب تقرير مستخدم"""
        context.user_data['waiting_for'] = 'user_report'

        message = """📊︙تقرير مستخدم شامل

📝︙أرسل معرفات المستخدمين المراد إنشاء تقارير لهم:

🔹︙الطرق المدعومة︙
👤︙معرف المستخدم︙@username
👤︙اسم المستخدم︙أحمد محمد
🔢︙الأيدي︙123456789
💈︙خليط︙@user1, 123456789, أحمد

📋︙التقرير يتضمن:
• المعلومات الأساسية
• إحصائيات النشاط
• حالة الحساب (عادي/محظور/مقيد)
• تاريخ التسجيل والنشاط
• عدد الزيارات والتفاعلات

💡︙ملاحظات:
• يمكنك إنشاء تقارير لعدة مستخدمين
• التقارير مفصلة وشاملة
• مفيدة لمراجعة نشاط المستخدمين

✍️︙أرسل المعرفات الآن:"""

        # إنشاء زر الإلغاء المتضمن
        from telegram import InlineKeyboardButton, InlineKeyboardMarkup
        keyboard = [[InlineKeyboardButton("❌ إلغاء", callback_data="cancel_user_report")]]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.message.reply_text(
            message,
            parse_mode=ParseMode.MARKDOWN,
            reply_markup=reply_markup
        )

    async def process_user_input(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالجة مدخلات المستخدم للعمليات المختلفة"""
        waiting_for = context.user_data.get('waiting_for')

        if not waiting_for:
            return False

        text = update.message.text.strip()

        # إلغاء العملية (للحالات القديمة التي قد تستخدم النص)
        if text == "❌ إلغاء":
            context.user_data.pop('waiting_for', None)
            await update.message.reply_text(
                "❌ تم إلغاء العملية",
                reply_markup=self.get_user_management_keyboard()
            )
            return True

        # معالجة العمليات المختلفة
        if waiting_for == 'add_user':
            await self.processors.process_add_user(update, context, text)
        elif waiting_for == 'remove_user':
            await self.processors.process_remove_user(update, context, text)
        elif waiting_for == 'ban_user':
            await self.processors.process_ban_user(update, context, text)
        elif waiting_for == 'restrict_user':
            await self.processors.process_restrict_user(update, context, text)
        elif waiting_for == 'search_user':
            await self.processors.process_search_user(update, context, text)
        elif waiting_for == 'user_report':
            await self.processors.process_user_report(update, context, text)

        # إنهاء حالة الانتظار
        context.user_data.pop('waiting_for', None)
        return True

    async def process_add_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE, text: str):
        """معالجة إضافة مستخدمين"""
        identifiers = self.user_manager.parse_user_identifiers(text)

        if not identifiers:
            await update.message.reply_text(
                "❌ لم يتم العثور على معرفات صحيحة\nيرجى المحاولة مرة أخرى",
                reply_markup=self.get_user_management_keyboard()
            )
            return

        message = f"➕ **نتائج إضافة المستخدمين**\n\n"
        message += f"📝 تم معالجة {len(identifiers)} معرف\n\n"

        for identifier in identifiers:
            # هنا يمكن إضافة منطق إرسال الدعوات
            # حالياً سنعرض فقط المعرفات المعالجة
            message += f"✅︙تم إرسال دعوة إلى︙<code>{identifier}</code>\n"

        message += f"\n💡 **ملاحظة:** تم إرسال دعوات الانضمام لجميع المعرفات"

        await update.message.reply_text(
            message,
            parse_mode=ParseMode.MARKDOWN,
                reply_markup=self.get_user_management_keyboard()
        )

    async def process_remove_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE, text: str):
        """معالجة إزالة مستخدمين"""
        identifiers = self.user_manager.parse_user_identifiers(text)

        if not identifiers:
            await update.message.reply_text(
                "❌ لم يتم العثور على معرفات صحيحة",
                reply_markup=self.get_user_management_keyboard()
            )
            return

        found_users = self.user_manager.find_users_by_identifiers(identifiers)

        message = f"➖ **نتائج إزالة المستخدمين**\n\n"

        if not found_users:
            message += "❌ لم يتم العثور على أي مستخدمين مطابقين"
        else:
            removed_count = 0
            for user_id, user_info in found_users.items():
                success, msg = self.user_manager.remove_user(user_id)
                if success:
                    removed_count += 1
                    name = user_info.get('اسم المستخدم', 'غير محدد')
                    message += f"✅︙تم حذف︙<code>{name}</code> (<code>{user_id}</code>)\n"
                else:
                    message += f"❌ فشل حذف: {user_id} - {msg}\n"

            message += f"\n📊 **الملخص:** تم حذف {removed_count} من {len(found_users)} مستخدم"

        await update.message.reply_text(
            message,
            parse_mode=ParseMode.MARKDOWN,
                reply_markup=self.get_user_management_keyboard()
        )

    async def show_restriction_interface(self, update: Update, context: ContextTypes.DEFAULT_TYPE, user_id: str, user_info: dict):
        """عرض واجهة التقييد التفاعلية"""
        try:
            # الحصول على القيود الحالية
            restricted_users = self.user_manager.load_restricted_users()
            current_restrictions = []
            if user_id in restricted_users:
                current_restrictions = restricted_users[user_id].get('restrictions', [])

            # تنسيق الرسالة
            restriction_data = self.text_processor.format_restriction_selection_message(
                user_info, current_restrictions
            )

            # إنشاء لوحة المفاتيح المضمنة
            keyboard = self.text_processor.create_restriction_inline_keyboard(
                user_id, current_restrictions
            )

            # حفظ معلومات المستخدم في السياق
            context.user_data[f'restriction_user_{user_id}'] = {
                'user_info': user_info,
                'current_restrictions': current_restrictions.copy()
            }

            await update.message.reply_text(
                restriction_data['message'],
                reply_markup=keyboard
            )

        except Exception as e:
            logger.error(f"خطأ في عرض واجهة التقييد: {e}")
            await update.message.reply_text(
                "❌ حدث خطأ في عرض واجهة التقييد",
                reply_markup=self.get_user_management_keyboard()
            )

    async def handle_copy_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج أزرار النسخ"""
        query = update.callback_query
        await query.answer()

        try:
            callback_data = query.data

            if callback_data.startswith("copy_name_"):
                user_id = callback_data.replace("copy_name_", "")
                users_data = self.user_manager.load_users_data()
                if user_id in users_data:
                    name = users_data[user_id].get('اسم المستخدم', 'غير محدد')
                    await query.answer(f"تم نسخ الاسم: {name}", show_alert=True)

            elif callback_data.startswith("copy_username_"):
                user_id = callback_data.replace("copy_username_", "")
                users_data = self.user_manager.load_users_data()
                if user_id in users_data:
                    username = users_data[user_id].get('معرف المستخدم', 'غير محدد')
                    await query.answer(f"تم نسخ المعرف: {username}", show_alert=True)

            elif callback_data.startswith("copy_userid_"):
                user_id = callback_data.replace("copy_userid_", "")
                await query.answer(f"تم نسخ الأيدي: {user_id}", show_alert=True)

        except Exception as e:
            logger.error(f"خطأ في معالجة زر النسخ: {e}")
            await query.answer("❌ حدث خطأ في النسخ", show_alert=True)

    async def handle_restriction_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالج أزرار نظام التقييد"""
        query = update.callback_query
        await query.answer()

        try:
            callback_data = query.data

            if callback_data.startswith("toggle_restriction_"):
                # تبديل حالة قيد معين
                parts = callback_data.split("_", 3)
                if len(parts) >= 4:
                    user_id = parts[2]
                    restriction_type = parts[3]

                    # الحصول على البيانات المحفوظة
                    user_data_key = f'restriction_user_{user_id}'
                    if user_data_key not in context.user_data:
                        await query.edit_message_text("❌ انتهت صلاحية الجلسة، يرجى المحاولة مرة أخرى")
                        return

                    user_session = context.user_data[user_data_key]
                    current_restrictions = user_session['current_restrictions']
                    user_info = user_session['user_info']

                    # تبديل حالة القيد
                    if restriction_type in current_restrictions:
                        current_restrictions.remove(restriction_type)
                    else:
                        current_restrictions.append(restriction_type)

                    # تحديث البيانات المحفوظة
                    user_session['current_restrictions'] = current_restrictions

                    # تحديث الرسالة والأزرار
                    restriction_data = self.text_processor.format_restriction_selection_message(
                        user_info, current_restrictions
                    )

                    keyboard = self.text_processor.create_restriction_inline_keyboard(
                        user_id, current_restrictions
                    )

                    # فحص ما إذا كان المحتوى مختلف قبل التحديث
                    try:
                        await query.edit_message_text(
                            restriction_data['message'],
                            reply_markup=keyboard
                        )
                    except Exception as e:
                        if "message is not modified" in str(e).lower():
                            # إذا كان المحتوى نفسه، أرسل تنبيه فقط
                            await query.answer("تم تحديث الاختيار", show_alert=False)
                        else:
                            raise e

            elif callback_data.startswith("remove_all_restrictions_"):
                # إزالة جميع القيود
                user_id = callback_data.replace("remove_all_restrictions_", "")

                user_data_key = f'restriction_user_{user_id}'
                if user_data_key not in context.user_data:
                    await query.edit_message_text("❌ انتهت صلاحية الجلسة، يرجى المحاولة مرة أخرى")
                    return

                user_session = context.user_data[user_data_key]
                user_session['current_restrictions'] = []
                user_info = user_session['user_info']

                # إزالة من قائمة المقيدين في الإدارة
                removed_from_admin = False
                try:
                    # تحميل البيانات من user_manager
                    restricted_users = self.user_manager.load_restricted_users()
                    if user_id in restricted_users:
                        del restricted_users[user_id]
                        self.user_manager.save_restricted_users(restricted_users)
                        removed_from_admin = True
                except Exception as e:
                    logger.error(f"خطأ في إزالة من الإدارة: {e}")

                # إزالة من ملف البوت الرئيسي مباشرة
                import os
                import json
                base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                restrictions_file = os.path.join(base_dir, "data", "restricted_users.json")

                removed_from_main = False
                if os.path.exists(restrictions_file):
                    try:
                        with open(restrictions_file, 'r', encoding='utf-8') as f:
                            main_data = json.load(f)

                        if user_id in main_data:
                            del main_data[user_id]
                            removed_from_main = True

                            with open(restrictions_file, 'w', encoding='utf-8') as f:
                                json.dump(main_data, f, ensure_ascii=False, indent=2)

                            logger.info(f"تم حذف المستخدم {user_id} من ملف البوت الرئيسي")
                    except Exception as e:
                        logger.error(f"خطأ في تعديل ملف البوت الرئيسي: {e}")

                # مسح التخزين المؤقت
                await self.clear_restrictions_cache(update, context)

                # إرسال إشارة للبوت الرئيسي لإعادة التحميل
                try:
                    import sys
                    sys.path.append(os.path.join(base_dir, 'main', 'features'))
                    from ban_checker import ban_checker
                    ban_checker.clear_cache()
                    ban_checker.force_clean_restrictions_file()
                except Exception as e:
                    logger.error(f"خطأ في مسح تخزين البوت الرئيسي: {e}")

                # تحديث الواجهة
                restriction_data = self.text_processor.format_restriction_selection_message(
                    user_info, []
                )

                keyboard = self.text_processor.create_restriction_inline_keyboard(
                    user_id, []
                )

                try:
                    await query.edit_message_text(
                        restriction_data['message'],
                        reply_markup=keyboard
                    )

                    # إرسال رسالة تأكيد منفصلة
                    await query.answer(
                        f"✅ تم إلغاء جميع القيود\n"
                        f"الإدارة: {'✅' if removed_from_admin else '❌'} | "
                        f"البوت الرئيسي: {'✅' if removed_from_main else '❌'}",
                        show_alert=True
                    )

                except Exception as e:
                    if "message is not modified" in str(e).lower():
                        await query.answer("تم إزالة جميع القيود", show_alert=False)
                    else:
                        raise e

            elif callback_data.startswith("apply_restrictions_"):
                # تطبيق القيود
                user_id = callback_data.replace("apply_restrictions_", "")

                user_data_key = f'restriction_user_{user_id}'
                if user_data_key not in context.user_data:
                    await query.edit_message_text("❌ انتهت صلاحية الجلسة، يرجى المحاولة مرة أخرى")
                    return

                user_session = context.user_data[user_data_key]
                current_restrictions = user_session['current_restrictions']
                user_info = user_session['user_info']

                # تطبيق القيود
                if current_restrictions:
                    success, msg = self.user_manager.restrict_user(
                        user_id, user_info, current_restrictions, "تقييد متقدم من المدير"
                    )
                else:
                    # إزالة جميع القيود بطريقة شاملة
                    success, msg = self.remove_all_user_restrictions(user_id)

                # تنظيف شامل للبيانات
                if success:
                    await self.cleanup_empty_restrictions()
                    await self.force_reload_restrictions()

                if success:
                    result_message = self.text_processor.format_restriction_result_message(
                        user_info, current_restrictions
                    )

                    await query.edit_message_text(
                        result_message)

                    # تنظيف البيانات المؤقتة
                    if user_data_key in context.user_data:
                        del context.user_data[user_data_key]
                else:
                    await query.edit_message_text(f"❌ فشل في تطبيق القيود: {msg}")

            elif callback_data == "cancel_restriction":
                # إلغاء عملية التقييد
                await query.edit_message_text("❌ تم إلغاء عملية التقييد")

                # تنظيف البيانات المؤقتة
                keys_to_remove = [key for key in context.user_data.keys() if key.startswith('restriction_user_')]
                for key in keys_to_remove:
                    del context.user_data[key]

        except Exception as e:
            logger.error(f"خطأ في معالجة زر التقييد: {e}")
            await query.edit_message_text("❌ حدث خطأ في معالجة الطلب")

    def remove_all_user_restrictions(self, user_id: str) -> tuple[bool, str]:
        """إزالة جميع القيود من المستخدم بطريقة شاملة"""
        try:
            # تحميل البيانات
            restricted_users = self.user_manager.load_restricted_users()

            # إزالة المستخدم من قائمة المقيدين
            if user_id in restricted_users:
                del restricted_users[user_id]

                # حفظ البيانات
                self.user_manager.save_restricted_users(restricted_users)

                # التحقق من الحفظ
                verification_data = self.user_manager.load_restricted_users()
                if user_id not in verification_data:
                    logger.info(f"تم إزالة جميع القيود من المستخدم {user_id} بنجاح")
                    return True, "تم إزالة جميع القيود بنجاح"
                else:
                    logger.error(f"فشل في إزالة القيود من المستخدم {user_id}")
                    return False, "فشل في إزالة القيود"
            else:
                logger.info(f"المستخدم {user_id} غير مقيد أصلاً")
                return True, "المستخدم غير مقيد أصلاً"

        except Exception as e:
            logger.error(f"خطأ في إزالة القيود من المستخدم {user_id}: {e}")
            return False, f"خطأ في إزالة القيود: {e}"

    async def force_reload_restrictions(self):
        """إجبار إعادة تحميل بيانات التقييد في النظام"""
        try:
            # إنشاء ملف إشارة لإعادة التحميل
            import tempfile
            import time

            reload_signal_file = "data/reload_restrictions_signal.txt"
            with open(reload_signal_file, 'w', encoding='utf-8') as f:
                f.write(f"reload_requested_at_{int(time.time())}")

            logger.info("تم إرسال إشارة إعادة تحميل بيانات التقييد")

        except Exception as e:
            logger.error(f"خطأ في إرسال إشارة إعادة التحميل: {e}")

    async def diagnose_user_restrictions(self, update: Update, context: ContextTypes.DEFAULT_TYPE, user_id: str):
        """تشخيص حالة تقييد المستخدم"""
        try:
            # فحص البيانات في ملف الإدارة
            admin_restricted_users = self.user_manager.load_restricted_users()
            admin_has_restrictions = user_id in admin_restricted_users

            # فحص البيانات في البوت الرئيسي
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'main', 'features'))
            from ban_checker import ban_checker

            main_is_restricted, main_restriction_info = ban_checker.is_user_restricted(int(user_id), force_reload=True)
            cache_info = ban_checker.get_cache_info()

            # فحص إضافي: قراءة مباشرة من الملف
            import os
            import json
            base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            restrictions_file = os.path.join(base_dir, "data", "restricted_users.json")

            file_has_user = False
            file_restrictions = []
            if os.path.exists(restrictions_file):
                try:
                    with open(restrictions_file, 'r', encoding='utf-8') as f:
                        file_data = json.load(f)
                        if user_id in file_data:
                            file_has_user = True
                            file_restrictions = file_data[user_id].get('restrictions', [])
                except Exception as e:
                    logger.error(f"خطأ في قراءة ملف القيود: {e}")

            # تنسيق رسالة التشخيص
            admin_restrictions = admin_restricted_users.get(user_id, {}).get('restrictions', []) if admin_has_restrictions else []
            main_restrictions = main_restriction_info.get('restrictions', []) if main_restriction_info else []

            message = f"""🔍︙تشخيص حالة التقييد

👤︙المستخدم︙<code>{user_id}</code>

📊︙بيانات الإدارة︙
• مقيد في الإدارة︙{'✅ نعم' if admin_has_restrictions else '❌ لا'}
• القيود︙{admin_restrictions if admin_restrictions else 'لا توجد'}

📱︙بيانات البوت الرئيسي︙
• مقيد في البوت︙{'✅ نعم' if main_is_restricted else '❌ لا'}
• القيود︙{main_restrictions if main_restrictions else 'لا توجد'}

📄︙قراءة مباشرة من الملف︙
• موجود في الملف︙{'✅ نعم' if file_has_user else '❌ لا'}
• القيود في الملف︙{file_restrictions if file_restrictions else 'لا توجد'}

💾︙معلومات التخزين المؤقت︙
• يوجد تخزين مؤقت︙{'✅ نعم' if cache_info['cache_exists'] else '❌ لا'}
• عمر التخزين︙{cache_info['cache_age_seconds']:.1f} ثانية
• عدد المستخدمين المحفوظين︙{cache_info['cached_users_count']}

🔄︙تحليل الحالة︙
• الإدارة ↔ البوت الرئيسي︙{'✅ متطابق' if admin_has_restrictions == main_is_restricted else '❌ غير متطابق'}
• البوت الرئيسي ↔ الملف︙{'✅ متطابق' if main_is_restricted == file_has_user else '❌ غير متطابق'}
• الإدارة ↔ الملف︙{'✅ متطابق' if admin_has_restrictions == file_has_user else '❌ غير متطابق'}

📝 **التوصية:**
{self._get_diagnosis_recommendation(admin_has_restrictions, main_is_restricted, file_has_user)}"""

            await update.message.reply_text(
                message,
                reply_markup=self.get_user_management_keyboard()
            )

        except Exception as e:
            logger.error(f"خطأ في تشخيص التقييد: {e}")
            await update.message.reply_text(
                f"❌ خطأ في التشخيص: {e}",
                reply_markup=self.get_user_management_keyboard()
            )

    async def clear_restrictions_cache(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """مسح تخزين بيانات التقييد المؤقت"""
        try:
            # مسح التخزين المؤقت في البوت الرئيسي
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'main', 'features'))
            from ban_checker import ban_checker

            ban_checker.clear_cache()

            # إرسال إشارة إعادة التحميل
            await self.force_reload_restrictions()

            await update.message.reply_text(
                "✅ تم مسح التخزين المؤقت وإرسال إشارة إعادة التحميل",
                reply_markup=self.get_user_management_keyboard()
            )

        except Exception as e:
            logger.error(f"خطأ في مسح التخزين المؤقت: {e}")
            await update.message.reply_text(
                f"❌ خطأ في مسح التخزين المؤقت: {e}",
                reply_markup=self.get_user_management_keyboard()
            )

    async def cleanup_empty_restrictions(self):
        """تنظيف البيانات من القيود الفارغة"""
        try:
            # تحميل البيانات
            restricted_users = self.user_manager.load_restricted_users()
            original_count = len(restricted_users)

            # تنظيف المستخدمين بقيود فارغة
            cleaned_data = {}
            for user_id, user_data in restricted_users.items():
                restrictions = user_data.get('restrictions', [])
                if restrictions and len(restrictions) > 0:
                    cleaned_data[user_id] = user_data
                else:
                    logger.info(f"تم إزالة المستخدم {user_id} من قائمة المقيدين لأن قيوده فارغة")

            # حفظ البيانات المنظفة
            if len(cleaned_data) != original_count:
                self.user_manager.save_restricted_users(cleaned_data)
                logger.info(f"تم تنظيف {original_count - len(cleaned_data)} مستخدم من قائمة المقيدين")

        except Exception as e:
            logger.error(f"خطأ في تنظيف البيانات: {e}")

    def _get_diagnosis_recommendation(self, admin_has: bool, main_has: bool, file_has: bool) -> str:
        """الحصول على توصية بناءً على حالة التشخيص"""
        if not admin_has and not main_has and not file_has:
            return "✅ المستخدم غير مقيد في جميع الأنظمة - الحالة طبيعية"

        if admin_has and main_has and file_has:
            return "✅ المستخدم مقيد في جميع الأنظمة - الحالة متسقة"

        if not admin_has and (main_has or file_has):
            return "⚠️ يُنصح بتشغيل /cleanup لإزالة القيود المتبقية"

        if admin_has and not main_has:
            return "⚠️ يُنصح بتشغيل /clear_cache لتحديث البوت الرئيسي"

        return "⚠️ حالة غير متسقة - يُنصح بتشغيل /cleanup ثم /clear_cache"

    async def show_wallet_statistics(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض إحصائيات المحافظ"""
        try:
            # الحصول على إحصائيات المحافظ
            stats = self.wallet_manager.get_wallet_statistics()

            if not stats:
                await update.message.reply_text(
                    "❌ لا توجد بيانات محافظ متاحة",
                reply_markup=self.get_user_management_keyboard()
                )
                return

            # إنشاء رسالة الإحصائيات
            message = f"""💰︙إحصائيات المحافظ

📊︙الإحصائيات العامة︙
🔢︙إجمالي المحافظ : {stats['total_wallets']}
✅︙المحافظ النشطة : {stats['active_wallets']}
❌︙المحافظ المعطلة : {stats['inactive_wallets']}
💎︙إجمالي الرصيد : {stats['total_balance']:.2f} EXA

🔐︙حالة التحقق︙
✅︙محافظ مُحققة : {stats['verified_wallets']}
⚠️︙محافظ غير مُحققة : {stats['unverified_wallets']}

📅︙آخر تحديث : {stats['last_updated']}

💡︙ملاحظة : يتم إنشاء المحافظ تلقائياً عند دخول المستخدمين للبوت لأول مرة"""

            # إنشاء لوحة مفاتيح محفظة إكسا للعودة
            from telegram import ReplyKeyboardMarkup
            exa_wallet_keyboard = [
                ["💰 إحصائيات المحافظ"],
                ["🔙 العودة للقائمة الرئيسية"]
            ]
            reply_markup = ReplyKeyboardMarkup(exa_wallet_keyboard, resize_keyboard=True, one_time_keyboard=False)

            await update.message.reply_text(
                message,
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"خطأ في عرض إحصائيات المحافظ: {e}")
            # إنشاء لوحة مفاتيح محفظة إكسا للعودة في حالة الخطأ
            from telegram import ReplyKeyboardMarkup
            exa_wallet_keyboard = [
                ["💰 إحصائيات المحافظ"],
                ["🔙 العودة للقائمة الرئيسية"]
            ]
            reply_markup = ReplyKeyboardMarkup(exa_wallet_keyboard, resize_keyboard=True, one_time_keyboard=False)

            await update.message.reply_text(
                "❌ حدث خطأ في عرض إحصائيات المحافظ",
                reply_markup=reply_markup
            )

    # معالجات أزرار الإلغاء المتضمنة
    async def cancel_add_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """إلغاء إضافة مستخدم"""
        query = update.callback_query
        await query.answer()

        context.user_data.pop('waiting_for', None)
        await query.edit_message_text(
            "❌ تم إلغاء عملية إضافة المستخدم",
            reply_markup=None
        )

    async def cancel_remove_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """إلغاء إزالة مستخدم"""
        query = update.callback_query
        await query.answer()

        context.user_data.pop('waiting_for', None)
        await query.edit_message_text(
            "❌ تم إلغاء عملية إزالة المستخدم",
            reply_markup=None
        )

    async def cancel_ban_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """إلغاء حظر مستخدم"""
        query = update.callback_query
        await query.answer()

        context.user_data.pop('waiting_for', None)
        await query.edit_message_text(
            "❌ تم إلغاء عملية حظر المستخدم",
            reply_markup=None
        )

    async def cancel_restrict_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """إلغاء تقييد مستخدم"""
        query = update.callback_query
        await query.answer()

        context.user_data.pop('waiting_for', None)
        await query.edit_message_text(
            "❌ تم إلغاء عملية تقييد المستخدم",
            reply_markup=None
        )

    async def cancel_search_user(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """إلغاء البحث عن مستخدم"""
        query = update.callback_query
        await query.answer()

        context.user_data.pop('waiting_for', None)
        await query.edit_message_text(
            "❌ تم إلغاء عملية البحث عن المستخدم",
            reply_markup=None
        )

    async def cancel_user_report(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """إلغاء تقرير مستخدم"""
        query = update.callback_query
        await query.answer()

        context.user_data.pop('waiting_for', None)
        await query.edit_message_text(
            "❌ تم إلغاء عملية إنشاء تقرير المستخدم",
            reply_markup=None
        )
