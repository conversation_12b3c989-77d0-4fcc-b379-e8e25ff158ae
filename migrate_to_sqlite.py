#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت ترحيل النظام من JSON إلى SQLite
يقوم بتحويل جميع البيانات مع الحفاظ على سلامتها
"""

import os
import sys
import logging
from datetime import datetime

# إضافة مسار المجلد المشترك
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'shared'))

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('migration.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def check_requirements():
    """التحقق من المتطلبات"""
    try:
        import schedule
        import cryptography
        logger.info("✅ جميع المتطلبات متوفرة")
        return True
    except ImportError as e:
        logger.error(f"❌ مكتبة مفقودة: {e}")
        logger.error("يرجى تثبيت المتطلبات: pip install -r requirements.txt")
        return False

def main():
    """الدالة الرئيسية للترحيل"""
    print("="*60)
    print("🚀 سكريبت ترحيل النظام من JSON إلى SQLite")
    print("="*60)
    print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    # التحقق من المتطلبات
    if not check_requirements():
        return False
    
    try:
        # استيراد نظام الترحيل
        from database.migration_script import DataMigration
        
        print("\n🔄 بدء عملية الترحيل...")
        print("-" * 40)
        
        # إنشاء مثيل الترحيل
        migration = DataMigration()
        
        # تشغيل الترحيل الكامل
        report = migration.run_full_migration()
        
        print("\n" + "="*60)
        print("🎉 اكتملت عملية الترحيل بنجاح!")
        print("="*60)
        
        print(f"⏱️  المدة الإجمالية: {report['duration_seconds']:.2f} ثانية")
        print(f"📊 إجمالي السجلات المرحلة: {report['total_records']}")
        print(f"❌ إجمالي الأخطاء: {report['total_errors']}")
        
        print("\n📋 تفاصيل الترحيل:")
        print("-" * 30)
        
        for table_log in report['tables_migrated']:
            status = "✅" if table_log['errors'] == 0 else "⚠️"
            print(f"{status} {table_log['table']}: {table_log['migrated']} سجل")
            if table_log['errors'] > 0:
                print(f"   └── {table_log['errors']} أخطاء")
        
        print("\n📄 تقرير مفصل محفوظ في: backups/migration_report.json")
        print("📝 سجل الترحيل محفوظ في: migration.log")
        
        if report['total_errors'] == 0:
            print("\n🎊 تم الترحيل بدون أخطاء!")
        else:
            print(f"\n⚠️  تم الترحيل مع {report['total_errors']} أخطاء - راجع السجل للتفاصيل")
        
        print("\n" + "="*60)
        print("📌 الخطوات التالية:")
        print("1. راجع تقرير الترحيل للتأكد من سلامة البيانات")
        print("2. اختبر النظام للتأكد من عمله بشكل صحيح")
        print("3. قم بتشغيل النظام باستخدام: python start_system.py")
        print("="*60)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ فشلت عملية الترحيل: {e}")
        print(f"\n❌ خطأ في الترحيل: {e}")
        print("📝 راجع ملف migration.log للتفاصيل")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف عملية الترحيل بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ خطأ غير متوقع: {e}")
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
